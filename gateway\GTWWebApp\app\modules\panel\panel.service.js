System.register(["@angular/core", "./panel"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, panel_1, PanelService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (panel_1_1) {
                panel_1 = panel_1_1;
            }
        ],
        execute: function () {
            PanelService = (function () {
                function PanelService() {
                    this.panelList = [];
                }
                PanelService.prototype.panelInit = function () {
                    try {
                        if (this.panelLayoutDirection == null) {
                            if (localStorage.getItem("SDGDashboardLayoutDirection") != null)
                                this.panelLayoutDirection = Number(localStorage.getItem("SDGDashboardLayoutDirection"));
                            else
                                this.panelLayoutDirection = 1;
                        }
                    }
                    catch (err) {
                        this.panelLayoutDirection = 1;
                    }
                    if (this.panelList.length == 0) {
                        this.panelList.push(new panel_1.Panel("SDGConfigPanel_1", "configPanel.svg", "TR_CONFIG_VIEW_1", "", 0, 0, 0, 0, 0, "dashboardConfigComponent", true, "", "panel-heading-config-bg"));
                        this.panelList.push(new panel_1.Panel("SDGConfigPanel_2", "configPanel.svg", "TR_CONFIG_VIEW_2", "", 0, 0, 0, 0, 0, "dashboardConfigComponent", false, "", "panel-heading-config-bg"));
                        this.panelList.push(new panel_1.Panel("SDGConfigPanel_3", "configPanel.svg", "TR_CONFIG_VIEW_3", "", 0, 0, 0, 0, 0, "dashboardConfigComponent", false, "", "panel-heading-config-bg"));
                        this.panelList.push(new panel_1.Panel("SDGLogPanel", "dataLog.svg", "TR_LOG", "", 0, 0, 0, 0, 0, "dashboardLogComponent", true, ""));
                        this.toggleLayoutDasboard(this.panelLayoutDirection);
                        try {
                            var lsDashboardLayout = localStorage.getItem("SDGDashboardLayout");
                            if (lsDashboardLayout != null) {
                                var lsPanelList_1 = JSON.parse(lsDashboardLayout);
                                this.panelList.forEach(function (panel, index) {
                                    lsPanelList_1.forEach(function (lsPanel, index) {
                                        if (panel.lsName === lsPanel.lsName) {
                                            panel.width = lsPanel.width;
                                            panel.height = lsPanel.height;
                                            panel.x = lsPanel.x;
                                            panel.y = lsPanel.y;
                                            panel.zindex = lsPanel.zindex;
                                            panel.visible = lsPanel.visible;
                                            panel.parameters = lsPanel.parameters;
                                        }
                                    });
                                });
                            }
                        }
                        catch (err) {
                            localStorage.removeItem("SDGDashboardLayout");
                        }
                    }
                };
                PanelService.prototype.toggleLayoutDasboard = function (layoutDirection) {
                    this.panelLayoutDirection = layoutDirection;
                    var panelCount = this.panelList.filter(function (panel) { return panel.visible; }).length;
                    var topMargin = 60;
                    var rightMargin = 5;
                    var leftMargin = 5;
                    var bottomMargin = 10;
                    var gutter = 10;
                    if (layoutDirection == 2) {
                        this.panelList.filter(function (panel) { return panel.visible; }).forEach(function (panel, index) {
                            panel.width = (window.innerWidth / panelCount) - ((rightMargin + leftMargin) / panelCount) - gutter;
                            panel.height = window.innerHeight - topMargin - (gutter * 2) - bottomMargin;
                            panel.x = (panel.width * index) + rightMargin + (gutter * index);
                            panel.y = topMargin;
                        });
                    }
                    else {
                        this.panelList.filter(function (panel) { return panel.visible; }).forEach(function (panel, index) {
                            panel.width = window.innerWidth - rightMargin - leftMargin - gutter;
                            panel.height = ((window.innerHeight - topMargin - bottomMargin - gutter) / panelCount) - (gutter);
                            panel.x = rightMargin;
                            panel.y = (panel.height * index) + topMargin + (gutter * index);
                        });
                    }
                };
                PanelService.prototype.toggleLayoutDasboardCustom = function () {
                    try {
                        var lsDashboardLayout = localStorage.getItem("SDGDashboardLayout");
                        if (lsDashboardLayout != null) {
                            var lsPanelList_2 = JSON.parse(lsDashboardLayout);
                            this.panelList.forEach(function (panel, index) {
                                lsPanelList_2.forEach(function (lsPanel, index) {
                                    if (panel.lsName === lsPanel.lsName) {
                                        panel.width = lsPanel.width;
                                        panel.height = lsPanel.height;
                                        panel.x = lsPanel.x;
                                        panel.y = lsPanel.y;
                                        panel.zindex = lsPanel.zindex;
                                        panel.visible = lsPanel.visible;
                                        panel.parameters = lsPanel.parameters;
                                    }
                                });
                            });
                            this.panelLayoutDirection = 0;
                        }
                    }
                    catch (err) {
                        localStorage.removeItem("SDGDashboardLayout");
                    }
                };
                PanelService = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [])
                ], PanelService);
                return PanelService;
            }());
            exports_1("PanelService", PanelService);
        }
    };
});
//# sourceMappingURL=panel.service.js.map