System.register(["@angular/core", "@angular/common/http", "../data/api/api", "../modules/alert/alert.service", "../authentication/authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, http_1, api_1, alert_service_1, authentication_service_1, HelpUpdateComponent, UpdateResult;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            HelpUpdateComponent = (function () {
                function HelpUpdateComponent(miscService, alertService, authenticationService, http) {
                    this.miscService = miscService;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.http = http;
                    this.currentVersion = "";
                    this.updateVersion = "";
                    this.updateResult = UpdateResult.Wait;
                    this.updateResultEnum = UpdateResult;
                }
                HelpUpdateComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    try {
                        this.miscService.getAbout().subscribe(function (dataAbout) {
                            var aboutDTO;
                            aboutDTO = dataAbout;
                            _this.currentVersion = aboutDTO.currentVersion;
                            var mpExpires = aboutDTO.mpExpires;
                            if (mpExpires == null || _this.currentVersion == null) {
                                _this.updateResult = UpdateResult.NoLicense;
                                return;
                            }
                            var updateDetails = aboutDTO.releaseDetails;
                            if (updateDetails != null) {
                                if (_this.isMPValidForUpdate(mpExpires, updateDetails.ReleaseDate)) {
                                    _this.updateVersion = updateDetails.Version;
                                    if (_this.isNewerVersion(_this.currentVersion, updateDetails.Version)) {
                                        _this.updateResult = UpdateResult.NeedUpdate;
                                    }
                                    else {
                                        _this.updateResult = UpdateResult.UpToDate;
                                    }
                                }
                                else {
                                    _this.updateResult = UpdateResult.OutOffMaintenance;
                                }
                            }
                            else {
                                _this.updateResult = UpdateResult.Failed;
                            }
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_CAN_T_READ_UPDATE");
                            }
                            _this.updateResult = UpdateResult.Failed;
                        });
                    }
                    catch (e) {
                        this.alertService.error("TR_FAILED_CONTACT_UPDATE_SERVER");
                    }
                };
                HelpUpdateComponent.prototype.isNewerVersion = function (oldVer, newVer) {
                    var oldParts = oldVer.split('.');
                    var newParts = newVer.split('.');
                    for (var i = 0; i < newParts.length; i++) {
                        var a = ~~newParts[i];
                        var b = ~~oldParts[i];
                        if (a > b)
                            return true;
                        if (a < b)
                            return false;
                    }
                    return false;
                };
                HelpUpdateComponent.prototype.isMPValidForUpdate = function (mpExpires, versionRelease) {
                    if (this.isValidDate(mpExpires) && this.isValidDate(versionRelease)) {
                        var mpExpiresDate = new Date(mpExpires);
                        var versionReleaseDate = new Date(versionRelease);
                        if (versionReleaseDate <= mpExpiresDate)
                            return true;
                    }
                    return false;
                };
                HelpUpdateComponent.prototype.isValidDate = function (d) {
                    var date = Date.parse(d);
                    return !isNaN(date);
                };
                HelpUpdateComponent = __decorate([
                    core_1.Component({
                        selector: "helpUpdateComponent",
                        styles: ["\n   .center{\n      text-align: center;\n      font-weight: bold;\n    }\n\t"],
                        template: "\n\t<div>\n    <div class=\"panel panel-default panel-main\" style=\"width:99%; margin-left:6px; margin-top:60px\">\n      <div class=\"panel-heading\"><img src=\"../../images/about.svg\" class=\"module-icon\" />{{'TR_UPDATE' | translate}}</div>\n      <div class=\"panel-body center\">\n        <div *ngIf=\"updateResult===updateResultEnum.Failed\">{{'TR_FAILED_CONTACT_UPDATE_SERVER' | translate}}</div>\n        <div *ngIf=\"updateResult===updateResultEnum.Wait\">{{'TR_PLEASE_WAIT_UPDATE_SDG' | translate}}</div>\n        <div *ngIf=\"updateResult===updateResultEnum.NoLicense\">{{'TR_NO_LICENSE_UPDATE' | translate}}</div>\n        <div *ngIf=\"updateResult===updateResultEnum.OutOffMaintenance\" >{{'TR_LICENSE_OUT_OF_MAINTENANCE' | translate}}</div>\n        <div *ngIf=\"updateResult===updateResultEnum.UpToDate\">{{'TR_VERSION_UP_TO_DATE' | translate: { currentVersion: currentVersion } }}</div>\n        <div *ngIf=\"updateResult===updateResultEnum.NeedUpdate\">{{'TR_NEW_VERSION_AVAILABLE' | translate: { currentVersion: currentVersion, newVersion: updateVersion } }}</div>\n        <div *ngIf=\"updateResult===updateResultEnum.NeedUpdate\" [innerHTML]=\"'TR_GO_TO_TMW_TO_DOWNLOAD_UPDATE' | translate\"></div>\n      </div>\n\t</div>"
                    }),
                    __metadata("design:paramtypes", [api_1.MiscService, alert_service_1.AlertService, authentication_service_1.AuthenticationService, http_1.HttpClient])
                ], HelpUpdateComponent);
                return HelpUpdateComponent;
            }());
            exports_1("HelpUpdateComponent", HelpUpdateComponent);
            (function (UpdateResult) {
                UpdateResult[UpdateResult["Failed"] = 0] = "Failed";
                UpdateResult[UpdateResult["Wait"] = 1] = "Wait";
                UpdateResult[UpdateResult["NoLicense"] = 2] = "NoLicense";
                UpdateResult[UpdateResult["OutOffMaintenance"] = 3] = "OutOffMaintenance";
                UpdateResult[UpdateResult["UpToDate"] = 4] = "UpToDate";
                UpdateResult[UpdateResult["NeedUpdate"] = 5] = "NeedUpdate";
            })(UpdateResult || (UpdateResult = {}));
        }
    };
});
//# sourceMappingURL=help.update.component.js.map