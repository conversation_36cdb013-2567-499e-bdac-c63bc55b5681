System.register(["@angular/core", "./authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, authentication_service_1, CheckRolePipe;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            CheckRolePipe = (function () {
                function CheckRolePipe() {
                }
                CheckRolePipe.prototype.transform = function (role, minRole) {
                    if (authentication_service_1.UserRoleEnum[role] >= authentication_service_1.UserRoleEnum[minRole])
                        return true;
                    return false;
                };
                CheckRolePipe = __decorate([
                    core_1.Pipe({ name: 'checkRole' })
                ], CheckRolePipe);
                return CheckRolePipe;
            }());
            exports_1("CheckRolePipe", CheckRolePipe);
        }
    };
});
//# sourceMappingURL=check.role.pipe.js.map