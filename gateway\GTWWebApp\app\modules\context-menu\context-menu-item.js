System.register([], function (exports_1, context_1) {
    "use strict";
    var ContextMenuItem, ContextMenuType;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            ContextMenuItem = (function () {
                function ContextMenuItem(title, subject, type, value, isLicensed, icon) {
                    if (type === void 0) { type = ContextMenuType.ACTION; }
                    if (value === void 0) { value = null; }
                    if (isLicensed === void 0) { isLicensed = true; }
                    if (icon === void 0) { icon = null; }
                    this.title = title;
                    this.subject = subject;
                    this.type = type;
                    this.value = value;
                    this.isLicensed = isLicensed;
                    this.icon = icon;
                }
                return ContextMenuItem;
            }());
            exports_1("ContextMenuItem", ContextMenuItem);
            (function (ContextMenuType) {
                ContextMenuType[ContextMenuType["ACTION"] = 0] = "ACTION";
                ContextMenuType[ContextMenuType["ACTION_BOLD"] = 1] = "ACTION_BOLD";
                ContextMenuType[ContextMenuType["CHECKBOX"] = 2] = "CHECKBOX";
                ContextMenuType[ContextMenuType["SEPARATOR"] = 3] = "SEPARATOR";
            })(ContextMenuType || (ContextMenuType = {}));
            exports_1("ContextMenuType", ContextMenuType);
        }
    };
});
//# sourceMappingURL=context-menu-item.js.map