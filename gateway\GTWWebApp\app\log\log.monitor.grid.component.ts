import { Component, Input, OnInit, On<PERSON><PERSON>roy } from "@angular/core";
import { Subscription } from "rxjs";
import { Router } from "@angular/router";
import { LogEntryDTO } from "../data/model/models";
import { LogWSApi } from "../data/wsApi/wsApi";
import { LogService } from "../data/api/api";
import { AlertService } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { GlobalObjectToCSVUtil } from '../global/global.objectToCSV.util';
import { LogEntryContainPipe } from "../global/logEntryContain.pipe";

@Component({
	selector: "logMonitorGridComponent",
  template: `
      <table style="table-layout:fixed; width:100%;">
        <tr>
          <th style="width: 15%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 55%">&nbsp;</th>
        </tr>
        <tr *ngFor="let logEntry of logEntries | logEntryContain:logEntryFilter;" [ngClass]="{'error': (logEntry.severity == 'Exception' || logEntry.severity == 'Error')}">
          <td>{{logEntry.timeStamp}}</td>
          <td>{{logEntry.source}}</td>
          <td>{{logEntry.category}}</td>
          <td>{{logEntry.severity}}</td>
          <td>{{logEntry.message}}</td>
        </tr>
			</table>
      <button class="btn btn-default btn-sm copy-button" (click)="copyToCSV()"><img src="../../images/download.svg" class="image-button"/>&nbsp;{{'TR_DOWNLOAD_DISPLAYED_LOG_ENTRIES' | translate}}</button>
      <button class="btn btn-default btn-sm clear-button" (click)="clearDisplayLog()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CLEAR_DISPLAY' | translate}}</button>
		  <div *ngIf="logEntries.length == 0" class="grid-no-results">{{'TR_NO_RESULTS' | translate}}</div>
  `,
  styles: [`
          td {
				    padding-right: 60px;
				    padding-left: 6px;
            padding-top: 8px
          }
          th {
            line-height:1px;
            height: 1px;
            overflow: hidden;
          }
          tr:nth-of-type(odd) {
            background-color: rgba(255,	255, 255, 0.35) !important;
          }
          .error {
            color: red;
          }
          .copy-button {
            position: absolute;
            top: 106px;
            right: 300px;
            z-index: 999;
          }
          .clear-button {
            position: absolute;
            top: 106px;
            right: 150px;
            z-index: 999;
          }
        `]
})

export class LogMonitorGridComponent implements OnInit, OnDestroy {
  private logServiceSubscription: Subscription;
  @Input() logEntryFilter: LogEntryDTO = {};
  private logEntries: LogEntryDTO[] = [];
  private websocket: WebSocket;
  private reconnectAttempts: number = 0;

  constructor(private monitorLogService: LogService, private monitorLogWSApi: LogWSApi, private authenticationService: AuthenticationService, private logEntryContainPipe: LogEntryContainPipe, private alertService: AlertService, private translate: TranslateService, private router: Router) {
  }

  public ngOnInit(): void {
    this.loadAPI();
  }

  public ngOnDestroy(): void {
    if (this.websocket && this.websocket.readyState != null && this.websocket.readyState === WebSocket.OPEN)
      this.websocket.close();
    if (this.logServiceSubscription != null)
      this.logServiceSubscription.unsubscribe();
  }

  private loadAPI(): void {
    let monWSHostPort: string = "ws://" + location.host;
    let monHostPort: string = "http://" + location.host + "/rest";
    if (location.protocol === "https:") {
      monWSHostPort = "wss://" + location.host;
      monHostPort = "https://" + location.host + "/rest";
    }
    this.monitorLogWSApi.basePath = monWSHostPort;
    this.monitorLogService.basePath = monHostPort;

    this.monitorLogWSApi.openWebsocket().subscribe(
      data => {
        this.websocket = data;
        this.wsGetLogData();
      },
      error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Open fail" }); }
    );


    this.monitorLogService.getLastLogEntryID().subscribe(
      lastLogEntryID => {
        if (isNaN(lastLogEntryID))
          lastLogEntryID = 0;
        if (lastLogEntryID && lastLogEntryID > 500) {
          this.monitorLogService.getLogEntriesRange(lastLogEntryID - 500).subscribe(
            data => {
              if (GlobalObjectToCSVUtil.isNotEmpty(data))
                this.logEntries = data;
            },
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
            }
          );
        }
        else {
          this.monitorLogService.basePath = monHostPort;
          this.monitorLogService.getLogEntriesRange(0, lastLogEntryID).subscribe(
            data => {
              if (GlobalObjectToCSVUtil.isNotEmpty(data))
                this.logEntries = data;
            },
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
            }
          );
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      }
    );
  }

  private copyToCSV(): void {
    let logEntriesFiltered: Array<LogEntryDTO> = []
    logEntriesFiltered = this.logEntryContainPipe.transform(this.logEntries, this.logEntryFilter);
    GlobalObjectToCSVUtil.copyToCSV("ErrorLog.csv", logEntriesFiltered);
  }

  private clearDisplayLog(): void {
    this.logEntries = [];
  }

  private wsGetLogData(): void {
    this.logServiceSubscription = this.monitorLogWSApi.getLogData(this.websocket).subscribe(
      (event) => {
        if (event.type === 'message') {
          this.reconnectAttempts = 0;
          let wsLogEntries = JSON.parse(event.data);
          Array.prototype.push.apply(this.logEntries, wsLogEntries);
          if (this.logEntries.length > 1000)
            this.logEntries = this.logEntries.slice(this.logEntries.length - 1000, 1000);
        }
        else if (event.type === 'close') {
          if (this.reconnectAttempts < 3) {
            setTimeout(() => {
              const currentPath: string = this.router.url;
              const pageName: string = currentPath.split('/').pop();
              if (currentPath !== "/monitorLog")
                return;
              this.reconnectAttempts++;
              this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getLogEntries", reconnectAttempt: this.reconnectAttempts }).subscribe(res => {
                this.alertService.debug(res);
              });
              this.monitorLogWSApi.openWebsocket().subscribe(
                data => {
                  this.websocket = data;
                  this.wsGetLogData();
                },
                error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Open fail" }); }
              );
            }, 5000);
          }
        }
      }
    );
  }
}
