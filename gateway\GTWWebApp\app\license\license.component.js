System.register(["@angular/core", "../data/api/api", "../modules/alert/alert.service", "../modules/grid/column", "../authentication/authentication.service", "@ngx-translate/core", "../global/keys.pipe", "../modules/loader/loader.service", "../modules/download/download.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, api_1, alert_service_1, column_1, authentication_service_1, core_2, keys_pipe_1, loader_service_1, download_component_1, LicenseComponent, LicenceForm;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (column_1_1) {
                column_1 = column_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (keys_pipe_1_1) {
                keys_pipe_1 = keys_pipe_1_1;
            },
            function (loader_service_1_1) {
                loader_service_1 = loader_service_1_1;
            },
            function (download_component_1_1) {
                download_component_1 = download_component_1_1;
            }
        ],
        execute: function () {
            LicenseComponent = (function () {
                function LicenseComponent(alertService, authenticationService, configService, fileService, translate, keysPipe, loaderService) {
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.configService = configService;
                    this.fileService = fileService;
                    this.translate = translate;
                    this.keysPipe = keysPipe;
                    this.loaderService = loaderService;
                    this.licenceForm = new LicenceForm(null, "", false);
                    this.columns = this.getColumns();
                }
                LicenseComponent.prototype.ngOnInit = function () {
                    this.licenceForm.isNewLicense = true;
                    this.refreshLicense();
                };
                LicenseComponent.prototype.refreshLicense = function () {
                    var _this = this;
                    this.configService.getLicense().subscribe(function (data) {
                        _this.licenseInfo = data;
                        _this.rows = _this.licenseInfo.features;
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("LICENSE.ERROR_CAN_T_READ_LICENSE");
                        }
                    });
                };
                LicenseComponent.prototype.isNumber = function (val) {
                    return typeof val === 'number';
                };
                LicenseComponent.prototype.isChecked = function (val) {
                    return val == "1" ? true : false;
                };
                LicenseComponent.prototype.getColumns = function () {
                    return [
                        new column_1.Column("feature", "TR_OPTIONS", "", ""),
                        new column_1.Column("isLicensed", "TR_IS_LICENSED", "", "")
                    ];
                };
                LicenseComponent.prototype.onStopMonClick = function () {
                    var _this = this;
                    this.configService.stopMon(true).subscribe(function (data) {
                        _this.alertService.success("TR_MONITOR_IS_RE_STARTING");
                        _this.loaderService.toggleIsLoading(true);
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                    });
                };
                LicenseComponent.prototype.onlineActivation = function () {
                    var _this = this;
                    if (this.licenceForm.product_key != "") {
                        this.licenceForm.action_type = "onlineActivation";
                        this.configService.saveLicense(this.licenceForm.action_type, this.licenceForm.isNewLicense, this.licenceForm.product_key).subscribe(function (data) {
                            if (data != null && data != "") {
                                var result = data.result;
                                if (result) {
                                    _this.alertService.success("TR_SUCCESS");
                                    _this.onStopMonClick();
                                }
                            }
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error(error.error, null, null, 2 + 1 + 4);
                            }
                        });
                    }
                };
                LicenseComponent.prototype.offlineActivationStep1 = function () {
                    var _this = this;
                    this.licenceForm.action_type = "offlineActivationStep1";
                    this.configService.saveLicense(this.licenceForm.action_type, this.licenceForm.isNewLicense).subscribe(function (data) {
                        if (data != null && data != "") {
                            var result = data.result;
                            var fileC2V = data.C2V;
                            if (result && fileC2V != "") {
                                var blob = new Blob([fileC2V], { type: "text/csv" });
                                var url = window.URL.createObjectURL(blob);
                                _this.saveData(blob, "license.c2v");
                                _this.alertService.success("TR_SUCCESS");
                            }
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_CAN_T_SAVE_LICENSE");
                        }
                    });
                };
                LicenseComponent.prototype.offlineActivationStep2 = function (event) {
                    var _this = this;
                    var fileList = event.target.files;
                    if (fileList.length > 0) {
                        var file = fileList[0];
                        this.uploadV2CFile.nativeElement.value = "";
                        this.configService.sendV2CLicense(file).subscribe(function (data) {
                            if (data != null && data != "") {
                                var result = data.result;
                                if (result) {
                                    _this.alertService.success("TR_SUCCESS");
                                    _this.onStopMonClick();
                                }
                            }
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_CAN_T_SAVE_LICENSE");
                            }
                        });
                    }
                };
                LicenseComponent.prototype.onlogFileDownloadClick = function () {
                    var _this = this;
                    this.fileService.licenseLogZipFileGet().subscribe(function (data) {
                        _this.saveData(data, "LicenseLogs.zip");
                        _this.alertService.success("TR_FILE_DOWNLOADED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED");
                        }
                    });
                };
                LicenseComponent.prototype.saveData = function (blob, fileName) {
                    var a = document.createElement("a");
                    document.body.appendChild(a);
                    var url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = fileName;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    a.remove();
                };
                __decorate([
                    core_1.ViewChild("uploadV2CFile", { static: false }),
                    __metadata("design:type", Object)
                ], LicenseComponent.prototype, "uploadV2CFile", void 0);
                __decorate([
                    core_1.ViewChild("download", { static: false }),
                    __metadata("design:type", download_component_1.DownloadComponent)
                ], LicenseComponent.prototype, "download", void 0);
                LicenseComponent = __decorate([
                    core_1.Component({
                        selector: "logMonitorComponent",
                        providers: [keys_pipe_1.KeysPipe],
                        templateUrl: "app/license/license.component.template.html",
                        styles: ["\n    td {\n      padding: 10px;\n    }\n    .table>tr>td,\n    .table>tr>th{\n\t\t  padding: 4px !important;\n      vertical-align: middle !important;\n      line-height: 22px;\n    }\n    .table>tr:hover{\n      background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n    }\n    .table{\n      border: 1px solid lightgray;\n    }\n    .table-striped>tr:nth-child(odd)>td, \n    .table-striped>tr:nth-child(odd)>th {\n      background-color: rgba(0, 0, 0, 0.03) !important;\n    }\n    legend {\n      width: auto;\n      margin-bottom: 0px;\n      font-size: 12px;\n      border-bottom: none;\n    }\n    fieldset {\n      padding: 4px;\n      margin-left: -4px;\n      border: 1px solid lightgray;\n  }\n  "]
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, authentication_service_1.AuthenticationService, api_1.ConfigService, api_1.FileService, core_2.TranslateService, keys_pipe_1.KeysPipe, loader_service_1.LoaderService])
                ], LicenseComponent);
                return LicenseComponent;
            }());
            exports_1("LicenseComponent", LicenseComponent);
            LicenceForm = (function () {
                function LicenceForm(action_type, product_key, isNewLicense) {
                    this.action_type = action_type;
                    this.product_key = product_key;
                    this.isNewLicense = isNewLicense;
                }
                return LicenceForm;
            }());
        }
    };
});
//# sourceMappingURL=license.component.js.map