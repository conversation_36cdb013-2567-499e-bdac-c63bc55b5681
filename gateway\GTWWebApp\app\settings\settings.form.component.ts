﻿import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Subscription } from 'rxjs';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ConfigService, FileService, ManageService } from "../data/api/api";
import { KeysPipe } from "../global/keys.pipe";
import { SDGConfigDTO,  EngineStateEnumDTO, BroadcastEventTypeEnumDTO } from "../data/model/models";
import { AlertService, messageLogMask } from '../modules/alert/alert.service';
import { AuthenticationService } from "../authentication/authentication.service";
import { GlobalDataService } from "../global/global.data.service";
import { TranslateService } from "@ngx-translate/core";
import { Modal } from "ngx-modialog-7/plugins/bootstrap";
import { LoaderService } from "../modules/loader/loader.service";

@Component({
  selector: "settingsFormComponent",
  providers: [KeysPipe],
	styles: [`
		.filterCheckbox {
			display: inline-block;
			width: 140px;
			height: 10px;
			margin: 1px;
		}
    .warning-editable{
      color: #ff4242;
      padding-right: 4px;
      top: 1px;
    }
    .asterisk {
      color: red;
      font-weight: 900;
      display: inline;
    }`
	],
  templateUrl: "app/settings/settings.form.component.template.html"
})
export class SettingsFormComponent implements OnInit, OnDestroy {
	public configForm: FormGroup; // our model driven form
	public submitted: boolean; // keep track on whether form is submitted
  private currentConfig: SDGConfigDTO = {};
  private isSSLParamVisible: boolean = true;
  private isNoExpirationViewer: boolean = false;
  private httpsPrivateKeyList: string[];
  private httpsCertificateList: string[];
  private isLoading: boolean = false;
  private isLoadingChangeSubscriptionShow: Subscription

  constructor(private configService: ConfigService, private fileService: FileService, private manageService: ManageService, private alertService: AlertService, private authenticationService: AuthenticationService, private globalDataService: GlobalDataService, private modal: Modal, private translate: TranslateService, private keysPipe: KeysPipe, private loaderService: LoaderService) { }

  public ngOnInit(): void {
    this.configForm = new FormGroup({
      gtwTzPath: new FormControl("", [<any>Validators.required]),
      gtwAllowedIPs: new FormControl(""),
      gtwDoValidateConfig: new FormControl(""),
      gtwHost: new FormControl("", [<any>Validators.required]),
      gtwHttpPort: new FormControl(0, [<any>Validators.required]),
      gtwWebDir: new FormControl("", [<any>Validators.required]),
      gtwDoAuth: new FormControl(false),
      gtwDoAudit: new FormControl(false),
      fullLogOnRestart: new FormControl(false),
      mirrorAllToLog: new FormControl(false),
      gtwUseWebSSL: new FormControl(false),
      gtwUseLocalHostForEngineAndMonitorComms: new FormControl(false),
      gtwEnableHttpDeflate: new FormControl(false),
      gtwAuthExpVIEWER_ROLE: new FormControl(0, [<any>Validators.required, Validators.min(360), Validators.max(36000)]),
      gtwAuthExpOPERATOR_ROLE: new FormControl(0, [<any>Validators.required, Validators.min(360), Validators.max(36000)]),
      gtwAuthExpCONFIGURATOR_ROLE: new FormControl(0, [<any>Validators.required, Validators.min(360), Validators.max(36000)]),
      gtwAuthExpSU_ROLE: new FormControl(0, [<any>Validators.required, Validators.min(360), Validators.max(36000)]),
      monHost: new FormControl(""),
      monHttpPort: new FormControl(0, [<any>Validators.required]),
      gtwWsUpdateRate: new FormControl(0, [<any>Validators.required, Validators.min(1)]),
      gtwLicGracePeriodInMinutes: new FormControl(0, [<any>Validators.required, , Validators.min(0), Validators.max(1440)]),
      gtwWsUpdateBlockSize: new FormControl(0, [<any>Validators.required, Validators.min(100)]),
      gtwHttpPageBlockSize: new FormControl(0, [<any>Validators.required]),
      currentWorkSpaceName: new FormControl("", [<any>Validators.required]),
      enableIECFullStackAddressing: new FormControl(false),
      gtwMaxLogFiles: new FormControl(0, [<any>Validators.required, Validators.min(10), Validators.max(100)]),
      gtwMaxLogBufferEntries: new FormControl(0, [<any>Validators.required, Validators.min(0), Validators.max(100000)]),
      gtwHttpsCertIsTmwSigned: new FormControl("")
    });
		if (this.globalDataService.SDGConfig != null) {
			this.currentConfig = this.globalDataService.SDGConfig;
			(<FormGroup>this.configForm).setValue(this.currentConfig, { onlySelf: true });
    }
    this.checkboxSSLClick();

    if (this.configForm.controls["gtwAuthExpVIEWER_ROLE"].value == 0) {
      this.isNoExpirationViewer = true;
      this.configForm.controls["gtwAuthExpVIEWER_ROLE"].setValidators([<any>Validators.required, Validators.min(0), Validators.max(36000)]);
    }

    if (this.configForm.controls["gtwAuthExpVIEWER_ROLE"].value < 360)
      this.configForm.controls["gtwAuthExpVIEWER_ROLE"].setValue(360);
    if (this.configForm.controls["gtwAuthExpOPERATOR_ROLE"].value < 360)
      this.configForm.controls["gtwAuthExpOPERATOR_ROLE"].setValue(360);
    if (this.configForm.controls["gtwAuthExpCONFIGURATOR_ROLE"].value < 360)
      this.configForm.controls["gtwAuthExpCONFIGURATOR_ROLE"].setValue(360);
    if (this.configForm.controls["gtwAuthExpSU_ROLE"].value < 360)
      this.configForm.controls["gtwAuthExpSU_ROLE"].setValue(360);

    this.isLoadingChangeSubscriptionShow = this.loaderService.isLoadingChange.subscribe((value) => {
      this.isLoading = value;
    });
  }

  public ngOnDestroy(): void {
    if (this.isLoadingChangeSubscriptionShow) {
      this.isLoadingChangeSubscriptionShow.unsubscribe();
    }
  }

  private save(model: SDGConfigDTO, isValid: boolean): void {
		this.submitted = true; // set form submit to true
		if (isValid){
      if (this.authenticationService.userApiConfig.apiKeys["Authorization"] != null) {
        if (
          this.currentConfig.gtwDoValidateConfig != model.gtwDoValidateConfig ||
          this.currentConfig.monHost != model.monHost ||
          this.currentConfig.gtwHost != model.gtwHost ||
          this.currentConfig.monHttpPort != model.monHttpPort ||
          this.currentConfig.gtwHttpPort != model.gtwHttpPort ||
          this.currentConfig.gtwWebDir != model.gtwWebDir ||
          this.currentConfig.gtwUseWebSSL != model.gtwUseWebSSL ||
          this.currentConfig.gtwAllowedIPs != model.gtwAllowedIPs ||
          this.currentConfig.gtwDoAuth != model.gtwDoAuth ||
          this.currentConfig.enableIECFullStackAddressing != model.enableIECFullStackAddressing ||
          this.currentConfig.gtwHttpPageBlockSize != model.gtwHttpPageBlockSize
          ) {
          this.confirmSaveModal(model);
        }
        else {
          this.saveModel(model, false, model.gtwDoValidateConfig);
        }
			}
    }
  }

  private saveModel(model: SDGConfigDTO, isRestartNeeded: boolean, validateFlag: boolean): void {
    if (isRestartNeeded)
      this.loaderService.toggleIsLoading(true);
    else
      this.manageLoading(true);

    this.configService.setConfigJson(model, isRestartNeeded, validateFlag).subscribe(
      data => {
        if (data) {
          this.globalDataService.SDGConfig = model;
          this.currentConfig = this.globalDataService.SDGConfig;
          this.alertService.success("TR_DATA_SAVED");
        }
        else {
          this.alertService.error("TR_ERROR_DATA_NOT_SAVED");
          this.manageLoading(false);
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_DATA_NOT_SAVED"); }
      }
    );
  }

  private confirmSaveModal(model: SDGConfigDTO): void {
    if (this.globalDataService != null && this.globalDataService.healthData != null && this.globalDataService.healthData.engineState === EngineStateEnumDTO.RUNNING.toString()) {
      let confirmSaveModalRef;
      this.translate.get("TR_THE_MONITOR_AND_GATWAY_NEED_TO_RESTART_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_WORKSPACE").subscribe(res => {
        confirmSaveModalRef = this.modal.confirm()
          .size('lg')
          .showClose(false)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_YES'))
          .cancelBtn(this.translate.instant('TR_NO'))
          .okBtnClass('btn btn-default')
          .body(`
				    <div class="panel panel-warning">
					    <div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				    </div>
          `)
          .open()
      });
      confirmSaveModalRef.result.then(
        result => {
          this.manageService.saveFile().subscribe(
            data => this.alertService.success("TR_FILE_SAVED"),
            error => {
              if (error.status == 401) {
                this.authenticationService.onLoginFailed("/");
              }
              else {
                this.alertService.show({ messageKey: "TR_THERE_WERE_PROBLEMS_SAVING_THE_INI_CSV_FILES", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError }, false);
                this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
              }
            },
            () => this.saveModel(model, true, model.gtwDoValidateConfig)
          )
        },
        () => this.saveModel(model, true, model.gtwDoValidateConfig)
      );
    }
    else {
      this.saveModel(model, true, model.gtwDoValidateConfig);
    }
  }

  private checkboxSSLClick(): void {
    this.isSSLParamVisible = true;
    if (!this.configForm.controls["gtwUseWebSSL"].value)
      this.isSSLParamVisible = false;
  }
  
  private checkboxNoExpirationViewerClick(event: any): void {
    if (event.target.checked) {
      this.configForm.controls["gtwAuthExpVIEWER_ROLE"].setValidators([<any>Validators.required, Validators.min(0), Validators.max(36000)]);
      this.configForm.controls["gtwAuthExpVIEWER_ROLE"].setValue(0);
      this.isNoExpirationViewer = true;
    }
    else {
      this.configForm.controls["gtwAuthExpVIEWER_ROLE"].setValidators([<any>Validators.required, Validators.min(360), Validators.max(36000)]);
      this.configForm.controls["gtwAuthExpVIEWER_ROLE"].setValue(3600);
      this.isNoExpirationViewer = false;
    }
  }

  private checkboxDoAuthClick(): void {
    if (this.configForm.controls["gtwDoAuth"].value && this.isSSLParamVisible == false) {
      this.configForm.controls["gtwDoAuth"].setValue(false);
      this.alertService.error("TR_ERROR_ENABLING_AUTHENTICATION_IS_ONLY_AVAILABLE_IF_HTTPS_IS_ENABLED", null, null, messageLogMask.alertPopup + messageLogMask.eventLog + messageLogMask.statusBar);
    }
  }

  private manageLoading(isLoading: boolean): void {
    this.loaderService.toggleIsLoading(isLoading);
    if (this.isLoading) {
      let loadingInterval = setInterval(() => {
        if (this.globalDataService != null && this.globalDataService.healthData != null && this.globalDataService.healthData.engineState === EngineStateEnumDTO.RUNNING.toString()) {
          this.loaderService.toggleIsLoading(false);
          clearInterval(loadingInterval);
        }
      }, 10000);
    }
  }

  private onChangeGTWHost(): void {
    this.configForm.controls["monHost"].setValue(this.configForm.controls["gtwHost"].value); 
  }

  private cancel(): void {
    (<FormGroup>this.configForm).reset(this.currentConfig, { onlySelf: true });
  }
}