System.register([], function (exports_1, context_1) {
    "use strict";
    var EngineExitFailStateEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (EngineExitFailStateEnumDTO) {
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["SUCCESS"] = 'SUCCESS'] = "SUCCESS";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["EXCEPTION"] = 'EXCEPTION'] = "EXCEPTION";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["VALIDATE"] = 'VALIDATE'] = "VALIDATE";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["HTTPSTART"] = 'HTTP_START'] = "HTTPSTART";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["HTTPCREATE"] = 'HTTP_CREATE'] = "HTTPCREATE";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["HTTPSSTART"] = 'HTTPS_START'] = "HTTPSSTART";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["HTTPSCREATE"] = 'HTTPS_CREATE'] = "HTTPSCREATE";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["CREATEWORKSPACEDIR"] = 'CREATE_WORKSPACE_DIR'] = "CREATEWORKSPACEDIR";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["INILOAD"] = 'INI_LOAD'] = "INILOAD";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["SAVEPOINTMAP"] = 'SAVE_POINTMAP'] = "SAVEPOINTMAP";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["INITBEFORECSV"] = 'INIT_BEFORE_CSV'] = "INITBEFORECSV";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["OPCCLASSICSTART"] = 'OPC_CLASSIC_START'] = "OPCCLASSICSTART";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["LOADPOINTMAP"] = 'LOAD_POINTMAP'] = "LOADPOINTMAP";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["CREATETRIALLICENSE"] = 'CREATE_TRIAL_LICENSE'] = "CREATETRIALLICENSE";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["NOLICENSE"] = 'NO_LICENSE'] = "NOLICENSE";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["NOINIFILE"] = 'NO_INI_FILE'] = "NOINIFILE";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["INVALIDMONITORLOCATION"] = 'INVALID_MONITOR_LOCATION'] = "INVALIDMONITORLOCATION";
                EngineExitFailStateEnumDTO[EngineExitFailStateEnumDTO["KEYREJECTED"] = 'KEY_REJECTED'] = "KEYREJECTED";
            })(EngineExitFailStateEnumDTO || (EngineExitFailStateEnumDTO = {}));
            exports_1("EngineExitFailStateEnumDTO", EngineExitFailStateEnumDTO);
        }
    };
});
//# sourceMappingURL=EngineExitFailStateEnumDTO.js.map