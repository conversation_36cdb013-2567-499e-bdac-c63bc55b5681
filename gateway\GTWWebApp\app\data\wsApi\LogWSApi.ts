﻿import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { Configuration } from '../configuration';
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { share, map } from "rxjs/operators";

@Injectable()
export class LogWSApi {
  public basePath: string = "ws://localhost/rest";
	public configuration: Configuration = new Configuration();

  constructor(private alertService: AlertService, private translate: TranslateService) {
	}


  public openWebsocket(): Observable<WebSocket> {
    let websocket = new WebSocket(this.basePath + "/getLogEntries?token=" + this.configuration.apiKeys["Authorization"]);
    return Observable.create(observer => {
      websocket.onopen = (openEvent) => {
        this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getLogEntries" }).subscribe(res => {
          this.alertService.debug(res);
        });
        observer.next(websocket);
        observer.complete();
      };
    })
  }

  public getLogData(websocket: WebSocket): Observable<any> {
		return Observable.create(observer => {
			websocket.onmessage = (evt) => {
				observer.next(evt);
      };
      websocket.onclose = (evt) => {
        if (evt.reason === "panelClose") {
          this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "panelClose" }).subscribe(res => {
            this.alertService.debug(res);
          });
        }
        else {
          this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: evt.type }).subscribe(res => {
            this.alertService.debug(res);
          });
          observer.next(evt);
        }
        observer.complete();
      };
    })
    .pipe(share());
	}
}