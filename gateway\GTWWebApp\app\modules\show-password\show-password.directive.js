System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ShowPassword;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            ShowPassword = (function () {
                function ShowPassword(el) {
                    this.el = el;
                    this.shown = false;
                    this.setup();
                }
                ShowPassword.prototype.toggle = function (div) {
                    this.shown = !this.shown;
                    if (this.shown) {
                        this.el.nativeElement.setAttribute("type", "text");
                        div.className = "glyphicon glyphicon-eye-close";
                    }
                    else {
                        this.el.nativeElement.setAttribute("type", "password");
                        div.className = "glyphicon glyphicon-eye-open";
                    }
                };
                ShowPassword.prototype.setup = function () {
                    var _this = this;
                    var parent = this.el.nativeElement.parentNode.parentNode;
                    var div = document.createElement("div");
                    div.setAttribute("style", "display: inline-block;width:20px; text-align: right;");
                    div.className = "glyphicon glyphicon-eye-open";
                    div.addEventListener("click", function (event) {
                        _this.toggle(div);
                    });
                    parent.appendChild(div);
                };
                ShowPassword = __decorate([
                    core_1.Directive({
                        selector: "[show-password]"
                    }),
                    __metadata("design:paramtypes", [core_1.ElementRef])
                ], ShowPassword);
                return ShowPassword;
            }());
            exports_1("ShowPassword", ShowPassword);
        }
    };
});
//# sourceMappingURL=show-password.directive.js.map