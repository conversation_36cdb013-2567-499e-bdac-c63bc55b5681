import { Directive, ElementRef } from "@angular/core";

@Directive({
  selector: "[show-password]"
})
export class ShowPassword {
  private shown = false;

  constructor(private el: ElementRef) {
    this.setup();
  }

  private toggle(div: HTMLElement): void {
    this.shown = !this.shown;
    if (this.shown) {
      this.el.nativeElement.setAttribute("type", "text");
      div.className = "glyphicon glyphicon-eye-close";
    } else {
      this.el.nativeElement.setAttribute("type", "password");
      div.className = "glyphicon glyphicon-eye-open";
    }
  }

  private setup(): void {
    const parent = this.el.nativeElement.parentNode.parentNode;
    const div = document.createElement("div");
    div.setAttribute("style", "display: inline-block;width:20px; text-align: right;");
    div.className = "glyphicon glyphicon-eye-open";
    div.addEventListener("click", (event) => {
      this.toggle(div);
    });
    parent.appendChild(div);
  }
}