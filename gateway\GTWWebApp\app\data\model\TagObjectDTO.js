System.register([], function (exports_1, context_1) {
    "use strict";
    var TagObjectDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (TagObjectDTO) {
                var TagValueTypeEnum;
                (function (TagValueTypeEnum) {
                    TagValueTypeEnum[TagValueTypeEnum["Unknown"] = 'Unknown'] = "Unknown";
                    TagValueTypeEnum[TagValueTypeEnum["Bool"] = 'Bool'] = "Bool";
                    TagValueTypeEnum[TagValueTypeEnum["Spare"] = 'Spare'] = "Spare";
                    TagValueTypeEnum[TagValueTypeEnum["Char"] = 'Char'] = "Char";
                    TagValueTypeEnum[TagValueTypeEnum["UnsignedChar"] = 'Unsigned Char'] = "UnsignedChar";
                    TagValueTypeEnum[TagValueTypeEnum["Short"] = 'Short'] = "Short";
                    TagValueTypeEnum[TagValueTypeEnum["UnsignedShort"] = 'Unsigned Short'] = "UnsignedShort";
                    TagValueTypeEnum[TagValueTypeEnum["Long"] = 'Long'] = "Long";
                    TagValueTypeEnum[TagValueTypeEnum["UnsignedLong"] = 'Unsigned Int'] = "UnsignedLong";
                    TagValueTypeEnum[TagValueTypeEnum["Float"] = 'Float'] = "Float";
                    TagValueTypeEnum[TagValueTypeEnum["Double"] = 'Double'] = "Double";
                    TagValueTypeEnum[TagValueTypeEnum["String"] = 'String'] = "String";
                    TagValueTypeEnum[TagValueTypeEnum["Time"] = 'Time'] = "Time";
                    TagValueTypeEnum[TagValueTypeEnum["Int64"] = 'Int 64'] = "Int64";
                    TagValueTypeEnum[TagValueTypeEnum["UnsignedInt64"] = 'Unsigned Int 64'] = "UnsignedInt64";
                    TagValueTypeEnum[TagValueTypeEnum["Empty"] = ''] = "Empty";
                })(TagValueTypeEnum = TagObjectDTO.TagValueTypeEnum || (TagObjectDTO.TagValueTypeEnum = {}));
                var TagValueTypeTextEnum;
                (function (TagValueTypeTextEnum) {
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Unknown"] = 'Unknown'] = "Unknown";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Bool"] = 'Bool'] = "Bool";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Spare"] = 'Spare'] = "Spare";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Char"] = 'Char'] = "Char";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Unsigned Char"] = 'Unsigned Char'] = "Unsigned Char";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Short"] = 'Short'] = "Short";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Unsigned Short"] = 'Unsigned Short'] = "Unsigned Short";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Long"] = 'Long'] = "Long";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Unsigned Int"] = 'Unsigned Int'] = "Unsigned Int";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Float"] = 'Float'] = "Float";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Double"] = 'Double'] = "Double";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["String"] = 'String'] = "String";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Time"] = 'Time'] = "Time";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Int 64"] = 'Int 64'] = "Int 64";
                    TagValueTypeTextEnum[TagValueTypeTextEnum["Unsigned Int 64"] = 'Unsigned Int 64'] = "Unsigned Int 64";
                    TagValueTypeTextEnum[TagValueTypeTextEnum[""] = 'Empty'] = "";
                })(TagValueTypeTextEnum = TagObjectDTO.TagValueTypeTextEnum || (TagObjectDTO.TagValueTypeTextEnum = {}));
                var TagCollectionKindEnum;
                (function (TagCollectionKindEnum) {
                    TagCollectionKindEnum[TagCollectionKindEnum["MDO"] = 'MDO'] = "MDO";
                    TagCollectionKindEnum[TagCollectionKindEnum["SDO"] = 'SDO'] = "SDO";
                })(TagCollectionKindEnum = TagObjectDTO.TagCollectionKindEnum || (TagObjectDTO.TagCollectionKindEnum = {}));
                var TagReadWriteEnum;
                (function (TagReadWriteEnum) {
                    TagReadWriteEnum[TagReadWriteEnum["Unknown"] = 'Unknown'] = "Unknown";
                    TagReadWriteEnum[TagReadWriteEnum["R"] = 'R'] = "R";
                    TagReadWriteEnum[TagReadWriteEnum["W"] = 'W'] = "W";
                    TagReadWriteEnum[TagReadWriteEnum["RW"] = 'RW'] = "RW";
                })(TagReadWriteEnum = TagObjectDTO.TagReadWriteEnum || (TagObjectDTO.TagReadWriteEnum = {}));
                var TagPropertyMaskEnum;
                (function (TagPropertyMaskEnum) {
                    TagPropertyMaskEnum[TagPropertyMaskEnum["CAN_EDIT"] = 0] = "CAN_EDIT";
                    TagPropertyMaskEnum[TagPropertyMaskEnum["CAN_DELETE"] = 1] = "CAN_DELETE";
                    TagPropertyMaskEnum[TagPropertyMaskEnum["CAN_CHANGE_VALUE"] = 2] = "CAN_CHANGE_VALUE";
                    TagPropertyMaskEnum[TagPropertyMaskEnum["READ_WRITE"] = 3] = "READ_WRITE";
                    TagPropertyMaskEnum[TagPropertyMaskEnum["VALUE_TYPE"] = 4] = "VALUE_TYPE";
                    TagPropertyMaskEnum[TagPropertyMaskEnum["COLLEC_KIND"] = 5] = "COLLEC_KIND";
                    TagPropertyMaskEnum[TagPropertyMaskEnum["SPARE"] = 6] = "SPARE";
                })(TagPropertyMaskEnum = TagObjectDTO.TagPropertyMaskEnum || (TagObjectDTO.TagPropertyMaskEnum = {}));
                var tagPropertyMaskType = [
                    0x00000001,
                    0x00000002,
                    0x00000004,
                    0x00000018,
                    0x000003E0,
                    0x00000400,
                    0x00000800
                ];
                var tagPropertyMaskPosition = [
                    0,
                    1,
                    2,
                    3,
                    5,
                    10,
                    11
                ];
                function getTagPropertyMaskValue(tagPropertyMask, tagPropertyMaskEnum) {
                    var value;
                    value = (tagPropertyMask & tagPropertyMaskType[tagPropertyMaskEnum]) >> tagPropertyMaskPosition[tagPropertyMaskEnum];
                    return value;
                }
                TagObjectDTO.getTagPropertyMaskValue = getTagPropertyMaskValue;
                function getTagPropertyMaskStringValue(tagPropertyMask, tagPropertyMaskEnum) {
                    var value = this.getTagPropertyMaskValue(tagPropertyMask, tagPropertyMaskEnum);
                    var stringValue;
                    if (tagPropertyMaskEnum == TagPropertyMaskEnum.VALUE_TYPE)
                        stringValue = Object.keys(TagValueTypeTextEnum)[value];
                    else if (tagPropertyMaskEnum == TagPropertyMaskEnum.COLLEC_KIND)
                        stringValue = Object.keys(TagCollectionKindEnum)[value];
                    else if (tagPropertyMaskEnum == TagPropertyMaskEnum.READ_WRITE)
                        stringValue = Object.keys(TagReadWriteEnum)[value];
                    return stringValue;
                }
                TagObjectDTO.getTagPropertyMaskStringValue = getTagPropertyMaskStringValue;
            })(TagObjectDTO || (TagObjectDTO = {}));
            exports_1("TagObjectDTO", TagObjectDTO);
        }
    };
});
//# sourceMappingURL=TagObjectDTO.js.map