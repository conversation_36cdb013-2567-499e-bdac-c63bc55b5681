System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, LogEntryContainPipe;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            LogEntryContainPipe = (function () {
                function LogEntryContainPipe() {
                }
                LogEntryContainPipe.prototype.transform = function (logEntries, logEntryFilter) {
                    if (logEntries == null || !Array.isArray(logEntries) || logEntries.length == 0)
                        return;
                    return logEntries.filter(function (log) {
                        if (log.timeStamp.toLowerCase().indexOf(logEntryFilter.timeStamp.toLowerCase()) !== -1 &&
                            log.source.toLowerCase().indexOf(logEntryFilter.source.toLowerCase()) !== -1 &&
                            log.category.toLowerCase().indexOf(logEntryFilter.category.toLowerCase()) !== -1 &&
                            log.severity.toLowerCase().indexOf(logEntryFilter.severity.toLowerCase()) !== -1 &&
                            log.message.toLowerCase().indexOf(logEntryFilter.message.toLowerCase()) !== -1)
                            return log;
                    });
                };
                LogEntryContainPipe = __decorate([
                    core_1.Pipe({ name: "logEntryContain", pure: false })
                ], LogEntryContainPipe);
                return LogEntryContainPipe;
            }());
            exports_1("LogEntryContainPipe", LogEntryContainPipe);
        }
    };
});
//# sourceMappingURL=logEntryContain.pipe.js.map