System.register([], function (exports_1, context_1) {
    "use strict";
    var EditorFieldObjectDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (EditorFieldObjectDTO) {
                var ControlTypeEnum;
                (function (ControlTypeEnum) {
                    ControlTypeEnum[ControlTypeEnum["Label"] = 'Label'] = "Label";
                    ControlTypeEnum[ControlTypeEnum["Text"] = 'Text'] = "Text";
                    ControlTypeEnum[ControlTypeEnum["TextMultiLine"] = 'TextMultiLine'] = "TextMultiLine";
                    ControlTypeEnum[ControlTypeEnum["TextAction"] = 'TextAction'] = "TextAction";
                    ControlTypeEnum[ControlTypeEnum["TextPassword"] = 'TextPassword'] = "TextPassword";
                    ControlTypeEnum[ControlTypeEnum["TextId"] = 'TextId'] = "TextId";
                    ControlTypeEnum[ControlTypeEnum["Number"] = 'Number'] = "Number";
                    ControlTypeEnum[ControlTypeEnum["Checkbox"] = 'Checkbox'] = "Checkbox";
                    ControlTypeEnum[ControlTypeEnum["RadioButton"] = 'RadioButton'] = "RadioButton";
                    ControlTypeEnum[ControlTypeEnum["OptionsEditor"] = 'OptionsEditor'] = "OptionsEditor";
                    ControlTypeEnum[ControlTypeEnum["MaskEditor"] = 'MaskEditor'] = "MaskEditor";
                    ControlTypeEnum[ControlTypeEnum["Combobox"] = 'Combobox'] = "Combobox";
                    ControlTypeEnum[ControlTypeEnum["FileManagement"] = 'FileManagement'] = "FileManagement";
                    ControlTypeEnum[ControlTypeEnum["FileManagementCombobox"] = 'FileManagementCombobox'] = "FileManagementCombobox";
                    ControlTypeEnum[ControlTypeEnum["Grid"] = 'Grid'] = "Grid";
                    ControlTypeEnum[ControlTypeEnum["GridNoDefault"] = 'GridNoDefault'] = "GridNoDefault";
                    ControlTypeEnum[ControlTypeEnum["Treeview"] = 'Treeview'] = "Treeview";
                    ControlTypeEnum[ControlTypeEnum["TreeviewMutliChoice"] = 'TreeviewMutliChoice'] = "TreeviewMutliChoice";
                    ControlTypeEnum[ControlTypeEnum["TreeviewDynamic"] = 'TreeviewDynamic'] = "TreeviewDynamic";
                    ControlTypeEnum[ControlTypeEnum["TreeviewDynamicMutliChoice"] = 'TreeviewDynamicMutliChoice'] = "TreeviewDynamicMutliChoice";
                    ControlTypeEnum[ControlTypeEnum["TreeviewDynamicSelectMutliChoice"] = 'TreeviewDynamicSelectMutliChoice'] = "TreeviewDynamicSelectMutliChoice";
                    ControlTypeEnum[ControlTypeEnum["Action"] = 'Action'] = "Action";
                    ControlTypeEnum[ControlTypeEnum["Info"] = 'Info'] = "Info";
                    ControlTypeEnum[ControlTypeEnum["Hidden"] = 'Hidden'] = "Hidden";
                    ControlTypeEnum[ControlTypeEnum["AdvanceParameters"] = 'AdvanceParameters'] = "AdvanceParameters";
                })(ControlTypeEnum = EditorFieldObjectDTO.ControlTypeEnum || (EditorFieldObjectDTO.ControlTypeEnum = {}));
                var IsEditableEnum;
                (function (IsEditableEnum) {
                    IsEditableEnum[IsEditableEnum["Yes"] = 'Yes'] = "Yes";
                    IsEditableEnum[IsEditableEnum["No"] = 'No'] = "No";
                })(IsEditableEnum = EditorFieldObjectDTO.IsEditableEnum || (EditorFieldObjectDTO.IsEditableEnum = {}));
            })(EditorFieldObjectDTO || (EditorFieldObjectDTO = {}));
            exports_1("EditorFieldObjectDTO", EditorFieldObjectDTO);
        }
    };
});
//# sourceMappingURL=EditorFieldObjectDTO.js.map