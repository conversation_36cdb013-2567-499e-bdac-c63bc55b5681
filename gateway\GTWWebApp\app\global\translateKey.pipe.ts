﻿import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from "@ngx-translate/core";

@Pipe({ name: 'translateKey' })
export class TranslateKeyPipe implements PipeTransform {
  transform(key: string, defaultText: string, translate: TranslateService): string {
    if (key == "")
      return "";

    let translated = translate.instant(key);
    if (translated == key)
      return defaultText;
    return translated;
  }
}
