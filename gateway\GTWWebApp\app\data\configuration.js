System.register([], function (exports_1, context_1) {
    "use strict";
    var Configuration;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            Configuration = (function () {
                function Configuration(configurationParameters) {
                    if (configurationParameters === void 0) { configurationParameters = {}; }
                    this.apiKeys = configurationParameters.apiKeys;
                    this.username = configurationParameters.username;
                    this.password = configurationParameters.password;
                    this.accessToken = configurationParameters.accessToken;
                    this.basePath = configurationParameters.basePath;
                    this.withCredentials = configurationParameters.withCredentials;
                }
                Configuration.prototype.selectHeaderContentType = function (contentTypes) {
                    var _this = this;
                    if (contentTypes.length == 0) {
                        return undefined;
                    }
                    var type = contentTypes.find(function (x) { return _this.isJsonMime(x); });
                    if (type === undefined) {
                        return contentTypes[0];
                    }
                    return type;
                };
                Configuration.prototype.selectHeaderAccept = function (accepts) {
                    var _this = this;
                    if (accepts.length == 0) {
                        return undefined;
                    }
                    var type = accepts.find(function (x) { return _this.isJsonMime(x); });
                    if (type === undefined) {
                        return accepts[0];
                    }
                    return type;
                };
                Configuration.prototype.isJsonMime = function (mime) {
                    var jsonMime = new RegExp('^(application\/json|[^;/ \t]+\/[^;/ \t]+[+]json)[ \t]*(;.*)?$', 'i');
                    return mime != null && (jsonMime.test(mime) || mime.toLowerCase() === 'application/json-patch+json');
                };
                return Configuration;
            }());
            exports_1("Configuration", Configuration);
        }
    };
});
//# sourceMappingURL=configuration.js.map