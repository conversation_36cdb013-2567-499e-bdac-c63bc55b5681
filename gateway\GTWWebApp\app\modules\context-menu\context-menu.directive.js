System.register(["@angular/core", "./context-menu.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, context_menu_service_1, ContextMenuDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (context_menu_service_1_1) {
                context_menu_service_1 = context_menu_service_1_1;
            }
        ],
        execute: function () {
            ContextMenuDirective = (function () {
                function ContextMenuDirective(contextMenuService) {
                    this.contextMenuService = contextMenuService;
                }
                ContextMenuDirective.prototype.rightClicked = function (event) {
                    this.contextMenuService.show.next({ event: event, obj: this.contextMenuitems });
                    event.preventDefault();
                };
                __decorate([
                    core_1.Input("context-menu"),
                    __metadata("design:type", Object)
                ], ContextMenuDirective.prototype, "contextMenuitems", void 0);
                ContextMenuDirective = __decorate([
                    core_1.Directive({
                        selector: "[context-menu]",
                        host: { "(contextmenu)": "rightClicked($event)" }
                    }),
                    __metadata("design:paramtypes", [context_menu_service_1.ContextMenuService])
                ], ContextMenuDirective);
                return ContextMenuDirective;
            }());
            exports_1("ContextMenuDirective", ContextMenuDirective);
        }
    };
});
//# sourceMappingURL=context-menu.directive.js.map