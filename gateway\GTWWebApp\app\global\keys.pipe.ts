﻿import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'keys' })
export class KeysPipe implements PipeTransform {
	transform(keyPairs, args?: string[]): any {
		let keyPairsList: Array<any> = [];
		for (let keyPair in keyPairs) {
			let valueNameLength = keyPairsList.length;
      try {
        if (valueNameLength === 0) {
          keyPairsList.push({ key: keyPair, value: keyPairs[keyPair].replace(/_/g, ' ') });
        } else {
          if (keyPairsList[valueNameLength - 1]["key"] !== keyPair && keyPairsList[valueNameLength - 1]["value"].replace(/_/g, ' ') !== keyPair.replace(/_/g, ' ')) {
            keyPairsList.push({ key: keyPair, value: keyPairs[keyPair].replace(/_/g, ' ') });
          }
        }
      } catch (err) {}
		}
		return keyPairsList
  }
  transformJson(values: any[], args?: string[]): any {
    let keys = [];
    if (values)
      values.sort();
    try {
      values.forEach((item) => {
        var key = Object.keys(item)[0];
        keys.push({ key: key, value: item[key] });
      });
    } catch(err) {}
    keys = this.sort(keys);
    return keys;
  }
  transformEditorGroup(items: any[], filter: any): any {
    let result = [];
    try {
      if (!items || !filter) {
        return items;
      }
      // filter items array, items which match and return true will be
      // kept, false will be filtered out
      result =  items.filter(item => item.groupName == filter);
    }
    catch (err) {}
    return result;
  }
  //sort(jsonObjects: any[]): any {
  //  const clone: any[] = jsonObjects;
  //  clone.sort(function (a, b) {
  //    if (a["key"] && b["key"]) {
  //      a = a["key"];
  //      b = b["key"];
  //    }
  //    if ((a && b) && (a.match && b.match)) {
  //      // convert numeric strings to integers
  //      a = a.match(/^\d+$/) ? +a : a.toLowerCase();
  //      b = b.match(/^\d+$/) ? +b : b.toLowerCase();
  //      return ((a < b) ? -1 : ((a > b) ? 1 : 0));
  //    }
  //  });
  //  return clone;
  //}

  sort(jsonObjects: any[]): any {
    const clone: any[] = jsonObjects;
    clone.sort(function (a, b) {
      if (a["key"] && b["key"]) {
        a = a["key"];
        b = b["key"];
      }
      let reA = /[^a-zA-Z]/g;
      let reN = /[^0-9]/g;
      var aA = a.toString().replace(reA, "");
      var bA = b.toString().replace(reA, "");
      if (aA === bA) {
        var aN = parseInt(a.toString().replace(reN, ""), 10);
        var bN = parseInt(b.toString().replace(reN, ""), 10);
        return aN === bN ? 0 : aN > bN ? 1 : -1;
      }
      else {
        return aA > bA ? 1 : -1;
      }
    });
    return clone;
  }
}