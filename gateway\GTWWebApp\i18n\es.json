{"TR_61850_CANT_DELETE_DATASET_DISCONNECTED": "No se puede eliminar el conjunto de datos '{{arg1}}' mientras está desconectado. Conéctese e intente nuevamente ", "TR_61850_CANT_DELETE_DATASET_IN_USE": "No se puede eliminar el conjunto de datos '{{arg1}}' porque pertenece a uno o más bloques de control. Elimine los bloques de control e intente nuevamente. ", "TR_61850_CANT_DELETE_NON_DYNAMIC_DATASET": "El conjunto de datos '{{arg1}}' no es dinámico y no se puede eliminar", "TR_61850_CLIENT_DUP_LIC": "Error al agregar el cliente IEC 61850 (podría estar duplicado o no tener licencia)", "TR_61850_CLIENT_DUPLICATE": "No se puede agregar el cliente 61850: '{{arg1}}'. Nombre duplicado ", "TR_61850_CLIENT_FAILED_DUPLICATE": "Error al crear el cliente 61850 porque tiene un nombre duplicado: '{{arg1}}'", "TR_61850_CREATE_SDO_FAILED_DUPLICATE": "No se pudo crear {{arg1}} SDO.  Nombre de etiqueta duplicado?", "TR_61850_CREATE_SDO_FAILED_INVALID_TAG": "No se pudo crear {{arg1}} SDO.  Nombre de etiqueta inválido?", "TR_61850_CREATE_SDO_FAILED_LIC_LIMIT": "No se pudo crear {{arg1}} SDO.  Se alcanzó el límite de licencia ", "TR_61850_DATASET_VERIFY_FAILED": "Error en la verificación del conjunto de datos para '{{arg1}}'. Asegúrese de que las definiciones del conjunto de datos del cliente y del servidor coincidan; de lo contrario, los informes no funcionarán correctamente ", "TR_61850_DATASET_VERIFY_SUCCEED": "¡Conjunto de datos '{{arg1}}' verificado correctamente!", "TR_61850_INVALID_MODEL": "Error: modelo 61850 no válido", "TR_61850_LOAD_COMMUNICATION": "Error al cargar la configuración desde el archivo SCL. Asegúrese de que la ruta del archivo SCL, el nombre del IED y la sección de comunicación del archivo SCL sean válidos. ", "TR_61850_MD_BIND_MISMATCH": "No se pudo vincular SDO con MDO. ¿No coincide el tipo?", "TR_61850_MDO_DELETE": "Los MDO 61850 no se pueden eliminar", "TR_61850_MDO_SLAVE_MAP_FAILED": "No se puede asignar un punto no MDO a este punto esclavo", "TR_61850_NO_POINTS_FOUND": "No hay puntos válidos", "TR_61850_NO_POLLED": "No más IEC 61850 PolledPointSets disponibles", "TR_61850_POLLED_ADD": "No se pudo agregar {{arg1}} PolledPointSet en el servidor 61850: {{arg2}}.", "TR_61850_POLLED_DELETE": "No se puede eliminar el conjunto de datos sondeados '{{arg1}}', tiene elementos existentes: {{arg2}}", "TR_61850_POLLED_DELETE_EXISTING": "No se puede eliminar '{{arg1}}' Conjunto de puntos sondeados, tiene elementos existentes: {{arg2}}", "TR_61850_REPORT_DELETE_ERROR": "No se puede eliminar el bloque de control de informe '{{arg1}}', actualmente tiene un subproceso de reintento en ejecución. Cambie el 'Retry Enable Count' a cero para detener el hilo e intente nuevamente ", "TR_61850_REPORT_DELETE_EXISTING": "No se puede eliminar el bloque de control de informe '{{arg1}}', tiene elementos existentes: {{arg2}}", "TR_61850_SERVER_DUP_LIC": "Error al agregar el servidor IEC 61850 (podría estar duplicado o no tener licencia)", "TR_61850_SERVER_DUPLICATE": "No se puede agregar el servidor 61850: '{{arg1}}'. Nombre duplicado ", "TR_61850_SERVER_FAILED_DUPLICATE": "Error al crear el servidor 61850 porque tiene un nombre duplicado: '{{arg1}}'", "TR_6T_CATEGORY_APP": "APP", "TR_6T_CATEGORY_C_APP": "APLICACIÓN C", "TR_6T_CATEGORY_C_CASM": "C CASM", "TR_6T_CATEGORY_C_CLIENT": "CLIENTE C", "TR_6T_CATEGORY_C_CLIENTRPT": "C CLIENTRPT", "TR_6T_CATEGORY_C_CLIENTSTATE": "C CLIENTSTATE", "TR_6T_CATEGORY_C_DYNAMIC_DATASETS": "C DATASETS DINÁMICOS", "TR_6T_CATEGORY_C_EXTREF": "C EXTREF", "TR_6T_CATEGORY_C_SCL": "C SCL", "TR_6T_CATEGORY_C_STACK": "C STACK", "TR_6T_CATEGORY_C_TARGET": "C TARGET", "TR_6T_CATEGORY_C_TEST": "PRUEBA C", "TR_6T_CATEGORY_C_TIME": "C TIME", "TR_6T_CATEGORY_C_TRANSLOW": "C TRANSLOW", "TR_6T_CATEGORY_C_TRANSPORT": "TRANSPORTE C", "TR_6T_CATEGORY_CLIENTPARSEVALUES": "CLIENTPARSEVALUES", "TR_6T_CATEGORY_CONTROL": "CONTROL", "TR_6T_CATEGORY_DISCOVERY": "DESCUBRIMIENTO", "TR_6T_CATEGORY_EXTREF": "EXTREF", "TR_6T_CATEGORY_GENERAL": "GENERAL", "TR_6T_CATEGORY_GOOSE": "GOOSE", "TR_6T_CATEGORY_READ": "LEER", "TR_6T_CATEGORY_READ_HANDLER": "LEER MANEJO", "TR_6T_CATEGORY_REPORT": "INFORME", "TR_6T_CATEGORY_TIME_SYNCH": "SINCRONIZACIÓN DE TIEMPO", "TR_6T_CATEGORY_WRITE": "ESCRIBIR", "TR_6T_CATEGORY_XML": "XML", "TR_6T_FILTER": "Filtro MMS", "TR_6T_LOW_LEVEL_STACK": "<PERSON><PERSON> de nivel bajo MMS", "TR_6T_STANDARD_STACK": "<PERSON><PERSON>", "TR_MDO_CAN_NOT_CREATE": "MDO no encontrado, no se puede crear", "TR_SDO_CREATE_DEFINED_ALREADY": "SDO ya está definido, no se puede crear", "TR_SDO_BIND_FAIL": "No se pudo vincular SDO", "TR_SDO_CREATE_FAIL": "No se pudo crear SDO", "TR_SDO_CAN_NOT_SET_OPTIONS": "No se pudieron establecer las opciones SDO {{arg1}}", "TR_ABOUT": "Acerca de", "TR_ACCEPT_CLOCK_SYNC": "Aceptar sincronizaciones de reloj", "TR_ACCEPT_CLOCK_SYNC_DESC": "Si es verdadero y UseSystemTime es verdadero, entonces el reloj del sistema de Windows se ajustará mediante una sincronización horaria recibida de un dispositivo maestro externo. Si las falsas sincronizaciones de hora no ajustan el reloj del sistema de Windows.Cuando se usa un reloj simulado, esta configuración no tiene efecto y las sincronizaciones de reloj siempre se aceptan y ajustan el reloj simulado ", "TR_ACCESS_READ_AND_WRITE_USERS_OF_MANAGEMENT": "Acceso de lectura y escritura de la gestión de usuarios", "TR_ACCESS_RIGHT": "Derecho de acceso", "TR_ACCESS_RIGHT_DESC": "Definición del derecho de acceso", "TR_ACCESS_TO_CONFIG_UI_FOR_READ_AND_WRITE": "Acceso a la interfaz de usuario de configuración para lectura / escritura", "TR_ACCESS_TO_CONFIG_UI_FOR_READ_ONLY": "Acceso a la interfaz de usuario de configuración para solo lectura", "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_AND_WRITE": "Acceso a la interfaz de usuario de tiempo de ejecución para leer y escribir", "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_ONLY": "Acceso a la interfaz de usuario de tiempo de ejecución para solo lectura", "TR_ACTION": "Acción", "TR_ACTION_NAME": "Nombre de acción", "TR_ACTION_NAME_DESC": "Nombre de acción", "TR_ACTION_VALUES": "Valores de acción", "TR_ACTION_VALUES_DESC": "Valores de acción", "TR_ACTIVATE_ONLINE": "Activar en línea", "TR_ACTIVATE_PRODUCT_KEY_OFFLINE": "Clave de producto activa sin conexión", "TR_ACTIVATE_PRODUCT_KEY_ONLINE": "Clave de producto activa en línea", "TR_ACTIVE": "Activo", "TR_ACTIVE_DESC": "Activo", "TR_ADD": "Agregar", "TR_ADD_GOOSE": "No se pudo agregar {{arg1}} GOOSE en el servidor 61850: {{arg2}}", "TR_ADD_ITEM": "Agregar elemento", "TR_ADD_ITEM_DESC": "Agregar elemento", "TR_ADD_NEW_USER": "Agregar nuevo usuario", "TR_ADD_PROPERTY": "<PERSON><PERSON><PERSON><PERSON> propiedad", "TR_ADD_PROPERTY_DESC": "<PERSON><PERSON><PERSON><PERSON> propiedad", "TR_ADD_SUBSCRIPTION": "Agregar suscripción", "TR_ADD_SUBSCRIPTION_DESC": "Agregar suscripción", "TR_ALARM_ARRAY": "<PERSON><PERSON>", "TR_ALARM_ARRAY_DESC": "Definir la matriz de alarma", "TR_ALIAS_NAME": "Nombre", "TR_ALIAS_NAME_DESC": "Especifica el nombre", "TR_APP_CERT_DIRECTORY": "Directorio de certificados de aplicación", "TR_APP_CERT_DIRECTORY_DESC": "Especifica el directorio de certificados de la aplicación", "TR_APP_ERROR": "Error de aplicación", "TR_APP_ERROR_DESC": "Registrar mensajes de error de la aplicación", "TR_APP_START_STOP": "App. Start / Stop", "TR_APP_START_STOP_DESC": "Registrar mensajes de inicio / apagado de la aplicación", "TR_APP_STATUS": "Estado de la aplicación", "TR_APP_STATUS_DESC": "Registrar mensajes periódicos de estado de la aplicación", "TR_APPL_AUTO_REQ_MODE": "Modo de solicitud automática", "TR_APPL_AUTO_REQ_MODE_DESC": "Cada bit habilita (1) o deshabilita (0) una solicitud automática. Este parámetro solo se usa para sesiones maestras o esclavas que utilizan el protocolo DNP3.", "TR_APPL_DNPABS_RESP_TIMEOUT": "Tiempo de espera de respuesta (ms) - DNP", "TR_APPL_DNPABS_RESP_TIMEOUT_DESC": "Tiempo de espera predeterminado de respuesta de la aplicación DNP. Este valor es la cantidad máxima de tiempo (en milisegundos) que se permitirá antes de que se cancele un comando debido al tiempo de espera. Este tiempo comienza cuando se envía la solicitud y finaliza cuando se recibe la respuesta final de nivel de aplicación. Este valor generalmente se puede anular para puntos de datos específicos mediante la opción 'TO' en el archivo de mapeo de puntos ", "TR_APPL_IECABS_RESP_TIMEOUT": "Tiempo de espera de respuesta (ms) - IEC", "TR_APPL_IECABS_RESP_TIMEOUT_DESC": "Tiempo de espera predeterminado de respuesta de la aplicación IEC. Este valor es la cantidad máxima de tiempo (en milisegundos) que se permitirá antes de que se cancele un comando debido al tiempo de espera. Este tiempo comienza cuando se envía la solicitud y finaliza cuando se recibe la respuesta final de nivel de aplicación. Este valor generalmente se puede anular para puntos de datos específicos mediante la opción 'TO' en el archivo de mapeo de puntos ", "TR_APPL_INCR_RESP_TIMEOUT": "Tiempo de espera de respuesta incremental (ms)", "TR_APPL_INCR_RESP_TIMEOUT_DESC": "Cantidad máxima de tiempo para permitir entre mensajes desde un dispositivo remoto cuando hay una solicitud pendiente para ese dispositivo. El mensaje no necesita ser una respuesta directa a la solicitud pendiente. Si no se recibe ningún mensaje del dispositivo remoto dentro de este período, se supone que el dispositivo ha terminado el procesamiento de la solicitud y la solicitud se cancela debido a un tiempo de espera de nivel de aplicación. Este temporizador se reinicia cada vez que se recibe un mensaje del dispositivo remoto ", "TR_APPL_MBABS_RESP_TIMEOUT": "Tiempo de espera de respuesta (ms) - Modbus", "TR_APPL_MBABS_RESP_TIMEOUT_DESC": "Tiempo de espera predeterminado de respuesta de la aplicación Modbus. Este valor es la cantidad máxima de tiempo (en milisegundos) que se permitirá antes de que se cancele un comando debido al tiempo de espera. Este tiempo comienza cuando se envía la solicitud y finaliza cuando se recibe la respuesta final de nivel de aplicación. Este valor generalmente se puede anular para puntos de datos específicos mediante la opción 'TO' en el archivo de mapeo de puntos ", "TR_APPLICATION_FUNCTIONS": "Funciones de la aplicación", "TR_APPLICATION_LAYER": "Capa de aplicación", "TR_ARE_YOU_SURE_TO_CLEAR_MODEL": "¿Está seguro de que desea borrar el modelo?", "TR_ARE_YOU_SURE_TO_DELETE_DATASET": "¿Está seguro de que desea eliminar este conjunto de datos?", "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME": "¿Está seguro de eliminar {{NodeName}}?", "TR_ARE_YOU_SURE_TO_REMOVE_MAPPING": "¿Está seguro de eliminar the asignación {{mapping}}?", "TR_ARE_YOU_SURE_TO_DELETE_USER_X": "¿Está seguro de eliminar el usuario {{username}}?", "TR_AREA_SPACE": "Área de espacio", "TR_AREA_SPACE_DESC": "Definir el área de espacio", "TR_ASDUORIGINATOR_ADDR": "Dirección del originador", "TR_ASDUORIGINATOR_ADDR_DESC": "Dirección del creador (para COT de 2 octetos).  Este parámetro solo se utiliza para sesiones maestras que utilizan los perfiles de protocolo IEC 60870-5-101 o IEC 60870-5-104.", "TR_ASDUSIZE_CMN_ADDR": "Número de octetos (bytes) en la dirección común de ASDU", "TR_ASDUSIZE_CMN_ADDR_DESC": "Número de octetos (bytes) en el campo Dirección común del ASDU (dirección de sector).  Este parámetro solo se utiliza para sesiones maestras y esclavas que utilizan los perfiles de protocolo IEC 60870-5-101 o IEC 60870-5-104.", "TR_ASDUSIZE_COT": "Número de octetos (bytes) en la causa de la transmisión", "TR_ASDUSIZE_COT_DESC": "Número de octetos (bytes) en el campo Causa de transmisión (COT) de ASDU.  Este parámetro solo se utiliza para sesiones maestras y esclavas que utilizan los perfiles de protocolo IEC 60870-5-101 o IEC 60870-5-104.", "TR_ASDUSIZE_IOA": "Número de octetos (bytes) en Número de punto", "TR_ASDUSIZE_IOA_DESC": "Número de octetos (bytes) en el campo Dirección del objeto de información (número de punto).  Este parámetro solo se utiliza para sesiones maestras y esclavas que utilizan los perfiles de protocolo IEC 60870-5-101 o IEC 60870-5-104.", "TR_AUDIT": "Auditoría", "TR_AUDIT_LOG": "Registro de auditoría", "TR_AUTH_CONFIG": "Configuración de autenticación", "TR_AUTH_MODE": "Modo de autenticación", "TR_AUTH_SEC_USERS_LIST": "Usuarios de SECAuth v5", "TR_AUTH_SEC_USERS_LIST_DESC": "Usuarios de SECAuth v5", "TR_AUTHENTICATION_PASSWORD": "Contraseña de autenticación", "TR_AUTO_MAP_QUALITY_TIME": "Auto Map Quality & Time", "TR_AUTO_MAP_QUALITY_TIME_DESC": "Especifica si la asignación automática de calidad y tiempo está activa.", "TR_AUTO_SAVE": "Guardar automáticamente", "TR_AUTO_SAVE_ENABLE": "habilitar", "TR_AUTO_SAVE_ENABLE_DESC": "Habilitar guardado automático", "TR_AUTO_SAVE_PRD": "Período de guardado automático", "TR_AUTO_SAVE_PRD_DESC": "Cantidad máxima de tiempo entre guardar los archivos de configuración de la aplicación .ini y .csv en milisegundos. Un valor de 0 deshabilitará guardar. El valor más bajo para el período de ahorro es 60000 ms (1 minuto) ", "TR_AUTO_START_ENGINE_ON_GUI_EXIT": "Motor de arranque automático al salir de la GUI", "TR_AUTO_START_SERVICE_ON_GUI_EXIT": "Motor de servicio automático al salir de la GUI", "TR_AVAILABLE_SERVERS": "Servidores disponibles", "TR_AVAILABLE_SERVERS_DESC": "Servidores disponibles", "TR_BLOCKED": "BLOCKED", "TR_BLOCKED_DESC": "Bloqueado", "TR_BUFFER_OVERFLOW": "Desbordamiento de búfer", "TR_BUFFER_OVERFLOW_DESC": "Especifica si el desbordamiento del búfer está activo", "TR_BUFFER_TIME": "<PERSON><PERSON><PERSON> (ms)", "TR_BUFFER_TIME_DESC": "Especifica el tiempo de almacenamiento intermedio actual", "TR_CAN_NOT_SET_SIGNED_AND_DUAL_REGISTER": "No se puede establecer SIGNED y DualRegister. La opción DualRegister se desactivará. Si se desea la opción Dual, apague FIRMADO ", "TR_CANCEL": "<PERSON><PERSON><PERSON>", "TR_CANT_CREATE_MDO": "No se pudo crear {{arg1}} MDO.  Posible nombre de etiqueta no válido ", "TR_CANT_DELETE_POINT_IN_USE": "La ecuación: {{arg1}} se asigna a otros puntos o se usa en una ecuación, no se puede editar / eliminar", "TR_CATEGORY": "Categoría", "TR_CENTRAL_AUTHORITY": "Autoridad central", "TR_CERT_AUTH_CHAINING_VERIFICATION_DEPTH": "Profundidad de verificación de encadenamiento de la autoridad de certificación", "TR_CERT_AUTH_DIR_PATH": "Directorio para la autoridad de certificación", "TR_CERT_AUTH_FILE_PATH": "Archivo de autoridad de certificación", "TR_CERT_REVOCATION_FILE_PATH": "Archivo de lista de revocación de autoridad de certificación", "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH": "Profundidad de verificación de encadenamiento de CA", "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH_DESC": "Especifica la profundidad de verificación de encadenamiento de la autoridad de certificación", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR": "Ruta del directorio de la autoridad de certificación", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR_DESC": "Ruta al directorio de certificados de la autoridad de certificación", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE": "Archivo de autoridad de certificación", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE_DESC": "Archivo que contiene certificados de autoridad de certificación", "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE": "Archivo de revocación de certificado", "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE_DESC": "Archivo que contiene la Lista de revocación de certificados", "TR_CHAN_TLSCOMMON_NAME": "Nombre común de TLS", "TR_CHAN_TLSCOMMON_NAME_DESC": "Nombre común que se espera en los certificados TLS entrantes (la cadena vacía se desactiva)", "TR_CHAN_TLSDH_FILE": "Archivo Di<PERSON><PERSON>", "TR_CHAN_TLSDH_FILE_DESC": "Archivo que contiene Di<PERSON><PERSON>", "TR_CHAN_TLSDSACERTIFICATE_FILE": "Archivo de certificado público DSA", "TR_CHAN_TLSDSACERTIFICATE_FILE_DESC": "Archivo que contiene el certificado de clave para los cifrados DSA TLS.", "TR_CHAN_TLSDSAPRIVATE_KEY_FILE": "Archivo de clave privada DSA", "TR_CHAN_TLSDSAPRIVATE_KEY_FILE_DESC": "Archivo que contiene la clave privada para los cifrados DSA TLS.", "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE": "Frase de paso de clave privada DSA", "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE_DESC": "PassPhrase para descifrar la clave privada para los cifrados DSA TLS.", "TR_CHAN_TLSENABLE": "Habilitar TLS", "TR_CHAN_TLSENABLE_DESC": "Si es verdadero, entonces la conexión se establecerá usando TLS por seguridad", "TR_CHAN_TLSHANDSHAKE_TIMEOUT": "TLS Handshake Timeout (sec)", "TR_CHAN_TLSHANDSHAKE_TIMEOUT_DESC": "Tiempo máximo en milisegundos para esperar a que se complete el protocolo de enlace TLS connect", "TR_CHAN_TLSRENEGOTIATION_COUNT": "TLS Max PDU antes de forzar la renegociación de cifrado", "TR_CHAN_TLSRENEGOTIATION_COUNT_DESC": "PDU máximas antes de forzar la renegociación de cifrado", "TR_CHAN_TLSRENEGOTIATION_SECONDS": "Renegociación TLS (seg)", "TR_CHAN_TLSRENEGOTIATION_SECONDS_DESC": "Tiempo máximo (segundos) antes de forzar la renegociación de cifrado", "TR_CHAN_TLSRSACERTIFICATE_FILE": "Archivo de certificado público RSA", "TR_CHAN_TLSRSACERTIFICATE_FILE_DESC": "Archivo que contiene el certificado de clave para los cifrados RSA TLS.", "TR_CHAN_TLSRSAPRIVATE_KEY_FILE": "Archivo de clave privada RSA", "TR_CHAN_TLSRSAPRIVATE_KEY_FILE_DESC": "Archivo que contiene la clave privada para los cifrados RSA TLS.", "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE": "Frase de contraseña de clave privada RSA", "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "PassPhrase para descifrar la clave privada para los cifrados RSA TLS.", "TR_CHANGE_PASSWORD": "Cambiar contraseña", "TR_CHANGE_VALUE": "Cambiar valor", "TR_CHANGE_VALUE_AND_QUALITY": "Cambiar valor y calidad", "TR_CHANGE_VALUE_AND_QUALITY_OF": "Cambiar valor y calidad de", "TR_CHANGE_VALUE_OF_SELECTED": "Cambiar el valor de las etiquetas seleccionadas", "TR_CHANNEL_DELETE_ONLY": "No se puede eliminar el último canal redundante: '{{arg1}}'. Debe existir al menos un canal redundante para el grupo de redundancia ", "TR_CHANNEL_DUPLICATE": "No se puede agregar el canal: '{{arg1}}'. Nombre duplicado ", "TR_CHANNEL_ENABLE_TLS": "Habilitar TLS", "TR_CHANNEL_IP_PORT_NUMBER": "Número de puerto IP", "TR_CHANNEL_LOCAL_IP": "IP local", "TR_CHANNEL_MBP_CARD_NUMBER": "Número de tarjeta", "TR_CHANNEL_MBP_RECEIVE_TIMEOUT": "Tiempo de espera de recepción", "TR_CHANNEL_MBP_ROUTE_ADDRESS": "Ruta / Dirección", "TR_CHANNEL_MBP_SLAVE_PATH": "Ruta del esclavo", "TR_CHANNEL_NAME": "Nombre de alias", "TR_CHANNEL_NAME_DESC": "Especifique el nombre del canal", "TR_CHANNEL_NO_MORE": "No hay más canales disponibles", "TR_CHANNEL_PROTOCOL_TYPE": "Protocolo de sesión", "TR_CHANNEL_PROTOCOL_TYPE_DESC": "Establece el protocolo para el canal Se aplica a todos los tipos de canales físicos", "TR_CHANNEL_START_DELAY": "Retardo de inicio de canal (ms)", "TR_CHANNEL_START_DELAY_DESC": "Tiempo de retraso en milisegundos para escalonar el inicio de todos los canales (esclavos y maestros). Solo afecta al inicio ", "TR_CHANNEL_TCP_MODE": "Modo", "TR_CHECK_BACK_ID": "Verificar ID de retorno", "TR_CHNG_INDICATED": "CHNG_INDICATED", "TR_CHNG_INDICATED_DESC": "La fuente de datos indica un cambio en los datos", "TR_CLEAR": "Bo<PERSON>r", "TR_CLEAR_LOG": "Borrar registro", "TR_CLEAR_MODEL": "Borrar modelo", "TR_CLEAR_MODEL_DESC": "Borrar modelo actual", "TR_CLEAR_SEARCH": "<PERSON><PERSON><PERSON>", "TR_CLIENT": "Cliente", "TR_CLIENT_AE_INVOKE_ID": "ID de invocación AE", "TR_CLIENT_AE_QUALIFIER": "Calificador AE", "TR_CLIENT_AP_INVOKE_ID": "ID de invocación AP", "TR_CLIENT_APP_ID": "ID de la aplicación", "TR_CLIENT_IP_ADDRESS": "Dirección IP del cliente", "TR_CLIENT_PRESENTATION_ADDRESS": "Selector de presentación", "TR_CLIENT_SERVER_CONNECTION_SETTINGS": "Configuración de conexión de cliente / servidor", "TR_CLIENT_SESSION_ADDRESS": "Selector de se<PERSON>", "TR_CLIENT_TRANSPORT_ADDRESS": "Selector de transporte", "TR_CLOSE": "<PERSON><PERSON><PERSON>", "TR_CLOSE_PANEL": "Cerrar panel", "TR_COILS": "Bobinas", "TR_COLLAPSE_CHILDREN": "<PERSON><PERSON><PERSON> niños", "TR_COMMAND_KIND": "Tipo de comando", "TR_COMMAND_SENT_TO_SERVER": "Comando enviado al servidor", "TR_COMMAND_SUCCESS": "Éxito", "TR_CONFIG": "Configuración", "TR_CONFIG_VIEW_1": "Vista de configuración # 1", "TR_CONFIG_VIEW_2": "Vista de configuración # 2", "TR_CONFIG_VIEW_3": "Vista de configuración # 3", "TR_CONFIGURATION": "Configuración", "TR_CONFIGURATION_REVISION": "Revisión de configuración", "TR_CONFIGURATION_REVISION_DESC": "Especifica si la revisión de configuración está activa", "TR_CONFIRM_NEW_PASSWORD": "Confirmar nueva contraseña", "TR_CONNECT_RECONNECT": "Conectar / reconectar", "TR_CONNECT_TIMEOUT": "Tiempo de espera de conexión (ms)", "TR_CONTROL_BLOCK": "Bloque de control", "TR_CONTROL_BLOCK_DESC": "Mostrar bloque de control para el MDO", "TR_CONTROL_POINT": "Punto de control", "TR_CONTROL_POINT_DESC": "Definir el punto de control", "TR_CONTROL_POINT_LIST": "Puntos de control", "TR_CONTROL_POINT_LIST_DESC": "Elija un punto de control", "TR_COPY_TO_CSV_FILE": "Copiar la lista del archivo CSV", "TR_COULD_NOT_SET_MDO_OPTIONS": "Error: No se pudieron establecer las opciones de MDO", "TR_CPU_ABBREVIATION": "CPU", "TR_CREATE_61400_ERROR": "No hay más alarmas disponibles", "TR_CREATE_61400MDO_ERROR": "No se pudo agregar {{arg1}} MDO en el servidor 61850: {{arg2}}. (¿Duplicado?)", "TR_CREATE_61850_CANT_DELETE": "No se puede eliminar el cliente {{arg1}} mientras está conectado. Por favor, desconecte e intente de nuevo ", "TR_CREATE_61850_CHANGE_DATASET_NAME_FAILED": "Error al cambiar el nombre del conjunto de datos. Error: {{arg1}} ", "TR_CREATE_61850_CREATE_MDO_INVALID_TAG": "No se pudo crear {{arg1}} MDO. ¿Nombre de etiqueta no válido?", "TR_CREATE_61850_DELETE": "MDO: '{{arg1}}' se asigna a puntos esclavos o se usa en una ecuación, no se puede eliminar", "TR_CREATE_61850_DISABLE_RPT_FAILED": "Error al deshabilitar RCB. Error: {{arg1}}", "TR_CREATE_61850_FAILED_TO_REENABLE_RPT": "Error al volver a habilitar el Bloque de Control. Error: {{arg1}} ", "TR_CREATE_61850_LIST_POINTS_NOT_A_CONTROL": "No es un bloque de control válido", "TR_CREATE_61850_MDO": "MDO debe tener un nombre, no se puede crear", "TR_CREATE_61850_MDO_EXISTS": "MDO ya está definido, no se puede crear", "TR_CREATE_61850_MDO_SET_OPTIONS": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_CREATE_61850_NO_MORE_CLIENTS": "Error al leer el bloque de control de informes. Error devuelto: {{arg1}} ", "TR_CREATE_61850_SERVER_NO_ADD_MDO": "No se pudo agregar {{arg1}} MDO en el servidor 61850: {{arg2}}. (¿Duplicado?)", "TR_CREATE_61850_SET_DATASET_NAME_FAILED": "Error al establecer el nombre del conjunto de datos. Error desconocido.", "TR_CREATE_61850_SET_MDO_OPTIONS": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_CREATE_61850_SET_MDO_PROPERTIES": "Error al establecer las propiedades de MDO (control no válido)", "TR_CREATE_61850_SET_MDO_PROPERTIES_Control": "Error al establecer las propiedades de MDO en el Control", "TR_CREATE_61850_SET_MDO_PROPERTIES_DATASET": "Error al establecer las propiedades de MDO en DataSetControl", "TR_CREATE_61850_SET_MDO_PROPERTIES_GOOSE": "Error al establecer las propiedades MDO en el Control GOOSE", "TR_CREATE_61850_SET_MDO_PROPERTIES_POINTSET": "Error al establecer las propiedades MDO en el control PointSet", "TR_CREATE_61850_SET_MDO_PROPERTIES_REPORT": "Error al establecer las propiedades de MDO en el Control de informes", "TR_CREATE_C2V": "Crear C2V", "TR_CREATE_DATASET_FAILED_CONNECTED": "No se puede crear un conjunto de datos mientras está desconectado. Conéctese e intente nuevamente ", "TR_CREATE_DATASET_FAILED_EMPTY": "Error: el modelo está vacío", "TR_CREATE_DATASET_FAILED_ERROR": "CreateDataSet falló con el error {{arg1}}", "TR_CREATE_DATASET_FAILED_EXISTS": "El conjunto de datos {{arg1}} ya existe. Intente eliminar primero y luego crear de nuevo ", "TR_CREATE_DTM_POINT": "Creado C: \\ dtm_points.csv", "TR_CREATE_EQUATION_FAILED": "No se pudo crear la ecuación {{arg1}} para {{arg2}}", "TR_CREATE_HTML_FILE": "Creado C: \\ test_harness_points.xml", "TR_CTRL_AT_DEVICE": "CTRL_AT_DEVICE", "TR_CTRL_AT_DEVICE_DESC": "Se indica un cambio en los datos debido a una acción en el dispositivo", "TR_CTRL_BY_COMM": "CTRL_BY_COMM", "TR_CTRL_BY_COMM_DESC": "Se indica un cambio en los datos debido a una solicitud a través de comunicaciones", "TR_CTRL_CONFIRM": "CTRL_CONFIRM", "TR_CTRL_CONFIRM_DESC": "Una solicitud de control ha sido confirmada por un dispositivo remoto, pero aún no está completa", "TR_CTRL_ERROR": "CTRL_ERROR", "TR_CTRL_ERROR_DESC": "Un dispositivo remoto ha respondido para indicar un error en una operación de control", "TR_CTRL_PENDING": "CTRL_PENDING", "TR_CTRL_PENDING_DESC": "Se ha transmitido una solicitud de control a un dispositivo remoto", "TR_CURRENT_ENTRY_ID": "ID de entrada actual", "TR_CURRENT_ENTRY_ID_DESC": "Muestra el ID de la entrada actual", "TR_CURRENT_INI_FILE": "Archivo INI actual", "TR_CURRENT_PASSWORD": "Contraseña actual", "TR_CUSTOM_LOGS": "Registros personalizados", "TR_CUSTOM_LOGS_DESC": "Registrar mensajes de rastreo para registros personalizados", "TR_DA_ITEM_PROPERTY_LIST": "Lista de propiedades del elemento DA", "TR_DA_ITEM_PROPERTY_LIST_DESC": "Lista de propiedades del elemento DA", "TR_DA_POINT_LIST": "Puntos de atributos de datos", "TR_DA_POINT_LIST_CHECKED": "Lista de puntos DA marcada", "TR_DA_POINT_LIST_DESC": "Especifica la lista de puntos de atributos de datos actual", "TR_DA_QUALITY_DESC": "Definir la calidad del atributo de datos", "TR_DA_QUALITY": "Calidad DA", "TR_DA_SEARCH": "Ingrese un nombre parcial para buscar (mayúsculas y minúsculas)", "TR_DA_SEARCH_DESC": "Ingrese un nombre parcial para buscar (distingue entre mayúsculas y minúsculas)", "TR_DA_TIME": "Hora DA", "TR_DA_TIME_DESC": "Definir el tiempo del atributo de datos", "TR_DA_VALUE": "Valor DA", "TR_DA_VALUE_DESC": "Definir el valor del atributo de datos", "TR_DASHBOARD": "Panel de control", "TR_DATA_ATTRIBUTE_SELECTION": "Selección de atributos de datos", "TR_DATA_CHANGE": "Cambio de datos", "TR_DATA_CHANGE_DESC": "Especifica si el cambio de datos está activo", "TR_DATA_CHNG_MON": "Cambio de datos", "TR_DATA_FILE_PATH": "Ruta del archivo de datos", "TR_DATA_POINTS_LIST": "Lista de puntos de datos", "TR_DATA_POINTS_LIST_DESC": "Mostrar la lista de puntos de datos para el dominio seleccionado", "TR_DATA_REFERENCE": "Referencia de datos", "TR_DATA_REFERENCE_DESC": "Especifique la referencia de datos", "TR_DATA_SAVED": "Datos guardados", "TR_DATA_SET": "DataSet", "TR_DATA_SET_NAME": "Nombre del conjunto de datos", "TR_DATA_TYPE": "Tipo de <PERSON>", "TR_DATA_UPDATE_CHANGE": "Cambio de actualización de datos", "TR_DATA_UPDATE_CHANGE_DESC": "Especifica si el cambio de actualización de datos está activo", "TR_DATABASE": "Base de datos", "TR_DATASET_NAME": "Nombre del conjunto de datos", "TR_DATASET_NAME_DESC": "Especifica si el nombre del conjunto de datos está activo", "TR_DATATYPE_DELETE_IN_USE": "'{{arg1}}' no se puede eliminar (intente desasignar sus puntos)", "TR_DATE_TIME": "Fecha / Hora", "TR_DBAS_SECTOR_ADDRESS": "Dirección ASDU", "TR_DBAS_SECTOR_ADDRESS_DESC": "Dirección ASDU de cada sector", "TR_DEBUG": "<PERSON><PERSON><PERSON>", "TR_DELETE": "Eliminar", "TR_DELETE_DATASET_FAILED": "Error al eliminar el conjunto de datos '{{arg1}}' en el servidor con un error desconocido. Verifique los registros y asegúrese de que el conjunto de datos no esté en uso en el servidor ", "TR_DELETE_DATASET_FAILED2": "Error al eliminar el conjunto de datos '{{arg1}}' en el servidor con un error desconocido. Verifique los registros y asegúrese de que el conjunto de datos existe en el servidor ", "TR_DELETE_DATASET_FAILED3": "Error al eliminar el conjunto de datos '{{arg1}}' con el error {{arg2}}", "TR_DELETE_DATASET_NOT_FOUND": "Conjunto de datos {{arg1}} no encontrado", "TR_DELETE_GOOSE": "No se puede eliminar '{{arg1}}' Bloque de control de ganso, tiene elementos existentes: {{arg2}}", "TR_DELETE_MAPPING": "Eliminar asignación", "TR_DESCRIPTION": "Descripción", "TR_DESTINATION_UDP_PORT": "Puerto UDP de destino", "TR_DH_FILE_PATH": "Ruta del archivo DH", "TR_DIAGNOSTICS_LOG_MASK": "Máscara de registro de diagnóstico", "TR_DIAGNOSTICS_LOG_MASK_DESC": "Cada bit habilita (1) / inhabilita (0) una razón para registrar datos de diagnóstico, como el cambio en el número de tramas transmitidas, en el archivo de registro de eventos.  Si es 0, no se registrarán datos de diagnóstico. ", "TR_DIAGNOSTICS_OPC_AELOG_MASK": "Diagnóstico OPC Alarma y máscara de evento", "TR_DIAGNOSTICS_OPC_AELOG_MASK_DESC": "Cada bit habilita (1) / inhabilita (0) una razón para registrar datos de diagnóstico, como el cambio en el número de tramas transmitidas, a través de la alarma OPC y el servidor de eventos. Si es 0, no se informarán datos de diagnóstico. ", "TR_DISABLE_SAVE_ON_EXIT_CHK": "Desactivar Guardar al salir", "TR_DISCRETE_INPUTS": "Entradas discretas", "TR_HORIZONTAL_DISPLAY": "Visualizar horizontal", "TR_VERTICAL_DISPLAY": "Visualizar vertical", "TR_DISPLAY_WARNING": "Mostrar advertencia", "TR_DNPACTION_MASK0": "Máscara de acción de sesión DNP", "TR_DNPACTION_MASK0_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con DNPActionPrd. \nDNP Definiciones de máscara de acción: \nVea la sección 4.2 'Nombres de etiquetas predefinidos para monitoreo y control' en el manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo DNP3.", "TR_DNPACTION_NOW": "DNP Session Action Now Mask", "TR_DNPACTION_NOW_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con DNPActionPrd. \nDNP Definiciones de máscara de acción: \nVer sección 4.2 'Nombres de etiquetas predefinidos para monitoreo y control' en el manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo DNP3.", "TR_DNPACTION_PRD0": "Período de acción de sesión DNP (ms)", "TR_DNPACTION_PRD0_DESC": "Tiempo entre acciones definidas en DNPActionMask. El período se deshabilita si se establece en cero.Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo DNP3. ", "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT": "Modo agresivo", "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT_DESC": "Habilitar el modo agresivo", "TR_DNPAUTH_EXTRA_DIAGS": "Diagnóstico adicional", "TR_DNPAUTH_EXTRA_DIAGS_DESC": "Enviar diagnósticos adicionales al analizador de protocolos", "TR_DNPAUTH_HMACALGORITHM": "Algoritmo HMAC", "TR_DNPAUTH_HMACALGORITHM_DESC": "Algoritmo HMAC que se utilizará en desafíos", "TR_DNPAUTH_KEY_CHANGE_INTERVAL": "Intervalo de cambio de clave (ms)", "TR_DNPAUTH_KEY_CHANGE_INTERVAL_DESC": "Para el maestro: intervalo de clave de sesión.  Cuando el tiempo transcurrido desde el último cambio de clave alcanza este valor, las claves de sesión se actualizarán. Para los sistemas que se comunican con poca frecuencia, esto puede establecerse en cero, utilizando solo maxKeyChangeCount para determinar cuándo actualizar las claves. \nPara esclavo: intervalo de clave de sesión esperado y recuento. Cuando transcurre este tiempo o se envían o reciben esta cantidad de mensajes de autenticación, las claves de sesión para este usuario se invalidarán. El intervalo y el recuento deben ser 2 veces el intervalo de cambio de la clave maestra y el recuento. Para los sistemas que se comunican con poca frecuencia, DNPAuthKeyChangeInterval puede establecerse en cero, utilizando solo DNPAuthMaxKeyChangeCount para determinar cuándo las claves deben considerarse viejas y deben invalidarse.", "TR_DNPAUTH_MAX_ERROR_COUNT": "Recuento máximo de errores (solo para Sav2)", "TR_DNPAUTH_MAX_ERROR_COUNT_DESC": "Número de mensajes de error que se enviarán antes de deshabilitar la transmisión de mensajes de error", "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT": "Número máximo de cambios de clave", "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT_DESC": "Recuento de ASDU de autenticación de sesión desde el último cambio de clave, cuando este número de ASDU de autenticación se transmite o recibe desde el último cambio de clave, las claves de sesión se actualizarán", "TR_DNPAUTH_OSNAME": "Nombre de la estación de salida", "TR_DNPAUTH_OSNAME_DESC": "El nombre de la estación de salida de esta sesión dnp. Esto debe configurarse para que coincida tanto en el maestro como en la estación externa ", "TR_DNPAUTH_REPLY_TIMEOUT": "Tiempo de espera de respuesta de autenticación (ms)", "TR_DNPAUTH_REPLY_TIMEOUT_DESC": "Cómo int esperar una respuesta de autenticación", "TR_DNPAUTH_SAV5ENABLE": "DNP Secure Authentication Version 5", "TR_DNPAUTH_SAV5ENABLE_DESC": "VERDADERO si se trata de una sesión DNP Secure Authentication Version 5", "TR_DNPAUTH_USER_KEY": "<PERSON><PERSON><PERSON> de usuario (debe tener 16, 24 o 32 valores hexadecimales)", "TR_DNPAUTH_USER_KEY_CHANGE_METHOD": "Método de cambio de clave. (Coloque el titular en el archivo INI)", "TR_DNPAUTH_USER_KEY_CHANGE_METHOD_DESC": "Método de cambio de clave. (Coloque el titular en el archivo INI)", "TR_DNPAUTH_USER_KEY_DESC": "Clave de usuario (debe tener 16, 24 o 32 valores hexadecimales).  Para cada clave debe haber un número de usuario único DNPAuthUserNumber ", "TR_DNPAUTH_USER_NAME": "Nombre de usuario", "TR_DNPAUTH_USER_NAME_DESC": "El nombre del usuario", "TR_DNPAUTH_USER_NUMBER": "Número de usuario", "TR_DNPAUTH_USER_NUMBER_DESC": "Número de usuario: configuración para cada usuario.  La especificación dice que el número de usuario predeterminado es 1 que proporciona un número de usuario para el dispositivo o 'cualquier' usuario, configúrelo como primer usuario en esta matriz. Agregue cualquier otro número de usuario. Para cada número de usuario en el archivo ini debe haber una DNPAuthUserKey ", "TR_DNPAUTO_ENABLE_UNSOL_CLASS1": "Clase 1", "TR_DNPAUTO_ENABLE_UNSOL_CLASS1_DESC": "Si se configura la mensajería no solicitada habilitada, este indicador indicará que la clase de evento 1 debe estar habilitada para los informes no solicitados. Este parámetro solo se usa para sesiones maestras ", "TR_DNPAUTO_ENABLE_UNSOL_CLASS2": "Habilitar automáticamente Unsol Clase 2", "TR_DNPAUTO_ENABLE_UNSOL_CLASS2_DESC": "Si se configura la mensajería no solicitada habilitada, este indicador indicará que la clase de evento 2 debe estar habilitada para los informes no solicitados. Este parámetro solo se usa para sesiones maestras ", "TR_DNPAUTO_ENABLE_UNSOL_CLASS3": "Habilitar automáticamente Unsol Clase 3", "TR_DNPAUTO_ENABLE_UNSOL_CLASS3_DESC": "Si se activa la mensajería no solicitada habilitada, este indicador indicará que la clase de evento 3 debería estar habilitada para la notificación no solicitada. Este parámetro solo se usa para sesiones maestras ", "TR_DNPCHANNEL_ACTION_MASK0": "Máscara de acción del canal DNP", "TR_DNPCHANNEL_ACTION_MASK0_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con DNPChannelActionPrd. \nDNP Definiciones de máscara de acción: \nVer sección 4.2 'Nombres de etiquetas predefinidos para monitoreo y control' en el manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo DNP3.", "TR_DNPCHANNEL_ACTION_NOW": "DNP Channel Action Now Mask", "TR_DNPCHANNEL_ACTION_NOW_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con DNPActionPrd. \nDNP Definiciones de máscara de acción: \nVer la sección 4.2 'Nombres de etiquetas predefinidos para monitoreo y control' en el Manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo DNP3.", "TR_DNPCHANNEL_ACTION_PRD0": "Período de acción del canal DNP (ms)", "TR_DNPCHANNEL_ACTION_PRD0_DESC": "Tiempo entre acciones definidas en DNPChannelActionMask. El período se deshabilita si se establece en cero.Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo DNP3. ", "TR_DNPCHANNEL_RESPONSE_TIMEOUT": "Tiempo de espera de respuesta de canal DNP (ms)", "TR_DNPCHANNEL_RESPONSE_TIMEOUT_DESC": "Para un maestro DNP, cómo int esperar una respuesta a una solicitud que realmente se ha transmitido. Este valor puede ser más corto que el tiempo de espera de respuesta predeterminado de la sesión para determinar rápidamente si un dispositivo en particular no responde, sin provocar que las solicitudes a otros dispositivos en el mismo canal también excedan el tiempo. NOTA: Esto no lo utiliza un esclavo DNP. ", "TR_DNPENABLE_SECURE_AUTHENTICATION": "Habilita la autenticación segura DNP", "TR_DNPENABLE_SECURE_AUTHENTICATION_DESC": "Habilita la autenticación segura DNP para esta sesión", "TR_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_INI_CSV_FILE": "¿Desea guardar sus archivos INI / CSV actuales", "TR_DOMAIN_DESTINATION": "Nodo lógico para crear un conjunto de datos", "TR_DOMAIN_DESTINATION_DESC": "Elija el nodo de dominio para crear el conjunto de datos", "TR_DOMAINS_LIST": "<PERSON><PERSON><PERSON>", "TR_DOMAINS_LIST_DESC": "Mostrar la lista de dominios para el modelo actual", "TR_DOWNLOAD_CURRENT_CSV_FILE": "Descargar el archivo CSV actual", "TR_DOWNLOAD_CURRENT_INI_FILE": "Descargar el archivo INI actual", "TR_DOWNLOAD_FILE": "Descargar archivo", "TR_DOWNLOAD_SELECTED_FILE": "Descargar el archivo seleccionado", "TR_DS_CONDITIONS_DETECTED": "Incluir condiciones DS detectadas", "TR_DS_CONDITIONS_DETECTED_DESC": "Incluir condiciones DS detectadas", "TR_DS_CREATE_NEW": "<PERSON>rear nuevo <PERSON>", "TR_DS_CREATE_NEW_DESC": "<PERSON>rear nuevo <PERSON>", "TR_DS_DELETE_DESC": "Eliminar DS seleccionados", "TR_DS_LIST": "Lista de conjunto de datos", "TR_DS_LIST_DESC": "Especifica la lista actual del conjunto de datos", "TR_DS_MEMBER_LIST": "Lista de miembros del conjunto de datos", "TR_DS_MEMBER_LIST_DESC": "Lista de miembros del conjunto de datos", "TR_DS_NAME": "Nombre del conjunto de datos", "TR_DS_NAME_DESC": "Especifica el nombre del conjunto de datos actual", "TR_DS_SELECTED_DELETE": "Eliminar DS seleccionado", "TR_DSA": "DSA", "TR_DSA_PRIVATE_KEY_FILE": "Archivo de clave privada DSA", "TR_DSA_PRIVATE_KEY_PASS_PHRASE": "DSA Private PassPhrase", "TR_DSA_PUBLIC_CERT_FILE": "Archivo de certificado público DSA", "TR_DSLCT_CONFIRM": "DSLCT_CONFIRM", "TR_DSLCT_CONFIRM_DESC": "Una operación de cancelación ha sido confirmada por un dispositivo remoto", "TR_DSLCT_PENDING": "DSLCT_PENDING", "TR_DSLCT_PENDING_DESC": "Se ha transmitido una operación de cancelación a un dispositivo remoto para cancelar una operación de control de 2 pasadas entre la 1ra y la 2da pasada", "TR_DSN_LIST": "Lista DSN", "TR_DSN_LIST_DESC": "Lista DSN", "TR_DUAL_END_POINT_IP_PORT": "Puerto IP de punto final dual", "TR_DUAL_REGISTER_TYPE": "Tipo de registro dual", "TR_DUP_CHNG_MON": "Cambio de actualización de datos", "TR_EDIT": "<PERSON><PERSON>", "TR_EDIT_USER": "<PERSON>ar usuario", "TR_ELEMENT_INDEX_M103": "<PERSON><PERSON><PERSON> de elemento (M103):", "TR_ELEMENT_INDEX_M103_DESC": "el índice del elemento (M103)", "TR_EMAIL": "Correo electrónico", "TR_ENABLE_EVENT_LOG_FILE": "Habilitar secuencia de registro de eventos", "TR_ENABLE_EVENT_LOG_FILE_DESC": "Si es verdadero, se habilitará el registro de secuencia de eventos. Nota: habilitar este registro puede degradar el rendimiento del SDG ", "TR_ENABLE_IEC_FULL_STACK_ADDRESSING": "Habilitar el direccionamiento de pila completa de IEC", "TR_ENABLE_UNSOLICITED_EVENT_CLASS": "Habilitar clase de evento no solicitado", "TR_ENABLE_UNSOLICITED_EVENT_CLASS_INFO": "Estas opciones requieren que el bit 'habilitar automáticamente eventos no solicitados al iniciar el dispositivo remoto o maestro 0x0100' se establezca en la máscara de modo de solicitud automática", "TR_END_DATE": "Fecha de finalización", "TR_ENGINE_INI_FILE_NAME": "Nombre del archivo INI del motor", "TR_ENTER_A_PARTIAL_OR_COMPLETE_NODE_NAME": "Ingrese una melena de nodo parcial o completa", "TR_ENTER_FILTER": "Ingrese un filtro", "TR_ENTER_PRODUCT_KEY": "Introducir clave de producto", "TR_ENTRY_ID": "ID de entrada", "TR_ENTRY_ID_DESC": "Especifica si la ID de entrada está activa", "TR_EQUATION_SYNTAX": "No se pudo modificar la ecuación {{arg1}} para {{arg2}}. Asegúrese de que la sintaxis de la ecuación sea correcta ", "TR_EQUATION_ALLOC_MEMORY": "No se pudo asignar memoria para '{{arg1}}'", "TR_EQUATION_BLANK": "Ecuación: el nombre de la ecuación no puede estar en blanco", "TR_EQUATION_END_OF_COMMENT": "Fin del comentario no encontrado", "TR_EQUATION_FUNCTION_CONVERT_FAILED": "No se pudo convertir '{{arg1}}' a '{{arg2}}'", "TR_EQUATION_FUNCTION_FIVE_ARGS": "La función '{{arg1}}' solo puede tener 5 argumentos", "TR_EQUATION_FUNCTION_FOUR_ARGS": "La función '{{arg1}}' solo puede tener 4 argumentos", "TR_EQUATION_FUNCTION_FOUT_ARGS": "La función '{{arg1}}' debe tener 4 argumentos", "TR_EQUATION_FUNCTION_NO_ARGS": "La función '{{arg1}}' solo puede tener un argumento", "TR_EQUATION_FUNCTION_ONE_ARGS": "La función '{{arg1}}' debe tener un argumento", "TR_EQUATION_FUNCTION_SIX_ARGS": "La función '{{arg1}}' solo puede tener 6 argumentos", "TR_EQUATION_FUNCTION_THREE_ARGS": "La función '{{arg1}}' solo puede tener 3 argumentos", "TR_EQUATION_FUNCTION_TO_MANY_ARGS": "Demasiados argumentos para la función '{{arg1}}'", "TR_EQUATION_FUNCTION_TOO_MANY_ARGS": "Demasiados argumentos para la función '{{arg1}}'", "TR_EQUATION_FUNCTION_TWO_ARG": "La función '{{arg1}}' debe tener al menos dos argumentos", "TR_EQUATION_FUNCTION_TWO_ARGS": "La función '{{arg1}}' solo puede tener 2 argumentos", "TR_EQUATION_ILLEGAL_CHAR": "Carácter de control ilegal dentro de la cadena", "TR_EQUATION_ILLEGAL_CHAR_IGNORED": "Carácter ilegal '{{arg1}}'; el carácter se ignora", "TR_EQUATION_ILLEGAL_TAB": "TAB ilegal dentro de la cadena", "TR_EQUATION_MEMORY_ALLOCATION": "No se pudo asignar memoria para '{{arg1}}'", "TR_EQUATION_MISSING_QUOTE": "Falta la cita final antes del final de la línea", "TR_EQUATION_PRODUCES_BAD_TYPE": "La ecuación produce un tipo que no se puede usar", "TR_EQUATION_TIME_SOURCE": "Fuente de tiempo de ecuación", "TR_EQUATION_TIME_SOURCE_DESC": "Especifica el origen de la etiqueta de tiempo para los puntos de datos que se generan como resultado de una ecuación. Los valores posibles son Update o Reported, donde Update significa la hora, en relación con el reloj del sistema SDG, en la que se calculó la ecuación por última vez, Reported especifica la hora informada del evento más reciente que causó el cambio del resultado de la ecuación. El tiempo informado será relativo al reloj del sistema del dispositivo esclavo remoto, excepto en la inicialización donde se usa el reloj del sistema SDG hasta que se reciba el primer evento con tiempo. Es importante tener en cuenta que el sondeo de datos estáticos, o los eventos recibidos que no especifican un tiempo informado, pueden hacer que cambie el valor de un punto de datos específico sin que se modifique el tiempo de su evento. Según las tasas de sondeo del sistema y otros parámetros, esto podría dar lugar a informes de tiempos discontinuos, especialmente en ecuaciones que tienen entradas de múltiples dispositivos esclavos. ", "TR_EQUATION_UNEXPCTED_TOKEN": "Token inesperado '{{arg1}}'", "TR_EQUATION_UNEXPECTED_EQUATION": "Fin inesperado de la ecuación", "TR_EQUATION_UNKONWN_IDENTIFIER": "Identificador desconocido '{{arg1}}'", "TR_EQUATION_WRONG_TYPES": "Escriba una coincidencia incorrecta entre '{{arg1}}' y '{{arg2}}' ('{{arg3}}' espera que un tipo {{arg4}} y {{arg5}} sea de { {arg6}} tipo, intente hacer que los tipos sean iguales utilizando un molde o un MDO diferente) ", "TR_EQUATIONS_LOG_MASK": "Máscara de registro de ecuaciones", "TR_EQUATIONS_LOG_MASK_DESC": "Cada bit habilita (1) / inhabilita (0) una razón para registrar los resultados de las ecuaciones. Si es 0, no se registrarán resultados de ecuaciones. ", "TR_EQUATIONS_OPC_AELOG_MASK": "Ecuaciones OPC Alarma y Máscara de evento", "TR_EQUATIONS_OPC_AELOG_MASK_DESC": "Cada bit habilita (1) / inhabilita (0) una razón para registrar los resultados de las ecuaciones a través de la alarma OPC y el servidor de eventos. Si es 0, no se informarán los resultados de la ecuación. ", "TR_ERR": "Error: {{arg1}}", "TR_ERROR": "Error", "TR_ERROR_": "Error: {{error}}", "TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED": "Error: la lista de puntos 61850 DA no ha cambiado", "TR_ERROR_61850_FC_DATASET_NOT_CHANGED": "Error: restricción funcional 61850 no modificada", "TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE": "Error: miembro del conjunto de datos GOOSE 61850 no disponible", "TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED": "Error: el conjunto de datos 61850 GOOSE no ha cambiado", "TR_ERROR_61850_IED_LIST_UNAVAILABLE": "Error: lista de IED 61850 no disponible", "TR_ERROR_61850_MODEL_NOT_SAVED": "Error: modelo 61850 no guardado", "TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE": "Error: miembro del conjunto de datos del informe 61850 no disponible", "TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED": "Error: el conjunto de datos del informe 61850 no ha cambiado", "TR_ERROR_61850_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: servidor 61850 no desconectado / conectado", "TR_ERROR_61850_SERVER_NOT_RESTARTED": "Error: servidor 61850 no reiniciado", "TR_ERROR_61850_SERVER_READ": "Error: error de lectura del servidor 61850", "TR_ERROR_ACTIVATE_OPC_ITEM_FAILED": "Error: Error en la activación del elemento OPC", "TR_ERROR_ARG1": "Error: {{arg1}}", "TR_ERROR_AUTO_CREATE_TAGS": "Error en la creación automática de etiquetas", "TR_ERROR_CAN_T_READ_AUDIT_DATABASE": "Error: no se puede leer la base de datos de auditoría", "TR_ERROR_CAN_T_READ_USERS_DATABASE": "Error: No se puede leer la base de datos de los usuarios", "TR_ERROR_CAN_T_SAVE_LICENSE": "Error: licencia no guardada", "TR_ERROR_CAN_T_SAVE_LICENSE_": "El error no puede guardar la licencia", "TR_ERROR_CAN_T_SAVE_LICENSE_PRODUCT_KEY_EXHAUSTED": "Error de licencia: clave de producto agotada", "TR_ERROR_CAN_T_SAVE_LICENSE_UNSPECIFIED_ERROR": "Error de licencia: error no especificado", "TR_ERROR_COMMAND_FAILED": "Error: error de comando", "TR_ERROR_DATA_NOT_SAVED": "Error: datos no guardados", "TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK": "Error: No se puede habilitar o deshabilitar el bloque de control de informes 61850", "TR_ERROR_FILE_NOT_DOWNLOADED": "Error: archivo no descargado", "TR_ERROR_FILE_NOT_SAVED": "Error: archivo no guardado", "TR_ERROR_GOOSE_MONITOR_STREAMS_UNAVAILABLE": "Error:", "TR_ERROR_IN_CSV_FILE": "Error: el archivo CSV no se puede guardar", "TR_ERROR_IN_INI_FILE": "Error: el archivo INI no se puede guardar", "TR_ERROR_IN_NEW_INI_FILE": "Error: el archivo INI debe tener una extensión de archivo .ini", "TR_ERROR_INVALID_EQUATION": "Error: ecuación no válida", "TR_ERROR_INVALID_FILENAME": "Error: nombre de archivo no válido", "TR_ERROR_NO_OPC_ITEM_SELECTED": "Error: No se ha seleccionado ningún elemento OPC", "TR_ERROR_OBJECT_NOT_DELETED": "Error: Objeto no eliminado", "TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE": "Error: la información de la tabla ODBC no está disponible", "TR_ERROR_OPC_ADD_ITEM_FAILED": "Error: no se puede agregar un elemento OPC", "TR_ERROR_OPC_LOAD_ITEM_FAILED": "Error: no se puede cargar el elemento OPC", "TR_ERROR_OPC_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: el servidor OPC no se puede conectar o desconectar", "TR_ERROR_OPCAE_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: el servidor OPC AE no se puede conectar o desconectar", "TR_ERROR_OPCUA_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: el servidor OPC UA no se puede conectar o desconectar", "TR_ERROR_PERFORM_WRITE_ACTION": "Error: falló la acción de escritura de preformación", "TR_ERROR_READ_OPC_ITEM_FAILED": "Error: no se puede leer el elemento OPC", "TR_ERROR_REFRESH_OPC_PROPERTIES_FAILED": "Error: no se pueden actualizar las propiedades del elemento OPC", "TR_ERROR_RESET_61850_RETRY_CONNECT_COUNT": "Error: no se puede restablecer el recuento de reintentos para 61850", "TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM": "Error: No puedo cancelar la suscripción o suscribir GOOSE Stream", "TR_ERROR_TASE2_OPERATE_CONTROL": "Error: error de control de operación ICCP", "TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE": "Error: el miembro del conjunto de datos del informe ICCP no está disponible", "TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED": "Error: el conjunto de datos del informe ICCP no ha cambiado", "TR_ERROR_TASE2_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: servidor ICCP no desconectado / conectado", "TR_ERROR_TASE2_SERVER_NOT_RESTARTED": "Error: el servidor ICCP no se reinició", "TR_ERROR_TASE2_SERVER_NOT_SAVED": "Error: el servidor ICCP no se puede guardar", "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER": "Error <br /> La comunicación con el Monitor se ha perdido. <br /> Actualice su navegador", "TR_ERROR_THE_GATEWAY_IS_NOT_RUNNING": "Error: la puerta de enlace no se está ejecutando", "TR_ERROR_USER_NOT_DELETED": "Error: usuario no eliminado", "TR_ERROR_VERIFY_61850_DATASET": "Error: No se puede verificar el conjunto de datos 61850", "TR_ERRORS_LOG_MASK": "Máscara de registro de errores", "TR_ERRORS_LOG_MASK_DESC": "Cada bit habilita (1) / deshabilita (0) una razón para registrar errores, como cambios en el número de fallas de la suma de comprobación de la capa de enlace o en el estado de la sesión de enlace en línea / fuera de línea, en el archivo de registro de eventos .  Si es 0, no se registrará nada. ", "TR_ERRORS_OPC_AELOG_MASK": "Errores OPC de alarma y evento", "TR_ERRORS_OPC_AELOG_MASK_DESC": "Cada bit habilita (1) / deshabilita (0) una razón para informar errores, como cambios en el número de fallas de suma de comprobación de la capa de enlace o en el estado de la sesión de enlace en línea / fuera de línea, a través de la alarma OPC y Servidor de eventos. Si es 0, no se informarán errores. ", "TR_EU_Type": "Tipo de UE", "TR_EU_Type_DESC": "Definir el tipo de UE", "TR_EVENT_CODE_DETECTED": "Incluir código de evento detectado", "TR_EVENT_CODE_DETECTED_DESC": "Incluir código de evento detectado", "TR_EVENT_LOG_FILE_NAME": "Nombre del archivo de registro de secuencia de eventos", "TR_EVENT_LOG_FILE_NAME_DESC": "Secuencia de nombre y ruta del archivo de registro de eventos.  Consulte el manual para obtener una descripción de las palabras clave de propiedad% xxx disponibles ", "TR_EVENT_LOG_RECORD_FORMAT": "Formato de registro de registro de secuencia de eventos", "TR_EVENT_LOG_RECORD_FORMAT_DESC": "Formato de registro de registro de secuencia de eventos.  Consulte el manual para obtener una descripción de las palabras clave de propiedad% xxx disponibles ", "TR_EVENT_NAME": "Nombre del evento", "TR_EVENT_NAME_DESC": "Definir el nombre del evento", "TR_EVENT_SPACE": "Espacio de eventos", "TR_EVENT_SPACE_DESC": "Definir el espacio del evento", "TR_EXCEPTION_STATUS": "Estado de excepción", "TR_EXECUTE_SQL": "Ejecutar / Probar consulta SQL", "TR_EXECUTE_SQL_DESC": "Ejecutar / Probar consulta SQL", "TR_EXPAND_CHILDREN": "<PERSON><PERSON><PERSON>", "TR_EXPIRES": "Vence", "TR_EXPRESSION": "Expresión:", "TR_EXPRESSION_DESC": "la expresión de la etiqueta", "TR_EXTRA": "Extra", "TR_FAILED_TO_ADD_MDO_OPC_SERVER": "No se pudo agregar {{arg1}} MDO en el servidor OPC: {{arg2}}. (¿Duplicado?)", "TR_FAILED_TO_APPLY_ALIAS_TO_61850_TAG": "Error al aplicar el alias '{{arg1}}' a la etiqueta 61850 (¿duplicado?)", "TR_FAILED_TO_APPLY_ALIAS_TO_ICCP_TAG": "Error al aplicar el alias '{{arg1}}' a la etiqueta ICCP (¿duplicado?)", "TR_FAILED_TO_APPLY_ALIAS_TO_OPC_TAG": "Error al aplicar el alias '{{arg1}}' a la etiqueta OPC (¿duplicado?)", "TR_FC_CF": "CF - Configuración", "TR_FC_CF_DESC": "Habilitar la restricción funcional CF - Configuración", "TR_FC_SP": "SP - Puntos de ajuste", "TR_FC_SP_DESC": "Habilitar la restricción funcional SP - Establecer puntos", "TR_FC_SV": "SV - Sustitución", "TR_FC_SV_DESC": "Habilitar la restricción funcional SV - Sustitución", "TR_FILE": "Archivo", "TR_FILE_ALREADY_EXISTS_OVERWRITE_IT": "El archivo seleccionado ya existe en la carpeta de destino, ¿desea sobrescribirlo?", "TR_FILE_DOWNLOADED": "Archivo descargado", "TR_FILE_SAVED": "Archivo guardado", "TR_FILTER": "Filtro", "TR_FUNCTION_M103": "Función (M103):", "TR_FUNCTION_M103_DESC": "la función (M103) de la etiqueta", "TR_FUNCTIONAL_CONSTRAINT": "Restricción funcional", "TR_FUNCTIONAL_CONSTRAINT_DESC": "Especifica la restricción funcional", "TR_GATEWAY": "<PERSON><PERSON><PERSON>", "TR_GATEWAY_ABBREVIATION": "G:", "TR_GATEWAY_API_HOST_AND_HTTP_PORT": "Supervisar host de API y puerto HTTP", "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION": "Tiempo de espera de caducidad de autenticación de usuario (segundos)", "TR_GATEWAY_CONTROL_LOG_MASK": "Máscara de registro de control", "TR_GATEWAY_CONTROL_LOG_MASK_DESC": "Cada bit habilita (1) / deshabilita (0) una razón para registrar datos de control, como cambios a pollEnabled o GeneralInterrogationPeriod, en el archivo de registro de eventos.  Si es 0, no se registrará nada. ", "TR_GATEWAY_CONTROL_OPC_AELOG_MASK": "Control OPC Alarm and Event Mask", "TR_GATEWAY_CONTROL_OPC_AELOG_MASK_DESC": "Cada bit habilita (1) / deshabilita (0) una razón para registrar datos de control, como cambios en pollEnabled o GeneralInterrogationPeriod, a través de la alarma OPC y el servidor de eventos. Si es 0, no se informará nada. ", "TR_GATEWAY_ENABLE_AUDIT": "Habilitar registro de auditoría", "TR_GATEWAY_ENABLE_AUTHENTICATION": "Habilitar autenticación de usuario", "TR_GATEWAY_ENABLE_TRACE": "Gateway Enable Trace", "TR_GATEWAY_EXE_NAME": "Gateway Exe Name", "TR_GATEWAY_HOST_AND_HTTP_PORT": "Host de puerta de enlace y puerto Http (s)", "TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE": "Elementos por página para mapeos y listas de etiquetas en la IU WEB (0 para deshabilitar)", "TR_GATEWAY_INI_FILE_PATH": "Ruta de acceso del archivo INI de la puerta de enlace", "TR_GATEWAY_IS_RUNNING": "La puerta de enlace se está ejecutando", "TR_GATEWAY_IS_STARTING": "La puerta de enlace se está iniciando", "TR_GATEWAY_IS_STOPPED": "La puerta de enlace está detenida", "TR_GATEWAY_MANAGEMENT": "Gestión de puerta de enlace", "TR_GATEWAY_TIME_ZONE_DB_PATH": "Ruta de la base de datos de la zona horaria de la puerta de enlace", "TR_GATEWAY_WEB_DIRECTORY": "Directorio web de puerta de enlace", "TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT": "Host de API del motor y puerto HTTP", "TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE": "Tamaño del bloque de actualización de Gateway Websocket", "TR_GATEWAY_WEBSOCKET_UPDATE_RATE": "Velocidad de actualización de Gateway Websocket (segundos)", "TR_GCB_LIST": "Lista de informes", "TR_GCB_LIST_DESC": "Especifica la lista de informes actual", "TR_GCB_NAME": "Nombre del informe", "TR_GCB_NAME_DESC": "Nombre del informe actual", "TR_GENERAL_INTERROGATION": "Interrogatorio general", "TR_GENERAL_INTERROGATION_DESC": "Especifica si la interrogación general está activa", "TR_GLOBAL_CREATE_TAG_AUTOMATIC": "Crear etiquetas globales automáticamente", "TR_GLOBAL_CREATE_TAG_AUTOMATIC_DESC": "si es verdadero, las etiquetas (y el espacio de almacenamiento) se crearán automáticamente al recibir nuevos puntos de datos dentro de los mensajes de respuesta entrantes desde dispositivos remotos", "TR_GOOD": "BUENO", "TR_GOOD_DESC": "no se han establecido bits", "TR_GOOSE_ADAPTER": "Adaptador GOOSE", "TR_GOOSE_ADAPTOR": "Adaptador GOOSE", "TR_GOOSE_MONTIOR_ADAPTOR": "Goose Monitor '{{arg1}}': No se pudo encontrar el adaptador ({{arg2}}, {{arg3}}). El Goose Monitor no funcionará correctamente hasta que se solucione esto ", "TR_GOOSE_MONTIOR_LOAD_SCL": "Goose Monitor '{{arg1}}': No se pudo cargar el archivo SCL: {{arg2}}. Asegúrese de que el archivo y la ruta sean correctos ", "TR_GOOSE_MONTIOR_NO_MORE": "No hay más GooseMonitors disponibles", "TR_GOOSE_NAME": "Nombre GOOSE", "TR_GOOSE_STREAM_MANAGEMENT_ADAPTOR": "Gestión del adaptador de flujo de ganso", "TR_GOOSEMONITOR_ADAPTER_DEVICE": "Ruta del sistema del dispositivo adaptador GOOSE", "TR_GOOSEMONITOR_ADAPTER_DEVICE_DESC": "Especifica una ruta de sistema del dispositivo adaptador GOOSE", "TR_GOOSEMONITOR_NAME": "Nombre del dispositivo del monitor GOOSE", "TR_GOOSEMONITOR_NAME_DESC": "Especifica el nombre del dispositivo GOOSE Monitor", "TR_GOOSEMONITOR_SCLFILE": "Archivo SCL / ICD del monitor GOOSE", "TR_GOOSEMONITOR_SCLFILE_DESC": "Especifica el archivo SCL / ICD del monitor GOOSE", "TR_GOOSEMONITOR_STREAM": "Transmisión GOOSE supervisada", "TR_GOOSEMONITOR_STREAM_DESC": "Especifica una secuencia GOOSE supervisada para un dispositivo GOOSE Monitor", "TR_GOOSEMONITOR_STREAM_THRESHOLD": "Tiempo de umbral no válido (segundos)", "TR_GOOSEMONITOR_STREAM_THRESHOLD_DESC": "Especifica un tiempo de umbral no válido de la secuencia GOOSE supervisada en segundos", "TR_GTWDEFS_UPDTRSN_CHNG_INDICATED_DESC": "La fuente de datos indica que esta actualización es un cambio", "TR_GTWDEFS_UPDTRSN_CTRL_AT_DEVICE_DESC": "Los datos se modificaron como resultado de una operación de control ejecutada localmente en el dispositivo", "TR_GTWDEFS_UPDTRSN_CTRL_BY_COMM_DESC": "Los datos se modificaron como resultado de una operación de control a través de las comunicaciones", "TR_GTWDEFS_UPDTRSN_CTRL_CONFIRM_DESC": "Utilizado para puntos de control: se ha confirmado una operación de control (por ejemplo, el dispositivo remoto envió la confirmación de haber recibido una operación de control).  Sin embargo, aunque confirmado, la operación de control puede no estar terminada todavía. <PERSON>uando termine, se utilizará GTWDEFS_UPDTRSN_CTRL_BY_COMM ", "TR_GTWDEFS_UPDTRSN_CTRL_ERROR_DESC": "Utilizado para puntos de control: Se produjo un error con la operación de control", "TR_GTWDEFS_UPDTRSN_CTRL_PENDING_DESC": "Utilizado para puntos de control: se ha iniciado una operación de control (por ejemplo, enviada a un dispositivo remoto).  Esto podría ocurrir para la segunda pasada de una operación de 2 pasadas, o podría ocurrir para la única pasada de una operación de 1 pasada", "TR_GTWDEFS_UPDTRSN_DSLCT_CONFIRM_DESC": "Utilizado para los puntos de control: se ha confirmado una operación de selección (cancelación de una operación de selección de primer paso) (por ejemplo, el dispositivo remoto envió la confirmación de haber desactivado la operación de control)", "TR_GTWDEFS_UPDTRSN_DSLCT_PENDING_DESC": "Utilizado para los puntos de control: la operación de selección (cancelación de una operación de selección de primer paso) se ha iniciado (por ejemplo, enviada a un dispositivo remoto)", "TR_GTWDEFS_UPDTRSN_NONE_DESC": "Se puede usar como una máscara de registro / * para deshabilitar el registro", "TR_GTWDEFS_UPDTRSN_REFRESH_DESC": "La fuente de datos está actualizando los datos (sin solicitud); no se indica necesariamente ningún evento de cambio", "TR_GTWDEFS_UPDTRSN_REQUESTED_DESC": "Se solicitaron los datos; no se indica necesariamente ningún evento de cambio", "TR_GTWDEFS_UPDTRSN_SLCT_CONFIRM_DESC": "Utilizado para puntos de control: se ha confirmado un primer pase en una operación de control de dos pasos (por ejemplo, el dispositivo remoto envió confirmación de haber recibido la operación de control de primer paso)", "TR_GTWDEFS_UPDTRSN_SLCT_PENDING_DESC": "Utilizado para puntos de control: se ha iniciado el primer paso en una operación de control de dos pasos (por ejemplo, enviado a un dispositivo remoto)", "TR_GTWDEFS_UPDTRSN_TEST_MODE_DESC": "Utilizado por algunos protocolos para indicar que el punto o dispositivo está funcionando en un modo de prueba", "TR_GTWDEFS_UPDTRSN_UNKNOWN_DESC": "Los datos se están actualizando para un desconocido", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_ALL": "TODOS", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_DATA": "Datos", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_HEALTH": "<PERSON><PERSON>", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_PERFORMANCE": "Perf", "TR_GTWTYPES_TAG_PURPOSE_ALL": "Todos", "TR_GTWTYPES_TAG_PURPOSE_MASK_DATA": "Filtro de datos", "TR_GTWTYPES_TAG_PURPOSE_MASK_HEALTH": "Filtro de estado", "TR_GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE": "Filtro de rendimiento", "TR_HEALTH_DESC": "<PERSON><PERSON>", "TR_HEALTH_VIEW": "Vista de estado", "TR_HELP": "<PERSON><PERSON><PERSON>", "TR_HI": "<PERSON><PERSON>", "TR_HIGH_ORDER_INDEX": "Índice de registro de pedido alto", "TR_HIGH_ORDER_INDEX_DESC": "Especifique el índice de registro de orden superior", "TR_HOLDING_REGISTERS": "Manteniendo registros", "TR_I14AUTH_ENABLE": "Habilita la autenticación segura", "TR_I14AUTH_ENABLE_DESC": "Habilita la autenticación segura para este sector", "TR_I14AUTH_EXTRA_DIAGS": "Diagnóstico adicional", "TR_I14AUTH_EXTRA_DIAGS_DESC": "Enviar diagnósticos adicionales al analizador de protocolos", "TR_I14AUTH_HMACALGORITHM": "algoritmo HMAC", "TR_I14AUTH_HMACALGORITHM_DESC": "Algoritmo HMAC para ser utilizado en desafíos", "TR_I14AUTH_KEY_CHANGE_INTERVAL": "Intervalo de clave", "TR_I14AUTH_KEY_CHANGE_INTERVAL_DESC": "Para maestro: intervalo de clave de sesión.  Cuando el tiempo transcurrido desde el último cambio de clave alcanza este valor, las claves de sesión se actualizarán. Para los sistemas que se comunican con poca frecuencia, esto puede establecerse en cero, utilizando solo maxKeyChangeCount para determinar cuándo actualizar las claves. \nPara esclavo: intervalo de clave de sesión esperado y recuento. Cuando transcurre este tiempo o se envían o reciben esta cantidad de mensajes de autenticación, las claves de sesión para este usuario se invalidarán. El intervalo y el recuento deben ser 2 veces el intervalo de cambio de la clave maestra y el recuento. Para los sistemas que se comunican con poca frecuencia, I14AuthKeyChangeInterval puede establecerse en cero, utilizando solo I14AuthMaxKeyChangeCount para determinar cuándo las claves deben considerarse viejas y deben invalidarse.", "TR_I14AUTH_MAX_KEY_CHANGE_COUNT": "Recuento de cambio de clave", "TR_I14AUTH_MAX_KEY_CHANGE_COUNT_DESC": "Recuento de ASDU de autenticación de sesión desde el último cambio de clave, cuando este número de ASDU de autenticación se transmite o recibe desde el último cambio de clave, las claves de sesión se actualizarán", "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH": "Longitud de los datos de desafío aleatorio", "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH_DESC": "Longitud de los datos de desafío aleatorios para enviar en la solicitud de desafío", "TR_I14AUTH_REPLY_TIMEOUT": "Cómo int esperar una respuesta de autenticación (ms)", "TR_I14AUTH_REPLY_TIMEOUT_DESC": "Cómo int esperar una respuesta de autenticación", "TR_I14AUTH_SECURITY_STATS_IOA": "Número de punto base para las estadísticas de seguridad", "TR_I14AUTH_SECURITY_STATS_IOA_DESC": "Dirección del objeto de información base (IOA) para las estadísticas de seguridad", "TR_I14AUTH_USER_KEY": "<PERSON><PERSON>e de usuario (debe tener 32 valores hexadecimales)", "TR_I14AUTH_USER_KEY_DESC": "Clave de usuario (debe tener 32 valores hexadecimales).  Para cada clave debe haber un número de usuario único I14AuthUserNumber. ", "TR_I14AUTH_USER_NAME": "Nombre del usuario", "TR_I14AUTH_USER_NAME_DESC": "El nombre del usuario", "TR_I14AUTH_USER_NUMBER": "Número de usuario", "TR_I14AUTH_USER_NUMBER_DESC": "Número de usuario: configuración para cada usuario.  La especificación dice que el número de usuario predeterminado es 1 que proporciona un número de usuario para el dispositivo o 'cualquier' usuario, configúrelo como primer usuario en esta matriz. Agregue cualquier otro número de usuario. Para cada número de usuario en el archivo ini debe haber una I14AuthUserKey ", "TR_I61400ALARMS_NAME": "Nombre del nodo de alarma", "TR_I61400ALARMS_NAME_DESC": "Especifica el nombre del nodo SDG Alarms", "TR_I61400EVENT_ALARMS_ARRAY_NAME": "Nombre de matriz de alarmas de eventos", "TR_I61400EVENT_ALARMS_ARRAY_NAME_DESC": "Especifica el nombre de la matriz de alarmas de eventos IEC 61400-25 (WALM)", "TR_I61400STATUS_ALARMS_ARRAY_NAME": "Nombre de matriz de alarmas de estado", "TR_I61400STATUS_ALARMS_ARRAY_NAME_DESC": "Especifica el nombre de la matriz de alarmas de estado IEC 61400-25 (WALM)", "TR_I61850AUTH_MECHANISM": "Mecanismo de autorización ('Ning<PERSON>' o 'Contraseña' o 'Certificado')", "TR_I61850AUTH_MECHANISM_DESC": "Mecanismo de autorización IEC 61850 ('<PERSON><PERSON><PERSON>' o 'Contraseña' o 'Certificado')", "TR_I61850AUTH_PASSWORD": "Contraseña", "TR_I61850AUTH_PASSWORD_DESC": "Contraseña de autorización IEC 61850", "TR_I61850CLIENT_AEINVOKE_ID": "ID de invocación AE", "TR_I61850CLIENT_AEINVOKE_ID_DESC": "Especifica el ID de invocación AE para este cliente IEC 61850", "TR_I61850CLIENT_AEQUALIFIER": "Calificador AE", "TR_I61850CLIENT_AEQUALIFIER_DESC": "Especifica el calificador AE para este cliente IEC 61850", "TR_I61850CLIENT_APINVOKE_ID": "ID de invocación AP", "TR_I61850CLIENT_APINVOKE_ID_DESC": "Especifica la ID de invocación AP para este cliente IEC 61850", "TR_I61850CLIENT_APP_ID": "ID de la aplicación", "TR_I61850CLIENT_APP_ID_DESC": "Especifica el ID de la aplicación de este cliente IEC 61850", "TR_I61850CLIENT_CONNECT_TIMEOUT": "Tiempo de espera de conexión MMS (ms)", "TR_I61850CLIENT_CONNECT_TIMEOUT_DESC": "Especifica el tiempo de espera de conexión MMS para el Cliente IEC 61850.  Después de comenzar un intento de conexión, así es como int esperar el éxito.  La longitud de este parámetro dependerá de la topología de su red.  Este valor también se utiliza para especificar el tiempo de espera para los mensajes de solicitud 61850 ", "TR_I61850CLIENT_IPADDRESS": "Dirección IP del cliente", "TR_I61850CLIENT_IPADDRESS_DESC": "Especifica la dirección IP de este cliente IEC 61850.  Esto puede ser útil para seleccionar un adaptador de red diferente ", "TR_I61850CLIENT_MMSCOMMON_NAME": "Nombre común de MMS", "TR_I61850CLIENT_MMSCOMMON_NAME_DESC": "Especifica el nombre común de MMS", "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE": "Archivo de clave privada", "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE_DESC": "Especifica el archivo de clave privada MMS", "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE": "Frase de contraseña de clave privada", "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE_DESC": "Especifica la frase de paso de clave privada MMS", "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE": "Archivo de certificado público", "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE_DESC": "Especifica el archivo de certificado público MMS", "TR_I61850CLIENT_NAME": "Nombre del cliente", "TR_I61850CLIENT_NAME_DESC": "Nombre del cliente IEC 61850", "TR_I61850CLIENT__PRESENTATION_ADDRESS": "Dirección de presentación", "TR_I61850CLIENT_PRESENTATION_ADDRESS_DESC": "Especifica la dirección de presentación de este cliente IEC 61850", "TR_I61850CLIENT_RECONNECT_RETRY_COUNT": "Reconectar recuento de reintentos", "TR_I61850CLIENT_RECONNECT_RETRY_COUNT_DESC": "Especifica el recuento de reconexión de reconexión para el Cliente IEC 61850 (0 = intento de reconexión para siempre) Una conexión exitosa hará que el contador de límite interno se vuelva a establecer en 0 resultando en intentos continuos de conexión al servidor IEC 61850 ", "TR_I61850CLIENT_RECONNECT_TIME": "Reconectar tiempo de espera (ms)", "TR_I61850CLIENT_RECONNECT_TIME_DESC": "Especifica el tiempo de espera de reconexión para el Cliente IEC 61850 (0 = sin reconexión), debe ser mayor que I61850ClientConnectTimeout", "TR_I61850CLIENT_SESSION_ADDRESS": "Dirección de sesión", "TR_I61850CLIENT_SESSION_ADDRESS_DESC": "Especifica la dirección de sesión de este cliente IEC 61850", "TR_I61850CLIENT_TLSMAX_PDUS": "PDU máx. Antes de forzar la renegociación de cifrado", "TR_I61850CLIENT_TLSMAX_PDUS_DESC": "Especifica la PDU máxima de TLS antes de forzar la renegociación de cifrado", "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME": "Tiempo de espera de renegociación máxima (ms)", "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME_DESC": "Especifica el tiempo de renegociación máximo de TLS", "TR_I61850CLIENT_TLSRENEGOTIATION": "Renegociación (seg)", "TR_I61850CLIENT_TLSRENEGOTIATION_DESC": "Tiempo máximo (segundos) antes de forzar la renegociación de cifrado", "TR_I61850CLIENT_TRANSPORT_ADDRESS": "Dirección de transporte", "TR_I61850CLIENT_TRANSPORT_ADDRESS_DESC": "Especifica la dirección de transporte de este cliente IEC 61850", "TR_I61850CLIENT_USE_SISCO_COMPATABILITY": "Usar compatibilidad ED1", "TR_I61850CLIENT_USE_SISCO_COMPATABILITY_DESC": "Especifica si la configuración de seguridad usa compatibilidad ED1 o Sisco", "TR_I61850GOOSE_ADAPTER_NAME": "Nombre del adaptador de ganso", "TR_I61850GOOSE_ADAPTER_NAME_DESC": "Nombre del adaptador de ganso seleccionado", "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED": "Cargar modelo desde archivo", "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED_DESC": "¿Debe el cliente IEC 61850 cargar el modelo del archivo en I61850SCLFileName []", "TR_I61850POLLED_DATA_SET_NAME": "Nombre del conjunto de datos sondeados", "TR_I61850POLLED_DATA_SET_NAME_DESC": "Especifica el nombre del conjunto de datos encuestados", "TR_I61850POLLED_DATA_SET_PERIOD": "Período del conjunto de datos sondeados (ms)", "TR_I61850POLLED_DATA_SET_PERIOD_DESC": "Especifica el período para leer el conjunto de datos encuestados. Un valor de cero significa deshabilitar, es decir, no se realizará ningún sondeo ", "TR_I61850POLLED_POINT_SET_NAME": "Nombre del conjunto de puntos sondeados", "TR_I61850POLLED_POINT_SET_NAME_DESC": "Especifica el nombre del conjunto de puntos encuestados", "TR_I61850POLLED_POINT_SET_PERIOD": "Periodo establecido de punto de sondeo (ms)", "TR_I61850POLLED_POINT_SET_PERIOD_DESC": "Especifica el período para leer el conjunto de puntos encuestados. Un valor de cero significa deshabilitar, es decir, no se realizará ningún sondeo ", "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT": "Purgar RCB antes de habilitar al volver a conectar", "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Purgar RCB antes de habilitar al volver a conectar", "TR_I61850RCBPURGE_BEFORE1ST_ENABLE": "Purgar RCB antes de la primera habilitación", "TR_I61850RCBPURGE_BEFORE1ST_ENABLE_DESC": "Purgar RCB antes de la primera habilitación", "TR_I61850RCBRCBRETRY_ENABLE_COUNT": "Número de veces que el SDG debe volver a intentar habilitar", "TR_I61850RCBRCBRETRY_ENABLE_COUNT_DESC": "El número de veces que el SDG debe volver a intentar habilitar este informe cuando no se habilita en el servidor activo. -1 = para siempre, 0 = nunca", "TR_I61850RCBRETRY_ENABLE_PERIOD": "reintentando habilitar el período RCB (ms)", "TR_I61850RCBRETRY_ENABLE_PERIOD_DESC": "El período para volver a intentar habilitar RCB en milisegundos", "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW": "Desbordamiento de búfer incluido", "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW_DESC": "Especifica la propiedad incluida Desbordamiento de búfer", "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME": "Tiempo de almacenamiento intermedio (ms)", "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME_DESC": "Especifica el tiempo de búfer de un bloque de control de informe", "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV": "Revisión de configuración incluida", "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV_DESC": "Especifica la propiedad incluida Revisión de configuración", "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE": "Supervisar cambio de datos", "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE_DESC": "Especifica la propiedad de cambio de datos del monitor del bloque de control de informes", "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF": "Referencia de datos incluida", "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF_DESC": "Especifica la propiedad incluida de referencia de datos", "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME": "Nombre del conjunto de datos incluido", "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME_DESC": "Especifica la propiedad incluida del Nombre del conjunto de datos", "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME": "Nombre del conjunto de datos", "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME_DESC": "Especifica el nombre de un conjunto de datos de bloque de control de informe en un servidor IEC 61850", "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE": "Supervisar cambio de actualización de datos", "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE_DESC": "Especifica la propiedad Cambio de actualización de datos del monitor del bloque de control de informes", "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID": "ID de entrada incluida", "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID_DESC": "Especifica la propiedad incluida ID de entrada", "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG": "Interrogatorio general admitido", "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG_DESC": "Especifica la propiedad compatible con la interrogación general del bloque de control de informes", "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED": "Período de integridad monitoreado", "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED_DESC": "Especifica la propiedad monitoreada del período de integridad del bloque de control de informes", "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD": "Período de integridad (ms)", "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD_DESC": "Especifica el período de actualizaciones de integridad de un Bloque de control de informes", "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE": "Supervisar el cambio de calidad", "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE_DESC": "Especifica la propiedad de cambio de calidad del monitor del bloque de control de informes", "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL": "Motivo de la inclusión incluido", "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL_DESC": "Especifica la razón para la propiedad incluida Incl", "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM": "Número de secuencia incluido", "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM_DESC": "Especifica la propiedad incluida del número de secuencia", "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP": "Sello de tiempo incluido", "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP_DESC": "Especifica la propiedad incluida de sello de tiempo", "TR_I61850SCLCLIENT_IEDNAME": "Modelo de IED del cliente para cargar", "TR_I61850SCLCLIENT_IEDNAME_DESC": "Modelo de IED de cliente opcional para cargar en el archivo SCL", "TR_I61850SCLFILE_IEDNAME": "Modelo de IED del servidor para cargar", "TR_I61850SCLFILE_IEDNAME_DESC": "Modelo de IED de servidor opcional para cargar en el archivo SCL", "TR_I61850SCLFILE_NAME": "Nombre de archivo SCL", "TR_I61850SCLFILE_NAME_DESC": "Nombre de archivo SCL opcional para cargar el modelo.  Si está en el mismo directorio que el archivo INI, la ruta no es necesaria. ", "TR_I61850SERVER_AEINVOKE_ID": "ID de invocación AE", "TR_I61850SERVER_AEINVOKE_ID_DESC": "Especifica la ID de invocación AE para el servidor IEC 61850", "TR_I61850SERVER_AEQUALIFIER": "Calificador AE", "TR_I61850SERVER_AEQUALIFIER_DESC": "Especifica el calificador AE para el servidor IEC 61850", "TR_I61850SERVER_APINVOKE_ID": "AP Qualifier", "TR_I61850SERVER_APINVOKE_ID_DESC": "Especifica el calificador AP para el servidor IEC 61850", "TR_I61850SERVER_APP_ID": "ID de la aplicación", "TR_I61850SERVER_APP_ID_DESC": "Especifica el ID de la aplicación del servidor IEC 61850 para conectarse", "TR_I61850SERVER_GOOSE_ADAPTER_NAME": "Nombre del adaptador de ganso", "TR_I61850SERVER_GOOSE_ADAPTER_NAME_DESC": "Nombre del adaptador de ganso seleccionado", "TR_I61850SERVER_IPADDRESS": "Dirección IP del servidor", "TR_I61850SERVER_IPADDRESS_DESC": "Especifica la dirección IP del servidor IEC 61850 para conectarse", "TR_I61850SERVER_IPPORT": "Puerto IP del servidor", "TR_I61850SERVER_IPPORT_DESC": "Establece el número de puerto TCP / IP para usar", "TR_I61850SERVER_PRESENTATION_ADDRESS": "Dirección de presentación", "TR_I61850SERVER_PRESENTATION_ADDRESS_DESC": "Especifica la dirección de presentación del servidor IEC 61850 para conectarse", "TR_I61850SERVER_SCLFILE_IEDNAME": "modelo IED", "TR_I61850SERVER_SCLFILE_IEDNAME_DESC": "Modelo de IED opcional para cargar en el archivo SCL", "TR_I61850SERVER_SESSION_ADDRESS": "Dirección de sesión", "TR_I61850SERVER_SESSION_ADDRESS_DESC": "Especifica la dirección de sesión del servidor IEC 61850 para conectarse", "TR_I61850SERVER_TRANSPORT_ADDRESS": "Dirección de transporte", "TR_I61850SERVER_TRANSPORT_ADDRESS_DESC": "Especifica la dirección de transporte del servidor IEC 61850 para conectarse", "TR_I61850TIME_ZONE_BIAS": "Desplazamiento de zona horaria en minutos", "TR_I61850TIME_ZONE_BIAS_DESC": "Un desplazamiento en minutos para ajustar para servidores que no envían tiempo en UTC", "TR_ICCP_CONFIG": "Configuración ICCP", "TR_ID": "ID", "TR_IECACTION_MASK0": "Máscara de acción IEC", "TR_IECACTION_MASK0_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con IECActionPrd. \nIEC Definiciones de máscara de acción: \nVea la sección 4.2 'Nombres de etiquetas predefinidos para monitoreo y control' en el manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el perfil de protocolo IEC 60870-5", "TR_IECACTION_NOW": "Máscara de acción IEC ahora", "TR_IECACTION_NOW_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con IECActionPrd. \nIEC Definiciones de máscara de acción: \nVer sección 4.2 'Nombres de etiquetas predefinidas para monitoreo y control' en el manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el perfil de protocolo IEC 60870-5", "TR_IECACTION_PRD0": "Período de acción IEC (ms)", "TR_IECACTION_PRD0_DESC": "Tiempo entre acciones definidas en la IECActionMask.El período se deshabilita si se establece en cero.Este parámetro solo se utiliza para sesiones maestras que utilizan el perfil de protocolo IEC 60870-5 ", "TR_IECACTION_RETRY_COUNT0": "Recuento de reintentos de acción IEC", "TR_IECACTION_RETRY_COUNT0_DESC": "Cuántas veces volver a intentar la máscara de acción en caso de error.Si el tiempo de reintento es 0, esto no tiene efecto.Este parámetro solo se utiliza para sesiones maestras que utilizan el perfil de protocolo IEC 60870-5 ", "TR_IECACTION_RETRY_TIME0": "Tiempo de reintento de acción IEC (ms)", "TR_IECACTION_RETRY_TIME0_DESC": "El período de tiempo entre las repeticiones de esta máscara de acción.Si el tiempo es 0, esto no tiene efecto.Este parámetro solo se utiliza para sesiones maestras que utilizan el perfil de protocolo IEC 60870-5 ", "TR_IED_CLIENT": "IED del cliente", "TR_IED_SERVER": "Servidor IED", "TR_IGNORE_DST": "Ignorar DST", "TR_IGNORE_DST_DESC": "Si verdadero y UseTimeZoneClock es verdadero, los cambios en el horario de verano se ignoran para mostrar la hora", "TR_ILLEGAL_CHARACTER": "Carácter ilegal", "TR_IN_TRANSIT": "IN_TRANSIT", "TR_IN_TRANSIT_DESC": "En tránsito / charla", "TR_INFORMATION": "Información", "TR_INFORMATION_OBJECT_ADDRESS": "Dirección del objeto de información:", "TR_INFORMATION_OBJECT_ADDRESS_DESC": "la dirección del objeto de información de la etiqueta", "TR_INPUT_REGISTERS": "Registros de entrada", "TR_INSTALL_V2C": "Instalar V2C", "TR_INTEG_PERIOD": "Período de integridad", "TR_INTEG_PRD_MON": "Período de integridad supervisado", "TR_INTEGRITY_PERIOD": "Período de integridad (ms)", "TR_INTEGRITY_PERIOD_DESC": "Especifica el período de integridad actual", "TR_INTEGRITY_PERIOD_MONITORED": "Período de integridad monitoreado", "TR_INTEGRITY_PERIOD_MONITORED_DESC": "Especifica si el período de integridad monitoreado está activo", "TR_INTERNAL_MDO_ALREADY_DEFINED": "MDO ya está definido, no se puede crear", "TR_INTERNAL_SAVE_BLANK_NAME": "Error: el nombre no puede estar en blanco", "TR_INTERNAL_SET_MDO_OPTIONS": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_INTERNAL_SET_OPTIONS_MDO": "No se pudieron establecer las opciones internas de MDO {{arg1}}", "TR_INTERROGATION_COMMAND_AND_TOTALS_COUNTERS": "Comando de interrogación y contadores de totales", "TR_INTERVAL_DESC": "Especifica el intervalo actual", "TR_INVALID": "NO VÁLIDO", "TR_INVALID_DESC": "<PERSON>v<PERSON><PERSON><PERSON>", "TR_INVALID_TIME": "INVALID_TIME", "TR_INVALID_TIME_DESC": "Tiempo transcurrido / inválido", "TR_INVOICE": "Factura", "TR_IP_ADDRESS": "Dirección IP", "TR_IS_ACTIVE": "Activo", "TR_IS_INCORRECT": "es incorrecto", "TR_IS_LICENSED": "Con licencia", "TR_IS_REDUNDANCY_GROUP": "Es el grupo de redundancia", "TR_IS_REDUNDANCY_GROUP_DESC": "¿Es este canal un grupo de redundancia", "TR_IS_REDUNDANT_CHANNEL": "Es un canal redundante", "TR_IS_REDUNDANT_CHANNEL_DESC": "¿Este canal es redundante", "TR_IS_REQUIRED": "es obligatorio", "TR_IS_REQUIRED_NUMERICAL": "es obligatorio (numérico)", "TR_IS_XML_CLIENT": "Es un cliente XML", "TR_IS_XML_CLIENT_DESC": "Si el OPC CLient es un cliente XML", "TR_ISRV61850SERVER_AEINVOKE_ID": "ID de invocación AE", "TR_ISRV61850SERVER_AEINVOKE_ID_DESC": "Especifica la ID de invocación AE para el servidor IEC 61850", "TR_ISRV61850SERVER_AEQUALIFIER": "Calificador AE", "TR_ISRV61850SERVER_AEQUALIFIER_DESC": "Especifica el calificador AE para el servidor IEC 61850", "TR_ISRV61850SERVER_APINVOKE_ID": "ID de invocación AP", "TR_ISRV61850SERVER_APINVOKE_ID_DESC": "Especifica la ID de invocación AP para el servidor IEC 61850", "TR_ISRV61850SERVER_APP_ID": "ID de la aplicación", "TR_ISRV61850SERVER_APP_ID_DESC": "Especifica el ID de la aplicación del servidor IEC 61850", "TR_ISRV61850SERVER_AUTH_MECHANISM": "Mecanismo de autorización", "TR_ISRV61850SERVER_AUTH_MECHANISM_DESC": "Mecanismo de autorización del servidor IEC 61850 ('Aucun' ou 'Mot de passe' ou 'Certificat')", "TR_ISRV61850SERVER_AUTH_PASSWORD": "Contraseña", "TR_ISRV61850SERVER_AUTH_PASSWORD_DESC": "Contraseña de autorización del servidor IEC 61850", "TR_ISRV61850SERVER_ICDFILE": "Nombre de archivo ICD", "TR_ISRV61850SERVER_ICDFILE_DESC": "Nombre de archivo ICD para el servidor IEC 61850.  Si está en el mismo directorio que el archivo INI, la ruta no es necesaria ", "TR_ISRV61850SERVER_IPADDRESS": "dirección IP", "TR_ISRV61850SERVER_IPADDRESS_DESC": "Especifica la dirección IP del servidor IEC 61850", "TR_ISRV61850SERVER_IPPORT": "Puerto IP para escuchar", "TR_ISRV61850SERVER_IPPORT_DESC": "Especifica el puerto IP para escuchar este servidor IEC 61850", "TR_ISRV61850SERVER_NAME": "Nombre del servidor 61850", "TR_ISRV61850SERVER_NAME_DESC": "Nombre del servidor IEC 61850", "TR_ISRV61850SERVER_PRESENTATION_ADDRESS": "Dirección de presentación", "TR_ISRV61850SERVER_PRESENTATION_ADDRESS_DESC": "Especifica la dirección de presentación del servidor IEC 61850", "TR_ISRV61850SERVER_SESSION_ADDRESS": "Dirección de sesión", "TR_ISRV61850SERVER_SESSION_ADDRESS_DESC": "Especifica la dirección de sesión del servidor IEC 61850", "TR_ISRV61850SERVER_TRANSPORT_ADDRESS": "Dirección de transporte", "TR_ISRV61850SERVER_TRANSPORT_ADDRESS_DESC": "Especifica la dirección de transporte del servidor IEC 61850", "TR_ITEM_ATTRIBUTES": "Atributos del elemento", "TR_ITEM_DESCRIPTION": "Descripción del artículo", "TR_ITEM_DESCRIPTION_DESC": "Definir la descripción del artículo", "TR_ITEM_ID": "ID del artículo", "TR_ITEM_ID_DESC": "Definir la ID del artículo", "TR_ITEM_NAME": "Nombre del elemento", "TR_ITEM_NAME_DESC": "Definir el nombre del elemento", "TR_ITEM_PARENT_BROWSER": "Explorador de elementos", "TR_ITEM_TYPE": "Tipo de elemento", "TR_ITEM_TYPE_DESC": "Tipo de elemento", "TR_ITEM_VALUE_TYPE": "Tipo de valor del artículo", "TR_ITEM_VALUE_TYPE_DESC": "Definir el tipo de valor del artículo", "TR_ITEMS_PARENT_BROWSER_DESC": "Explorador de elementos", "TR_KEY_ID": "ID de clave", "TR_LANGUAGE": "Idioma", "TR_LICENSE": "Licencia", "TR_LICENSE_DEMO": "La sesión {{arg2}} está configurada como el protocolo {{arg2}} que actualmente no tiene licencia. Esta sesión se ha configurado inactiva ", "TR_LICENSE_DEMO_EXPIRES": "La licencia de demostración de SCADA Data Gateway caducará el {{arg1}}", "TR_LICENSE_DESC": "Registrar mensajes de rastreo para el código de licencia", "TR_LICENSE_EXCEPTION": "Error del administrador de licencias: {{arg1}} Póngase en contacto con el servicio de atención al cliente de Triangle Microworks", "TR_LICENSE_FAILED_SEVERE": "Se produjo un error desconocido del Administrador de licencias. Póngase en contacto con el servicio de atención al cliente de Triangle Microworks ", "TR_LICENSE_INFORMATION": "Información de licencia", "TR_LICENSE_LOST": "Se perdió la conexión de licencia para la aplicación. Todas las sesiones están deshabilitadas ", "TR_LICENSE_MANAGER": "Administrador de licencias", "TR_LICENSE_OTPIONS": "Opciones de licencia", "TR_LICENSE_REACQUIRED": "Se ha vuelto a adquirir la conexión de licencia para la aplicación. Todas las sesiones se han vuelto a habilitar. ", "TR_LICENSE_TYPE": "Tipo de licencia", "TR_LINK_CLASS_PENDING_CNT": "Conteo pendiente de clase", "TR_LINK_CLASS_PENDING_CNT_DESC": "Para un enlace de comunicación maestro no balanceado, el número total de tramas de solicitud consecutivas de clase 1 y clase 2 que pueden enviarse a un dispositivo cuando hay un mensaje de respuesta de capa de aplicación pendiente de este dispositivo antes de pasar al siguiente dispositivo en Una red multipunto.  Este parámetro no tiene efecto si solo un dispositivo está configurado para un canal de comunicación.  Si este parámetro se establece en cero, el dispositivo aún se sondea como se describe para el parámetro M870CNFG_LINK_CLASS1_POLL_CNT.Este parámetro solo se aplica a las sesiones maestras IEC 60870-5-101 e IEC 60870-5-103", "TR_LINK_CLASS1PENDING_DLY": "Retraso pendiente de clase 1 (ms)", "TR_LINK_CLASS1PENDING_DLY_DESC": "Para un enlace de comunicación maestra no balanceado, el retraso mínimo en milisegundos después de enviar la solicitud de datos de clase 1 cuando una respuesta de la capa de aplicación está pendiente para esta sesión.  Este parámetro puede usarse para limitar el ancho de banda en un medio compartido como Ethernet o para evitar gravar el dispositivo objetivo con una sobrecarga de comunicación innecesaria.Este parámetro solo se aplica a las sesiones maestras IEC 60870-5-101 e IEC 60870-5-103", "TR_LINK_CLASS1POLL_CNT": "Recuento de encuestas de clase 1", "TR_LINK_CLASS1POLL_CNT_DESC": "Para un enlace de comunicación maestra desequilibrado, el número total de tramas de solicitud de clase 1 consecutivas que pueden enviarse a un dispositivo antes de pasar al siguiente dispositivo en una red multipunto (la clase 2 siempre está limitada a una trama de solicitud) a menos que haya una respuesta de capa de aplicación pendiente).  Este parámetro no tiene efecto si solo un dispositivo está configurado para un canal de comunicación.  En una topología de red multipunto, este parámetro se usa para equilibrar el sondeo entre todos los dispositivos y evitar que un dispositivo capture todos los mensajes de sondeo. Este parámetro solo se aplica a las sesiones maestras IEC 60870-5-101 e IEC 60870-5-103 ", "TR_LINK_CLASS1POLL_DLY": "Retraso de encuesta de clase 1 (ms)", "TR_LINK_CLASS1POLL_DLY_DESC": "Para un enlace de comunicación maestro no balanceado, el retraso mínimo en milisegundos después de enviar la solicitud de datos de clase 1 cuando una respuesta de la capa de aplicación no está pendiente para esta sesión.  Este parámetro puede usarse para limitar el ancho de banda en un medio compartido como ethernet o para evitar gravar el dispositivo de destino con una sobrecarga de comunicación innecesaria.Este parámetro solo se aplica a las sesiones maestras IEC 60870-5-101 e IEC 60870-5-103", "TR_LINK_CLASS2PENDING_DLY": "Retraso pendiente de clase 2 (ms)", "TR_LINK_CLASS2PENDING_DLY_DESC": "Para un enlace de comunicación maestra desequilibrado, el retraso mínimo en milisegundos después de enviar la solicitud de datos de clase 2 cuando hay una respuesta de capa de aplicación pendiente para esta sesión.  Este parámetro puede usarse para limitar el ancho de banda en un medio compartido como ethernet o para evitar gravar el dispositivo de destino con una sobrecarga de comunicación innecesaria.Este parámetro solo se aplica a las sesiones maestras IEC 60870-5-101 e IEC 60870-5-103", "TR_LINK_CLASS2POLL_DLY": "Retraso de encuesta de clase 2 (ms)", "TR_LINK_CLASS2POLL_DLY_DESC": "Para un enlace de comunicación maestro no balanceado, el retraso mínimo en milisegundos después de enviar la solicitud de datos de clase 2 cuando una respuesta de capa de aplicación no está pendiente para esta sesión.  Este parámetro puede usarse para limitar el ancho de banda en un medio compartido como ethernet o para evitar gravar el dispositivo de destino con una sobrecarga de comunicación innecesaria.Este parámetro solo se aplica a las sesiones maestras IEC 60870-5-101 e IEC 60870-5-103", "TR_LINK_CNFM_TIMEOUT": "T1 - Tiempo de espera de confirmación de capa de enlace (ms)", "TR_LINK_CNFM_TIMEOUT_DESC": "Tiempo máximo para esperar la confirmación de la trama. Para una sesión IEC 60870-5-104, este es el parámetro T1.  Este parámetro no se aplica a las conexiones de capa de enlace (sesiones) cuando GATEWAY está actuando como un esclavo desequilibrado. ", "TR_LINK_CONFIRM_MODE": "Modo de confirmación de enlace", "TR_LINK_CONFIRM_MODE_DESC": "Solicitar al dispositivo remoto que envíe una confirmación de la capa de enlace de datos de la última trama enviada.  Tenga en cuenta que esta configuración es independiente de si el dispositivo remoto requerirá que este dispositivo envíe una confirmación de enlace de datos a las tramas que recibe. Este parámetro solo se utiliza para sesiones maestras o esclavas que utilizan el protocolo DNP3. ", "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES": "K - Número máximo de tramas de transmisión no reconocidas", "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES_DESC": "Número máximo de tramas de transmisión no reconocidas", "TR_LINK_LAYER": "capa de enlace", "TR_LINK_MAX_RETRIES": "Número máximo de intentos de retransmisión de datos", "TR_LINK_MAX_RETRIES_DESC": "Número máximo de intentos de retransmitir tramas de capa de enlace de datos que no se confirmaron.  Este parámetro no se aplica a las conexiones de capa de enlace (sesiones) cuando GATEWAY está actuando como un esclavo desequilibrado. ", "TR_LINK_MODE": "Modo de transmisión de enlace de datos", "TR_LINK_MODE_DESC": "Modo de transmisión de enlace de datos. Requerido para cada canal de comunicaciones ", "TR_LINK_SEND_ACK_DELAY": "T2 - Tiempo máximo de espera para enviar una trama de Reconocimiento (ms)", "TR_LINK_SEND_ACK_DELAY_DESC": "Tiempo máximo de espera para enviar un marco de Reconocimiento.  Para una sesión IEC 60870-5-104, este es el parámetro T2 de la cláusula 5.1 de IEC 60870-5-104. (La cantidad máxima de tiempo después de recibir la última APDU I-FORMAT antes de transmitir una APDU S-FORMAT). Este parámetro no se aplica a las conexiones de capa de enlace (sesiones) cuando GATEWAY está actuando como un esclavo desequilibrado. ", "TR_LINK_SIZE_ADDRESS": "Tamaño de la dirección del enlace (bytes)", "TR_LINK_SIZE_ADDRESS_0_NOT_ALLOW_IN_UNBALANCED_LINK": "La dirección de tamaño de enlace 0 no está permitida en un enlace no equilibrado", "TR_LINK_SIZE_ADDRESS_DESC": "Número de octetos (bytes) en el campo Dirección del enlace. Tenga en cuenta que un valor de 0 solo es válido para sesiones cuyo modo de enlace está equilibrado. Este parámetro solo se usa para sesiones maestras y esclavas IEC60870-5-101 ", "TR_LINK_TEST_FRAME_INTERVAL": "T3 - Intervalo de marco de prueba (ms)", "TR_LINK_TEST_FRAME_INTERVAL_DESC": "Tiempo para el intervalo del marco de prueba. Para una sesión IEC 60870-5-104, este es el parámetro T3. Se recomienda que T3 sea mayor que T1. Este parámetro no se aplica a las conexiones de capa de enlace (sesiones) cuando GATEWAY está actuando como un esclavo desequilibrado. ", "TR_LINK_WRECIEVED_UN_ACK_FRAMES": "W - Número máximo de tramas de recepción no reconocidas", "TR_LINK_WRECIEVED_UN_ACK_FRAMES_DESC": "Número máximo de tramas de recepción no reconocidas", "TR_LN_DESTINATION": "Nodo lógico para crear un conjunto de datos", "TR_LN_DESTINATION_DESC": "Elija el nodo lógico para crear el conjunto de datos", "TR_LOAD_CONFIG_FROM_SERVER": "Cargar configuración del servidor IED / SCL", "TR_LOAD_CONFIG_FROM_SERVER_DESC": "Cargar configuración desde el servidor IED / SCL", "TR_LOAD_MODEL": "<PERSON>o de carga", "TR_LOAD_MODEL_DESC": "Cargar modelo del archivo seleccionado", "TR_LOCAL_UDP_PORT": "Puerto UDP local", "TR_LOCK_SCROLL": "Bloquear desplazamiento", "TR_LOG": "Registro", "TR_LOG_MASKS_EVENT_LOG": "Registro de eventos de máscaras de registro", "TR_LOG_PARAMETERS": "Parámetros de registro", "TR_LOGIN": "In<PERSON><PERSON>", "TR_LOGIN_BUTTON": "In<PERSON><PERSON>", "TR_LOGOFF": "<PERSON><PERSON><PERSON>", "TR_LOGS": "Registros", "TR_LOW_ORDER_INDEX": "Índice de registro de pedido bajo", "TR_LOW_ORDER_INDEX_DESC": "Especifique el índice de registro de bajo orden", "TR_M103APPL_BLOCKING_ACTION_MASK": "Máscara de acción de bloqueo", "TR_M103APPL_BLOCKING_ACTION_MASK_DESC": "Cada bit habilita (1) o deshabilita (0) una solicitud automática que se enviará como resultado de que un dispositivo esclavo abandone el modo de bloqueo. Este parámetro solo se utiliza para sesiones maestras IEC60870-5-103.", "TR_M103APPL_EOI_ACTION_MASK": "Máscara de acción EOI", "TR_M103APPL_EOI_ACTION_MASK_DESC": "Cada bit habilita (1) o deshabilita (0) una solicitud automática que se enviará como resultado de recibir un mensaje de inicialización de un dispositivo esclavo. Este parámetro solo se utiliza para sesiones maestras IEC60870-5-103.", "TR_M103APPL_ONLINE_ACTION_MASK": "Máscara de acción en línea", "TR_M103APPL_ONLINE_ACTION_MASK_DESC": "Cada bit habilita (1) o deshabilita (0) una solicitud automática que se enviará como resultado de la conexión de un dispositivo esclavo. Este parámetro solo se utiliza para sesiones maestras IEC60870-5-103.", "TR_M14APPL_EOI_ACTION_MASK": "Máscara de acción EOI", "TR_M14APPL_EOI_ACTION_MASK_DESC": "Cada bit habilita (1) o deshabilita (0) una solicitud automática que se enviará como resultado de recibir un mensaje de inicialización de un dispositivo esclavo. Este parámetro solo se utiliza para IEC60870-5 101,104 sesiones maestras.", "TR_M14APPL_ONLINE_ACTION_MASK": "Máscara de acción en línea", "TR_M14APPL_ONLINE_ACTION_MASK_DESC": "Cada bit habilita (1) o deshabilita (0) una solicitud automática que se enviará como resultado de la conexión de un dispositivo esclavo. Este parámetro solo se utiliza para IEC60870-5 101,104 sesiones maestras.", "TR_MAIN_PARAMETERS": "Parámetros principales", "TR_MAINTENANCE_PLAN_EXPIRES": "El plan de mantenimiento caduca", "TR_MANAGE_CONFIGURATION FILE": "Administrar archivos de configuración", "TR_MAP_FAILED": "Hubo problemas al cargar el archivo de mapa de puntos (.csv).  Corrija los problemas manualmente abriendo el archivo CSV con un editor de texto y solucionando los problemas. La información del error se encuentra en la pantalla del analizador de protocolos ", "TR_MAPPING": "Asignación", "TR_MASTER_DATA_OBJECT": "Objeto de datos maestros", "TR_MAX_NUM_THREADS": "Número máxi<PERSON>ard", "TR_MAX_NUM_UNACK_FRAMES": "Número máximo de tramas no reconocidas", "TR_MAX_SOEQSIZE": "Recuento máximo de cola SOE", "TR_MAX_SOEQSIZE_DESC": "El número máximo de elementos permitidos en la Cola SOE (y Archivo). Si se supera este límite, se eliminan los elementos más antiguos. Cualquier valor especificado inferior a 1000 tendrá como valor predeterminado 1000", "TR_MBACTION_PRD0": "Período de acción de sesión Modbus (ms)", "TR_MBACTION_PRD0_DESC": "Tiempo entre acciones definidas en MBActionMask. El período se deshabilita si se establece en cero.Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo Modbus.", "TR_MBCHANNEL_ACTION_MASK0": "Máscara de acción del canal Modbus", "TR_MBCHANNEL_ACTION_MASK0_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con MBChannelActionPrd. \nMB Definiciones de máscara de acción: \nEste parámetro solo se usa para sesiones maestras que usan el protocolo Modbus.", "TR_MBCHANNEL_ACTION_NOW": "Máscara de acción del canal Modbus ahora", "TR_MBCHANNEL_ACTION_NOW_DESC": "Use esta máscara para forzar eventos únicos o eventos periódicos junto con MBChannelActionPrd. \nMB Definiciones de máscara de acción: \nVea la sección 4.2 'Nombres de etiquetas predefinidos para monitoreo y control' en el Manual. Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo Modbus.", "TR_MBCHANNEL_ACTION_PRD0": "Periodo de acción del canal Modbus (ms)", "TR_MBCHANNEL_ACTION_PRD0_DESC": "Tiempo entre acciones definidas en MBChannelActionMask. El período se deshabilita si se establece en cero.Este parámetro solo se utiliza para sesiones maestras que utilizan el protocolo Modbus. ", "TR_MDO": "MDO:", "TR_MDO_ALREADY_EXITS": "MDO ya está definido, no se puede crear", "TR_MDO_CREATE_ALREADY": "MDO ya está definido, no se puede crear", "TR_MDO_DESC": "MDO", "TR_MDO_EDITOR_CREATE_INVALID_TAG": "No se pudo crear MDO: {{arg1}}. Puede ser un nombre de etiqueta no válido o un tipo no válido ", "TR_MDO_EDITOR_CREATE_MDO_FOUND": "MDO no encontrado, no se puede crear", "TR_MDO_EDITOR_CREATE_MDO_USER_TAG": "No se pudo establecer el nombre de la etiqueta de usuario MDO {{arg1}} (posiblemente un duplicado, ver registros)", "TR_MDO_EDITOR_CREATE_SDO": "No se pudo crear SDO", "TR_MDO_EDITOR_CREATE_TAG": "Error al establecer el nombre de la etiqueta de usuario en '{{arg1}}' (posiblemente un duplicado)", "TR_MDO_EDITOR_CREATE_TAG_BAD": "No se pudo crear {{arg1}} MDO. Puede ser un nombre de etiqueta no válido o un tipo no válido", "TR_MDO_EDITOR_MDO_SET_OPTIONS": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_MDO_EDITOR_OPTIONS": "No se pudieron establecer las opciones de SDO {{arg1}}", "TR_MDO_EDITOR_OPTIONS_SDO": "No se pudieron establecer las opciones de SDO {{arg1}}", "TR_MDO_EDITOR_SDO_BIND": "No se pudo vincular SDO", "TR_MDO_EDITOR_SDO_DEFINED": "SDO ya definido, no se puede crear", "TR_MDO_EDITOR_SET_OPTIONS": "No se pudieron establecer las opciones de MDO '{{arg1}}'", "TR_MDO_LIST": "MDO", "TR_MDO_LIST_DESC": "Lista de MDO", "TR_MDO_OPTIONS": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_MEMORY_ABBREVIATION": "<PERSON><PERSON>", "TR_MENU": "Menú", "TR_MENU_CMD_ADD_61400_ALARM_MDO": "Agregar 61400 MDO de alarma", "TR_MENU_CMD_ADD_61400_ALARMS_NODE": "Agregar nodo de alarma IEC 61400", "TR_MENU_CMD_ADD_61850_CLIENT": "Agregar cliente IEC 61850", "TR_MENU_CMD_ADD_61850_COMMAND_POINT": "Agregar punto de control IEC 61850", "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING": "Agregar control 61850 al mapeo OPC", "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM": "Agregar control 61850 al elemento de mapeo OPC", "TR_MENU_CMD_ADD_61850_GOOSE": "Agregar bloque de control de ganso IEC 61850", "TR_MENU_CMD_ADD_61850_ITEM": "Agregar artículo 61850", "TR_MENU_CMD_ADD_61850_POLLED_DATA_SET": "Agregar conjunto de datos sondeados IEC 61850", "TR_MENU_CMD_ADD_61850_POLLED_POINT_SET": "Agregar conjunto de puntos sondeados IEC 61850", "TR_MENU_CMD_ADD_61850_REPORT": "Agregar bloque de control de informe IEC 61850", "TR_MENU_CMD_ADD_61850_SERVER": "Agregar servidor IEC 61850", "TR_MENU_CMD_ADD_61850_WRITABLE_POINT": "Agregar punto de escritura IEC 61850", "TR_MENU_CMD_ADD_DATA_TYPE": "Agregar tipo de datos", "TR_MENU_CMD_ADD_DATASET_ELEMENT": "Agregar elemento de conjunto de datos", "TR_MENU_CMD_ADD_DNP_DESCP": "Agregar un descriptor DNP", "TR_MENU_CMD_ADD_DNP_PROTO": "Agregar protocolo DNP", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL": "Agregar canal DNP3 UDP / TCP", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER": "Agregar DNP3 UDP / TCP Channel Master", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE": "Agregar esclavo de salida de canal UDP / TCP DNP3", "TR_MENU_CMD_ADD_DNP3_XML_DEVICE": "Agregar dispositivo DNP3 XML", "TR_MENU_CMD_ADD_EQ_MDO": "Agregar ecuación MDO", "TR_MENU_CMD_ADD_GOOSE_MONITOR": "Agregar Goose Monitor", "TR_MENU_CMD_ADD_INTERNAL_MDO": "Agregar MDO interno", "TR_MENU_CMD_ADD_MAPPING_SDO": "Agregar SDO", "TR_MENU_CMD_ADD_MAPPING_SDOS": "Agregar SDOS", "TR_MENU_CMD_ADD_MBP_CHANNEL": "Agregar canal ModbusPlus", "TR_MENU_CMD_ADD_MDO": "Agregar MDO", "TR_MENU_CMD_ADD_MODEM": "Agregar módem", "TR_MENU_CMD_ADD_MODEM_POOL": "Agregar grupo de módems", "TR_MENU_CMD_ADD_MODEM_POOL_CHANNEL": "Agregar canal de grupo de módem", "TR_MENU_CMD_ADD_MULTI_POINT": "Agregar Mulipoint", "TR_MENU_CMD_ADD_ODBC_CLIENT": "Agregar cliente ODBC", "TR_MENU_CMD_ADD_ODBC_ITEM": "Agregar elemento ODBC", "TR_MENU_CMD_ADD_OPC_AE_ATTR": "Agregar atributo OPC AE", "TR_MENU_CMD_ADD_OPC_AE_CLIENT": "Agregar cliente OPC AE", "TR_MENU_CMD_ADD_OPC_AE_ITEM": "Agregar elemento OPC AE", "TR_MENU_CMD_ADD_OPC_CLIENT": "Agregar cliente OPC", "TR_MENU_CMD_ADD_OPC_ITEM": "AGREGAR Elemento OPC", "TR_MENU_CMD_ADD_OPC_UA_CLIENT": "Agregar cliente OPC UA", "TR_MENU_CMD_ADD_REDUNDANT_CHANNEL": "Agregar canal redundante", "TR_MENU_CMD_ADD_SECTOR": "Agregar sector", "TR_MENU_CMD_ADD_SERIAL_CHANNEL": "Agregar canal serie", "TR_MENU_CMD_ADD_SERIAL_CHANNEL_MASTER": "Agregar maestro de canal serie", "TR_MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE": "Agregar esclavo de salida de canal serie", "TR_MENU_CMD_ADD_SESSION": "Agregar sesi<PERSON>", "TR_MENU_CMD_ADD_TASE2": "Agregar <PERSON>liente / Servidor ICCP", "TR_MENU_CMD_ADD_TASE2_COMMAND_POINT": "AGREGAR el punto de comando ICCP", "TR_MENU_CMD_ADD_TASE2_DSTS": "AGREGAR DSTS ICCP", "TR_MENU_CMD_ADD_TASE2_ITEM": "AGREGAR Artículo ICCP", "TR_MENU_CMD_ADD_TASE2_LOGICAL_DEVICE": "Agregar dispositivo lógico ICCP", "TR_MENU_CMD_ADD_TASE2_POLLED_DATA_SET": "AGREGAR el conjunto de datos sondeados de ICCP", "TR_MENU_CMD_ADD_TASE2_POLLED_POINT_SET": "AGREGAR conjunto de puntos sondeados de ICCP", "TR_MENU_CMD_ADD_TCP_CHANNEL": "Agregar canal TCP", "TR_MENU_CMD_ADD_TCP_CHANNEL_MASTER": "Agregar maestro de canal TCP", "TR_MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE": "Agregar esclavo de salida de canal TCP", "TR_MENU_CMD_ADD_WRITE_ACTION": "Agregar acción de escritura", "TR_MENU_CMD_AUTO_CREATE_TAGS": "Crear etiquetas automáticamente", "TR_MENU_CMD_CHANGE_VALUE": "Cambiar valor", "TR_MENU_CMD_CONNECT_OPC_AE_SERVER": "Conectar el servidor OPC AE", "TR_MENU_CMD_CONNECT_OPC_SERVER": "Conectar servidor OPC", "TR_MENU_CMD_CONNECT_OPC_UA_SERVER": "Conectar el servidor OPC UA", "TR_MENU_CMD_CONNECT_TO_61850_SERVER": "Conéctese a un servidor 61850", "TR_MENU_CMD_CREATE_DTM_CSV_POINT_FILE": "Crear archivo de puntos DTM CSV", "TR_MENU_CMD_CREATE_THXML_POINT_FILE": "Crear archivo de puntos THXML", "TR_MENU_CMD_DELETE": "Eliminar", "TR_MENU_CMD_DELETE_REDUNDANT_CHANNEL": "Eliminar canal redundante", "TR_MENU_CMD_DISCONNECT_OPC_AE_SERVER": "Desconectar el servidor OPC AE", "TR_MENU_CMD_DISCONNECT_OPC_SERVER": "Desconectar el servidor OPC", "TR_MENU_CMD_DISCONNECT_OPC_UA_SERVER": "Desconectar el servidor OPC UA", "TR_MENU_CMD_EDIT": "<PERSON><PERSON>", "TR_MENU_CMD_NONE": "N / A", "TR_MENU_CMD_PERFORM_WRITE_ACTION": "Realizar acción de escritura", "TR_MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT": "Restablecer recuento de reintentos de conexión", "TR_MENU_CMD_RESTART_61850_SERVER": "Reiniciar el servidor 61850", "TR_MENU_CMD_SAVE_MODEL_TO_FILE": "Guardar modelo en archivo ICD", "TR_MENU_CMD_SUBSCRIBE_GOOSE_STREAM": "Suscribir GOOSE Stream", "TR_MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM": "Anular suscripción de GOOSE Stream", "TR_MESSAGE": "Men<PERSON><PERSON>", "TR_MMS": "MMS", "TR_MMS_COMMON_NAME": "Nombre común", "TR_MMS_PRIVATE_KEY_FILE": "Archivo de clave privada", "TR_MMS_PRIVATE_KEY_PASS": "Frase de contraseña de clave privada", "TR_MMS_PUB_CERT_FILE": "Archivo de certificado público", "TR_MODBUS_ACTION_COILS": "<PERSON><PERSON> bob<PERSON>", "TR_MODBUS_ACTION_COILS_DESC": "<PERSON><PERSON> bob<PERSON>", "TR_MODBUS_ACTION_DISCRETE_INPUTS": "Entradas discretas", "TR_MODBUS_ACTION_DISCRETE_INPUTS_DESC": "Entradas discretas", "TR_MODBUS_ACTION_EXCEPTION_STATUS": "Exception_Status", "TR_MODBUS_ACTION_EXCEPTION_STATUS_DESC": "Exception_Status", "TR_MODBUS_ACTION_HOLDING_REGISTERS": "Registros de retención", "TR_MODBUS_ACTION_HOLDING_REGISTERS_DESC": "Registros de retención", "TR_MODBUS_ACTION_INPUT_REGISTERS": "Registros de entrada", "TR_MODBUS_ACTION_INPUT_REGISTERS_DESC": "Registros de entrada", "TR_MODBUS_ACTION_PRD": "Periodo (ms)", "TR_MODBUS_ACTION_PRD_DESC": "Periodo (ms)", "TR_MODBUS_HIGH_INDEX": "Error: el índice de orden superior especificado no existe. El Holding Register MDO debe existir", "TR_MODBUS_LOW_INDEX": "Error: el índice de orden inferior especificado no existe. El Holding Register MDO debe existir", "TR_MODBUS_MAP_EQUATION": "MDO: '{{arg1}}' se asigna a puntos esclavos o se usa en una ecuación, no se puede eliminar. Puede eliminar la dependencia e intentarlo de nuevo ", "TR_MODBUS_REG_EXISTS": "Error: ya existe un registro dual con ese nombre. Proporcione un nombre único ", "TR_MODBUS_SAME_INDEX": "Error: los índices de orden superior e inferior no pueden ser iguales", "TR_MODEL_FILE_NOT_FOUND": "Archivo de modelo no encontrado: '{{arg1}}'", "TR_MODEM_BAUD": "Velocidad en baudios", "TR_MODEM_BAUD_DESC": "Establece la velocidad en baudios para el módem correspondiente", "TR_MODEM_COM_PORT": "Puerto com módem", "TR_MODEM_COM_PORT_DESC": "Define un puerto com de módem", "TR_MODEM_DATA_BITS": "Bits de datos", "TR_MODEM_DATA_BITS_DESC": "Establece el número de bits de datos para el módem correspondiente", "TR_MODEM_DIALING_MODE": "Modo de marcación", "TR_MODEM_DIALING_MODE_DESC": "Establece el modo de marcación del módem: 'pulso' - use la marcación por pulso. 'Tono' - use la marcación por tono.", "TR_MODEM_ENABLE": "Habilitar módem", "TR_MODEM_ENABLE_DESC": "Si es verdadero, el módem estará habilitado y disponible para su uso", "TR_MODEM_HANGUP_STRING": "Cadena de colgar", "TR_MODEM_HANGUP_STRING_DESC": "Establece la cadena de colgar para el módem", "TR_MODEM_INIT_STRING": "Cadena de inicialización", "TR_MODEM_INIT_STRING_DESC": "Establece la cadena de inicialización para el módem", "TR_MODEM_NAME": "Nombre del alias del módem", "TR_MODEM_NAME_DESC": "Define un nombre de alias de m<PERSON><PERSON>m", "TR_MODEM_NO_DIAL_OUT": "Recibiendo solo llamadas telefónicas", "TR_MODEM_NO_DIAL_OUT_DESC": "Si es verdadero, entonces el módem se configurará para recibir solo llamadas telefónicas", "TR_MODEM_PARITY": "Paridad", "TR_MODEM_PARITY_DESC": "Establece la paridad para el canal de módem de módem correspondiente", "TR_MODEM_POOL_NAME": "Nombre del grupo de módems", "TR_MODEM_POOL_NAME_DESC": "Define un grupo de módems que contiene módems para un canal", "TR_MODEM_READ_CMD_TIMEOUT": "Tiempo de espera de lectura de comando (segundos)", "TR_MODEM_READ_CMD_TIMEOUT_DESC": "Especifica el tiempo de espera en segundos que el SDG espera a que un módem responda a un comando", "TR_MODEM_RESP_TERMINATOR_CHAR": "Carácter de terminación de respuesta", "TR_MODEM_RESP_TERMINATOR_CHAR_DESC": "Establece el carácter utilizado para terminar una respuesta desde el Módem: 'ninguno' - no use un carácter para terminar la respuesta (se supone que no hay respuestas de comando). 'Avance de línea' - use un carácter de 'avance de línea' para terminar la respuesta. 'retorno de carro' - use un carácter 'retorno de carro' para terminar la respuesta. ", "TR_MODEM_SERIAL_MODE": "Modo serie", "TR_MODEM_SERIAL_MODE_DESC": "Establece el modo serie del módem: 'hardware' - use control de flujo de hardware ('ninguno' y 'windows' no funcionan para módems).", "TR_MODEM_SETUP": "Configuración del módem", "TR_MODEM_STOP_BITS": "Detener bits", "TR_MODEM_STOP_BITS_DESC": "Establece el número de bits de parada para el módem correspondiente", "TR_MODEM_WRITE_CMD_TIMEOUT": "Tiempo de espera de escritura de comando (segundos)", "TR_MODEM_WRITE_CMD_TIMEOUT_DESC": "Especifica el tiempo de espera en segundos que el SDG espera para enviar un comando a un módem", "TR_MONITOR": "Monitor", "TR_MONITOR_ABBREVIATION": "M:", "TR_MONITOR_ENABLE_TRACE": "Monitorizar rastreo de habilitación", "TR_MONITOR_HOST_AND_HTTP_PORT": "Supervisar host y puerto (s) HTTP", "TR_MONITOR_IS_RE_STARTING": "El monitor se está reiniciando", "TR_MONITOR_LOG": "Monitorizar registro", "TR_MONITOR_MANAGEMENT": "Supervisar administración", "TR_MONITOR_VIEW_1": "Monitor View # 1", "TR_MONITOR_VIEW_2": "Vista de monitor # 2", "TR_MONITOR_VIEW_3": "Monitor View # 3", "TR_NAME": "Nombre", "TR_NAME_DESC": "Especifica el nombre actual", "TR_NATIVE_DATA_TYPE": "Tipo de datos nativo", "TR_NATIVE_DATA_TYPE_DESC": "Definir el tipo de datos nativo", "TR_NEW_GATEWAY": "Nueva configuración de puerta de enlace", "TR_NEW_PASSWORD": "Nueva contraseña", "TR_NEW_VALUE": "Nuevo valor", "TR_NO": "No", "TR_NO_EDITOR": "No hay editor disponible para cambiar el valor", "TR_NO_FILE_TO_DOWNLOAD": "No hay archivo para descargar", "TR_NO_GOOSE": "No hay más GOOSE IEC 61850 disponibles", "TR_NO_LICENSE_INFORMATION_SAVED": "No se guardó información de licencia", "TR_NO_POLLED": "No más IEC 61850 PolledDataSets disponibles", "TR_NO_RESULTS": "Sin resultados", "TR_NO_SECTORS": "No hay más sectores disponibles", "TR_NO_SESSIONS": "No hay más sesiones disponibles", "TR_NODE_LIST": "Lista de nodos", "TR_NODE_LIST_DESC": "<PERSON>ja nodos para agregar al nuevo conjunto de datos", "TR_NODE_LIST_SDO_DESC": "Elija nodos para agregar al nuevo SDO", "TR_NODE_NAME": "Nombre de nodo", "TR_NODE_NAME_DESC": "Definir el nombre del nodo", "TR_NODE_SDO_LIST": "Lista de nodos", "TR_NONE": "NINGUNO", "TR_NONE_DESC": "Eliminar selecci<PERSON>", "TR_NOT_TOPICAL": "NOT_TOPICAL", "TR_NOT_TOPICAL_DESC": "No tópico (fuera de línea / sin fecha)", "TR_NUM_THREADS": "<PERSON><PERSON><PERSON><PERSON>", "TR_OBJECT_NODENAME_DELETED": "Objeto {{NodeName}} eliminado", "TR_OBJECT_TAGNAME_ALREADY_EXISTS": "El objeto {{tagName}} ya existe", "TR_OBJECT_TAGNAME_CAN_T_BE_DELETED": "El objeto {{tagName}} no se puede eliminar", "TR_OBJECT_TAGNAME_CAN_T_BE_EDITED": "El objeto {{tagName}} no se puede editar", "TR_OBJECT_TAGNAME_CAN_T_BE_OPERATED_ON": "El objeto {{tagName}} no se puede operar", "TR_OBJECT_TAGNAME_DELETED": "Objeto {{tagName}} eliminado", "TR_ODBC_CLIENT_DUPLICATE": "No se puede agregar el cliente ODBC: '{{arg1}}'. Nombre duplicado ", "TR_ODBC_DELETE_EQUATION": "MDO: '{{}}' se asigna a otros puntos o se usa en una ecuación, no se puede eliminar", "TR_ODBC_EXECUTE_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ExecuteSql :: Excepción detectada: {{arg3}}", "TR_ODBC_EXECUTE_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, Severe ExecuteSql :: Excepción detectada", "TR_ODBC_FAILED_TO_FIND_QUERY": "Error al encontrar la consulta", "TR_ODBC_LIST_DSNS_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListDataSourceNames :: Excepción detectada: {{arg3}}", "TR_ODBC_LIST_DSNS_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, Grave ListDataSourceNames :: Excepción detectada", "TR_ODBC_LIST_TABLES_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListTables :: Excepción detectada: {{arg3}}", "TR_ODBC_LIST_TABLES_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, Tablas de listas severas :: Excepción detectada", "TR_ODBC_MAP": "MDO: '{{arg1}}' se asigna a otros puntos o se usa en una ecuación, no se puede eliminar", "TR_ODBC_MDO_DELETE": "Los MDO ODBC no se pueden eliminar", "TR_ODBC_NO_MORE": "No hay más clientes odbc disponibles", "TR_ODBC_NO_QUERIES": "No hay más consultas disponibles", "TR_ODBC_OPENDB_FAILED": "No se puede abrir la base de datos", "TR_ODBC_QUERY_CHANGE": "La consulta SQL ha cambiado; como resultado, las etiquetas de consulta (MDO) se pueden volver a crear y se pueden perder las asignaciones", "TR_ODBC_ROOT_ODBC_SERVER_CONNECT": "No se pudo conectar al servidor ODBC: OPCServerName [{{arg1}}] = '{{arg2}}' OPCServerProgID [{{arg3}}] = '{{arg4}}' OPCServerNode [{{arg5} }] = '{{arg6}}' ", "TR_ODBC_SHOW_TABLES_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ShowTable :: Excepción detectada: {{arg3}}", "TR_ODBC_SHOW_TABLES_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ShowTable grave :: Excepción detectada", "TR_ODBCALIAS_NAME": "Nombre", "TR_ODBCALIAS_NAME_DESC": "Nombre de alias para esta conexión ODBC", "TR_ODBCCONNECTION_STRING": "cadena de conexión ODBC", "TR_ODBCCONNECTION_STRING_DESC": "Especifica la cadena de conexión para esta conexión ODBC", "TR_ODBCQUERY": "consulta SQL", "TR_ODBCQUERY_ALIAS_NAME": "Nombre de consulta", "TR_ODBCQUERY_ALIAS_NAME_DESC": "El nombre de alias de la consulta en esta conexión ODBC", "TR_ODBCQUERY_ALWAYS_REFRESH_RS": "Actualizar siempre el conjunto de registros", "TR_ODBCQUERY_ALWAYS_REFRESH_RS_DESC": "La consulta siempre leerá los datos de la base de datos cuando GetNextRecord / MoveToRecord MDO se cambia en esta conexión ODBC cuando ODBCQueryAlwaysRefreshRS es verdadero. Si es falso, los datos se leen cuando ExecuteQuery mdo cambia pero no cuando GetNextRecord / MoveToRecord mdo cambia. Tenga en cuenta que si la tabla cambia en la base de datos, se debe emitir una ExecuteQuery para actualizar la memoria caché SDG local de la tabla / conjunto de registros ", "TR_ODBCQUERY_DESC": "La consulta SQL para ejecutar en esta conexión ODBC", "TR_OFFLINE_ACTIVATION": "Activación sin conexión", "TR_OFFLINE_ACTIVATION_NEW_LICENSE": "Activación sin conexión", "TR_OFFLINE_ACTIVATION_STEP1_TEXT": "Paso 1: Si esta computadora NO tiene acceso a Internet, haga clic en este botón para crear un archivo C2V que pueda usarse para activar la clave. Transfiera el archivo C2V a una computadora con acceso a Internet y busque: ", "TR_OFFLINE_ACTIVATION_STEP2_TEXT": "Paso 2: en el portal del cliente, inicie sesión con la clave del producto y luego seleccione la activación sin conexión. Suba el archivo C2V al portal. Haga clic en el botón Generar y luego descargue el archivo V2C. Se guardará un archivo V2C en la computadora conectada a Internet.  Transfiere ese archivo a esta computadora. Haga clic en el botón Instalar V2C y busque el archivo V2C. Consulte la Guía de licencias para obtener más detalles ", "TR_OK": "OK", "TR_OLD_VALUE": "Valor anterior", "TR_ONLINE_ACTIVATION": "Activación en línea (requiere acceso a Internet)", "TR_ONLINE_ACTIVATION_TEXT": "Si esta computadora tiene acceso a Internet, haga clic en este botón para activar la clave del producto en línea", "TR_ONLY_EXT_REF": "Mostrar solo ExtRef", "TR_ONLY_EXT_REF_DESC": "Especifica si solo se debe mostrar ExtRef.", "TR_OPC": "OPC", "TR_OPC_61850_SERVER_CONTROL_IS_IN_MIDDLE_OPERATION_AND_CANNOT_BE_DELETED_NOW": "El control SDO '{{arg1}}' está en medio de una operación y no se puede eliminar actualmente", "TR_OPC_ADD_CAUSE_POINT": "Agregar punto de causa", "TR_OPC_ADD_CAUSE_POINT_DESC": "Agregar punto de causa", "TR_OPC_AE_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Error al cargar el elemento de espacio de dirección OPC AE. Error desconocido.", "TR_OPC_AE_ITEM_LIST_UNAVAILABLE": "Lista de elementos no disponible", "TR_OPC_AE_SERVER_LIST_UNAVAILABLE": "Lista de servidores no disponible", "TR_OPC_AESUBSCRIPTION_MASK": "Máscara de suscripción AE de acceso a datos OPC", "TR_OPC_AESUBSCRIPTION_MASK_DESC": "Cada bit habilita (1) / inhabilita (0) una razón por la cual una suscripción de artículo a través del SDG OPC Data Access Server debería habilitar las notificaciones de alarma y evento de OPC para el artículo. Tenga en cuenta que esta máscara anula todas las demás máscaras si está habilitada. Si es 0, no se producirán notificaciones de eventos y alarmas de OPC como resultado de la suscripción de un artículo. ", "TR_OPC_CANCEL_REQUEST_POINT": "Cancelar punto de solicitud", "TR_OPC_CANCEL_REQUEST_POINT_DESC": "Definir el punto de solicitud de cancelación", "TR_OPC_CANCEL_RESPONSE_POINT": "Cancelar punto de respuesta", "TR_OPC_CANCEL_RESPONSE_POINT_DESC": "Definir el punto de respuesta de cancelación", "TR_OPC_CANT_ADD_MOD_SERVER_DUPLICATE": "No se pudo agregar {{arg1}} MDO en el servidor OPC: {{arg2}}. (¿Duplicado?)", "TR_OPC_CANT_ADD_SERVER_DUP": "No se pudo agregar {{arg1}} MDO en el servidor OPC: {{arg2}}. (¿Duplicado?)", "TR_OPC_CLIENT_CONNECT": "No se pudo conectar al servidor OPC: \n OPCServerName [{{arg1}}] = '{{arg2}}' \n OPCServerProgID [{{arg3}}] = '{{arg4}}' \n OPCServerNode [{{arg5}}] = '{{arg6}}' ", "TR_OPC_CLIENT_IS_EMPTY": "El cliente está vacío", "TR_OPC_CLIENT_IS_NOT_CONNECTED_CANNOT_SELECT_POINTS_UNTIL_CONNECTED.": "El Cliente OPC {{arg1}} no está conectado, no puede seleccionar puntos hasta que esté conectado", "TR_OPC_CLIENT_NAME": "Nombre del cliente", "TR_OPC_COMMAND_TERM_POINT": "Punto de término del comando", "TR_OPC_COMMAND_TERM_POINT_DESC": "Definir el punto de término del comando", "TR_OPC_DESC": "Registrar mensajes de rastreo para OPC", "TR_OPC_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Error al cargar el elemento de espacio de direcciones OPC. Error desconocido.", "TR_OPC_EXCEPTION_BROWSE_EVENT": "Se produjo una excepción grave en BrowseEventSpace", "TR_OPC_ITEM_LIST_UNAVAILABLE": "Lista de elementos no disponible", "TR_OPC_MDO_OPTIONS": "Opciones MDO", "TR_OPC_MDO_OPTIONS_DESC": "Definir las opciones de MDO", "TR_OPC_MDO_OPTIONS_SET": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_OPC_NO_MORE_CLIENTS": "No hay más clientes opc disponibles", "TR_OPC_OPERATE_REQUEST_POINT": "Operar punto de solicitud", "TR_OPC_OPERATE_REQUEST_POINT_DESC": "Definir el punto de solicitud de operación", "TR_OPC_OPERATE_RESPONSE_POINT": "Operar el punto de respuesta", "TR_OPC_OPERATE_RESPONSE_POINT_DESC": "Definir el punto de respuesta de operación", "TR_OPC_SELECT_REQUEST_POINT": "Seleccionar punto de solicitud", "TR_OPC_SELECT_REQUEST_POINT_DESC": "Definir el punto de solicitud de selección", "TR_OPC_SELECT_RESPONSE_POINT": "Seleccionar punto de respuesta", "TR_OPC_SELECT_RESPONSE_POINT_DESC": "Definir Seleccionar punto de respuesta", "TR_OPC_SERVER_LIST_UNAVAILABLE": "Lista de servidores no disponible", "TR_OPC_SERVER_PROG_ID_OR_XML_SERVER_PATH": "ID del programa del servidor o ruta del servidor XML", "TR_OPC_SERVER_PROG_ID_OR_XML_SERVER_PATH_DESC": "Definir la ID del programa del servidor o la ruta del servidor XML", "TR_OPC_SET_MDO_OPTIONS": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_OPC_STARTUP": "Inicio O<PERSON>", "TR_OPC_STARTUP_DESC": "Registrar mensajes de rastreo para el inicio de opc", "TR_OPC_STD_EXCEPTION_BROWSE_EVENT": "Se produjo una excepción en BrowseEventSpace: {{arg1}}", "TR_OPC_UA_CLIENT_CERTIFICATE_FILE": "Archivo de certificado", "TR_OPC_UA_CLIENT_CERTIFICATE_FILE_DESC": "Especifica el archivo de certificado UA Opc del cliente", "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES": "Descartar los cambios más antiguos", "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES_DESC": "Cuando se producen más cambios de datos de los que este elemento puede almacenar, se descartará el valor más antiguo", "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH": "Notificaciones máximas por publicación", "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH_DESC": "Número máximo de elementos informados, si es 0, el servidor informará tantos como sea posible", "TR_OPC_UA_CLIENT_NAME": "Nombre de este cliente OPC UA", "TR_OPC_UA_CLIENT_NAME_DESC": "Especifica el nombre de este cliente OPC UA. Los nombres deben ser únicos. Si no se especifica, el valor predeterminado será OpcUaClient-'index '", "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE": "Archivo de clave privada", "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE_DESC": "Especifica el archivo de clave privada de Opc UA del cliente", "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE": "Frase de clave privada", "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE_DESC": "Especifica la frase de paso de clave privada de Opc UA del cliente", "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE": "Tamaño de cola de publicación", "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE_DESC": "El número de cambios que se pueden almacenar para ese elemento en el servidor hasta el próximo ciclo de publicación", "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL": "Intervalo de publicación (ms)", "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL_DESC": "Hora en que el servidor enviará las modificaciones", "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT": "Reintentar recuento", "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT_DESC": "Especifica el recuento de reconexión de reintento para el Cliente OPC UA (0 = intento de reconexión para siempre) Al configurar el MDO de actualización del cliente, el contador de límite interno se volverá a establecer en 0 resultados en intentos de conexión continua al OPC Servidor UA ", "TR_OPC_UA_CLIENT_RECONNECT_TIME": "Reconectar tiempo de espera (ms)", "TR_OPC_UA_CLIENT_RECONNECT_TIME_DESC": "Especifica el tiempo de espera de reconexión para el Cliente OPC UA (0 = sin reconexión)", "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL": "Intervalo de muestreo (ms)", "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL_DESC": "Intervalo en que el elemento almacena cambios, el valor predeterminado es 5 por segundo", "TR_OPC_UA_CLIENT_SECURITY_MODE": "<PERSON><PERSON> de seguridad", "TR_OPC_UA_CLIENT_SECURITY_MODE_DESC": "Selecciona el modo de seguridad para una conexión OPC UA Client", "TR_OPC_UA_CLIENT_SECURITY_POLICY": "Política de seguridad", "TR_OPC_UA_CLIENT_SECURITY_POLICY_DESC": "Selecciona la política de seguridad para una conexión OPC UA Client", "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE": "Tipo de <PERSON>", "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE_DESC": "Selecciona el tipo de token de seguridad para una conexión OPC UA Client", "TR_OPC_UA_CLIENT_SERVER_URL": "URL del servidor OPC UA al que conectarse", "TR_OPC_UA_CLIENT_SERVER_URL_DESC": "Especifica la URL del servidor OPC UA al que conectarse", "TR_OPC_UA_CLIENT_SESSION_TIMEOUT": "Tiempo de espera de sesión (ms)", "TR_OPC_UA_CLIENT_SESSION_TIMEOUT_DESC": "Tiempo hasta que caduque la sesión si no hay comunicación", "TR_OPC_UA_CONNECTION_SETTINGS": "Configuración de conexión OPC UA", "TR_OPC_UA_SECURITY_SETTINGS": "Configuración de seguridad de OPC UA", "TR_OPC_UA_SERVER_LIST_UNAVAILABLE": "Lista de servidores no disponible", "TR_OPCAE_CLIENT_CONNECT": "No se pudo conectar al servidor OPC AE: \n OPCServerName [{{arg1}}] = '{{arg2}}' \n OPCServerProgID [{{arg3}}] = '{{arg4}} '\n OPCServerNode [{{arg5}}] =' {{arg6}} '", "TR_OPCAE_CLIENT_DUPLICATE": "No se puede agregar el cliente OPC AE: '{{arg1}}'. Nombre duplicado ", "TR_OPCAE_NO_MORE": "No hay más clientes opc AE disponibles", "TR_OPCAESERVER_BUFFER_TIME": "Tiempo de almacenamiento intermedio", "TR_OPCAESERVER_BUFFER_TIME_DESC": "El tiempo de almacenamiento intermedio, especificado en milisegundos, indica el número de veces que las notificaciones de eventos pueden enviarse al objeto de suscripción. Este parámetro es el tiempo mínimo extendido entre dos notificaciones sucesivas de eventos. El valor 0 significa que todas las notificaciones de eventos se enviarán inmediatamente desde el servidor. Si el parámetro MaxSize es mayor que 0, indica al servidor que envíe notificaciones de eventos más rápido para mantener el tamaño del búfer dentro de MaxSizeSpecifica el tiempo de búfer para la suscripción al servidor OPC AE ", "TR_OPCAESERVER_MAX_SIZE": "<PERSON><PERSON><PERSON> m<PERSON>", "TR_OPCAESERVER_MAX_SIZE_DESC": "Este parámetro es el número máximo de eventos que se pueden especificar en una llamada. El valor 0 significa que no hay restricción para el número de eventos. Tenga en cuenta que si el valor MaxSize es mayor que 0, los eventos se pueden enviar más rápido desde el servidor que a través del parámetro BufferTime. Especifica el tamaño máximo para la suscripción al servidor OPC AE ", "TR_OPCAESERVER_NAME": "Nombre del servidor", "TR_OPCAESERVER_NAME_DESC": "Nombre opcional para que el servidor OPC AE se conecte, si no se especifica, utilice el valor de OPCAEserverProgID. Se recomienda encarecidamente que este parámetro se defina como un cliente OPC AE externo que no podrá buscar etiquetas en SDG Servidor OPC AE si el nombre del servidor OPC AE contiene uno o más caracteres de punto ('.'). Para solucionar este problema, defina este nombre de alias (sin puntos para el servidor externo OPC AE y haga referencia al servidor por su alias. ", "TR_OPCAESERVER_NODE": "Nombre de nodo del servidor OPC AE", "TR_OPCAESERVER_NODE_DESC": "Especifica el nombre de nodo del servidor OPC AE al que conectarse", "TR_OPCAESERVER_PROG_ID": "ID del programa", "TR_OPCAESERVER_PROG_ID_DESC": "Especifica el ID de PROG del servidor OPC AE al que conectarse", "TR_OPCAESERVER_RECONNECT_RETRY_COUNT": "Reconectar recuento de reintentos", "TR_OPCAESERVER_RECONNECT_RETRY_COUNT_DESC": "Especifica el recuento de reconexión de reintento para el servidor externo OPC AE (0 = intento de reconexión para siempre) Al configurar el MDO de actualización del cliente, el contador de límite interno se volverá a establecer en 0 resultados en intentos continuos de conexión al Servidor OPC AE ", "TR_OPCAESERVER_RECONNECT_TIME": "Reconectar tiempo de espera (ms)", "TR_OPCAESERVER_RECONNECT_TIME_DESC": "Especifica el tiempo de espera de reconexión para el servidor OPC AE (0 = sin reconexión)", "TR_OPCAETIME_SOURCE": "Fuente de tiempo del evento OPC", "TR_OPCAETIME_SOURCE_DESC": "Especifica la fuente de la etiqueta de tiempo para los puntos de datos de eventos y alarmas OPC. Los valores posibles son Update o Reported donde Update significa la hora, en relación con el reloj del sistema SDG, en el que el punto de datos se actualizó por última vez. Informado especifica la hora informada del evento que causó el cambio de los datos. El tiempo informado será relativo al reloj del sistema del dispositivo esclavo remoto, excepto en la inicialización donde se usa el reloj del sistema SDG hasta que se reciba el primer evento con tiempo. Es importante tener en cuenta que el sondeo de datos estáticos, o los eventos recibidos que no especifican un tiempo informado, pueden hacer que cambie el valor de un punto de datos específico sin que se genere un evento, por lo tanto, el tiempo del evento no cambiará.  Nota: este parámetro especifica cómo proporcionará el tiempo el servidor SDG OPC AE ", "TR_OPCAEUSE_SIMPLE_EVENTS": "Usar alarma OPC y eventos simples", "TR_OPCAEUSE_SIMPLE_EVENTS_DESC": "Si es verdadero, todos los eventos OPC AE se informan como eventos simples", "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE": "Velocidad de actualización del estado del cliente OPC (ms)", "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE_DESC": "El intervalo en el que un cliente SDG OPC solicita información de estado de su servidor.  Si el cliente no desea solicitar actualizaciones de estado, configúrelo en 0. ", "TR_OPCDA_CLIENT_DUPLICATE": "No se puede agregar el cliente OPC DA: '{{arg1}}'. Nombre duplicado ", "TR_OPCSERVER_NAME": "Nombre", "TR_OPCSERVER_NAME_DESC": "Nombre opcional para que el servidor OPC se conecte, si no se especifica, use el valor de OPCserverProgID. Se recomienda encarecidamente que este parámetro se defina como un cliente OPC externo que no puede buscar etiquetas en el servidor SDG OPC si el nombre del servidor OPC contiene uno o más caracteres de punto ('.'). Para solucionar este problema, defina este nombre de alias (sin puntos para el servidor OPC externo y haga referencia al servidor por su alias. ", "TR_OPCSERVER_NODE": "Nombre de nodo", "TR_OPCSERVER_NODE_DESC": "Especifica el nombre de nodo del servidor OPC para conectarse", "TR_OPCSERVER_READ_PROPERTIES_TIME": "Periodo de lectura de propiedades (ms)", "TR_OPCSERVER_READ_PROPERTIES_TIME_DESC": "Especifica el período en el que se leen las propiedades (0 = no leer)", "TR_OPCSERVER_RECONNECT_DELAY": "Demora de reconexión (ms)", "TR_OPCSERVER_RECONNECT_DELAY_DESC": "Si no es cero, esto especifica el tiempo (en ms) para esperar para restablecer el recuento de reintentos y continuar intentando volver a conectar. Si es cero, se ignora y los reintentos no continuarán más allá del valor de recuento de reintentos ", "TR_OPCSERVER_RECONNECT_RETRY_COUNT": "Reintentar recuento", "TR_OPCSERVER_RECONNECT_RETRY_COUNT_DESC": "Especifica el recuento de reconexión de reconexión para el Servidor OPC Externo (0 = intento de reconexión para siempre) Al configurar el MDO de actualización del cliente, el contador de límite interno se restablecerá a 0 como resultado en intentos continuos de conexión al OPC servidor.", "TR_OPCSERVER_RECONNECT_TIME": "Reconecte el tiempo de reintento (ms)", "TR_OPCSERVER_RECONNECT_TIME_DESC": "Especifica el tiempo de reintento de reconexión para que el cliente OPC intente reconectarse al servidor (0 = sin reconexión)", "TR_OPCSERVER_UPDATE_RATE": "Velocidad de actualización (ms)", "TR_OPCSERVER_UPDATE_RATE_DESC": "Especifica la velocidad de actualización para el servidor OPC", "TR_OPCTIME_SOURCE": "Fuente de tiempo OPC", "TR_OPCTIME_SOURCE_DESC": "Especifica el origen de la etiqueta de tiempo para los puntos de datos OPC. Los valores posibles son Update o Reported donde Update significa la hora, en relación con el reloj del sistema SDG, en el que el punto de datos se actualizó por última vez. Informado especifica la hora informada del evento más reciente que causó el cambio de los datos. El tiempo informado será relativo al reloj del sistema del dispositivo esclavo remoto, excepto en la inicialización donde se usa el reloj del sistema SDG hasta que se reciba el primer evento con tiempo. Es importante tener en cuenta que el sondeo de datos estáticos, o los eventos recibidos que no especifican un tiempo informado, pueden hacer que cambie el valor de un punto de datos específico sin que se genere un evento, por lo tanto, el tiempo del evento no cambiará.  Nota: este parámetro especifica cómo proporcionará el tiempo el servidor SDG OPC DA ", "TR_OPCUA_CLIENT_DUPLICATE": "No se puede agregar el cliente OPC UA: '{{arg1}}'. Nombre duplicado ", "TR_OPCUA_NO_MORE": "No hay más clientes OPC UA disponibles", "TR_OPCXML_DA_SERVER_NAME": "Nombre del servidor OPC Xml Da", "TR_OPCXML_DA_SERVER_NAME_DESC": "Especifica el nombre utilizado por un cliente OPC XML DA para conectarse al SDG.  Con el archivo INI predeterminado, un cliente local opc xml da puede conectarse al SDG de la siguiente manera: http: // localhost: 8081 / SDG ", "TR_OPCXML_DA_SERVER_PORT": "OPC Xml DA Server Port", "TR_OPCXML_DA_SERVER_PORT_DESC": "El puerto TCP / IP para el servidor OPC XML DA.  Con el archivo INI predeterminado, un cliente local opc xml da puede conectarse al SDG de la siguiente manera: http: // localhost: 8081 / SDG ", "TR_OPEN_FILE": "{{arg1}} ({{arg2}}) Grabar demasiado int (más de {{arg3}} bytes)", "TR_OPERATION_DESCRIPTION": "Especifique la operación", "TR_OPERATION_HELP": "Ayuda de operación", "TR_OPERATION_LIST": "Operaciones", "TR_OPERATION_LIST_DESC": "Lista de posibles operaciones", "TR_OPTION": "Opción", "TR_OPTIONAL_ITEMS_TO_INCUDE_IN_REPORT": "Elementos de la operación a incluir en el informe", "TR_OPTIONS": "Opciones", "TR_OPTIONS_EDITOR": "Editor de opciones", "TR_OVERALL": "General", "TR_OVERFLOW": "OVERFLOW", "TR_OVERFLOW_DESC": "Desbordamiento / Roll-over", "TR_PARAMETERS": "Parámetros", "TR_PARSE_OPTION_VALIDATION_FAILED": "Error al validar el nombre de categoría / condición / subcondición '{{arg1}}'", "TR_PARSE_OPTION_VALIDATION_FAILED2": "Error al validar el nombre de categoría / condición / subcondición '{{arg1}}'", "TR_PASSWORD": "Contraseña", "TR_PASSWORD_IS_REQUIRED": "Se requiere contraseña", "TR_PASSWORD_IS_REQUIRED_CHARACTERS_MINIMUM": "Se requiere contraseña (mínimo 6 caracteres)", "TR_PAUSE": "Pausa", "TR_PERFORMANCE_DESC": "Rendimiento", "TR_PERFORMANCE_VIEW": "Vista de rendimiento", "TR_PHYS_COM_BAUD": "Velocidad en baudios", "TR_PHYS_COM_BAUD_DESC": "Establece la velocidad en baudios para el PhysComChannel serial correspondiente", "TR_PHYS_COM_CHANNEL": "Canal físico, es decir, puerto COM, IP remota / Nombre de nodo", "TR_PHYS_COM_CHANNEL_DESC": "Establece el canal de comunicación. Ejemplos: 'COM1', '************' o 'ROCKYHILLSUBSTATION' (para un canal TCP / IP). En el puerto serie - este es el nombre del puerto com para usar En el cliente TCP - este es el nombre del host o la dirección IP para configurar la conexión TCP con En el servidor TCP - este es el nombre del host o la dirección IP para aceptar la conexión TCP. Tal vez *.*.*. * que indica aceptar conexión de cualquier cliente. También puede ser una lista de ';' nombres de host separados o direcciones IP para permitir conexiones. En el dispositivo de punto final dual TCP: estos son los nombres de host o la dirección IP para aceptar o conectarse a la conexión TCP. También puede ser una lista de ';' o ',' nombres de host separados o direcciones IP para permitir conexiones, solo intentará conectarse al primero de la lista. \norte", "TR_PHYS_COM_CHNL_NAME": "Nombre del canal", "TR_PHYS_COM_CHNL_NAME_DESC": "Nombre de alias para el canal de comunicaciones. Debe especificarse y ser único. ", "TR_PHYS_COM_DATA_BITS": "Bits de datos", "TR_PHYS_COM_DATA_BITS_DESC": "Establece el número de bits de datos para el PhysComChannel serial correspondiente", "TR_PHYS_COM_DEST_UDPPORT": "Puerto UDP de destino", "TR_PHYS_COM_DEST_UDPPORT_DESC": "En el maestro: si TCP y UDP están configurados, esto especifica el puerto UDP / IP de destino para enviar solicitudes de difusión en datagramas UDP. Si UDP SOLO está configurado, esto especifica el puerto UDP / IP de destino para enviar todas las solicitudes en datagramas UDP a. Esto debe coincidir con el 'localUDPPort' en el esclavo. En esclavo: si TCP y UDP no se utiliza. Si UDP SOLO está configurado, esto especifica el destino UDP / Puerto IP para enviar respuestas. Puede ser WINTCP_UDP_PORT_SRC = 2 indicando que use el puerto src desde una solicitud UDP recibida del maestro. ", "TR_PHYS_COM_DIAL_OUT": "Habilitar marcación de salida", "TR_PHYS_COM_DIAL_OUT_DESC": "Si es verdadero, entonces el módem se configurará para marcar y responder llamadas telefónicas. Si es falso, el módem se configurará para responder solo llamadas telefónicas. ", "TR_PHYS_COM_DUAL_END_POINT_IP_PORT": "Número de puerto IP de punto final dual", "TR_PHYS_COM_DUAL_END_POINT_IP_PORT_DESC": "Si se admite el punto final dual, se realizará una escucha en el PhysComIpPort and se enviará una solicitud de conexión a este número de puerto cuando sea necesario.  Esto debería coincidir con el IP Port en el dispositivo remoto. El estado normal es escuchar, conexión se realizará cuando haya datos para enviar ", "TR_PHYS_COM_INIT_UNSOL_UDPPORT": "Puerto UDP no solicitado", "TR_PHYS_COM_INIT_UNSOL_UDPPORT_DESC": "En maestro - No utilizado. En esclavo - si TCP y UDP no se utilizan. Si UDP SOLO está configurado, esto especifica el puerto UDP / IP de destino para enviar la respuesta nula no solicitada inicial a. Después de recibir una solicitud UDP del maestro, destUDPPort (que) puede indicar usar puerto src) se usará para todas las respuestas. Esto no debe ser WINTCP_UDP_PORT_NONE (0), WINTCP_UDP_PORT_ANY (1) o WINTCP_UDP_PORT_SRC (2) para un esclavo que admita UDP ", "TR_PHYS_COM_IP_CONNECT_TIMEOUT": "Tiempo de espera de conexión (ms)", "TR_PHYS_COM_IP_CONNECT_TIMEOUT_DESC": "Establece el tiempo de espera de conexión TCP / IP para usar si PhysComChannel especifica una dirección IP. Para IEC 60870-5-104, este es el parámetro T0. Tenga en cuenta que este parámetro debe establecerse en el valor más bajo que funcione de manera confiable. En los casos en que no se pueda establecer una conexión, el proceso se bloqueará durante el período especificado", "TR_PHYS_COM_IP_MODE": "Modo IP", "TR_PHYS_COM_IP_MODE_DESC": "Establece el modo de conexión a usar si PhysComChannel especifica una dirección IP", "TR_PHYS_COM_IP_PORT": "Número de puerto IP", "TR_PHYS_COM_IP_PORT_DESC": "Establece el número de puerto TCP / IP para usar si PhysComChannel especifica una dirección IP", "TR_PHYS_COM_LOCAL_IP_ADDRESS": "Dirección IP local", "TR_PHYS_COM_LOCAL_IP_ADDRESS_DESC": "En el cliente - Dirección para vincular el socket. Esto le permite especificar qué dirección IP enviar como dirección de origen en los mensajes TCP si hay varias direcciones IP, por ejemplo, cuando hay varias tarjetas de interfaz de red (NIC). Si se usa '0.0.0.0', la pila TCP elegirá qué dirección IP usar. Si se especifica una dirección que no está presente, el enlace fallará y la pila TCP elegirá qué dirección. Esta dirección también se usa para DNP Master cuando se envían datagramas UDP. La vinculación de esta dirección no garantiza el envío de una NIC en particular. Esto está determinado por la tabla de enrutamiento IP dependiendo del destino dirección IP. Puede mostrar esta tabla ingresando 'ruta de impresión' en una ventana de comandos. Es posible agregar rutas manuales para hacer que se use una NIC particular. 'ruta agregar destIPAddress gateway'. Ingresar 'ruta?' para obtener más detalles. En el esclavo: se debe elegir para que coincida con el puerto UDP que el maestro usa para enviar Datagram mensajes a. Esto no debe ser WINTCP_UDP_PORT_ANY = 1 o WINTCP_UDP_PORT_SRC = 2. ", "TR_PHYS_COM_MBPCARD_NUMBER": "Número de tarjeta del canal MBP", "TR_PHYS_COM_MBPCARD_NUMBER_DESC": "Establece el número de tarjeta Modbus Plus para usar si PhysComType especifica un canal Modbus Plus", "TR_PHYS_COM_MBPRECIEVE_TIMEOUT": "Tiempo de espera de recepción Modbus Plus", "TR_PHYS_COM_MBPRECIEVE_TIMEOUT_DESC": "Establece el tiempo de espera de recepción Modbus Plus para usar si PhysComType especifica un canal Modbus Plus", "TR_PHYS_COM_MBPROUTE_PATH": "Ruta de ruta Modbus Plus", "TR_PHYS_COM_MBPROUTE_PATH_DESC": "Especifica la ruta de ruta Modbus Plus a usar si PhysComType especifica un canal Modbus Plus. Este parámetro solo se aplica a modbus plus masters ", "TR_PHYS_COM_MBPSLAVE_PATH": "Modbus Plus Slave Path", "TR_PHYS_COM_MBPSLAVE_PATH_DESC": "Establece la ruta del esclavo Modbus Plus para usar si PhysComType especifica un canal Modbus Plus. Este parámetro solo se aplica a Modbus más esclavos", "TR_PHYS_COM_MODBUS_RTU": "Habilitar Modbus RTU", "TR_PHYS_COM_MODBUS_RTU_DESC": "Si es verdadero, entonces el canal se configurará para comunicaciones serie modbus RTU", "TR_PHYS_COM_MODEM_ANSWER_TIME": "Tiempo de espera de respuesta (segundos)", "TR_PHYS_COM_MODEM_ANSWER_TIME_DESC": "Establece la cantidad de tiempo que el módem espera una respuesta después de marcar (en segundos)", "TR_PHYS_COM_MODEM_IDLE_TIME": "Tiempo de espera para colgar después de inactivo (segundos)", "TR_PHYS_COM_MODEM_IDLE_TIME_DESC": "Establece la cantidad de tiempo que el módem espera para colgar después de que el canal esté inactivo (en segundos)", "TR_PHYS_COM_MODEM_POOL": "Grupo de módems", "TR_PHYS_COM_MODEM_POOL_DESC": "El grupo de módems que utiliza este canal", "TR_PHYS_COM_MODEM_REDIAL_LIMIT": "Tiempos de remarcación", "TR_PHYS_COM_MODEM_REDIAL_LIMIT_DESC": "Establece el número de veces que marcará el módem antes de concluir que la comunicación ha fallado. Si la marcación ha fallado (es decir, se ha alcanzado el límite), el canal del módem MDO ChannelRedialLimitControl será VERDADERO, configure este MDO (ChannelRedialLimitControl) en falso para comenzar a marcar nuevamente. Un valor de 0 hará que el módem intente marcar para siempre. Tenga en cuenta que establecer este valor en 0 inmovilizará el módem para siempre si nunca se establece una conexión. ", "TR_PHYS_COM_PARITY": "Paridad", "TR_PHYS_COM_PARITY_DESC": "Establece la paridad para el PhysComChannel serial correspondiente", "TR_PHYS_COM_PHONE_NUMBER": "Número de teléfono", "TR_PHYS_COM_PHONE_NUMBER_DESC": "Número de teléfono para marcar en un módem", "TR_PHYS_COM_PROTOCOL": "Tipo de protocolo de canal", "TR_PHYS_COM_PROTOCOL_DESC": "Establece el protocolo para el canal Se aplica a todos los tipos de canales físicos", "TR_PHYS_COM_SERIAL_MODE": "Control de flujo", "TR_PHYS_COM_SERIAL_MODE_DESC": "Establece el modo del canal en serie: 'ninguno' - no use control de flujo. 'Hardware' - use control de flujo por hardware. 'Windows' - use control de flujo y parámetros en serie (velocidad de transmisión, paridad, etc.) como se especifica con el comando MODE ", "TR_PHYS_COM_STOP_BITS": "Stop Bits", "TR_PHYS_COM_STOP_BITS_DESC": "Establece el número de bits de parada para el PhysComChannel serial correspondiente", "TR_PHYS_COM_VALIDATE_UDPADDRESS": "Validar la dirección UDP", "TR_PHYS_COM_VALIDATE_UDPADDRESS_DESC": "Si se debe validar o no la dirección de origen del datagrama UDP recibido", "TR_PHYS_OFFLINE_POLL_PERIOD": "Período de encuesta sin conexión (ms)", "TR_PHYS_OFFLINE_POLL_PERIOD_DESC": "El período en el que las sesiones en el canal se sondean si están fuera de línea.  Tenga en cuenta que este parámetro solo se aplica a las sesiones maestras serie DNP y Modbus en este canal. Un valor de cero (0) deshabilitará esta función. ", "TR_PLEASE_SELECT_A_FILE": "Seleccione un archivo", "TR_PLEASE_SELECT_A_PROPERTY": "Seleccione una propiedad", "TR_PLEASE_SELECT_AT_LEAST_ONE_ROLE": "Seleccione al menos una función", "TR_POINT_IN_USE_CANT_DELETE": "MDO: '{{arg1}}' se asigna a puntos esclavos o maestros o se usa en una ecuación, no se puede eliminar", "TR_POINT_MAP": "Mapa de puntos", "TR_POINT_MAP_FILE": "Archivo de mapa de puntos", "TR_POINT_MAP_FILE_DESC": "Especifica el archivo de asignación de datos de puntos.  Este valor puede tener la ruta completa al archivo o solo el nombre del archivo.  Si solo se especifica el nombre del archivo, debe ubicarse en el mismo directorio que el archivo INI. ", "TR_POINT_TYPE": "Tipo de punto", "TR_POINT_TYPE_DESC": "Tipo de punto", "TR_POLLED_DATA_SET_NAME": "Nombre del conjunto de datos encuestados", "TR_POLLED_DS_DS_ID": "Nombre del conjunto de datos", "TR_POLLED_DS_DS_ID_DESC": "Especifique el nombre del conjunto de datos", "TR_POLLED_DS_ID": "Nombre del conjunto de datos sondeados", "TR_POLLED_DS_ID_DESC": "Especifique el nombre del conjunto de datos sondeados", "TR_POLLED_DS_PERIOD": "<PERSON><PERSON><PERSON> sondeado", "TR_POLLED_DS_PERIOD_DESC": "Especifica el período sondeado", "TR_POLLED_DS_SET_PERIOD": "DS Transfer Set Count", "TR_POLLED_DS_SET_PERIOD_DESC": "Especifique el recuento de conjuntos de transferencia DS", "TR_POLLED_POINT_SET_DESC": "Especifique el período de la encuesta", "TR_POLLED_POINT_SET_ID": "Nombre del conjunto de puntos sondeados", "TR_POLLED_POINT_SET_ID_DESC": "Especifique el nombre del conjunto de puntos encuestados", "TR_POLLED_POINT_SET_PERIOD": "Período de encuesta", "TR_POLLED_POINT_SET_PERIOD_DESC": "Especifique el período establecido de puntos", "TR_PRODUCT_KEY_EXHAUSTED": "Clave de producto agotada", "TR_PRODUCT_KEY_FOR_NEW_LICENSE": "Esta clave de producto es para una nueva licencia", "TR_PRODUCT_KEY_FOR_UPDATE_LICENSE": "Esta clave de producto actualizará la licencia actual (es decir, la actualización del plan de mantenimiento)", "TR_PROG_ID": "ID del programa", "TR_PROPERTIES": "Propiedades", "TR_PROPERTY_DESC": "Definir la propiedad", "TR_PROTO_ANALYZER": "Analizador de protocolos", "TR_PROTO_ANALYZER_DESC": "Registrar mensajes de rastreo para el Analizador de protocolos", "TR_PROTOCOL_ANALYSER": "Analizador de protocolos", "TR_PROTOCOL_ANALYSER_PARAMETERS": "Parámetros del analizador de protocolos", "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT": "Purgar antes de habilitar al volver a conectar", "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Especifica si la purga antes de habilitar la reconexión está activa", "TR_QUAL_CHNG_MON": "Cambio de calidad", "TR_QUALITY": "Calidad", "TR_QUALITY_CHANGE": "Cambio de calidad", "TR_QUALITY_CHANGE_DESC": "Especifica si el cambio de calidad está activo", "TR_QUALITY_ENUM_BLOCKED_DESC": "Bloquear", "TR_QUALITY_ENUM_GOOD_DESC": "Bueno", "TR_QUALITY_ENUM_IN_TRANSIT_DESC": "en tránsito", "TR_QUALITY_ENUM_INVALID_DESC": "<PERSON>v<PERSON><PERSON><PERSON>", "TR_QUALITY_ENUM_INVALID_TIME_DESC": "Tiempo no válido", "TR_QUALITY_ENUM_NOT_TOPICAL_DESC": "No es tópico", "TR_QUALITY_ENUM_OVERFLOW_DESC": "Desbordamiento", "TR_QUALITY_ENUM_REF_ERROR_DESC": "<PERSON><PERSON><PERSON> de referencia", "TR_QUALITY_ENUM_SUBSTITUTED_DESC": "Sustituido", "TR_QUALITY_ENUM_TEST_DESC": "Prueba", "TR_QUALITY_ENUM_UNINITIALIZED_DESC": "Sin inicializar", "TR_QUERY_RESULT_DESC": "Especifica los resultados de la consulta", "TR_QUERY_RESULTS": "Resultados de la consulta", "TR_RCB_DATASET_NAME": "Nombre del conjunto de datos", "TR_RCB_DATASET_NAME_DESC": "Especifique el nombre del conjunto de datos", "TR_RCB_LIST": "Lista de informes", "TR_RCB_LIST_DESC": "Especifica la lista de informes actual", "TR_RCB_NAME": "Nombre del informe", "TR_RCB_NAME_DESC": "Especifique el nombre del informe", "TR_RE_START_MONITOR": "Reiniciar monitor", "TR_READ": "<PERSON><PERSON>", "TR_READ_CONFIG_FROM_SERVER": "Leer configuración del servidor", "TR_READ_CONFIG_FROM_SERVER_DESC": "Leer configuración del servidor", "TR_READ_DESC": "<PERSON><PERSON>", "TR_READ_GOOSE": "Advertencia: no se pudo leer el informe de ganso '{{arg1}}', error = {{arg2}}", "TR_READ_POINT_MAP_AT_STARTUP": "Leer mapa de puntos al inicio", "TR_READ_POINT_MAP_AT_STARTUP_DESC": "si es verdadero, el archivo de asignación de puntos se leerá al inicio", "TR_REASON_FOR_INCLUSION": "Motivo de inclusión", "TR_REASON_FOR_INCLUSION_DESC": "Especifica si el motivo de inclusión está activo", "TR_RECONNECT_RETRY_COUNT": "Reconectar recuento de reintentos", "TR_RECONNECT_TIME": "Tiempo de reconexión (ms)", "TR_RECONNECTION_SETTINGS": "Configuración de reconexión", "TR_REF_ERROR": "REF_ERROR", "TR_REF_ERROR_DESC": "<PERSON><PERSON><PERSON> de <PERSON> (ADC)", "TR_REFRESH": "REFRESH", "TR_REFRESH_AREA_SPACE": "Actualizar á<PERSON> es<PERSON>", "TR_REFRESH_AREA_SPACE_DESC": "Actualizar espacio del área", "TR_REFRESH_DESC": "La fuente de datos está actualizando los datos sin solicitud directa.  Ningún cambio se indica necesariamente ", "TR_REFRESH_ITEM_PARENT": "Actualizar elemento", "TR_REFRESH_ITEM_PARENT_DESC": "Actualizar elemento", "TR_REFRESH_LIST_DESC": "Actualizar lista", "TR_REFRESH_PROPERTIES": "Actualiza<PERSON> propiedades", "TR_REFRESH_PROPERTIES_DESC": "Actualiza<PERSON> propiedades", "TR_REFRESH_SERVER_LIST": "Actualizar lista de servidores", "TR_REGISTER": "Registrarse", "TR_REMEMBER_ME": "Recordarme", "TR_REMOTE_IP_NODE_NAME": "IP remota / Nombre de nodo", "TR_REMOVE_SUBSCRIPTION": "Eliminar suscripción", "TR_REMOVE_SUBSCRIPTION_DESC": "Eliminar suscripción", "TR_REPORT_BY_EXCEPTION": "Informe por excepción", "TR_REPORT_BY_EXCEPTION_DESC": "Especifica el informe actual por excepción", "TR_REPORT_NAME": "Nombre del informe", "TR_REPORT_NONE_LEFT": "No más informes IEC 61850 disponibles", "TR_REQUEST_DATA_TYPE": "Solicitar tipo de datos", "TR_REQUEST_DATA_TYPE_DESC": "Definir el tipo de datos de solicitud", "TR_REQUESTED": "SOLICITADO", "TR_REQUESTED_DESC": "Los datos se están actualizando porque se solicitaron", "TR_REQUIRES_RESTART": "Requiere reiniciar", "TR_RESUMED": "Reanudado", "TR_RETRY_ENABLE_COUNT": "Reintentar cuenta de habilitación (-1 = para siempre, 0 = nunca)", "TR_RETRY_ENABLE_COUNT_DESC": "Especifica el recuento de habilitación de reintento actual", "TR_RETRY_ENABLE_PERIOD": "Reintentar período de habilitación (ms)", "TR_RETRY_ENABLE_PERIOD_DESC": "Especifica el período de habilitación de reintento actual", "TR_RETRY_FAILED_TRANSACTION": "Reintentar transacción fallida", "TR_ROLE": "Role", "TR_RR_COILS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_COILS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_COILS_START_INDEX": "Índice de inicio", "TR_RR_COILS_START_INDEX_DESC": "Índice de inicio", "TR_RR_DISCRETE_INPUTS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_DISCRETE_INPUTS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_DISCRETE_INPUTS_START_INDEX": "Índice de inicio", "TR_RR_DISCRETE_INPUTS_START_INDEX_DESC": "Índice de inicio", "TR_RR_HOLDING_REGISTERS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_HOLDING_REGISTERS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_HOLDING_REGISTERS_START_INDEX": "Índice de inicio", "TR_RR_HOLDING_REGISTERS_START_INDEX_DESC": "Índice de inicio", "TR_RR_INPUT_REGISTERS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_INPUT_REGISTERS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_INPUT_REGISTERS_START_INDEX": "Índice de inicio", "TR_RR_INPUT_REGISTERS_START_INDEX_DESC": "Índice de inicio", "TR_RSA": "TLS RSA", "TR_RSA_PRIVATE_KEY_FILE": "Archivo de clave privada RSA", "TR_RSA_PRIVATE_KEY_PASS_PHRASE": "RSA Private PassPhrase", "TR_RSA_PUBLIC_CERT_FILE": "Archivo de certificado público RSA", "TR_RUN_SELECTED_INI_FILE": "Ejecutar el archivo INI seleccionado", "TR_RUNTIME_PARAMETERS": "Parámetros de tiempo de ejecución", "TR_SAVE": "Guardar", "TR_SAVE_GATEWAY": "Guardar INI / CSV actual", "TR_SAVE_INI / CSV": "Guardar INI / CSV actual", "TR_SAVE_UNMAPPED_POINTS": "Guardar puntos sin asignar", "TR_SAVE_UNMAPPED_POINTS_DESC": "si es verdadero, las etiquetas no asignadas se guardarán en el archivo de asignación de puntos", "TR_SBO": "SBO", "TR_SCL_CATEGORY _...FÍSICO ": " ...FÍSICO", "TR_SCL_CATEGORY_ ~~~ TRANSPORT": "~~~ TRANSPORT", "TR_SCL_CATEGORY _ +++ USER": "+++ USER", "TR_SCL_CATEGORY _ === APLICACIÓN": "=== APLICACIÓN", "TR_SCL_CATEGORY_CYCLIC_DATA": "DATOS CÍCLICOS", "TR_SCL_CATEGORY_CYCLIC_HDRS": "HDRS CÍCLICO", "TR_SCL_CATEGORY _--- DATA_LINK": "--- DATA_LINK", "TR_SCL_CATEGORY_EVENT_DATA": "DATOS DEL EVENTO", "TR_SCL_CATEGORY_EVENT_HDRS": "EVENT HDRS", "TR_SCL_CATEGORY_MMI": "MMI", "TR_SCL_CATEGORY_SECURITY_DATA": "DATOS DE SEGURIDAD", "TR_SCL_CATEGORY_SECURITY_HDRS": "HDUR DE SEGURIDAD", "TR_SCL_CATEGORY_STATIC_DATA": "DATOS ESTÁTICOS", "TR_SCL_CATEGORY_STATIC_HDRS": "HDRS ESTÁTICAS", "TR_SCL_CATEGORY_TARGET": "TARGET", "TR_SCL_DATABASE": "Base de datos", "TR_SCL_FILE_NAME": "Archivo SCL", "TR_SCL_FILTER": "Filtro SCL", "TR_SCL_PROTOCOL_LAYER": "Capa de protocolo", "TR_SDG_CATEGORY_61850": "61850", "TR_SDG_CATEGORY_870": "870", "TR_SDG_CATEGORY_DNP": "DNP", "TR_SDG_CATEGORY_EQUATION": "ECUACIÓN", "TR_SDG_CATEGORY_MODBUS": "MODBUS", "TR_SDG_CATEGORY_ODBC": "ODBC", "TR_SDG_CATEGORY_OPC": "OPC", "TR_SDG_CATEGORY_OPC_DEEP": "OPC DEEP", "TR_SDG_CATEGORY_OPC_SU": "OPC SU", "TR_SDG_CATEGORY_OPC_UA": "OPC UA", "TR_SDG_CATEGORY_TASE2": "ICCP", "TR_SDG_FILTER": "Filtro SDG", "TR_SDG_MMS": "MMS", "TR_SDG_OPC": "OPC", "TR_SDG_OTHER": "<PERSON><PERSON>", "TR_SDG_SCL": "SCL", "TR_SDO_DESC": "Nombre del SDO", "TR_SDO_NAME": "Nombre SDO", "TR_SDO_NAME_DESC": "El nombre del SDO", "TR_SDO_OPTIONS": "Opciones SDO", "TR_SDO_OPTIONS_DESC": "Las opciones del SDO", "TR_SEARCH": "Buscar", "TR_SEARCH_CRITERIA": "Criterios de búsqueda", "TR_SECTOR_DUPLICATE": "Error al agregar el sector. El sector en la dirección {{arg1}} ya existe ", "TR_SECURITY": "Seguridad", "TR_SECURITY_PARAMETERS": "Parámetros de seguridad", "TR_SECURITY_SETUP": "Configuración de seguridad", "TR_SELECT_ALL": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "TR_SELECT_LANGUAGE": "Cambiar idioma", "TR_SELECT_ONE": "Seleccione uno:", "TR_SEQUENCE_NUMBER": "Número de secuencia", "TR_SEQUENCE_NUMBER_DESC": "Especifica si el número de secuencia está activo", "TR_SERIAL_PORT_SETUP": "Configuración del puerto serie", "TR_SERVER": "<PERSON><PERSON><PERSON>", "TR_SERVER_AE_INVOKE_ID": "ID de invocación AE", "TR_SERVER_AE_QUALIFIER": "Calificador AE", "TR_SERVER_AP_INVOKE_ID": "ID de invocación AP", "TR_SERVER_APP_ID": "ID de la aplicación", "TR_SERVER_IP_ADDRESS": "Dirección IP del servidor", "TR_SERVER_IP_PORT": "Puerto TCP del servidor", "TR_SERVER_LIST": "Lista de servidores", "TR_SERVER_LIST_DESC": "Definir la lista de servidores", "TR_SERVER_NAME": "Nombre del servidor", "TR_SERVER_NAME_DESC": "Definir el nombre del servidor", "TR_SERVER_NODE": "Nodo del servidor", "TR_SERVER_NODE_DESC": "Definir el nodo del servidor", "TR_SERVER_PRESENTATION_ADDRESS": "Selector de presentación", "TR_SERVER_SESSION_ADDRESS": "Selector de se<PERSON>", "TR_SERVER_SUPPORTED_FEATURES": "Funciones compatibles con el servidor", "TR_SERVER_TRANSPORT_ADDRESS": "Selector de transporte", "TR_SERVICE_INI_FILE_NAME": "Nombre del archivo INI del servicio", "TR_SERVICE_PARAMETERS": "Parámetros de servicio", "TR_SESSION_CONFIG": "Configuración de sesión", "TR_SESSION_DUPLICATE": "Error al agregar sesión. La sesión {{arg1}} ya existe ", "TR_SESSION_LINK_ADDRESS": "Dirección del enlace", "TR_SESSION_LINK_ADDRESS_DESC": "Dirección de enlace de datos del componente esclavo o dispositivo remoto. Cada índice identifica una sesión única, que es una conexión de capa de enlace entre un dispositivo maestro y un dispositivo esclavo. Establezca en 0xffff (65535) para que la sesión sea una 'sesión de difusión'.", "TR_SESSION_LOCAL_ADDRESS": "Fuente / Dirección local", "TR_SESSION_LOCAL_ADDRESS_DESC": "Dirección de enlace de datos del dispositivo local. Este parámetro solo se utiliza para sesiones maestras o esclavas que utilizan el protocolo DNP3. ", "TR_SESSION_NAME": "Nombre de sesión", "TR_SESSION_NAME_DESC": "Nombre de la sesión", "TR_SET_MDO_OPTIONS_FAILED": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_SET_TAGNAME_FAILED": "Error al establecer el nombre de la etiqueta de usuario en {{arg1}} (podría ser un duplicado)", "TR_SETTINGS": "Configuración", "TR_SEVERITY": "Severidad", "TR_SHOW_ALL_FCS": "Mostrar todas las restricciones funcionales", "TR_SHOW_ALL_FCS_DESC": "Cambiar la visualización de restricciones funcionales", "TR_SHOW_ONLY_ST_MX": "<PERSON>rar solo ST / MX", "TR_SISCO_COMPATABILITY": "Compatibilidad SISCO", "TR_SLAVE_MASTER_DATA_OBJECT": "Objeto de datos maestro / esclavo", "TR_SLCT_CONFIRM": "SLCT_CONFIRM", "TR_SLCT_CONFIRM_DESC": "Un dispositivo remoto confirmó una primera pasada en una operación de control de 2 pasadas", "TR_SLCT_PENDING": "SLCT_PENDING", "TR_SLCT_PENDING_DESC": "Una primera pasada en una operación de control de 2 pasadas se ha transmitido a un dispositivo remoto", "TR_SOE_LOG": "Secuencia de registro de eventos", "TR_SOEQ": "SOEQ", "TR_SOEQFILE_NAME": "Nombre de archivo de cola SOE", "TR_SOEQFILE_NAME_DESC": "Secuencia de eventos Nombre y ruta del archivo de la cola", "TR_SOURCE": "Fuente", "TR_START_DATE": "Fecha de inicio", "TR_START_GATEWAY": "Iniciar puerta de enlace", "TR_STATUS": "ESTADO", "TR_STATUS_CODE": "Código de estado", "TR_STATUS_CODE_DESC": "Definir código de estado", "TR_STOP_GATEWAY": "Detener puerta de enlace", "TR_SUBMIT_SUPPORT_REQUEST": "Enviar solicitud de soporte", "TR_SUBSCRIBED_STREAM": "Transmisión suscrita", "TR_SUBSCRIBED_STREAM_DESC": "Transmisión suscrita", "TR_SUBSTITUTED": "SUSTITUIDO", "TR_SUBSTITUTED_DESC": "Sustituido (anulación o forzado)", "TR_SUCCES_ARG1": "Éxito: {{arg1}}", "TR_SUCCESS": "Éxito", "TR_SUPER_USERS": "Superusuarios", "TR_SYSTEM_LOGS": "Registros del sistema", "TR_SYSTEM_SETTINGS": "Configuración del sistema", "TR_SYSTEM_TRACE": "Rastreo del sistema", "TR_SYSTEMS_LOGS": "Registros de sistemas", "TR_SYSTEMS_LOGS_DESC": "Registros de sistemas", "TR_TABLE_INFO": "Información de la tabla", "TR_TABLE_INFO_DESC": "Especifica la información de la tabla", "TR_TABLE_LIST": "Lista de tablas", "TR_TABLE_LIST_DESC": "Especifica la lista de tablas", "TR_TAG_DESCRIPTION": "Descripción de la etiqueta:", "TR_TAG_DESCRIPTION_DESC": "Especifique la descripción de la etiqueta", "TR_TAG_EDIT_": "<PERSON><PERSON>", "TR_TAG_NAME": "Nombre de etiqueta:", "TR_TAG_NAME_DESC": "Especifique el nombre de la etiqueta", "TR_TAG_OPTIONS": "Opciones de etiqueta:", "TR_TAG_OPTIONS_DESC": "Especifique las opciones de etiqueta", "TR_TAG_QUALITY": "Calidad", "TR_TAG_QUALITY_DESC": "Definir la calidad para el MDO", "TR_TAG_TIME": "Tiempo de etiqueta", "TR_TAG_TIME_DESC": "Definir la hora para el MDO", "TR_TAG_VALUE_TYPE": "Tipo de valor de etiqueta:", "TR_TAG_VALUE_TYPE_DESC": "El tipo del valor de la etiqueta", "TR_TARGET_LAYER": "Capa de destino", "TR_TARGET_LAYER_DESC": "Registrar mensajes de rastreo para la capa de destino", "TR_TASE2_ADD_DS_TRANSFERSET": "No se pudo agregar {{arg1}} DS Transfer Set en el servidor ICCP en {{arg2}}. Asegúrese de que el Conjunto de datos seleccionado exista en el servidor y que haya más conjuntos de transferencia disponibles. ", "TR_TASE2_ADD_MDO": "No se pudo agregar {{arg1}} MDO en el servidor 61850: {{arg2}}. (¿Duplicado?)", "TR_TASE2_ADD_MDO_DUPLICATE": "No se pudo agregar {{arg1}} MDO en el servidor ICCP: {{arg2}}. (¿Duplicado?)", "TR_TASE2_CLIENT_DELETE": "El cliente '{{arg1}}' tiene MDO secundarios que deben eliminarse antes de que se pueda cargar el modelo.", "TR_TASE2_CLIENT_DELETE_CLEAR_MODEL": "El cliente '{{arg1}}' tiene MDO secundarios que deben eliminarse antes de que el modelo pueda borrarse", "TR_TASE2_CLIENT_DUP_LIC": "No se pudo agregar el Cliente ICCP (podría estar duplicado o no tener licencia)", "TR_TASE2_CLIENT_DUPLICATE": "No se puede agregar el cliente ICCP: '{{arg1}}'. Nombre duplicado ", "TR_TASE2_CLIENT_EXISTS": "Error: el cliente con este nombre ya existe", "TR_TASE2_CLIENT_NO_MORE": "No hay más clientes ICCP disponibles", "TR_TASE2_CREAT_MDO_NAME": "MDO debe tener un nombre, no se puede crear", "TR_TASE2_CREATE_DS": "Error al crear el conjunto de datos: {{arg1}} en el servidor.Ver error en el analizador de protocolos para más información ", "TR_TASE2_CREATE_MDO_INVALID_TAG": "No se pudo crear {{arg1}} MDO.  Nombre de etiqueta inválido?", "TR_TASE2_DELETE_DS_NOT_FOUND": "Conjunto de datos {{arg1}} no encontrado", "TR_TASE2_DELETE_IN_USE": "MDO: '{{arg1}}' se asigna a puntos esclavos o se usa en una ecuación, no se puede eliminar", "TR_TASE2_DELETE_DS": "DataSet no se puede eliminar", "TR_TASE2_DUPLICATE_DOMAIN": "No se pueden tener nombres de dominio duplicados. Por favor, introduzca un nombre único ", "TR_TASE2_MDO_ALREADY_DEFINED": "MDO ya está definido, no se puede crear", "TR_TASE2_MDO_DELETE": "No se pueden eliminar los MDO ICCP", "TR_TASE2_MDO_DUP_TAG": "No se pudo establecer el nombre de la etiqueta de usuario MDO {{arg1}} (¿duplicado?)", "TR_TASE2_NO_CONTROL_BLOCK": "No es un bloque de control válido", "TR_TASE2_NO_EDIT_CONN": "No se puede editar el servidor mientras la conexión está activa", "TR_TASE2_NO_EDIT_WITH_CLIENTS_CON": "No se puede editar el servidor mientras los clientes están conectados", "TR_TASE2_NO_MORE": "No hay más clientes ICCP disponibles", "TR_TASE2_NO_MORE_DS_TRANSFER": "No hay más conjuntos de transferencia DS disponibles", "TR_TASE2_NO_MORE_LD": "No hay más dispositivos lógicos disponibles", "TR_TASE2_NO_MORE_POLLED": "No hay más ICCP PolledPointSets disponibles", "TR_TASE2_NO_MORE_POLLED_DS": "No se pudo agregar {{arg1}} PolledPointSet en el servidor ICCP: {{arg2}}.", "TR_TASE2_NO_POINTS": "No hay puntos ICCP disponibles", "TR_TASE2_NO_POLLED": "No hay elementos de conjunto de datos sondeados disponibles", "TR_TASE2_NONE_LEFT": "No hay más clientes ICCP disponibles", "TR_TASE2_OPT_MDO": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_TASE2_SELECT": "Error al seleccionar el control - no se hizo nada", "TR_TASE2_SELECT_INTEGER": "Escribir valor no es válido, debe ser un número entero", "TR_TASE2_SELECT_WRONG_VALUE": "Escribir valor no es válido, debe ser un número decimal", "TR_TASE2_SERVER_ADD_POLLED_DS": "No se pudo agregar {{arg1}} PolledDataSet en el servidor ICCP: {{arg2}}.", "TR_TASE2_SERVER_DUP_LIC": "Error al agregar el servidor ICCP (podría estar duplicado o no tener licencia)", "TR_TASE2_SERVER_EXISTS": "Error: el servidor con este nombre ya existe", "TR_TASE2_SET_MDO": "No se pudieron establecer las opciones de MDO {{arg1}}", "TR_TASE2CLIENT_AEINVOKE_ID": "ID de invocación AE", "TR_TASE2CLIENT_AEINVOKE_ID_DESC": "Especifica el ID de invocación AE del Cliente ICCP", "TR_TASE2CLIENT_AEQUALIFIER": "Calificador AE", "TR_TASE2CLIENT_AEQUALIFIER_DESC": "Especifica el calificador AE del cliente ICCP", "TR_TASE2CLIENT_APINVOKE_ID": "ID de invocación AP", "TR_TASE2CLIENT_APINVOKE_ID_DESC": "Especifica el ID de invocación AP del Cliente ICCP", "TR_TASE2CLIENT_APP_ID": "ID de la aplicación", "TR_TASE2CLIENT_APP_ID_DESC": "Especifica el ID de la aplicación del Cliente ICCP", "TR_TASE2CLIENT_CONNECT_TIMEOUT": "Tiempo de espera MMS (ms)", "TR_TASE2CLIENT_CONNECT_TIMEOUT_DESC": "Especifica el tiempo de espera de conexión MMS para el Cliente ICCP.  Después de comenzar un intento de conexión, así es como int esperar el éxito ", "TR_TASE2CLIENT_CONNECTING_PORT": "Puerto IP", "TR_TASE2CLIENT_CONNECTING_PORT_DESC": "Especifica el puerto TCP / IP al que el cliente intentará conectarse en el servidor", "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME": "Tiempo de búfer del conjunto de transferencia de conjunto de datos (segundos)", "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME_DESC": "Especifica el atributo Tiempo de búfer para un conjunto de transferencia de conjunto de datos", "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL": "Intervalo de conjunto de transferencia de conjunto de datos (segundos)", "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL_DESC": "Especifica el atributo de intervalo de un conjunto de transferencia de conjunto de datos", "TR_TASE2CLIENT_DSTRANSFER_SET_RBE": "Informe de conjunto de transferencia de conjunto de datos por excepción", "TR_TASE2CLIENT_DSTRANSFER_SET_RBE_DESC": "Especifica el atributo Informe por excepción de un conjunto de transferencia de conjunto de datos", "TR_TASE2CLIENT_INITIATE": "Iniciar una conexión", "TR_TASE2CLIENT_INITIATE_DESC": "Si el cliente inicia una conexión", "TR_TASE2CLIENT_PRESENTATION_ADDRESS": "Dirección de presentación", "TR_TASE2CLIENT_PRESENTATION_ADDRESS_DESC": "Especifica la dirección de presentación del cliente ICCP", "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT": "Reconectar recuento de reintentos", "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT_DESC": "Especifica el recuento de reintentos de reconexión para el Cliente ICCP (0 = intento de reconexión para siempre) Una conexión exitosa hará que el contador de límite interno se vuelva a establecer en 0 resultados en intentos continuos de conexión al servidor ICCP", "TR_TASE2CLIENT_RECONNECT_TIME": "Reconectar tiempo de espera (ms)", "TR_TASE2CLIENT_RECONNECT_TIME_DESC": "Especifica el tiempo de espera de reconexión para el Cliente ICCP (0 = sin reconexión)", "TR_TASE2CLIENT_RFC_IP_ADDR": "Dirección IP RFC del cliente ICCP", "TR_TASE2CLIENT_RFC_IP_ADDR_DESC": "Especifica la dirección IP de RFC del Cliente ICCP", "TR_TASE2CLIENT_SESSION_ADDRESS": "Dirección de sesión", "TR_TASE2CLIENT_SESSION_ADDRESS_DESC": "Especifica la dirección de sesión del cliente ICCP", "TR_TASE2CLIENT_TRANSPORT_ADDRESS": "Dirección de transporte", "TR_TASE2CLIENT_TRANSPORT_ADDRESS_DESC": "Especifica la dirección de transporte del cliente ICCP", "TR_TASE2CLT_POLLED_POINT_SET_NAME": "Nombre del conjunto de puntos sondeados", "TR_TASE2CLT_POLLED_POINT_SET_NAME_DESC": "Especifica el nombre del conjunto de puntos encuestados", "TR_TASE2CLT_POLLED_POINT_SET_PERIOD": "Período establecido de punto de sondeo (ms)", "TR_TASE2CLT_POLLED_POINT_SET_PERIOD_DESC": "Especifica el período para leer el conjunto de puntos encuestados", "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME": "dominio ICCP del conjunto de datos del conjunto de datos informados", "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME_DESC": "Especifica el dominio de un conjunto de datos de conjunto de datos reportados en un servidor ICCP", "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD": "Período del conjunto de transferencia de conjunto de datos (segundos)", "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD_DESC": "Especifica el período de actualizaciones de integridad de un conjunto de transferencia de conjunto de datos", "TR_TASE2CLT_REPORTED_DSNAME": "Nombre del conjunto de datos informados", "TR_TASE2CLT_REPORTED_DSNAME_DESC": "Especifica el nombre de un conjunto de datos reportados en un servidor ICCP", "TR_TASE2SECURITY_ON": "Habilitar seguridad", "TR_TASE2SECURITY_ON_DESC": "En caso de que la seguridad esté activada", "TR_TASE2SERVER_AEINVOKE_ID": "ID de invocación AE", "TR_TASE2SERVER_AEINVOKE_ID_DESC": "Especifica el ID de invocación AE del servidor ICCP", "TR_TASE2SERVER_AEQUALIFIER": "Calificador AE", "TR_TASE2SERVER_AEQUALIFIER_DESC": "Especifica el calificador AE del servidor ICCP", "TR_TASE2SERVER_APINVOKE_ID": "ID de invocación AP", "TR_TASE2SERVER_APINVOKE_ID_DESC": "Especifica el ID de invocación AP del servidor ICCP", "TR_TASE2SERVER_APP_ID": "ID de la aplicación", "TR_TASE2SERVER_APP_ID_DESC": "Especifica el ID de la aplicación del servidor ICCP", "TR_TASE2SERVER_IPADDRESS": "Dirección IP del servidor ICCP", "TR_TASE2SERVER_IPADDRESS_DESC": "Especifica la dirección IP del servidor ICCP al que el cliente intentará conectarse", "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID": "ID de tabla bilateral", "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID_DESC": "Especifica la ID de la tabla bilateral", "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT": "Número de conjuntos de transferencia DS", "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT_DESC": "Especifica el número de conjuntos de transferencia DS", "TR_TASE2SERVER_LOGICAL_DEVICE_NAME": "Nombre de dispositivo lógico", "TR_TASE2SERVER_LOGICAL_DEVICE_NAME_DESC": "Especifica el nombre de un dispositivo lógico en un servidor ICCP", "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED": "Número máximo de clientes", "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED_DESC": "Especifica el número máximo de clientes permitidos para conectarse a este servidor. Sin especificar significa que no hay máx. ", "TR_TASE2SERVER_PRESENTATION_ADDRESS": "Dirección de presentación", "TR_TASE2SERVER_PRESENTATION_ADDRESS_DESC": "Especifica la dirección de presentación del servidor ICCP", "TR_TASE2SERVER_RFC_IP_ADDR": "Dirección IP RFC del servidor ICCP", "TR_TASE2SERVER_RFC_IP_ADDR_DESC": "Especifica la dirección IP de RFC del servidor ICCP", "TR_TASE2SERVER_SESSION_ADDRESS": "Dirección de sesión", "TR_TASE2SERVER_SESSION_ADDRESS_DESC": "Especifica la dirección de sesión del servidor ICCP", "TR_TASE2SERVER_SUPPORTED_FEATURES": "Funciones compatibles con el servidor ICCP", "TR_TASE2SERVER_SUPPORTED_FEATURES_DESC": "Especifica los bloques ICCP que se informan a los clientes de conexión como Supported_Features", "TR_TASE2SERVER_TRANSPORT_ADDRESS": "Dirección de transporte", "TR_TASE2SERVER_TRANSPORT_ADDRESS_DESC": "Especifica la dirección de transporte del servidor ICCP", "TR_TASE2SERVICE_ROLE": "Rol de servicio", "TR_TASE2SERVICE_ROLE_DESC": "Especifica el rol del servicio Tase2", "TR_TASE2SYNC_DATA_SETS": "Sincronizar conjuntos de datos", "TR_TASE2SYNC_DATA_SETS_DESC": "Si el cliente intenta sincronizar conjuntos de datos en la conexión", "TR_TASE2VERSION": "Versión de la aplicación ICCP", "TR_TASE2VERSION_DESC": "Especifica la versión de la aplicación ICCP", "TR_TEST": "TEST", "TR_TEST_DESC": "Prueba", "TR_TEST_MODE": "<PERSON><PERSON>", "TR_TEST_MODE_DESC": "El punto de datos o el dispositivo remoto está funcionando en modo de prueba", "TR_THE_CURRENT_PASSWORD_IS_REQUIRED": "Se requiere la contraseña actual", "TR_THE_NEW_PASSWORD_MUST_BE_AT_LEAST_CHARACTERS_LONG": "La nueva contraseña debe tener al menos 6 caracteres", "TR_THE_TWO_PASSWORD_FIELDS_DIDN_T_MATCH": "Los dos campos de contraseña no coinciden", "TR_THE_USER_ADMIN_CAN_NOT_BE_DELETED": "El usuario Admin no se puede eliminar", "TR_THE_USERNAME_X_IS_ALREADY_USED": "El nombre de usuario {{username}} ya está en uso", "TR_THIS_SYSTEM": "Este sistema", "TR_THRESHOLD": "Theshold", "TR_TIME": "<PERSON><PERSON>", "TR_TIME_AND_TIMING": "Hora y tiempo", "TR_TIME_STAMP": "Marca de tiempo", "TR_TIME_STAMP_DESC": "Especifica si la marca de tiempo está activa", "TR_TIME_ZONE_BIAS": "Sesgo de zona horaria", "TR_TIME_ZONE_NAME": "Zona horaria", "TR_TIME_ZONE_NAME_DESC": "El nombre de la zona horaria a usar para el tiempo de visualización SDG (la cadena vacía / predeterminada se establecerá en UTC).  Nota: SDG usa UTC para tiempos internos.  UseTimeZoneClock debe ser verdadero ", "TR_TLS": "TLS", "TR_TLS_COMMON_NAME": "Nombre común", "TR_TLS_CONFIG": "Configuración de seguridad 62351-3", "TR_TLS_HANDSHAKE_TIMEOUT": "TLS Handshake Timeout", "TR_TLS_MAX_PDUS": "PDU máx. Antes de forzar la renegociación de cifrado", "TR_TLS_MAX_RENEG_WAIT_TIME": "Tiempo de espera de renegociación máxima (ms)", "TR_TLS_RENEGOTIATION": "Renegociación (seg)", "TR_TLS_RENEGOTIATION_COUNT": "Renegociation Count", "TR_TLS_RENEGOTIATION_SECONDS": "Renegociación (seg)", "TR_TRIGGER_OPTIONS": "Opciones de activación", "TR_TS_CONDITIONS_DETECTED": "Incluir condiciones de TS detectadas", "TR_TS_CONDITIONS_DETECTED_DESC": "Incluir condiciones de TS detectadas", "TR_TS_SET_NAME": "Incluir nombre del conjunto de transferencia", "TR_TS_SET_NAME_DESC": "Incluir nombre de conjunto de transferencia", "TR_TS_SET_TIMESTAMP": "Incluir marca de tiempo de conjunto de transferencia", "TR_TS_SET_TIMESTAMP_DESC": "Incluir marca de tiempo de conjunto de transferencia", "TR_TYPE": "Tipo", "TR_UNAUTHORIZED": "No autorizado", "TR_UNINITIALIZED": "Sin inicializar", "TR_UNINITIALIZED_DESC": "No establecido desde el inicio", "TR_UNKNOWN": "Desconocido", "TR_UNKNOWN_DESC": "Los datos se están actualizando, pero se desconoce el motivo de la actualización", "TR_UNLOCK_SCROLL": "Desbloquear desplazamiento", "TR_UNSELECT_ALL": "<PERSON>elecci<PERSON><PERSON> todo", "TR_UNSOLICITED_UDP_PORT": "Puerto UDP no solicitado", "TR_UNSPECIFIED_ERROR": "Error no especificado", "TR_UPLOAD_FILE": "Subir archivo", "TR_UPLOAD_NEW_CONFIGURATION_FILE": "Cargar nuevo archivo de configuración", "TR_UPLOAD_NEW_CSV_FILE": "Cargar nuevo archivo CSV", "TR_UPLOAD_NEW_INI_FILE": "Cargar nuevo archivo INI", "TR_USE": "<PERSON><PERSON>", "TR_USE_DEFLATE": "Usar compresión gzip", "TR_USE_REPORTED_TIME": "Usar el tiempo informado para los MDO", "TR_USE_REPORTED_TIME_DESC": "si es verdadero, no actualice el tiempo en actualizaciones estáticas, esto se aplica a los MDO que se pueden informar como eventos", "TR_USE_SCL_FILE": "Usar archivo SCL", "TR_USE_SYSTEM_TIME": "Usar reloj del sistema", "TR_USE_SYSTEM_TIME_DESC": "Si es verdadero, siempre lea la fecha y la hora del reloj del sistema de Windows en lugar de un reloj mantenido internamente. El reloj interno se inicializa en el reloj del sistema de Windows al inicio, pero se ajustará siempre que se reciba una sincronización de reloj de un maestro externo. Por lo general, establecería UseSystemTime en verdadero si tiene un mecanismo de sincronización de reloj externo que sincroniza el reloj del sistema de Windows fuera de SCADA Data Gateway, en este caso se recomienda que AcceptClockSync se configure en falso. ", "TR_USE_TIME_ZONE_CLOCK": "Usar reloj de zona horaria", "TR_USE_TIME_ZONE_CLOCK_DESC": "Si es verdadero, muestra la fecha y la hora del SDG en el TimeZoneName especificado", "TR_USE_WEB_SSL": "Usar Web SSL / HTTPS", "TR_USER_TAG_NAME": "Nombre de etiqueta de usuario", "TR_USER_TAG_NAME_DESC": "El nombre de la etiqueta de usuario del MDO", "TR_USER_X_DELETED": "Usuario {{username}} eliminado", "TR_USERNAME": "Nombre de usuario", "TR_USERNAME_IS_REQUIRED": "Se requiere nombre de usuario", "TR_USERNAME_IS_REQUIRED_CHARACTERS_MINIMUM": "Se requiere nombre de usuario (mínimo 4 caracteres)", "TR_USERS": "Usuarios", "TR_USERS_MANAGEMENT": "Gestión de usuarios", "TR_VALID_EQUATION_SUCCESS": "Éxito: ecuación válida", "TR_VALIDATE_EQUATION": "Validar ecuación", "TR_VALIDATE_UDP_ADDRESS": "Validar la dirección UDP", "TR_VALUE": "Valor", "TR_VERBOSE": "<PERSON><PERSON><PERSON>", "TR_VERIFY_CERTIFICATE": "Verificar el certificado HTTPS", "TR_VERSION": "Versión", "TR_VIEW": "<PERSON>er", "TR_VIEWS": "Vistas especiales", "TR_WARNING": "Advertencia", "TR_WARNING_DESC": "Advertencia", "TR_WARNING_VIEW": "Vista de advertencia", "TR_WEB_SERVER": "Servidor web", "TR_WEB_SERVER_DESC": "Registrar mensajes de rastreo para el servidor web", "TR_WEBSOCKET_CLOSE": "Websocket {{websocketName}} cerrar: {{reason}}", "TR_WEBSOCKET_OPEN": "Websocket {{websocketName}} abierto", "TR_WEBSOCKET_UPDATE_PARAMETERS": "Parámetros de actualización de Websocket", "TR_WHAT_NAME_DO_YOU_TO_USE_FOR_YOUR_NEW_INI_CSV_FILE": "¿Qué nombre quieres usar para tu nuevo archivo INI", "TR_WIN_TIMER_FAILED": "Ha ocurrido un error fatal: {{arg1}}. El SDG ahora intentará salir. Por favor, consulte los registros para más detalles", "TR_YES": "Sí", "TR_YOUR_BROWSER_WILL_REFRESH_IN_X_SECONDS": "<PERSON> Broswer se actualizará en {{value}} segundos", "TR_ERROR_FAILED_TO_STOP_AFTER_120_SEC": "No puede parar el motor después de 120 segundos", "TR_ARE_YOU_SURE_TO_DELETE_USER_": "Are you sure to delete user {{username}}?"}