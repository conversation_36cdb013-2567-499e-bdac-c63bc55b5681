System.register(["@angular/core", "rxjs", "../configuration", "../../modules/alert/alert.service", "@ngx-translate/core", "rxjs/operators"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, rxjs_1, rxjs_2, configuration_1, alert_service_1, core_2, operators_1, BroadcastEventWSApi;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
                rxjs_2 = rxjs_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            }
        ],
        execute: function () {
            BroadcastEventWSApi = (function () {
                function BroadcastEventWSApi(alertService, translate) {
                    this.alertService = alertService;
                    this.translate = translate;
                    this.basePath = "ws://localhost/rest";
                    this.configuration = new configuration_1.Configuration();
                    this.isReconnectNeeded = false;
                    this.reconnectAttempts = 0;
                    this.maxReconnectAttempts = 5;
                    this.reconnectInterval = 3000;
                }
                BroadcastEventWSApi.prototype.getBroadcastEventData = function () {
                    if (!this.subject)
                        this.subject = this.create();
                    return this.subject;
                };
                BroadcastEventWSApi.prototype.close = function () {
                    if (this.websocket) {
                        this.websocket.close();
                        this.subject = null;
                    }
                };
                BroadcastEventWSApi.prototype.create = function () {
                    var _this = this;
                    var observable = rxjs_2.Observable.create(function (obs) {
                        _this.createObservable(obs);
                    }).pipe(operators_1.share());
                    var observer = {
                        next: function (data) {
                            if (_this.websocket && _this.websocket.readyState === WebSocket.OPEN) {
                                _this.websocket.send(JSON.stringify(data));
                            }
                        }
                    };
                    return rxjs_1.Subject.create(observer, observable);
                };
                BroadcastEventWSApi.prototype.createObservable = function (obs) {
                    var _this = this;
                    if (this.configuration.apiKeys) {
                        var wsUrl = "";
                        if (location.protocol != 'https:')
                            wsUrl = "ws://" + window.location.host + "/broadcastEvent?token=" + this.configuration.apiKeys["Authorization"];
                        else
                            wsUrl = "wss://" + window.location.host + "/broadcastEvent?token=" + this.configuration.apiKeys["Authorization"];
                        this.websocket = new WebSocket(wsUrl);
                        this.websocket.onopen = function (openEvent) {
                            _this.reconnectAttempts = 0;
                            _this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getBroadcastEvent" }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                        };
                        this.websocket.onmessage = obs.next.bind(obs);
                        this.websocket.onerror = function (error) { };
                        this.websocket.onclose = function (evt) {
                            if (evt.reason === "restart") {
                                _this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getBroadcastEvent", reason: evt.type }).subscribe(function (res) {
                                    _this.alertService.debug(res);
                                    obs.error("restart");
                                });
                            }
                            else {
                                _this.reconnectWS(obs);
                            }
                        };
                    }
                };
                BroadcastEventWSApi.prototype.reconnectWS = function (obs) {
                    var _this = this;
                    this.reconnectAttempts++;
                    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
                        setTimeout(function () {
                            _this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getBroadcastEvent", reconnectAttempt: _this.reconnectAttempts }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                            _this.createObservable(obs);
                        }, this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1));
                    }
                    else {
                        this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getBroadcastEvent", reason: "Reopen fail" }).subscribe(function (res) {
                            obs.error(res);
                        });
                        this.close();
                    }
                };
                BroadcastEventWSApi = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, core_2.TranslateService])
                ], BroadcastEventWSApi);
                return BroadcastEventWSApi;
            }());
            exports_1("BroadcastEventWSApi", BroadcastEventWSApi);
        }
    };
});
//# sourceMappingURL=BroadcastEventWSApi.js.map