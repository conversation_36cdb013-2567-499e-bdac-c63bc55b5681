import { Component, Input, Output, EventEmitter } from "@angular/core";
import { Node } from './node';

@Component({
  selector: "treeviewComponent",
  styles: [`
		  .iconButton {
			  display: inline-block; 
			  margin-right: 5px;
			  cursor: pointer;
			  color:#ecc96a;
		  }
		  .selectNode {

		  }
		  ul {
			  padding-left: 12px;
			  list-style-type: none;
		  }
		  li{
			  margin-top: 4px;
		  }
		  ol, ul {
			  margin-top: 0;
		  }
      .leaf:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
        font-weight: bold;
      }
      .leaf{
			  cursor: pointer;
      }
      .glyphicon-none {
        padding-right: 12px;  
        color: transparent !important;
      }
  `],
  template: `
    <ul [ngClass]="{'is-selected': (node.nodeFullName === this.selectedNodeFullName)}">
			<div *ngIf="!node.hasChildren" class="glyphicon-none iconButton"></div>
			<div *ngIf="node.hasChildren && node.isExpanded" class="glyphicon glyphicon-minus iconButton" title="{{TR_COLLAPSE_CHILDREN | translate}}" (click)="node.toggle()"></div>
			<div *ngIf="node.hasChildren && !node.isExpanded" class="glyphicon glyphicon-plus iconButton" title="{{TR_EXPAND_CHILDREN | translate}}" (click)="node.toggle()"></div>

      <span *ngIf="isCheckbox"><input type="checkbox" [checked]="node.checked" (click)="node.check()"/>&nbsp;{{node.nodeName}}</span>
      <span *ngIf="!isCheckbox" [ngClass]="{'leaf': (!node.hasChildren)}" (click)="(!node.hasChildren) && clickAction(node.nodeFullName)">{{node.nodeName}}</span>

      <ng-container *ngIf="node.isExpanded">    
        <li *ngFor="let nodeChildren of node.children">
          <treeviewComponent [node]="nodeChildren" [isCheckbox]="isCheckbox" [(selectedNodeFullName)]="selectedNodeFullName" (selectedNodeFullNameChange)="clickAction($event)" (selectedNodeLoadChildren)="nodeToggle($event)"></treeviewComponent>
        </li>
      </ng-container>
    </ul>
    `
})

export class TreeviewComponent {
  @Input() node: Node;
  @Input() isCheckbox;
  @Input() selectedNodeFullName;
  @Output() selectedNodeFullNameChange: EventEmitter<string> = new EventEmitter<string>();

  private clickAction(selectedNodeFullName: string): void {
    this.selectedNodeFullName = selectedNodeFullName;
    this.selectedNodeFullNameChange.emit(this.selectedNodeFullName);
  } 
}