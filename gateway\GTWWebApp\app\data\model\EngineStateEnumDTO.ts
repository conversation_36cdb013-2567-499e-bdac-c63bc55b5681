/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum EngineStateEnumDTO {
    NOTRUNNING = <any> 'NOT_RUNNING',
    RUNNING = <any> 'RUNNING',
    STARTUPSTARTING = <any> 'STARTUP_STARTING',
    STARTUPCSVLOADED = <any> 'STARTUP_CSV_LOADED',
    STARTUPINILOADED = <any> 'STARTUP_INI_LOADED',
    STARTUPDONE = <any> 'STARTUP_DONE',
    SAVINGINICSV = <any> 'SAVING_INI_CSV',
    SHUTTINGDOWN = <any>'SHUTTING_DOWN',
    ERRORININI = <any>'ERROR_IN_INI',
    STARTUPUNKNOWN = <any>'STARTUP_UNKNOWN',
    PROCESSLOADED = <any>'PROCESS_LOADED',
    RUNNING_NO_LICENSE = <any>'RUNNING_NO_LICENSE',
    RUNNING_IN_GRACE_PERIOD = <any>'RUNNING_IN_GRACE_PERIOD'
}
