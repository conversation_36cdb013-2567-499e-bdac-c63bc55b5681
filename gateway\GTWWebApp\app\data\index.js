System.register(["./api/api", "./model/models", "./variables", "./configuration", "./api.module"], function (exports_1, context_1) {
    "use strict";
    var __moduleName = context_1 && context_1.id;
    function exportStar_1(m) {
        var exports = {};
        for (var n in m) {
            if (n !== "default") exports[n] = m[n];
        }
        exports_1(exports);
    }
    return {
        setters: [
            function (api_1_1) {
                exportStar_1(api_1_1);
            },
            function (models_1_1) {
                exportStar_1(models_1_1);
            },
            function (variables_1_1) {
                exportStar_1(variables_1_1);
            },
            function (configuration_1_1) {
                exportStar_1(configuration_1_1);
            },
            function (api_module_1_1) {
                exportStar_1(api_module_1_1);
            }
        ],
        execute: function () {
        }
    };
});
//# sourceMappingURL=index.js.map