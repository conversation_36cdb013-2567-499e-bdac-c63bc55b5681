System.register(["@angular/core", "./context-menu.service", "./context-menu-item"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, context_menu_service_1, context_menu_item_1, ContextMenuComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (context_menu_service_1_1) {
                context_menu_service_1 = context_menu_service_1_1;
            },
            function (context_menu_item_1_1) {
                context_menu_item_1 = context_menu_item_1_1;
            }
        ],
        execute: function () {
            ContextMenuComponent = (function () {
                function ContextMenuComponent(contextMenuService) {
                    var _this = this;
                    this.contextMenuService = contextMenuService;
                    this.contextMenuitems = [];
                    this.isShown = false;
                    this.mouseLocation = { left: 0, top: 0 };
                    this.contextMenuType = context_menu_item_1.ContextMenuType;
                    this.contextMenuSubscriptionShow = contextMenuService.show.subscribe(function (e) { return _this.showMenu(e.event, e.obj); });
                    this.contextMenuSubscriptionHide = contextMenuService.hide.subscribe(function (e) { return _this.isShown = false; });
                }
                ContextMenuComponent.prototype.ngOnDestroy = function () {
                    this.contextMenuSubscriptionShow.unsubscribe();
                    this.contextMenuSubscriptionHide.unsubscribe();
                };
                Object.defineProperty(ContextMenuComponent.prototype, "locationCss", {
                    get: function () {
                        return {
                            "z-index": "2",
                            "position": "fixed",
                            "padding": "4px",
                            "display": this.isShown ? "block" : "none",
                            left: (this.mouseLocation.left - 4) + "px",
                            top: (this.mouseLocation.top - 4) + "px",
                        };
                    },
                    enumerable: false,
                    configurable: true
                });
                ContextMenuComponent.prototype.clickedOutside = function (event) {
                    if (event.target.id != "textSearch")
                        this.isShown = false;
                };
                ContextMenuComponent.prototype.showMenu = function (event, contextMenuitems) {
                    this.isShown = true;
                    this.contextMenuitems = contextMenuitems;
                    this.mouseLocation = {
                        left: event.clientX,
                        top: event.clientY
                    };
                };
                ContextMenuComponent.prototype.onClickItem = function (item) {
                    if (item.isLicensed)
                        item.subject.next(item.title);
                };
                ContextMenuComponent = __decorate([
                    core_1.Component({
                        selector: "contextMenuComponent",
                        styles: ["\n      .item {\n        cursor: pointer;\n        padding: 2px,\n      }\n      .item:hover {\n        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n      }\n      .separator {\n        margin: 1px;\n      }\n      .disabled {\n        opacity: 0.65; \n        cursor: not-allowed;\n      }\n      .not-license:after {\n        content: ' (not licensed)';\n        white-space: nowrap;\n      }\n      .logo {\n          width: 14px;\n          height: 14px;\n          margin-top: -4px;\n          margin-right: 3px;\n          margin-left: -1px;\n      }\n"],
                        host: {
                            "(document:click)": "clickedOutside($event)",
                        },
                        template: "\n      <div [ngStyle]=\"locationCss\" class=\"panel panel-default\">\n        <div class=\"item\" *ngFor=\"let item of contextMenuitems\">\n          <div *ngIf=\"item.type == contextMenuType.ACTION\" [ngClass]=\"{'disabled': (!item.isLicensed)}\" (click)=\"onClickItem(item)\"><img src=\"{{'../images/' + item.icon}}\" class=\"logo\" *ngIf=\"(item.icon != null)\"><div [ngClass]=\"{'not-license': (!item.isLicensed)}\">{{item.title | translate}}</div></div>\n          <div *ngIf=\"item.type == contextMenuType.ACTION_BOLD\" (click)=\"onClickItem(item)\"><img src=\"{{'../images/' + item.icon}}\" class=\"logo\" *ngIf=\"(item.icon != null)\"><b><i>{{item.title | translate}}</i></b></div>\n          <div *ngIf=\"item.type == contextMenuType.SEPARATOR\"><hr class=\"separator\"></div>\n          <div *ngIf=\"item.type == contextMenuType.CHECKBOX\" (click)=\"onClickItem(item)\"><input type=\"checkbox\" class=\"form-check\" [checked]=item.value />&nbsp;<span>{{item.title | translate}}</span></div>\n        </div>\n      </div>\n    "
                    }),
                    __metadata("design:paramtypes", [context_menu_service_1.ContextMenuService])
                ], ContextMenuComponent);
                return ContextMenuComponent;
            }());
            exports_1("ContextMenuComponent", ContextMenuComponent);
        }
    };
});
//# sourceMappingURL=context-menu.component.js.map