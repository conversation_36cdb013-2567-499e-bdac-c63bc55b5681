/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

/**
 * Sent by /getHealth web socket endpoint
 */
export interface HealthObjectDTO {
    activeUsers?: Array<string>;

    engineState?: string;

    engineExitFailState?: string;

		monitorOk?: boolean;

    sysCpu?: number;

    sysMem?: number;

    engineCpu?: number;

    engineMem?: number;

}
