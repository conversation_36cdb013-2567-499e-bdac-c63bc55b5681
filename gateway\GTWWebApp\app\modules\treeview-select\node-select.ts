﻿export class NodeSelect {
  nodeName: string;
  nodeFullName: string;
  parent: NodeSelect;
  children: Array<NodeSelect> = [];
  hasChildren: boolean = false;
  isExpanded: boolean = false;
  checked: boolean = false;
  displayCheckbox: boolean = false;
  indeterminated: boolean = false;

  constructor(nodeName: string, nodeFullName: string, parent: NodeSelect, checked: boolean = false, displayCheckbox: boolean = false, indeterminated: boolean = false) {
    this.nodeName = nodeName;
    this.nodeFullName = nodeFullName;
    this.parent = parent;
    this.checked = checked;
    this.indeterminated = indeterminated;
    this.displayCheckbox = displayCheckbox
    //Auto expand first level
    if (parent==null) {
      this.isExpanded = true;
    }
  }

  public toggle(): void {
    this.isExpanded = !this.isExpanded;
  }

  public checkRecursive(checked: boolean, node: NodeSelect) {
    node.checked = checked;
    if (node.children) {
      node.children.forEach(child => {
        if (!child.hasChildren)
          this.check(checked, child);
        else
          this.checkRecursive(checked, child);
      });
    }
    const descendants = node.children;
    if (!node.checked)
      node.indeterminated = descendants.some(child => child.checked);
    this.checkAllParents(node);
  }

  public check(checked: boolean, node: NodeSelect) {
    node.checked = checked;
    if (node.children) {
      node.children.forEach(child => {
        if (!child.hasChildren)
          this.check(checked, child);
        else
          this.checkRecursive(checked, child);
      });
    }
    const descendants = node.children;
    if (!node.checked)
      node.indeterminated = descendants.some(child => child.checked);
    this.checkAllParents(node);
  }

  private checkAllParents(node: NodeSelect) {
    if (node.parent) {
      const descendants = node.parent.children;
      node.parent.checked = descendants.every(child => child.checked);
      if (!node.parent.checked)
        node.parent.indeterminated = descendants.some(child => (child.checked || child.indeterminated));
      else
        node.parent.indeterminated = false;
      this.checkAllParents(node.parent);
    }
  }
}