﻿import { Component, EventEmitter , Output} from "@angular/core";
import { TreeNodeDTO } from "../../data/model/models";
import { AlertService} from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { NodesService } from "../../data/api/api";

@Component({
  selector: "dashboardConfigDevicesSearchComponent",
  styles: [`      
      .small-input-text {
        height: 24px !important;
        font-size: 14px !important;
        color: #337ab7;
        font-style: italic;
        font-weight: bolder;
        margin-top: -1px;
      }
      .small-input-button {
        margin-top: -2px;
        display: inline-block;
      }      
      .search-menu {
        border: 1px solid #f5f5f5;
        border-radius: 3px;
        padding: 2px;
        width:100%
      }
  `],
  template: `
    <div class="shadow-bg search-menu">
        <div style="display:table-cell;width:99%">
          <input placeholder="{{'TR_NODE_NAME' | translate}}" type="text" title="{{'TR_ENTER_A_PARTIAL_OR_COMPLETE_NODE_NAME' | translate}}" class="form-control small-input-text" [(ngModel)]="searchValue">
        </div>
        <div style="display: table-cell;padding-left: 4px;vertical-align:middle;min-width:56px">
          <div class="round-button" title="{{'TR_SEARCH' | translate}}" (click)="onSearchNode()"><img [src]="'../../images/magnifier.svg'" class="image-button"/></div>
          <div class="round-button" title="{{'TR_CLEAR_SEARCH' | translate}}" (click)="onClearSearchNode()"><img [src]="'../../images/delete.svg'" class="image-button"/></div>
        </div>
    </div>
  `
})

export class DashboardConfigDevicesSearchComponent{
  @Output() onSearchChanged: EventEmitter<object> = new EventEmitter();
  private searchValue = "";

  constructor(private alertService: AlertService, private nodesService: NodesService, private authenticationService: AuthenticationService) { }

  private onSearchNode(): void {
    try {
      this.nodesService.getNodes(null, this.searchValue, false, true, "ALL").subscribe(
        data => {
          let isNodeTreeFromSearch:boolean = false
          if (this.searchValue != "")
            isNodeTreeFromSearch = true;
          this.onSearchChanged.emit({ data, isNodeTreeFromSearch });
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        }
      );
    }
    catch (err) {
      return null;
    }
  }
  private onClearSearchNode(): void {
    this.searchValue = "";
    this.onSearchNode();
  }
}