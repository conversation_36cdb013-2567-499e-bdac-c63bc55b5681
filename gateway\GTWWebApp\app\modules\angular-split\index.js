System.register(["./angular-split.module", "./split.component", "./splitArea.directive", "./splitGutter.directive"], function (exports_1, context_1) {
    "use strict";
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (angular_split_module_1_1) {
                exports_1({
                    "AngularSplitModule": angular_split_module_1_1["AngularSplitModule"]
                });
            },
            function (split_component_1_1) {
                exports_1({
                    "SplitComponent": split_component_1_1["SplitComponent"]
                });
            },
            function (splitArea_directive_1_1) {
                exports_1({
                    "SplitAreaDirective": splitArea_directive_1_1["SplitAreaDirective"]
                });
            },
            function (splitGutter_directive_1_1) {
                exports_1({
                    "SplitGutterDirective": splitGutter_directive_1_1["SplitGutterDirective"]
                });
            }
        ],
        execute: function () {
        }
    };
});
//# sourceMappingURL=index.js.map