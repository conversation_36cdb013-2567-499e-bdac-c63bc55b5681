﻿import { Component, OnInit, Input, Output, EventEmitter} from "@angular/core";
import { EditorFieldObjectDTO } from "../../data/model/models";
import { FormControl} from '@angular/forms';

@Component({
  selector: "bitmaskEditorComponent",
  styles: [`
        .panel {
          margin-bottom: 2px; !important;
        }
 			  .chevron {
				  font-size: 16px;
				  margin-left: 6px;
				  margin-right: 4px;
			  }
        .input-bitmask {
          display: block;
          width: 104px;
          padding: 2px 12px;
          font-size: inherit;
          color: #555;
          background-color: #fff;
          background-image: none;
          border: 1px solid #ccc;
          border-radius: 4px;
        }
        .list-bitmask{
          padding:8px; 
          position:absolute; 
          background-color:#f8f8f8;
          border: 1px solid #ccc;
          z-index:99;
        }
        .truncate {
          width: 350px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
`],
	template: `
			  <div class="panel" style="width: 134px;background-color: #f8f8f8;border: 1px solid #ccc;" (mouseenter)="selectOpenToggle($event, true)"  (mouseleave)="selectOpenToggle($event, false)">
				  <div>
					  <div style="display: inline-block; width:100px;">
						  <input type="text" class="input-bitmask" [(ngModel)]="bitmaskString" [readonly]="true" />
					  </div>
					  <div style="display: inline-block; vertical-align:middle;">
						  <div *ngIf="isSelectOpen" class="glyphicon glyphicon-chevron-up chevron"></div>
						  <div *ngIf="!isSelectOpen" class="glyphicon glyphicon-chevron-down chevron"></div>
					  </div>
				  </div>
				  <div *ngIf="isSelectOpen" class="panel list-bitmask" style="">
					  <div *ngFor="let bitmask of this.bitmaskList">
						  <label class="truncate">
								  <input type="checkbox"
										class="form-check"
										[checked]=bitmask.isChecked
										(change)="bitmaskListOnChange(bitmask)"/>
								    <span [tooltip]="[bitmask.description]"> {{bitmask.description}}</span>
						  </label>
					  </div>
				  </div>
			  </div>
		    `
})

export class BitmaskEditorComponent implements OnInit {
  @Input() editorField: EditorFieldObjectDTO;
  @Input() editorFieldcontrol: FormControl;
  @Output() bitmaskOnChange: EventEmitter<null> = new EventEmitter();
  private bitmaskList: Bitmask[] = [];
  private bitmaskString: string = "0";
  private bitmaskLength: number = 8;
	private isSelectOpen: boolean;
  private bitmask: number = 0x0;

  constructor() {}

  public ngOnInit(): void {

    this.bitmask = parseInt(this.editorField.value);
    this.bitmaskString = this.decimalToHexConverter(this.bitmask);
    this.editorFieldcontrol.setValue(this.bitmaskString);

    if (this.bitmaskString != null)
      this.bitmask = parseInt(this.bitmaskString, 16);
    else
      this.bitmask = 0;

    let controlSourceList = JSON.parse(this.editorField.controlSource); 

    for (let i in controlSourceList) {
      let bitmaskListValue: number = parseInt(controlSourceList[i].mask_value)
      if (this.bitmask === 0 && controlSourceList[i].mask_value === 0x0) {
        this.bitmaskList.push({ language_id: controlSourceList[i].description, description: controlSourceList[i].description, mask_value: bitmaskListValue, isChecked: true });
      }
      else {
        if (controlSourceList[i].mask_value & this.bitmask)
          this.bitmaskList.push({ language_id: controlSourceList[i].language_id, description: controlSourceList[i].description, mask_value: bitmaskListValue, isChecked: true });
        else
          this.bitmaskList.push({ language_id: controlSourceList[i].language_id, description: controlSourceList[i].description, mask_value: bitmaskListValue, isChecked: false });
      }
    }
	}

  private selectOpenToggle(event: MouseEvent, isOpen: boolean): void {
    let eventRelatedTarget: any = event.relatedTarget;
    if (eventRelatedTarget != null && eventRelatedTarget.className != null && eventRelatedTarget.className.includes("tooltipComponent"))
      return;

    this.isSelectOpen = isOpen;
    event.preventDefault();
	}

  private bitmaskListOnChange(bitmask: Bitmask): void {
    bitmask.isChecked = !bitmask.isChecked;

    if (bitmask.isChecked && bitmask.mask_value === 0x0) {
      for (let i in this.bitmaskList)
        if (i > "0")
          this.bitmaskList[i].isChecked = false;
      this.bitmask = 0x0;
    }
    else if (this.bitmaskList[0].mask_value === 0x0) {
      this.bitmaskList[0].isChecked = false;
    }
      
    let bitmaskTemp = this.bitmask;
    if (bitmask.isChecked)
	    bitmaskTemp = bitmaskTemp | bitmask.mask_value;
    else
	    bitmaskTemp = bitmaskTemp & (~(bitmask.mask_value));

    this.bitmask = bitmaskTemp;
    this.bitmaskString = this.decimalToHexConverter(this.bitmask); 
    this.bitmaskOnChange.emit();
    this.editorFieldcontrol.setValue(this.bitmaskString);
	}

  private decimalToHexConverter(inputNumber: number): string{
    let str = inputNumber.toString(16);
    return ("0X" + "0".repeat(this.bitmaskLength - str.length) + str).toUpperCase();
	}
}

export interface Bitmask {
	description?: string;
	language_id?: string;
	mask_value?: number;
  isChecked?: boolean;
}