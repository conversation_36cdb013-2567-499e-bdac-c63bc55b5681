/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { BroadcastEventDTO } from '../model/broadcastEventDTO';
import { HealthObjectDTO } from '../model/healthObjectDTO';
import { SDGConfigDTO } from '../model/sDGConfigDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class ConfigService {

    protected basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Get SDG configuration
     * 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getConfig(observe?: 'body', reportProgress?: boolean): Observable<SDGConfigDTO>;
    public getConfig(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<SDGConfigDTO>>;
    public getConfig(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<SDGConfigDTO>>;
    public getConfig(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<SDGConfigDTO>(`${this.basePath}/get_config`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get license information.
     * Get license information.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getLicense(observe?: 'body', reportProgress?: boolean): Observable<any>;
    public getLicense(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public getLicense(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public getLicense(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];

        return this.httpClient.get<any>(`${this.basePath}/get_license`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get SDG health
     * Get SDG health
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getSdgStatus(observe?: 'body', reportProgress?: boolean): Observable<HealthObjectDTO>;
    public getSdgStatus(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<HealthObjectDTO>>;
    public getSdgStatus(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<HealthObjectDTO>>;
    public getSdgStatus(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<HealthObjectDTO>(`${this.basePath}/get_sdg_status`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create new SDG work space configuration
     * Create new work space and re-start SDG
     * @param workSpaceName name of the work space to create
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public newWorkSpace(workSpaceName?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public newWorkSpace(workSpaceName?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public newWorkSpace(workSpaceName?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public newWorkSpace(workSpaceName?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (workSpaceName !== undefined) {
            formParams = formParams.append('workSpaceName', <any>workSpaceName) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/newWorkSpace`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Process a broadcast event
     * messageLogMaskEnum:   NONE &#x3D; 0, EVENT_LOG &#x3D; 1,ALERT_POPUP &#x3D; 2,STATUS_BAR &#x3D; 4  messageTypeEnum: refresh_tag, refresh_log_parameter,  message_info,  message_warning,  message_error,  message_debug,  message_success
     * @param body 
     * @param productKey product key
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public processBroadcastEvent(body: BroadcastEventDTO, productKey?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public processBroadcastEvent(body: BroadcastEventDTO, productKey?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public processBroadcastEvent(body: BroadcastEventDTO, productKey?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public processBroadcastEvent(body: BroadcastEventDTO, productKey?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling processBroadcastEvent.');
        }


        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (productKey !== undefined && productKey !== null) {
            queryParameters = queryParameters.set('product_key', <any>productKey);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.post<any>(`${this.basePath}/process_broadcast_event`,
            body,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

  /**
 * Save license information.
 * Save license information.
 * @param actionType action type
 * @param isNewLicense license is new
 * @param productKey product key
 * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
 * @param reportProgress flag to report request and response progress.
 */
  public saveLicense(actionType: string, isNewLicense: boolean, productKey?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public saveLicense(actionType: string, isNewLicense: boolean, productKey?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public saveLicense(actionType: string, isNewLicense: boolean, productKey?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public saveLicense(actionType: string, isNewLicense: boolean, productKey?: string, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (actionType === null || actionType === undefined) {
      throw new Error('Required parameter actionType was null or undefined when calling saveLicense.');
    }

    if (isNewLicense === null || isNewLicense === undefined) {
      throw new Error('Required parameter isNewLicense was null or undefined when calling saveLicense.');
    }


    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    if (actionType !== undefined && actionType !== null) {
      queryParameters = queryParameters.set('action_type', <any>actionType);
    }
    if (productKey !== undefined && productKey !== null) {
      queryParameters = queryParameters.set('product_key', <any>productKey);
    }
    if (isNewLicense !== undefined && isNewLicense !== null) {
      queryParameters = queryParameters.set('is_new_license', <any>isNewLicense);
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];

    return this.httpClient.get<any>(`${this.basePath}/save_license`,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Send V2C file license.
   * Send V2C file license.
   * @param file file
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public sendV2CLicense(file: File, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public sendV2CLicense(file: File, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public sendV2CLicense(file: File, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public sendV2CLicense(file: File, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (file === null || file === undefined) {
      throw new Error('Required parameter file was null or undefined when calling sendV2CLicense.');
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'multipart/form-data'
    ];

    const canConsumeForm = this.canConsumeForm(consumes);

    let formParams: { append(param: string, value: any): void | HttpParams; };
    let useForm = false;
    let convertFormParamsToString = false;
    // use FormData to transmit files using content-type "multipart/form-data"
    // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
    useForm = canConsumeForm;
    if (useForm) {
      formParams = new FormData();
    } else {
      formParams = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    }

    if (file !== undefined) {
      formParams = formParams.append('file', <any>file) || formParams;
    }

    return this.httpClient.post<any>(`${this.basePath}/sendV2CLicense`,
      convertFormParamsToString ? formParams.toString() : formParams,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

    /**
     * Select SDG work space configuration to run it
     * Select work space and re-start SDG to run it
     * @param workSpaceName name of the work space to run
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public selectWorkSpace(workSpaceName?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public selectWorkSpace(workSpaceName?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public selectWorkSpace(workSpaceName?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public selectWorkSpace(workSpaceName?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (workSpaceName !== undefined) {
            formParams = formParams.append('workSpaceName', <any>workSpaceName) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/selectWorkSpace`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Configure SDG
     * 
     * @param body 
     * @param restartFlag restart monitor and engine flag (true to resart)
     * @param validateFlag validate config setting on server (true to validate)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public setConfigJson(body: SDGConfigDTO, restartFlag?: boolean, validateFlag?: boolean, observe?: 'body', reportProgress?: boolean): Observable<SDGConfigDTO>;
    public setConfigJson(body: SDGConfigDTO, restartFlag?: boolean, validateFlag?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<SDGConfigDTO>>;
    public setConfigJson(body: SDGConfigDTO, restartFlag?: boolean, validateFlag?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<SDGConfigDTO>>;
    public setConfigJson(body: SDGConfigDTO, restartFlag?: boolean, validateFlag?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling setConfigJson.');
        }



        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (restartFlag !== undefined && restartFlag !== null) {
            queryParameters = queryParameters.set('restartFlag', <any>restartFlag);
        }
        if (validateFlag !== undefined && validateFlag !== null) {
            queryParameters = queryParameters.set('validateFlag', <any>validateFlag);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.post<SDGConfigDTO>(`${this.basePath}/set_config_json`,
            body,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Start Engine
     * Starts Engine with arguments, if no args are specified it will start the configured sdg engine with the configured engine ini file
     * @param arg command line argument (argument to pass to engine executable, typically the ini file)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public startEngine(arg?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public startEngine(arg?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public startEngine(arg?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public startEngine(arg?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (arg !== undefined) {
            formParams = formParams.append('arg', <any>arg) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/startEngine`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Stop Engine
     * Stops the Engine
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public stopEngine(observe?: 'body', reportProgress?: boolean): Observable<any>;
    public stopEngine(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public stopEngine(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public stopEngine(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        return this.httpClient.post<any>(`${this.basePath}/stopEngine`,
            null,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Stop Monitor
     * Stops the Monitor
     * @param bothFlag stop monitor and engine flag (true to stop both)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public stopMon(bothFlag?: boolean, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public stopMon(bothFlag?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public stopMon(bothFlag?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public stopMon(bothFlag?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {


        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (bothFlag !== undefined && bothFlag !== null) {
            queryParameters = queryParameters.set('bothFlag', <any>bothFlag);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        return this.httpClient.post<any>(`${this.basePath}/stopMon`,
            null,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
