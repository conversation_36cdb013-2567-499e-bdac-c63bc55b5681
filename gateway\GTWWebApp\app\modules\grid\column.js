System.register([], function (exports_1, context_1) {
    "use strict";
    var Column;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            Column = (function () {
                function Column(field, header, action, tooltip, arg, image, checked) {
                    if (arg === void 0) { arg = ""; }
                    if (image === void 0) { image = ""; }
                    if (checked === void 0) { checked = false; }
                    this.field = field;
                    this.header = header;
                    this.action = action;
                    this.tooltip = tooltip;
                    this.arg = arg;
                    this.image = image;
                    this.checked = checked;
                }
                return Column;
            }());
            exports_1("Column", Column);
        }
    };
});
//# sourceMappingURL=column.js.map