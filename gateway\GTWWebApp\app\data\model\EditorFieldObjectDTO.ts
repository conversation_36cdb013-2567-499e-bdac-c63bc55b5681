/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

/**
 * a specific field of an editor
 */
export interface EditorFieldObjectDTO {
    schemaName?: string;

    name?: string;

    value?: string;

    help?: string;

    defaultHelp?: string;

    controlType?: EditorFieldObjectDTO.ControlTypeEnum;

    controlSource?: string;

    isRequired?: boolean;

    isEditable?: EditorFieldObjectDTO.IsEditableEnum;

    isEditableAtRuntime?: EditorFieldObjectDTO.IsEditableEnum;

    /**
     * json object: for controlType Text {minLen, maxLen} or Number {minValue, maxValue}
     */
    range?: string;

    groupPanelName?: string;

    isGroupPanelHeader?: boolean;

    fieldsetName?: string;

    isFieldsetHeader?: boolean;

}
export namespace EditorFieldObjectDTO {
    export enum ControlTypeEnum {
        Label = <any> 'Label',
        Text = <any>'Text',
        TextMultiLine = <any>'TextMultiLine',
        TextAction = <any>'TextAction',
        TextPassword = <any>'TextPassword',
        TextId = <any>'TextId',
        Number = <any> 'Number',
        Checkbox = <any> 'Checkbox',
        RadioButton = <any> 'RadioButton',
        OptionsEditor = <any> 'OptionsEditor',
        MaskEditor = <any> 'MaskEditor',
        Combobox = <any> 'Combobox',
        FileManagement = <any>'FileManagement',
        FileManagementCombobox = <any>'FileManagementCombobox',
        Grid = <any>'Grid',
        GridNoDefault = <any>'GridNoDefault',
        Treeview = <any>'Treeview',
        TreeviewMutliChoice = <any>'TreeviewMutliChoice',
        TreeviewDynamic = <any>'TreeviewDynamic',
        TreeviewDynamicMutliChoice = <any>'TreeviewDynamicMutliChoice',
        TreeviewDynamicSelectMutliChoice = <any>'TreeviewDynamicSelectMutliChoice',
        Action = <any> 'Action',
        Info = <any> 'Info',
        Hidden = <any>'Hidden',
        AdvanceParameters = <any>'AdvanceParameters'
    }
  export enum IsEditableEnum {
      Yes = <any>'Yes',
      No = <any>'No',
    }
}
