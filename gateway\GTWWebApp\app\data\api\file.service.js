System.register(["@angular/core", "@angular/common/http", "../encoder", "../variables", "../configuration"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, http_1, encoder_1, variables_1, configuration_1, FileService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (encoder_1_1) {
                encoder_1 = encoder_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            }
        ],
        execute: function () {
            FileService = (function () {
                function FileService(httpClient, basePath, configuration) {
                    this.httpClient = httpClient;
                    this.basePath = 'http://localhost/rest';
                    this.defaultHeaders = new http_1.HttpHeaders();
                    this.configuration = new configuration_1.Configuration();
                    if (basePath) {
                        this.basePath = basePath;
                    }
                    if (configuration) {
                        this.configuration = configuration;
                        this.basePath = basePath || configuration.basePath || this.basePath;
                    }
                }
                FileService.prototype.canConsumeForm = function (consumes) {
                    var form = 'multipart/form-data';
                    for (var _i = 0, consumes_1 = consumes; _i < consumes_1.length; _i++) {
                        var consume = consumes_1[_i];
                        if (form === consume) {
                            return true;
                        }
                    }
                    return false;
                };
                FileService.prototype.fileGet = function (fileName, fileType, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (fileName === null || fileName === undefined) {
                        throw new Error('Required parameter fileName was null or undefined when calling fileGet.');
                    }
                    if (fileType === null || fileType === undefined) {
                        throw new Error('Required parameter fileType was null or undefined when calling fileGet.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (fileName !== undefined && fileName !== null) {
                        queryParameters = queryParameters.set('fileName', fileName);
                    }
                    if (fileType !== undefined && fileType !== null) {
                        queryParameters = queryParameters.set('fileType', fileType);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/octet-stream'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    return this.httpClient.get(this.basePath + "/file", {
                        params: queryParameters,
                        responseType: "blob",
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                FileService.prototype.licenseLogZipFileGet = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/octet-stream'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    return this.httpClient.get(this.basePath + "/licenseLogZipFile", {
                        params: queryParameters,
                        responseType: "blob",
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                FileService.prototype.filePost = function (file, fileType, workspaceName, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (file === null || file === undefined) {
                        throw new Error('Required parameter file was null or undefined when calling filePost.');
                    }
                    if (fileType === null || fileType === undefined) {
                        throw new Error('Required parameter fileType was null or undefined when calling filePost.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (fileType !== undefined && fileType !== null) {
                        queryParameters = queryParameters.set('fileType', fileType);
                    }
                    if (workspaceName !== undefined && workspaceName !== null) {
                        queryParameters = queryParameters.set('workspaceName', workspaceName);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'multipart/form-data'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    useForm = canConsumeForm;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (file !== undefined) {
                        formParams = formParams.append('file', file) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/file", convertFormParamsToString ? formParams.toString() : formParams, {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                FileService.prototype.filesGet = function (fileType, fileExtensions, workspaceName, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (fileType === null || fileType === undefined) {
                        throw new Error('Required parameter fileType was null or undefined when calling filesGet.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (fileExtensions !== undefined && fileExtensions !== null) {
                        queryParameters = queryParameters.set('fileExtensions', fileExtensions);
                    }
                    if (fileType !== undefined && fileType !== null) {
                        queryParameters = queryParameters.set('fileType', fileType);
                    }
                    if (workspaceName !== undefined && workspaceName !== null) {
                        queryParameters = queryParameters.set('workspaceName', workspaceName);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    return this.httpClient.get(this.basePath + "/files", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                FileService.prototype.workSpacesGet = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/work_spaces", {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                FileService = __decorate([
                    core_1.Injectable(),
                    __param(1, core_1.Optional()),
                    __param(1, core_1.Inject(variables_1.BASE_PATH)),
                    __param(2, core_1.Optional()),
                    __metadata("design:paramtypes", [http_1.HttpClient, String, configuration_1.Configuration])
                ], FileService);
                return FileService;
            }());
            exports_1("FileService", FileService);
        }
    };
});
//# sourceMappingURL=file.service.js.map