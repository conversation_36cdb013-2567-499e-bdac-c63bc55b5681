﻿import { Component, Input } from "@angular/core";
import { AlertService } from "./modules/alert/alert.service";
import { AuthenticationService } from "./authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { TranslateKeyPipe } from "./global/translateKey.pipe";

@Component({
  selector: "appInfoComponent",
  providers: [TranslateKeyPipe],
  styles: [`
      .panel {
        position: absolute;
        z-index: 999;
        margin: auto;
        width: 50%;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
      }
      .panel-heading{
        padding: 4px;
      }
      .panel-body{
        padding: 8px;
        
      }
      .button-close {
        position: absolute;
        top: 6px;
        right: 6px;
      }
      `
    ],
  template: `
      <div class="modal-backdrop fade show" *ngIf="isVisible"></div>
      <div class="panel-main panel panel-default" *ngIf="isVisible" style="width: auto; z-index: 1042; filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .8));">
        <ng-container *ngIf="infoType === infoTypeEnumEnum.HTTPS_WARNING">
          <div class="panel-heading">
            {{'TR_HTTPS_WARNING_TITLE' | translate}}
            <div class="button-close round-button" title="{{'TR_CLOSE' | translate}}" (click)="close()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
          </div>
          <div class="panel-body">
            <div [innerHtml]="'TR_HTTPS_WARNING_DESCRIPTION' | translate"></div>
            <br>
            <label class="form-check-label" >
              <input type="checkbox" class="form-check" [checked]="isHttpsWarningAtStartupEnabled" (change)="hideHttpsWarningAtStartup()"/>
                {{ 'TR_HTTPS_WARNING_AT_STARTUP' | translate }}
            </label>
          </div>
        </ng-container>
        <ng-container *ngIf="infoType === infoTypeEnumEnum.HTTPS_TMW_CERT">
          <div class="panel-heading">
            {{'TR_HTTPS_TMW_CERT_TITLE' | translate}}
            <div class="button-close round-button" title="{{'TR_CLOSE' | translate}}" (click)="close()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
          </div>
          <div class="panel-body">
            <div [innerHtml]="'TR_HTTPS_TMW_CERT_DESCRIPTION' | translate"></div>
          </div>
        </ng-container>
        <ng-container *ngIf="infoType === infoTypeEnumEnum.BROWSER_WARNING">
          <div class="panel-heading">
            {{'TR_BROWSER_WARNING_TITLE' | translate}}
            <div class="button-close round-button" title="{{'TR_CLOSE' | translate}}" (click)="close()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
          </div>
          <div class="panel-body">
            <div [innerHtml]="'TR_HTTPS_BROWSER_DESCRIPTION' | translate"></div>
          </div>
        </ng-container>
      </div>
`
})

export class AppInfoComponent {
  private infoType: InfoTypeEnum;
  private isVisible: boolean = false;
  private infoTypeEnumEnum = InfoTypeEnum;
  private isHttpsWarningAtStartupEnabled: boolean = false;

  constructor(private alertService: AlertService, private authenticationService: AuthenticationService, private translate: TranslateService, private translateKeyPipe: TranslateKeyPipe) { }

  public hideHttpsWarningAtStartup() {
    if (this.isHttpsWarningAtStartupEnabled) {
      localStorage.setItem("SDGHideHTTPSWarningAtStartup", JSON.stringify(false));
    }
    else {
      localStorage.setItem("SDGHideHTTPSWarningAtStartup", JSON.stringify(true));
    }
    this.isHttpsWarningAtStartupEnabled = !this.isHttpsWarningAtStartupEnabled
  }

  set setInfoType(value: InfoTypeEnum) {
    this.infoType = value;
  }

  public show() {
    this.isVisible = true;
  }

  private close() {
    this.isVisible = false;
  }
}
export enum InfoTypeEnum {
  HTTPS_WARNING = 0,
  HTTPS_TMW_CERT = 1,
  BROWSER_WARNING = 2

}
