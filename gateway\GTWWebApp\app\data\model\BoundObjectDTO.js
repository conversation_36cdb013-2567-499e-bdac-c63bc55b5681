System.register([], function (exports_1, context_1) {
    "use strict";
    var BoundObjectDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (BoundObjectDTO) {
                var DirectionEnum;
                (function (DirectionEnum) {
                    DirectionEnum[DirectionEnum["LEFT"] = 'LEFT'] = "LEFT";
                    DirectionEnum[DirectionEnum["RIGHT"] = 'RIGHT'] = "RIGHT";
                    DirectionEnum[DirectionEnum["BOTH"] = 'BOTH'] = "BOTH";
                })(DirectionEnum = BoundObjectDTO.DirectionEnum || (BoundObjectDTO.DirectionEnum = {}));
            })(BoundObjectDTO || (BoundObjectDTO = {}));
            exports_1("BoundObjectDTO", BoundObjectDTO);
        }
    };
});
//# sourceMappingURL=BoundObjectDTO.js.map