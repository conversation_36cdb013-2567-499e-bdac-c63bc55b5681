System.register([], function (exports_1, context_1) {
    "use strict";
    var BroadcastEventTypeEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (BroadcastEventTypeEnumDTO) {
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["RefreshUI"] = 'refresh_ui'] = "RefreshUI";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["RefreshDirtyFlag"] = 'refresh_dirty_flag'] = "RefreshDirtyFlag";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["RefreshLogParameter"] = 'refresh_log_parameter'] = "RefreshLogParameter";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["ForceLogOffUser"] = 'force_log_off_user'] = "ForceLogOffUser";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["MessageInfo"] = 'message_info'] = "MessageInfo";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["MessageWarning"] = 'message_warning'] = "MessageWarning";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["MessageError"] = 'message_error'] = "MessageError";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["MessageDebug"] = 'message_debug'] = "MessageDebug";
                BroadcastEventTypeEnumDTO[BroadcastEventTypeEnumDTO["MessageSuccess"] = 'message_success'] = "MessageSuccess";
            })(BroadcastEventTypeEnumDTO || (BroadcastEventTypeEnumDTO = {}));
            exports_1("BroadcastEventTypeEnumDTO", BroadcastEventTypeEnumDTO);
        }
    };
});
//# sourceMappingURL=BroadcastEventTypeEnumDTO.js.map