﻿import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, HostListener } from "@angular/core";
import { Location } from "@angular/common";
import { NavigationStart, Router } from '@angular/router';
import { Subscription } from "rxjs";
import { AuditService, AuthService, ConfigService, NodesService, EditorsService, ManageService, MappingsService, LogService, TagsService, EditorContextMenuService, FileService, MiscService, HelpService, WorkspaceService } from "./data/api/api";
import { LogWSApi, TagsWSApi, NodesWSApi, HealthWSApi, BroadcastEventWSApi } from "./data/wsApi/wsApi";
import { BroadcastEventTypeEnumDTO, BroadcastEventDTO } from "./data/model/models";
import { AlertService, messageLogMask } from "./modules/alert/alert.service";
import { AuthenticationService } from "./authentication/authentication.service";
import { GlobalDataService } from "./global/global.data.service";
import { TranslateService } from "@ngx-translate/core";
import { HelpQuickStartComponent } from "./help/help.quick-start.component";
import { HelpIniComponent } from "./help/help.ini.component";
import { AppInfoComponent, InfoTypeEnum} from "./app.info.component";
import { DialogRef } from 'ngx-modialog-7';
import { Modal } from "ngx-modialog-7/plugins/bootstrap";


@Component({
  selector: "app",
  providers: [AuditService, AuthService, ConfigService, NodesService, EditorsService, ManageService, MappingsService, LogService, TagsService, FileService, MiscService, HelpService, WorkspaceService,
    LogWSApi, TagsWSApi, NodesWSApi, HealthWSApi, EditorContextMenuService, BroadcastEventWSApi],
  templateUrl: "app/app.component.template.html",
  styles: [`
      .navbar-header {
        margin-right: 0px;
        margin-left: 0px;
      }
			.status-bar {
				overflow: hidden;
				background-color: #f8f8f8;
				position: fixed;
				bottom: 0;
				left:0;
				width: 100%;
				border-top: 1px solid #a31c3f;
        z-index: 999;
			}
			nav {
				text-align: center;
			}
			nav > a {
				font-size:24px;
				padding:16px;
			}
			.navbar-inverse .navbar-nav>li>a {
        color: white;
			}
      a{
        color: white;
        text-decoration: none;
        cursor: pointer;
      }
      .dropdown-menu{
        margin: 0px;
        padding:0px;
        border-radius: 0px;
        background-color: black;
        font-size: 12px;
      }
      .dropdown-item{
        background-color: black;
        padding: 10px;
        border: 1px solid white;
        white-space: nowrap;
      }
      .container-fluid {
        padding-left: 0px;
        padding-right: 0px;
      }
      .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
        background-image: url(../images/background_7.svg), linear-gradient(to left, #4a4a4a, #000000);
        background-size: 100%;
        opacity: 0.9;
      }
      .navbar-brand {
        z-index: 999;
        position: relative;
      }
      .menu-icon{
        width: 20px;
        height: 20px;
        padding-right: 4px;
        padding-bottom: 2px;
      }
      .logo {
        width: 60px;
        height: 60px;
        margin-top: -12px;
        margin-right: 10px;
        margin-left: 6px;
      }
      .logo-text-container{
        display: inline-block; 
        margin-left: -28px; 
        transform: scale(.8, 1);
      }
      .logo-text {
        border:0; 
        vertical-align: 6px; 
        font-size: 30px; 
        font-weight:bold;
        color:#f1e186;
      }
      .logo-tmw {
        position: fixed; 
        top: 10px; 
        right: 35px; 
        width: 90px;
      }
      .navbar-red
      {
        width: 100%;
        height: 4px;
        background-color: #a31c3f;
        position: fixed;
        z-index: -999;
      }
      @media only screen and (max-width: 770px) {
        .dropdown-menu{
          padding: 0px !important;
        }
        .dropdown-item{
          padding: 5px 5px 5px 20px;
          border: none;
        }
        .logo-tmw {
          display: none;
        }
      }`]
})

export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('helpQuickStart', { static: false }) helpQuickStart: HelpQuickStartComponent;
  @ViewChild('helpIni', { static: false }) helpIni: HelpIniComponent;
  @ViewChild('appInfoHttpsCertIsTmwSigned', { static: false }) appInfoHttpsCertIsTmwSigned: AppInfoComponent;
  @ViewChild('appInfoValidBrowser', { static: false }) appInfoValidBrowser: AppInfoComponent;
  @ViewChild('appInfoUseWebSSL', { static: false }) appInfoUseWebSSL: AppInfoComponent;

  private title = "SCADA Data Gateway";
  private isNavigationIn = false;
  private tryBroadcastEventWS: any = null;
  private infoTypeEnumEnum = InfoTypeEnum;
  private isAppLoading: boolean = true;
  private globalBroadcastServiceSubscription: Subscription;

  constructor(private authenticationService: AuthenticationService, private alertService: AlertService, private modal: Modal, private globalDataService: GlobalDataService, private router: Router,
    private auditService: AuditService, private authService: AuthService,
    private configService: ConfigService, private nodesService: NodesService,
    private editorsService: EditorsService, private manageService: ManageService,
    private mappingsService: MappingsService, private logService: LogService,
    private tagsService: TagsService, private miscService: MiscService,
    private editorContextMenuService: EditorContextMenuService, private fileService: FileService,
    private helpService: HelpService, private workspaceService: WorkspaceService,
    private logWSApi: LogWSApi, private tagsWSApi: TagsWSApi,
    private nodesWSApi: NodesWSApi, private healthWSApi: HealthWSApi,
    private broadcastEventWSApi: BroadcastEventWSApi,
    private translate: TranslateService, private location: Location) {

    //translate.addLangs(["en", "fr"]);
    translate.addLangs(["en"]);
    translate.setDefaultLang("en");

    //if (localStorage.getItem("SDGLanguage") != null) {
    //  translate.use(localStorage.getItem("SDGLanguage"));
    //} 
    //else {
    //  let browserLang = translate.getBrowserLang();
    //  translate.use(browserLang.match(/en|fr/) ? browserLang : "en");
    //}
  }

  private isValidBrowser(): boolean {
    if (navigator.userAgent.indexOf("Chrome") != -1)
      return true;
    else if (navigator.userAgent.indexOf("Firefox") != -1) 
      return true;
    else
      return false;
}

  public ngOnInit(): void {
    this.configService.getConfig().subscribe(
      data => {
        this.globalDataService.SDGConfig = data;
        this.changeConfigAPI(this.globalDataService.SDGConfig.gtwHost, this.globalDataService.SDGConfig.gtwHttpPort, this.globalDataService.SDGConfig.monHost, this.globalDataService.SDGConfig.monHttpPort);
        if (location.hostname.toLowerCase() != this.globalDataService.SDGConfig.monHost.toLowerCase() || location.port != this.globalDataService.SDGConfig.monHttpPort.toString()) {
          let monURL = location.protocol + "//" + this.globalDataService.SDGConfig.monHost + ":" + this.globalDataService.SDGConfig.monHttpPort;
          location.href = monURL;
        }
        this.authService.checkAuth(this.authenticationService.userApiConfig.apiKeys["Authorization"]).subscribe(
          data => { this.authenticationService.login(data); },
          error => { if (error.status == 401) { this.authenticationService.onLoginFailed("/dashboard"); } else { this.alertService.debug(error.message.toString()); } }
        );
        if (!this.globalDataService.SDGConfig.gtwUseWebSSL && localStorage.getItem("SDGHideHTTPSWarningAtStartup") !== "true") {
          this.appInfoUseWebSSL.show();
          this.appInfoUseWebSSL.setInfoType = this.infoTypeEnumEnum.HTTPS_WARNING;
        }
        if (this.globalDataService.SDGConfig.gtwHttpsCertIsTmwSigned) {
          this.appInfoHttpsCertIsTmwSigned.show();
          this.appInfoHttpsCertIsTmwSigned.setInfoType = this.infoTypeEnumEnum.HTTPS_TMW_CERT;
        }
        if (!this.isValidBrowser()) {
          this.appInfoValidBrowser.show();
          this.appInfoValidBrowser.setInfoType = this.infoTypeEnumEnum.BROWSER_WARNING;
        }
      },
      error => {
        this.alertService.debug(error.toString());
      },
      () => {
        setTimeout(() => { this.isAppLoading = false; }, 10000);
        this.openGlobalBroadcastServiceSubscription();
      }
    );
  }

  public ngOnDestroy(): void {
    if (this.broadcastEventWSApi.websocket !== null && this.broadcastEventWSApi.websocket.readyState === WebSocket.OPEN)
      this.broadcastEventWSApi.websocket.close();
    if (this.globalBroadcastServiceSubscription != null)
      this.globalBroadcastServiceSubscription.unsubscribe();
  }

  @HostListener('window:beforeunload', ['$event'])
  beforeUnloadHander(event) {
    this.logoff();
  }

  private openGlobalBroadcastServiceSubscription(): void {
    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(
      event => {
        if (event.type === 'message') {
          let broadcastEventData = JSON.parse(event.data);
          if (broadcastEventData.messageType == BroadcastEventTypeEnumDTO.ForceLogOffUser.toString()) {
            this.router.navigate(["/blank"]);
            let modalLogoffRef: DialogRef<any>;
            this.translate.get(broadcastEventData.messageKey).subscribe(res => {
              modalLogoffRef = this.modal.alert()
                .size('lg')
                .showClose(true)
                .title(this.translate.instant('TR_ERROR'))
                .okBtn(this.translate.instant('TR_CLOSE'))
                .okBtnClass('btn btn-default')
                .isBlocking(true)
                .body(`
						      <div class="panel panel-danger">
							      <div class="panel-heading"><div class="glyphicon glyphicon-alert"></div>&nbsp;&nbsp;` + res + `</div>
						      </div>`)
                .open();
            });
            modalLogoffRef.result.then(
              result => location.reload(),
              () => location.reload()
            );
          }
          else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.RefreshUI.toString()) {
            // App just load ignore start RefreshUI from Webmonitor and Engine
            if (this.isAppLoading == true)
              return;

            if (broadcastEventData.parameters != null && broadcastEventData.parameters.refreshWebBrowser != null) {
              const currentUrl = this.router.url;
              this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
                this.router.navigate([currentUrl]);
              });
            }
          }
          else {
            this.alertService.show(broadcastEventData);
          }
        }
      },
      error => {
        if (error != "restart") {
          this.displayRefreshModal();
        }
      }
    );
  }

  private displayRefreshModal(): void {
    let broadcastEvent: BroadcastEventDTO = {
      messageKey: "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER",
      messageLogMask: 3, //  eventLog = 1, alertPopup = 2
      messageText: "The connection with the Gateway Monitor was lost.<br /> Please refresh your browser when the monitor is running again.",
      messageTime: new Date().toUTCString(),
      messageType: BroadcastEventTypeEnumDTO.MessageError,
      parameters: null
    }
    this.alertService.show(broadcastEvent, false, -1);
  }

  private changeConfigAPI(gtwHost: string, gtwHttpPort: number, monHost: string, monHttpPort: number): void {
    this.configService.configuration = this.authenticationService.userApiConfig;
    this.auditService.configuration = this.authenticationService.userApiConfig;
    this.fileService.configuration = this.authenticationService.userApiConfig;
    this.authService.configuration = this.authenticationService.userApiConfig;
    this.nodesService.configuration = this.authenticationService.userApiConfig;
    this.editorsService.configuration = this.authenticationService.userApiConfig;
    this.manageService.configuration = this.authenticationService.userApiConfig;
    this.mappingsService.configuration = this.authenticationService.userApiConfig;
    this.logService.configuration = this.authenticationService.userApiConfig;
    this.tagsService.configuration = this.authenticationService.userApiConfig;
    this.miscService.configuration = this.authenticationService.userApiConfig;
    this.helpService.configuration = this.authenticationService.userApiConfig;
    this.logWSApi.configuration = this.authenticationService.userApiConfig;
    this.tagsWSApi.configuration = this.authenticationService.userApiConfig;
    this.nodesWSApi.configuration = this.authenticationService.userApiConfig;
    this.editorContextMenuService.configuration = this.authenticationService.userApiConfig;
    this.broadcastEventWSApi.configuration = this.authenticationService.userApiConfig;
    this.healthWSApi.configuration = this.authenticationService.userApiConfig;
    this.workspaceService.configuration = this.authenticationService.userApiConfig;

    let gtwHttpHostPort: string = "http://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest"
    let gtwWSHostPort: string = "ws://" + gtwHost + ":" + gtwHttpPort.toString();
    let monWSHostPort: string = "ws://" + monHost + ":" + monHttpPort.toString();

    if (location.protocol === "https:") {
      gtwHttpHostPort = "https://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";
      gtwWSHostPort = "wss://" + gtwHost + ":" + gtwHttpPort.toString();
      monWSHostPort = "wss://" + monHost + ":" + monHttpPort.toString();
    }

    this.nodesService.basePath = gtwHttpHostPort;
    this.editorsService.basePath = gtwHttpHostPort;
    this.manageService.basePath = gtwHttpHostPort;
    this.mappingsService.basePath = gtwHttpHostPort;
    this.tagsService.basePath = gtwHttpHostPort;
    this.helpService.basePath = gtwHttpHostPort;
    this.logService.basePath = gtwHttpHostPort;
    this.editorContextMenuService.basePath = gtwHttpHostPort;
    this.logWSApi.basePath = gtwWSHostPort;
    this.tagsWSApi.basePath = gtwWSHostPort;
    this.nodesWSApi.basePath = gtwWSHostPort;
    this.broadcastEventWSApi.basePath = monWSHostPort;

    this.getConfigEngine();
  }

  private getConfigEngine(): void {
    this.logService.getNumLogEntries().subscribe(
      data => { },
      error => {
        if (navigator.userAgent.indexOf("Firefox") > 0) {
          let parameters = { URL_ENGINE: location.protocol + "//" + this.globalDataService.SDGConfig.gtwHost + ":" + this.globalDataService.SDGConfig.gtwHttpPort };
          let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_WEB_BROWSER_CANNOT_CONNECT_GATEWAY_ENGINE_ACCEPT_SELFSIGNED_CERTIFICATE_FIREFOX", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError, parameters };
          this.alertService.show(broadcastEvent, false);
        }
        else {
          let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_WEB_BROWSER_CANNOT_CONNECT_GATEWAY_ENGINE_CHECK_ENGINE_SERVER_FIREWALL", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError };
          this.alertService.show(broadcastEvent, false);
        }
      }
    );
  }

  private toggleNavigationState(): void {
    let bool = this.isNavigationIn;
    this.isNavigationIn = bool === false ? true : false;
  }

  private quickStartVisible(): void {
    this.helpQuickStart.show();
  }

  private iniHelpVisible(): void {
    this.helpIni.show();
  }

  private logoff(): void {
    this.authService.logoffUser()
      .subscribe(
        data => {
          this.authenticationService.logoff();
        },
        error => {
          this.alertService.debug(error.message.toString());
        },
        () => location.reload()
      );
  }
}