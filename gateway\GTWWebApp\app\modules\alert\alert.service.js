System.register(["@angular/core", "@angular/router", "rxjs", "@ngx-translate/core", "ngx-modialog-7/plugins/bootstrap", "../../data/model/models", "./message"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, router_1, rxjs_1, rxjs_2, core_2, bootstrap_1, models_1, message_1, AlertService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
                rxjs_2 = rxjs_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (message_1_1) {
                message_1 = message_1_1;
            }
        ],
        execute: function () {
            AlertService = (function () {
                function AlertService(router, modal, translate) {
                    var _this = this;
                    this.router = router;
                    this.modal = modal;
                    this.translate = translate;
                    this.subject = new rxjs_1.Subject();
                    this.replaySubject = new rxjs_2.ReplaySubject(1000);
                    this.keepAfterNavigationChange = false;
                    this.modalPosition = 0;
                    router.events.subscribe(function (event) {
                        if (event instanceof router_1.NavigationStart) {
                            if (_this.keepAfterNavigationChange) {
                                _this.keepAfterNavigationChange = false;
                            }
                            else {
                                _this.subject.next("");
                            }
                        }
                    });
                }
                AlertService.prototype.show = function (broadcastEventData, keepAfterNavigationChange, alertTimeout) {
                    var _this = this;
                    if (keepAfterNavigationChange === void 0) { keepAfterNavigationChange = false; }
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    try {
                        this.keepAfterNavigationChange = keepAfterNavigationChange;
                        if (broadcastEventData.messageKey == "" && broadcastEventData.messageText == "")
                            return;
                        if (broadcastEventData.messageKey != "" && broadcastEventData.parameters) {
                            this.translate.get(broadcastEventData.messageKey, broadcastEventData.parameters).subscribe(function (messageText) {
                                _this.showBase(broadcastEventData, messageText, alertTimeout);
                            });
                        }
                        else if (broadcastEventData.messageKey != "" && !broadcastEventData.parameters) {
                            this.translate.get(broadcastEventData.messageKey).subscribe(function (messageText) {
                                _this.showBase(broadcastEventData, messageText, alertTimeout);
                            });
                        }
                        else if (broadcastEventData.messageText != "") {
                            this.showBase(broadcastEventData, broadcastEventData.messageText, alertTimeout);
                        }
                    }
                    catch (err) {
                        console.log(err);
                    }
                };
                AlertService.prototype.info = function (message, keepAfterNavigationChange, destination, alertTimeout) {
                    if (keepAfterNavigationChange === void 0) { keepAfterNavigationChange = false; }
                    if (destination === void 0) { destination = 4; }
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    var broadcastEvent = { messageKey: message, messageLogMask: destination, messageType: models_1.BroadcastEventTypeEnumDTO.MessageInfo };
                    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
                };
                AlertService.prototype.success = function (message, keepAfterNavigationChange, destination, alertTimeout) {
                    if (keepAfterNavigationChange === void 0) { keepAfterNavigationChange = false; }
                    if (destination === void 0) { destination = 4; }
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    var broadcastEvent = { messageKey: message, messageLogMask: destination, messageType: models_1.BroadcastEventTypeEnumDTO.MessageSuccess };
                    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
                };
                AlertService.prototype.warning = function (message, keepAfterNavigationChange, destination, alertTimeout) {
                    if (keepAfterNavigationChange === void 0) { keepAfterNavigationChange = false; }
                    if (destination === void 0) { destination = 4; }
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    var broadcastEvent = { messageKey: message, messageLogMask: destination, messageType: models_1.BroadcastEventTypeEnumDTO.MessageWarning };
                    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
                };
                AlertService.prototype.error = function (message, errorDetail, keepAfterNavigationChange, destination, alertTimeout) {
                    if (errorDetail === void 0) { errorDetail = null; }
                    if (keepAfterNavigationChange === void 0) { keepAfterNavigationChange = false; }
                    if (destination === void 0) { destination = 4; }
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    var broadcastEvent = { messageKey: message, messageLogMask: destination, messageType: models_1.BroadcastEventTypeEnumDTO.MessageError, parameters: errorDetail };
                    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
                };
                AlertService.prototype.debug = function (message, keepAfterNavigationChange, destination, alertTimeout) {
                    if (keepAfterNavigationChange === void 0) { keepAfterNavigationChange = false; }
                    if (destination === void 0) { destination = 4; }
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    var broadcastEvent = { messageKey: message, messageLogMask: destination, messageType: models_1.BroadcastEventTypeEnumDTO.MessageDebug };
                    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
                };
                AlertService.prototype.getMessage = function () {
                    return this.subject.asObservable();
                };
                AlertService.prototype.getLogMessage = function () {
                    return this.replaySubject.asObservable();
                };
                AlertService.prototype.clearMessage = function () {
                    this.subject.next("");
                };
                AlertService.prototype.showBase = function (broadcastEventData, messageText, alertTimeout) {
                    var _this = this;
                    if (alertTimeout === void 0) { alertTimeout = -1; }
                    try {
                        if ((broadcastEventData.messageLogMask & 2) == 2) {
                            var popupTitle = "";
                            var popupContent = "";
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageInfo) {
                                popupTitle = "TR_INFORMATION";
                                popupContent = "<div class=\"panel panel-primary\"><div class=\"panel-heading scroll-panel\"><div class=\"glyphicon glyphicon-info-sign\"></div>&nbsp;&nbsp;" + messageText + "</div></div>";
                            }
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageWarning) {
                                popupTitle = "TR_WARNING";
                                popupContent = "<div class=\"panel panel-warning\"><div class=\"panel-heading scroll-panel\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + messageText + "</div></div>";
                            }
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageError) {
                                popupTitle = "TR_ERROR";
                                popupContent = "<div class=\"panel panel-danger\"><div class=\"panel-heading scroll-panel\"><div class=\"glyphicon glyphicon-warning-sign\"></div>&nbsp;&nbsp;" + messageText + "</div></div>";
                            }
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageSuccess) {
                                popupTitle = "TR_SUCCESS";
                                popupContent = "<div class=\"panel panel-success\"><div class=\"panel-heading scroll-panel\"><div class=\"glyphicon glyphicon-ok-sign\"></div>&nbsp;&nbsp;" + messageText + "</div></div>";
                            }
                            if (!this.modalAlertRef || this.modalAlertRef.context.body.substring(0, this.modalAlertRef.context.body.indexOf("<div class=\"timestamp\">")) != popupContent) {
                                if (this.modalPosition == 6)
                                    this.modalPosition = 0;
                                this.modalPosition++;
                                this.modalAlertRef = this.modal.alert()
                                    .size('lg')
                                    .showClose(true)
                                    .title(this.translate.instant(popupTitle))
                                    .okBtn(this.translate.instant('TR_CLOSE'))
                                    .okBtnClass('btn btn-default')
                                    .dialogClass('modal-dialog modalPosition' + this.modalPosition.toString())
                                    .body(popupContent + "<div class=\"timestamp\">UTC:&nbsp;" + broadcastEventData.messageTime + "</div>")
                                    .isBlocking(true)
                                    .open();
                                this.modalAlertRef.result.then(function (data) {
                                    _this.modalAlertRef = null;
                                    if (_this.modalPosition > 1)
                                        _this.modalPosition--;
                                });
                            }
                        }
                        if ((broadcastEventData.messageLogMask & 4) == 4) {
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageInfo) {
                                this.subject.next(new message_1.Message(messageText, 'info', null));
                                if (alertTimeout > 0)
                                    setTimeout(function () { _this.clearMessage(); }, alertTimeout);
                                else if (alertTimeout == -1)
                                    setTimeout(function () { _this.clearMessage(); }, 5000);
                            }
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageWarning) {
                                this.subject.next(new message_1.Message(messageText, 'warning', null));
                                if (alertTimeout > 0)
                                    setTimeout(function () { _this.clearMessage(); }, alertTimeout);
                                else if (alertTimeout == -1)
                                    setTimeout(function () { _this.clearMessage(); }, 20000);
                            }
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageError) {
                                this.subject.next(new message_1.Message(messageText, 'error', null));
                                if (alertTimeout > 0)
                                    setTimeout(function () { _this.clearMessage(); }, alertTimeout);
                                else if (alertTimeout == -1)
                                    setTimeout(function () { _this.clearMessage(); }, 20000);
                            }
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageSuccess) {
                                this.subject.next(new message_1.Message(messageText, 'success', null));
                                if (alertTimeout > 0)
                                    setTimeout(function () { _this.clearMessage(); }, alertTimeout);
                                else if (alertTimeout == -1)
                                    setTimeout(function () { _this.clearMessage(); }, 5000);
                            }
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageDebug) {
                                console.log(new Date().toUTCString() + ": " + messageText);
                            }
                        }
                        if ((broadcastEventData.messageLogMask & 1) == 1) {
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageInfo)
                                this.replaySubject.next(new message_1.Message(messageText, 'TR_INFORMATION', broadcastEventData.messageTime));
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageWarning)
                                this.replaySubject.next(new message_1.Message(messageText, 'TR_WARNING', broadcastEventData.messageTime));
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageError)
                                this.replaySubject.next(new message_1.Message(messageText, 'TR_ERROR', broadcastEventData.messageTime));
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageSuccess)
                                this.replaySubject.next(new message_1.Message(messageText, 'TR_SUCCESS', broadcastEventData.messageTime));
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.MessageDebug) {
                                this.replaySubject.next(new message_1.Message(messageText, 'TR_DEBUG', broadcastEventData.messageTime));
                            }
                        }
                    }
                    catch (err) {
                        console.log(err);
                    }
                };
                AlertService = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [router_1.Router, bootstrap_1.Modal, core_2.TranslateService])
                ], AlertService);
                return AlertService;
            }());
            exports_1("AlertService", AlertService);
        }
    };
});
//# sourceMappingURL=alert.service.js.map