System.register(["@angular/core", "../../data/model/models", "@angular/forms"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, models_1, forms_1, BitmaskEditorComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (forms_1_1) {
                forms_1 = forms_1_1;
            }
        ],
        execute: function () {
            BitmaskEditorComponent = (function () {
                function BitmaskEditorComponent() {
                    this.bitmaskOnChange = new core_1.EventEmitter();
                    this.bitmaskList = [];
                    this.bitmaskString = "0";
                    this.bitmaskLength = 8;
                    this.bitmask = 0x0;
                }
                BitmaskEditorComponent.prototype.ngOnInit = function () {
                    this.bitmask = parseInt(this.editorField.value);
                    this.bitmaskString = this.decimalToHexConverter(this.bitmask);
                    this.editorFieldcontrol.setValue(this.bitmaskString);
                    if (this.bitmaskString != null)
                        this.bitmask = parseInt(this.bitmaskString, 16);
                    else
                        this.bitmask = 0;
                    var controlSourceList = JSON.parse(this.editorField.controlSource);
                    for (var i in controlSourceList) {
                        var bitmaskListValue = parseInt(controlSourceList[i].mask_value);
                        if (this.bitmask === 0 && controlSourceList[i].mask_value === 0x0) {
                            this.bitmaskList.push({ language_id: controlSourceList[i].description, description: controlSourceList[i].description, mask_value: bitmaskListValue, isChecked: true });
                        }
                        else {
                            if (controlSourceList[i].mask_value & this.bitmask)
                                this.bitmaskList.push({ language_id: controlSourceList[i].language_id, description: controlSourceList[i].description, mask_value: bitmaskListValue, isChecked: true });
                            else
                                this.bitmaskList.push({ language_id: controlSourceList[i].language_id, description: controlSourceList[i].description, mask_value: bitmaskListValue, isChecked: false });
                        }
                    }
                };
                BitmaskEditorComponent.prototype.selectOpenToggle = function (event, isOpen) {
                    var eventRelatedTarget = event.relatedTarget;
                    if (eventRelatedTarget != null && eventRelatedTarget.className != null && eventRelatedTarget.className.includes("tooltipComponent"))
                        return;
                    this.isSelectOpen = isOpen;
                    event.preventDefault();
                };
                BitmaskEditorComponent.prototype.bitmaskListOnChange = function (bitmask) {
                    bitmask.isChecked = !bitmask.isChecked;
                    if (bitmask.isChecked && bitmask.mask_value === 0x0) {
                        for (var i in this.bitmaskList)
                            if (i > "0")
                                this.bitmaskList[i].isChecked = false;
                        this.bitmask = 0x0;
                    }
                    else if (this.bitmaskList[0].mask_value === 0x0) {
                        this.bitmaskList[0].isChecked = false;
                    }
                    var bitmaskTemp = this.bitmask;
                    if (bitmask.isChecked)
                        bitmaskTemp = bitmaskTemp | bitmask.mask_value;
                    else
                        bitmaskTemp = bitmaskTemp & (~(bitmask.mask_value));
                    this.bitmask = bitmaskTemp;
                    this.bitmaskString = this.decimalToHexConverter(this.bitmask);
                    this.bitmaskOnChange.emit();
                    this.editorFieldcontrol.setValue(this.bitmaskString);
                };
                BitmaskEditorComponent.prototype.decimalToHexConverter = function (inputNumber) {
                    var str = inputNumber.toString(16);
                    return ("0X" + "0".repeat(this.bitmaskLength - str.length) + str).toUpperCase();
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], BitmaskEditorComponent.prototype, "editorField", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", forms_1.FormControl)
                ], BitmaskEditorComponent.prototype, "editorFieldcontrol", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], BitmaskEditorComponent.prototype, "bitmaskOnChange", void 0);
                BitmaskEditorComponent = __decorate([
                    core_1.Component({
                        selector: "bitmaskEditorComponent",
                        styles: ["\n        .panel {\n          margin-bottom: 2px; !important;\n        }\n \t\t\t  .chevron {\n\t\t\t\t  font-size: 16px;\n\t\t\t\t  margin-left: 6px;\n\t\t\t\t  margin-right: 4px;\n\t\t\t  }\n        .input-bitmask {\n          display: block;\n          width: 104px;\n          padding: 2px 12px;\n          font-size: inherit;\n          color: #555;\n          background-color: #fff;\n          background-image: none;\n          border: 1px solid #ccc;\n          border-radius: 4px;\n        }\n        .list-bitmask{\n          padding:8px; \n          position:absolute; \n          background-color:#f8f8f8;\n          border: 1px solid #ccc;\n          z-index:99;\n        }\n        .truncate {\n          width: 350px;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n"],
                        template: "\n\t\t\t  <div class=\"panel\" style=\"width: 134px;background-color: #f8f8f8;border: 1px solid #ccc;\" (mouseenter)=\"selectOpenToggle($event, true)\"  (mouseleave)=\"selectOpenToggle($event, false)\">\n\t\t\t\t  <div>\n\t\t\t\t\t  <div style=\"display: inline-block; width:100px;\">\n\t\t\t\t\t\t  <input type=\"text\" class=\"input-bitmask\" [(ngModel)]=\"bitmaskString\" [readonly]=\"true\" />\n\t\t\t\t\t  </div>\n\t\t\t\t\t  <div style=\"display: inline-block; vertical-align:middle;\">\n\t\t\t\t\t\t  <div *ngIf=\"isSelectOpen\" class=\"glyphicon glyphicon-chevron-up chevron\"></div>\n\t\t\t\t\t\t  <div *ngIf=\"!isSelectOpen\" class=\"glyphicon glyphicon-chevron-down chevron\"></div>\n\t\t\t\t\t  </div>\n\t\t\t\t  </div>\n\t\t\t\t  <div *ngIf=\"isSelectOpen\" class=\"panel list-bitmask\" style=\"\">\n\t\t\t\t\t  <div *ngFor=\"let bitmask of this.bitmaskList\">\n\t\t\t\t\t\t  <label class=\"truncate\">\n\t\t\t\t\t\t\t\t  <input type=\"checkbox\"\n\t\t\t\t\t\t\t\t\t\tclass=\"form-check\"\n\t\t\t\t\t\t\t\t\t\t[checked]=bitmask.isChecked\n\t\t\t\t\t\t\t\t\t\t(change)=\"bitmaskListOnChange(bitmask)\"/>\n\t\t\t\t\t\t\t\t    <span [tooltip]=\"[bitmask.description]\"> {{bitmask.description}}</span>\n\t\t\t\t\t\t  </label>\n\t\t\t\t\t  </div>\n\t\t\t\t  </div>\n\t\t\t  </div>\n\t\t    "
                    }),
                    __metadata("design:paramtypes", [])
                ], BitmaskEditorComponent);
                return BitmaskEditorComponent;
            }());
            exports_1("BitmaskEditorComponent", BitmaskEditorComponent);
        }
    };
});
//# sourceMappingURL=bitmask.editor.component.js.map