﻿import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON>, ViewChildren, NgZone, HostListener } from "@angular/core";
import { FormGroup, FormControl, Validators, ValidationErrors } from '@angular/forms';
import { DialogRef, ModalComponent, CloseGuard, overlayConfigFactory } from "ngx-modialog-7";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { EditorSpecificationObjectDTO, EditorFieldObjectDTO, EditorCommandDTO, TagObjectDTO, TreeNodeDTO} from "../../data/model/models";
import { EditorsService } from "../../data/api/api";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { CollapsiblePanel } from "../../modules/collapsible-panel/collapsible-panel";
import { AlertLogModal } from "../../modules/alert/alert.log.modal";

@Component({
  selector: "dashboardConfigTagEditorModal",
  styles: [`
      .tag-modal::before {
        content: "";
        opacity: 0.1;
        background-image: url(images/background_7.svg), linear-gradient(#000000, #c5c5c5);
        background-size: 30%;
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: -999;
      }
      .button-close {
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .tag-modal {
        position: absolute;
        z-index: -998;
        -webkit-box-shadow: 0 5px 15px rgba(0,0,0,.5);
        box-shadow: 0 5px 15px rgba(0,0,0,.5);
        background-image: linear-gradient(to bottom, rgba(218, 218, 218, 0.06), rgba(0, 0, 0, 0.13));
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #999;
        border: 1px solid rgba(0,0,0,.2);
        border-radius: 6px;
        -webkit-box-shadow: 0 3px 9px rgba(0,0,0,.5);
        box-shadow: 0 3px 9px rgba(0,0,0,.5);
        outline: 0;
        min-width: 400px;
        width: 66vw;
        max-width: 1200px;
      }
      .tag-modal-heading {
        background-color: #d8d8d8;
        padding: 8px 10px 6px 10px;
        font-size: 22px;
        font-weight: bold;
        border-bottom: 1px solid #a31c3f;
        overflow-wrap: break-word;
			  cursor: move;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }
      .tag-modal-content {
        padding: 10px
      }
      .panel-heading {
        padding: 5px 5px;
      }
      fieldset {
        border-radius: 6px;
        border: 1px #a0a0a0 solid;
        padding: 0px 6px 0px 6px;
        margin-bottom: 10px;
        width: 100%
      }
      legend{
        width: auto;
        padding: 0 10px;
        border-bottom: none;
        margin-bottom: 4px;
        font-weight: bold;
        font-size: 15px;
      }
      .help-icon{
        position: absolute;
        top: 12px;
        right: 40px;
        cursor: pointer;
        height: 22px;
        width: 22px;
      }
  `],
  template: `
	<div novalidate class="container-fluid" *ngIf="isDataLoaded">
    <div [style.top.px]="this.panelY" [style.left.px]="this.panelX" class="tag-modal" #modalPopup>
      <div class="tag-modal-heading" (mousedown)="mouseDown($event)">
        <ng-container *ngIf="this.dialog.context.editorCommand == 'MENU_CMD_EDIT'" >
          {{ 'TR_EDIT' | translate }} {{this.dialog.context.objectName}}
        </ng-container>
        <ng-container *ngIf="this.dialog.context.editorCommand.includes('MENU_CMD_SHOW')" >
          {{ this.dialog.context.addTitle | translate }} {{this.dialog.context.objectName}}
        </ng-container>
        <ng-container *ngIf="this.dialog.context.editorCommand != 'MENU_CMD_EDIT' && !this.dialog.context.editorCommand.includes('MENU_CMD_SHOW')">
          {{ this.dialog.context.addTitle | translate }}
        </ng-container>
        <img src="../../../images/help.svg" class="help-icon" (click)="onChangeShowHelp()" alt="{{'TR_HIDE_HELP' | translate}}" title="{{'TR_SHOW_HELP' | translate}}"/>
        <div class="button-close round-button" title="{{'TR_CLOSE' | translate}}" (click)="close()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
	    </div>
      <div class="tag-modal-content">
	      <form (ngSubmit)="save(tagForm.getRawValue(), tagForm.valid)" [formGroup]="tagForm" (keydown.enter)="$event.preventDefault()">
          <ng-container *ngFor="let editorField of editorSpecification.children">
            <dashboardConfigTagEditorComponent [editorCommand]="this.dialog.context.editorCommand" [editorsService]="this.dialog.context.editorsService" [editorField]="editorField" [objectCollectionKind]="objectCollectionKind" [tagForm]="tagForm" [submitted]="submitted" [editorType]="editorType" *ngIf="(editorField.groupPanelName=='' && editorField.fieldsetName=='')" [collapsiblePanels]="collapsiblePanels" [showHelp]="showHelp"></dashboardConfigTagEditorComponent>
            <ng-container *ngIf="(editorField.fieldsetName != '' && editorField.groupPanelName == '')">
              <fieldset *ngIf="editorField.isFieldsetHeader"><legend>{{editorField.fieldsetName | translate}}</legend>
                <ng-container *ngFor="let editorField of editorSpecification.children | editorGroupPipe: null:editorField.fieldsetName">
                  <dashboardConfigTagEditorComponent [editorCommand]="this.dialog.context.editorCommand" [editorsService]="this.dialog.context.editorsService" [editorField]="editorField" [objectCollectionKind]="objectCollectionKind" [tagForm]="tagForm" [submitted]="submitted" [collapsiblePanels]="collapsiblePanels" [editorType]="editorType" [showHelp]="showHelp"></dashboardConfigTagEditorComponent>
                </ng-container>
              </fieldset>
            </ng-container>
            <collapsiblePanel [title]="editorField.groupPanelName| translate" [lsName]="editorField.groupPanelName" *ngIf="editorField.isGroupPanelHeader && editorField.groupPanelName != ''" [islocalStorageEnabled]=false>
              <ng-container *ngFor="let editorField of editorSpecification.children | editorGroupPipe:editorField.groupPanelName:null">
                <dashboardConfigTagEditorComponent [editorCommand]="this.dialog.context.editorCommand" [editorsService]="this.dialog.context.editorsService" [editorField]="editorField" [objectCollectionKind]="objectCollectionKind" [tagForm]="tagForm" [submitted]="submitted" *ngIf="(editorField.fieldsetName == '')" [collapsiblePanels]="collapsiblePanels" [editorType]="editorType" [showHelp]="showHelp"></dashboardConfigTagEditorComponent>
                <ng-container *ngIf="(editorField.fieldsetName != '')">
                  <fieldset *ngIf="editorField.isFieldsetHeader"><legend>{{editorField.fieldsetName | translate}}</legend>
                    <ng-container *ngFor="let editorField of editorSpecification.children | editorGroupPipe: editorField.groupPanelName:editorField.fieldsetName">
                      <dashboardConfigTagEditorComponent [editorCommand]="this.dialog.context.editorCommand" [editorsService]="this.dialog.context.editorsService" [editorField]="editorField" [objectCollectionKind]="objectCollectionKind" [tagForm]="tagForm" [submitted]="submitted" [collapsiblePanels]="collapsiblePanels" [editorType]="editorType" [showHelp]="showHelp"></dashboardConfigTagEditorComponent>
                    </ng-container>
                  </fieldset>
                </ng-container>
              </ng-container>
            </collapsiblePanel>
          </ng-container>
          <div *ngIf="!tagForm.valid && submitted" style="color: #a94442;margin-left: 14px;}">
            {{"TR_MANDATORY_FIELDS_ARE_EMPTY_OR_INCORRECT" | translate}}
          </div>
		      <div *ngIf="!this.editorSpecification.editorKind" class="form-group" style="display: inline-block;width:99%; text-align: center; margin-top: 20px;">
		        <div style="display: table-cell;">
		          <button type="button" class="btn btn-default" (click)="cancel(); $event.preventDefault();"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CANCEL' | translate}}</button>
		        </div>
		        <div style="display: table-cell;width:99%"></div>
		        <div style="display: table-cell;">
		          <button type="submit" class="btn btn-default"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_OK' | translate}}</button>
		        </div>
		      </div>
		      <div *ngIf="(this.editorSpecification.editorKind === this.editorKindEnum.AddItem)" class="form-group" style="display: inline-block;width:99%; text-align: center; margin-top: 20px;">
		        <div style="display: table-cell;">
		          <button type="button" (click)="close(); $event.preventDefault();" class="btn btn-default"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CLOSE' | translate}}</button>
		        </div>
		      </div>
		      <div *ngIf="(this.editorSpecification.editorKind === this.editorKindEnum.CloseSave)" class="form-group" style="display: inline-block;width:99%; text-align: center; margin-top: 20px;">
		        <div style="display: table-cell;">
		          <button type="submit" class="btn btn-default"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CLOSE' | translate}}</button>
		        </div>
		      </div>
		      <alertStatusBarComponent></alertStatusBarComponent>
	      </form>
      </div>
    </div>
	</div>`
})

export class DashboardConfigTagEditorModal implements OnInit, CloseGuard, ModalComponent<TagModalContext> {
  private context: TagModalContext;
  private editorSpecification: EditorSpecificationObjectDTO;
  private controlTypeEnum = EditorFieldObjectDTO.ControlTypeEnum;
  private tagForm: FormGroup;
  private editorType: string;
  private editorKindEnum = EditorSpecificationObjectDTO.EditorKind;
  private objectCollectionKind: string;
  private isDataLoaded: boolean = false;
  private submitted: boolean = false;
  private panelGroupNames: string[] = [];
  private showHelp: boolean = false;
  private px: number;
  private py: number;
  private panelX: number = 0;
  private panelY: number = 0;
  private draggingWindow: boolean;
  @ViewChildren(CollapsiblePanel) collapsiblePanels: QueryList<CollapsiblePanel>;

  constructor(private zone: NgZone, public dialog: DialogRef<TagModalContext>, private authenticationService: AuthenticationService,  private alertService: AlertService, private modal: Modal) {
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-lg";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }
  public ngOnInit(): void {
    this.initEditorData();
  }

  public beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private mouseDown(event: MouseEvent): void {
    this.draggingWindow = true;
    this.px = event.clientX;
    this.py = event.clientY;
    this.zone.runOutsideAngular(() => {
      window.document.addEventListener('mousemove', this.onWindowDrag.bind(this));
    });
  }
  @HostListener('document:mousemove', ['$event'])

  private onWindowDrag(event: MouseEvent): void {
    if (!this.draggingWindow)
      return;
    if (event.clientX >= window.innerWidth || event.clientX <= 0)
      return;
    if (event.clientY >= window.innerHeight || event.clientY <= 0)
      return;

    let offsetX = event.clientX - this.px;
    let offsetY = event.clientY - this.py;

    this.panelX += offsetX;
    this.panelY += offsetY;
    this.px = event.clientX;
    this.py = event.clientY;

    if (event.stopPropagation) event.stopPropagation();
    if (event.preventDefault) event.preventDefault();
    event.cancelBubble = true;
    event.returnValue = false;
  }

  @HostListener('document:mouseup', ['$event'])
  onCornerRelease(event: MouseEvent) {
    this.draggingWindow = false;
  }

  private cancel() {
    this.dialog.close(null);
  }

  private close() {
    this.dialog.close(null);
    if (this.editorSpecification.editorKind === this.editorKindEnum.CloseSave) {
      this.save(this.tagForm.getRawValue(), this.tagForm.valid);
    }
  }

  private onChangeShowHelp(): void {
    this.showHelp = !this.showHelp
  }

  private initEditorData(): void {
    let currentGroupPanelName: string = "";
    let currentFieldsetName: string = "";
    let objectClassName: string = "";
    if (this.dialog.context.node != null) {
      //For mapping we need the class of the object the container holds
      if (this.dialog.context.node.memberClass != "" && this.dialog.context.node.memberClass)
        objectClassName = this.dialog.context.node.memberClass;
      else
        objectClassName = this.dialog.context.node.nodeClassName;
      this.objectCollectionKind = this.dialog.context.node.nodeCollectionKind.toString();
    }
    else if (this.dialog.context.tag != null) {
      objectClassName = this.dialog.context.tag.tagClassName;
      this.objectCollectionKind = TagObjectDTO.getTagPropertyMaskStringValue(this.dialog.context.tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
    }
    this.dialog.context.editorsService.getEditorData(<any>this.dialog.context.editorCommand, this.dialog.context.objectName, this.dialog.context.parentObjectName, true, objectClassName, <any>this.objectCollectionKind).subscribe(
      data => {
        this.isDataLoaded = true;
        this.editorSpecification = data;
        let formControls: any = {};
        this.editorType = this.editorSpecification.editorType;
        this.editorSpecification.children.forEach((editorField) => {
          if ((editorField.controlType == this.controlTypeEnum.Checkbox || editorField.controlType == this.controlTypeEnum.RadioButton) && (editorField.value == "1" || editorField.value.toUpperCase() == "TRUE"))
            formControls[editorField.schemaName] = new FormControl(true);
          else if ((editorField.controlType == this.controlTypeEnum.Checkbox || editorField.controlType == this.controlTypeEnum.RadioButton) && (editorField.value == "0" || editorField.value.toUpperCase() == "FALSE"))
            formControls[editorField.schemaName] = new FormControl(false);
          else if (editorField.controlType == this.controlTypeEnum.OptionsEditor)
            formControls[editorField.schemaName] = new FormControl({ value: editorField.value, disabled: true });
          else
            formControls[editorField.schemaName] = new FormControl(editorField.value);

          formControls[editorField.schemaName].editorField = editorField;
          formControls[editorField.schemaName].enumJson = "";

          if (editorField.isRequired)
            formControls[editorField.schemaName].setValidators([<any>Validators.required]);

          //GroupPanel
          editorField.isGroupPanelHeader = false;
          if (editorField.groupPanelName != "" && currentGroupPanelName != editorField.groupPanelName) //Start Group Panel
            editorField.isGroupPanelHeader = true;
          else if (editorField.groupPanelName == "")
            currentGroupPanelName = "";
          currentGroupPanelName = editorField.groupPanelName;

          //Fieldset
          editorField.isFieldsetHeader = false;
          if (editorField.fieldsetName != "" && currentFieldsetName != editorField.fieldsetName) //Start Fieldset
            editorField.isFieldsetHeader = true;
          else if (editorField.fieldsetName == "")
            currentFieldsetName = "";
          currentFieldsetName = editorField.fieldsetName;
        });
        this.tagForm = new FormGroup(formControls);
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        this.dialog.close(false);
      }
    );
  }

  private save(model: any, isValid: boolean) :void {
    this.submitted = true;
    if (isValid) {
      // Convert everything to string
      for (var k in model) {
        if (model.hasOwnProperty(k)) {
          if (model[k]  && model[k].checkedItem) {
            model[k] = model[k].checkedItem
          }
          else {
            let tempString = String(model[k]);
            model[k] = tempString.replace(/(\r\n|\n|\r)/gm, " ");
          }
        }
      }
      let objectClassName: string = "";
      let objectCollectionKind: string = "";
      if (this.dialog.context.node != null) {
        objectClassName = this.dialog.context.node.nodeClassName;
        objectCollectionKind = this.dialog.context.node.nodeCollectionKind.toString();
      }
      else if (this.dialog.context.tag != null) {
        objectClassName = this.dialog.context.tag.tagClassName;
        objectCollectionKind = TagObjectDTO.getTagPropertyMaskStringValue(this.dialog.context.tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
      }
      let editorResult: EditorCommandDTO = {
        objectName: model.objectName,
        parentObjectName: this.dialog.context.parentObjectName,
        objectClassName: objectClassName,
        objectCollectionKind: objectCollectionKind.toString(),
        oldObjectDataJson: this.editorSpecification.objectDataJson,
        objectDataJson: JSON.stringify(model),
        arg1: "",
        arg2: ""
      };

      this.dialog.context.editorsService.createOrUpdateEditorObject(<any>this.dialog.context.editorCommand, editorResult).subscribe(
        data => {
          this.alertService.success("TR_DATA_SAVED");
          this.dialog.close(data);
        },
        error => {
          if (error.status == 401) {
            this.authenticationService.onLoginFailed("/");
          } else {
            if (error.error.messages && error.error.messages.length > 0)
              this.modal.open(AlertLogModal, overlayConfigFactory({ messages: error.error.messages, title:"TR_ERROR" }, BSModalContext));
            this.alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
          //this.dialog.close("");
        }
      );
    }
    else {
      try{
        Object.keys(this.tagForm.controls).forEach(key => {
          const controlErrors: ValidationErrors = this.tagForm.get(key).errors;
          if (controlErrors != null) {
            Object.keys(controlErrors).forEach(keyError => {
              let formControl: any = this.tagForm.controls[key];
              let groupPanelName = formControl.editorField.groupPanelName;
              if (groupPanelName != "") {
                let collapsiblePanelsfiltered = this.collapsiblePanels.filter(item => item.lsName == groupPanelName);
                if (collapsiblePanelsfiltered.length > 0)
                  collapsiblePanelsfiltered[0].isOpen = true;
              }
            });
          }
        });
      }
      catch (err) {
      }
    } 
  }
}

export class TagModalContext extends BSModalContext {
  public editorCommand: string = "";
  public objectName: string = "";
  public addTitle: string = "";
  public parentObjectName: string = "";
  public tag: TagObjectDTO = null;
  public node: TreeNodeDTO = null;
  public editorsService: EditorsService = null;
}