﻿/// <reference path= "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts" />

export class GlobalJsonUtil {
  public static sortByProperty(jsonObjects: any[], property: string, direction: number = 1): any {
    const clone: any[] = jsonObjects;
    const propPath: any = (property.constructor === Array) ? property : property.split(".");
    if (typeof clone === 'undefined')
      return null;
    clone.sort(function (a, b) {
      for (let p in propPath) {
        if (a[propPath[p]] && b[propPath[p]]) {
          a = a[propPath[p]];
          b = b[propPath[p]];
        }
      }
      // convert numeric strings to integers
      const reA = /[^a-zA-Z]/g;
      const reN = /[^0-9]/g;
      let aA = a.replace(reA, "");
      let bA = b.replace(reA, "");
      if (aA === bA) {
        let aN = parseInt(a.replace(reN, ""), 10);
        let bN = parseInt(b.replace(reN, ""), 10);
        return ((aN < bN) ? -1 * direction : ((aN > bN) ? 1 * direction : 0));
      }
      else {
        return ((aA < bA) ? -1 * direction : ((aA > bA) ? 1 * direction : 0));
      }
    });
    return clone;
  }
}