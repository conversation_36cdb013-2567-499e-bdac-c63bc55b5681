import { Component, HostBinding } from "@angular/core";

@Component({
  selector: "th[resizable]",
  template: `
	<div class="wrapper">
		<div class="content">
			<ng-content></ng-content>
		</div>
		<div class="bar" (resizable)="onResize($event)"></div>
	</div>
			`,
	styles: [`
		:host:last-child .bar {
			 display: none;
		}
		.wrapper {
			 display: flex;
			 justify-content: flex-end;
		}
		.content {
			 flex: 1;
		}
		.bar {
			width: 4px;
			justify-self: flex-end;
			border-left: 1px solid transparent;
			border-right: 1px solid transparent;
			background: #ffffff;
			cursor: ew-resize;
		}
		.bar:hover, .bar:active {
			 opacity: 1;
		}`],
})
export class ResizableComponent {
  @HostBinding("style.width.px")
  width: number | null = null;

  onResize(width: number) {
    this.width = width;
  }
}
