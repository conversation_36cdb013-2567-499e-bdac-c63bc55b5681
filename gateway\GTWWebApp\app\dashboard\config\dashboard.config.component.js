System.register(["@angular/core", "../../data/model/models", "../../modules/alert/alert.service", "../../authentication/authentication.service", "ngx-modialog-7/plugins/bootstrap", "ngx-modialog-7", "./dashboard.config.tag.value.quality.modal", "./dashboard.config.tag.editor.modal", "../../data/api/api", "@ngx-translate/core", "../../modules/panel/panel", "../../modules/alert/alert.log.modal", "../../modules/download/download.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, models_1, alert_service_1, authentication_service_1, bootstrap_1, ngx_modialog_7_1, dashboard_config_tag_value_quality_modal_1, dashboard_config_tag_editor_modal_1, api_1, core_2, panel_1, alert_log_modal_1, download_component_1, DashboardConfigComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (dashboard_config_tag_value_quality_modal_1_1) {
                dashboard_config_tag_value_quality_modal_1 = dashboard_config_tag_value_quality_modal_1_1;
            },
            function (dashboard_config_tag_editor_modal_1_1) {
                dashboard_config_tag_editor_modal_1 = dashboard_config_tag_editor_modal_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (panel_1_1) {
                panel_1 = panel_1_1;
            },
            function (alert_log_modal_1_1) {
                alert_log_modal_1 = alert_log_modal_1_1;
            },
            function (download_component_1_1) {
                download_component_1 = download_component_1_1;
            }
        ],
        execute: function () {
            DashboardConfigComponent = (function () {
                function DashboardConfigComponent(modal, alertService, authenticationService, editorsService, nodesService, tagsService, fileService, translate) {
                    this.modal = modal;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.editorsService = editorsService;
                    this.nodesService = nodesService;
                    this.tagsService = tagsService;
                    this.fileService = fileService;
                    this.translate = translate;
                    this.isTagSelectionRecursive = false;
                    this.tagPurposeFilter = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
                }
                DashboardConfigComponent.prototype.onNodeAction = function (editorCommandsDTOString, parentObjectNameParameter, objectNameParameter, tag, node) {
                    var _this = this;
                    if (parentObjectNameParameter === void 0) { parentObjectNameParameter = ""; }
                    if (objectNameParameter === void 0) { objectNameParameter = ""; }
                    if (tag === void 0) { tag = null; }
                    if (node === void 0) { node = null; }
                    var editorCommandsDTO = models_1.EditorCommandsDTO[editorCommandsDTOString];
                    var objectClassName = "";
                    var objectCollectionKind = "";
                    if (node != null) {
                        objectClassName = node.nodeClassName;
                        objectCollectionKind = node.nodeCollectionKind.toString();
                    }
                    else if (tag != null) {
                        objectClassName = tag.tagClassName;
                        objectCollectionKind = models_1.TagObjectDTO.getTagPropertyMaskStringValue(tag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
                    }
                    switch (editorCommandsDTO.toString()) {
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDEDITWORKSPACEPARAMETERS]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDODBCCLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDODBCITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMODEMPOOL]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMODEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTCPCHANNELOUTSTATIONSLAVE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDREDUNDANTSLAVECHANNEL]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDREDUNDANTMASTERCHANNEL]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTCPCHANNELMASTER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMBPCHANNEL]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDSERIALCHANNELOUTSTATIONSLAVE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDSERIALCHANNELMASTER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMODEMPOOLCHANNEL]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDDNP3UDPTCPCHANNELOUTSTATIONSLAVE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDDNP3UDPTCPCHANNELMASTER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDSESSION]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDSECTOR]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDDATATYPE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMULTIPLEMDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMAPPINGSDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMAPPINGSDOS]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMAPPINGMDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMAPPINGMDOS]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCCLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCAECLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCAEITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMULTIPLEOPCITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMULTIPLEOPCUAITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCAEATTR]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCUACLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCUACERTIFICATE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDEDITOPCUASERVER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDOPCUAITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850CONTROLTOOPCMAPPING]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850SERVER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850CLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850REPORT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850GOOSE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850POLLEDDATASET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850POLLEDPOINTSET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850ITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850WRITABLEPOINT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850WRITABLEPOINTSET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850COMMANDPOINT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61850COMMANDPOINTSET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDGOOSEMONITOR]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61400ALARMSNODE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADD61400ALARMMDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2CLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2SERVER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2CLIENTSERVER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDEDITTASE2DATAPOINTS]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2DSTS]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDMANAGETASE2DATASET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENU_CMD_EDIT_TASE2_EDIT_MODEL]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2LOGICALDEVICE]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2POLLEDPOINTSET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2POLLEDDATASET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2COMMANDPOINT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2COMMANDPOINTSET]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDTASE2ITEM]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSHOWCONFIGTASE2SERVER]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSHOWCONFIGTASE2CLIENT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDEDIT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDWRITEACTION]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDMULTIPOINT]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDEQMDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDINTERNALMDO]:
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDADDUSERDEFINEDFOLDER]:
                            break;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDPERFORMWRITEACTION]:
                            this.performWriteAction(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSETVALUEANDQUALITY]:
                            this.tagsService.getTag(objectNameParameter).subscribe(function (data) {
                                _this.changeValueTags([data]);
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                                }
                            });
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCHANGEVALUE]:
                            this.tagsService.getTag(objectNameParameter).subscribe(function (data) {
                                _this.changeValueTags([data]);
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                                }
                            });
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDELETE]:
                            if (node != null)
                                this.deleteNode(node, node.parentName, objectCollectionKind);
                            if (tag != null)
                                this.deleteTags([tag]);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCONNECTOPCSERVER]:
                            this.disconnectConnectFromOPCServer(objectNameParameter, objectCollectionKind, "Connect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISCONNECTOPCSERVER]:
                            this.disconnectConnectFromOPCServer(objectNameParameter, objectCollectionKind, "Disconnect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCONNECTOPCAESERVER]:
                            this.disconnectConnectFromOPCAEServer(objectNameParameter, objectCollectionKind, "Connect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISCONNECTOPCAESERVER]:
                            this.disconnectConnectFromOPCAEServer(objectNameParameter, objectCollectionKind, "Disconnect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCONNECTOPCUASERVER]:
                            this.disconnectConnectFromOPCUAServer(objectNameParameter, objectCollectionKind, "Connect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISCONNECTOPCUASERVER]:
                            this.disconnectConnectFromOPCUAServer(objectNameParameter, objectCollectionKind, "Disconnect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDOPCUAGETSERVERSTATUS]:
                            this.getStatusFromOPCUAServer(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDREAD61850MDO]:
                            this.read61850Tag(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDREADICCPMDO]:
                            this.readICCPTag(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISCONNECTFROM61850SERVER]:
                            this.disconnectConnectFrom61850Server(objectNameParameter, objectCollectionKind, "Disconnect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCONNECTTO61850SERVER]:
                            this.disconnectConnectFrom61850Server(objectNameParameter, objectCollectionKind, "Connect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDRESETAVERAGEMDOUPDATERATE]:
                            this.resetAverageMdoUpdateRate(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDRESET61850RETRYCONNECTCOUNT]:
                            this.reset61850RetryConnectCount(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDRESTART61850SERVER]:
                            this.restart61850Server(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSAVEMODELTOFILE]:
                            this.save61850ModelToFile(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDENABLERCB]:
                            this.enableDisable61850RCB(objectNameParameter, objectCollectionKind, "EnableReport");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISABLERCB]:
                            this.enableDisable61850RCB(objectNameParameter, objectCollectionKind, "DisableReport");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDENABLEDSTS]:
                            this.enableDisableDSTS(objectNameParameter, objectCollectionKind, "EnableDSTS");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISABLEDSTS]:
                            this.enableDisableDSTS(objectNameParameter, objectCollectionKind, "DisableDSTS");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDVERIFYDATASET]:
                            this.verifyDataset(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDREADDATASET]:
                            this.readDataset(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDREADOPCUAMDO]:
                            this.readOPCUAMDO(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISCONNECTFROMTASE2SERVER]:
                            this.disconnectConnectFromTase2Server(objectNameParameter, objectCollectionKind, "Disconnect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCONNECTTOTASE2SERVER]:
                            this.disconnectConnectFromTase2Server(objectNameParameter, objectCollectionKind, "Connect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCONNECTTODISCOVERTASE2SERVER]:
                            this.disconnectConnectFromTase2Server(objectNameParameter, objectCollectionKind, "ConnectDiscover");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDISCONNECTTASE2SERVER]:
                            this.disconnectRestartTase2Server(objectNameParameter, objectCollectionKind, "Disconnect");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDRESTARTTASE2SERVER]:
                            this.disconnectRestartTase2Server(objectNameParameter, objectCollectionKind, "Restart");
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDOPERATETASE2CONTROL]:
                            this.tagsService.getTag(objectNameParameter).subscribe(function (data) {
                                _this.operateTase2Control(objectNameParameter, objectCollectionKind, [data]);
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                                }
                            });
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSAVETASE2SERVER]:
                            this.saveTase2ServerToFile(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDAUTOCREATETAGS]:
                            this.autoCreateTags(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDDROPONFOLDER]:
                            this.dropOnFolder(parentObjectNameParameter, objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSUBSCRIBEGOOSESTREAM]:
                            this.subscribeUnsubscribeGooseStream(objectNameParameter, true);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDUNSUBSCRIBEGOOSESTREAM]:
                            this.subscribeUnsubscribeGooseStream(objectNameParameter, false);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCREATETHXMLPOINTFILE]:
                            this.createTHXMLPointFile(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDCREATEDTMCSVPOINTFILE]:
                            this.createDTMCSVPointFile(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDSWITCHTORCHANNEL]:
                            this.switchToRChannel(objectNameParameter, objectCollectionKind);
                            return;
                        case models_1.EditorCommandsDTO[models_1.EditorCommandsDTO.MENUCMDREADOPCDAMDO]:
                            this.readOPCDAMDO(objectNameParameter, objectCollectionKind);
                            return;
                        default: {
                            alert("Not implemented!");
                            return;
                        }
                    }
                    this.callEditorModal(editorCommandsDTOString, objectNameParameter, parentObjectNameParameter, tag, node);
                };
                DashboardConfigComponent.prototype.changeAllValueTag = function (tagList) {
                    var tagListToBeChanged = [];
                    tagList.forEach(function (tag) {
                        if (models_1.TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.CAN_CHANGE_VALUE)) {
                            tagListToBeChanged.push(tag);
                        }
                    });
                    if (tagListToBeChanged.length > 0)
                        this.changeValueTags(tagListToBeChanged);
                };
                DashboardConfigComponent.prototype.deleteAllTags = function (tagList) {
                    var tagListToBeDeleted = [];
                    tagList.forEach(function (tag) {
                        if (models_1.TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.CAN_DELETE)) {
                            tagListToBeDeleted.push(tag);
                        }
                    });
                    if (tagListToBeDeleted.length > 0)
                        this.deleteTags(tagListToBeDeleted);
                };
                DashboardConfigComponent.prototype.resetWarningTags = function () {
                    var _this = this;
                    var tagPurposeFilter = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY;
                    var tagValueType = models_1.TagObjectDTO.TagValueTypeEnum.UnsignedLong;
                    this.tagsService.setTagsFiltered(tagValueType, "0", "", "", "", "", tagPurposeFilter, true).subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    });
                };
                DashboardConfigComponent.prototype.onSelectNode = function (nodeObject) {
                    if (nodeObject.virtualNodeName == "warning") {
                        this.tagPurposeFilter = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY;
                        this.isTagSelectionRecursive = true;
                    }
                    else if (nodeObject.virtualNodeName == "health") {
                        this.tagPurposeFilter = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_HEALTH;
                        this.isTagSelectionRecursive = true;
                    }
                    else if (nodeObject.virtualNodeName == "performance") {
                        this.tagPurposeFilter = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE;
                        this.isTagSelectionRecursive = true;
                    }
                    else {
                        this.tagPurposeFilter = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
                        this.isTagSelectionRecursive = false;
                    }
                    this.selectedNode = nodeObject.selectedNode;
                };
                DashboardConfigComponent.prototype.callEditorModal = function (editorCommandsDTOString, objectNameParameter, parentObjectNameParameter, tag, node) {
                    var _this = this;
                    if (objectNameParameter === void 0) { objectNameParameter = ""; }
                    if (parentObjectNameParameter === void 0) { parentObjectNameParameter = ""; }
                    if (tag === void 0) { tag = null; }
                    if (node === void 0) { node = null; }
                    var addTitle = "TR_" + editorCommandsDTOString;
                    var dashboardConfigTagEditorModalRef = this.modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: objectNameParameter, parentObjectName: parentObjectNameParameter, tag: tag, node: node, addTitle: addTitle, editorsService: this.editorsService }, bootstrap_1.BSModalContext));
                    dashboardConfigTagEditorModalRef.result.then(function (data) {
                        if (data != null && data != "") {
                            _this.alertService.success("TR_DATA_SAVED");
                            var result = data.result;
                            var objectParentName = data.objectParentName;
                            var nextEditorCommand = data.nextEditorCommand;
                            var node_1 = { nodeClassName: data.objectClassName, nodeCollectionKind: data.objectCollectionKind };
                            if (result == true && objectParentName != null && objectParentName != "" && nextEditorCommand != null && nextEditorCommand != "")
                                _this.onNodeAction(nextEditorCommand, objectParentName, "", null, node_1);
                            if (data.messages && data.messages.length > 0) {
                                _this.modal.open(alert_log_modal_1.AlertLogModal, ngx_modialog_7_1.overlayConfigFactory({ messages: data.messages, title: addTitle }, bootstrap_1.BSModalContext));
                            }
                        }
                        else if (data == "") {
                            _this.alertService.error("TR_ERROR_DATA_NOT_SAVED");
                        }
                    }, function (error) { _this.alertService.debug(error.toString()); });
                };
                DashboardConfigComponent.prototype.changeValueTags = function (tagList) {
                    var _this = this;
                    if (tagList.length > 0) {
                        var dashboardConfigTagValueQualityModalRef_1 = this.modal.open(dashboard_config_tag_value_quality_modal_1.DashboardConfigTagValueQualityModal, ngx_modialog_7_1.overlayConfigFactory({ tagList: tagList, tagValueList: {} }, bootstrap_1.BSModalContext));
                        dashboardConfigTagValueQualityModalRef_1.result.then(function (result) {
                            if (result) {
                                var tagValueList = dashboardConfigTagValueQualityModalRef_1.context.tagValueList;
                                _this.tagsService.setTags(tagValueList).subscribe(function (data) {
                                    _this.alertService.success("TR_DATA_SAVED");
                                }, function (error) {
                                    if (error.status == 401) {
                                        _this.authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        _this.alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                    }
                                });
                            }
                        }, function (error) { _this.alertService.debug(error.toString()); });
                    }
                };
                DashboardConfigComponent.prototype.deleteTags = function (tagList) {
                    var _this = this;
                    if (tagList.length > 0) {
                        var dashboardUserModalDeleteRef_1;
                        var tagNameList_1 = "";
                        tagList.forEach(function (tag) {
                            var tagName = tag.tagName;
                            if (tag.tagClassName == "GTWOpcUaMDOArrayIndex") {
                                tagName = tag.tagName.split('_[')[0] + " array (and all indexes)";
                                if (!tagNameList_1.includes(tagName)) {
                                    tagNameList_1 += ("<br>&nbsp;&nbsp;&nbsp;-&nbsp;" + tagName);
                                }
                            }
                            else {
                                tagNameList_1 += ("<br>&nbsp;&nbsp;&nbsp;-&nbsp;" + tagName);
                            }
                        });
                        this.translate.get("TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME", { NodeName: tagNameList_1 }).subscribe(function (res) {
                            dashboardUserModalDeleteRef_1 = _this.modal.confirm()
                                .size('lg')
                                .showClose(true)
                                .title(_this.translate.instant('TR_WARNING'))
                                .okBtn(_this.translate.instant('TR_DELETE'))
                                .okBtnClass('btn btn-default')
                                .body("\n\t\t\t\t\t<div class=\"panel panel-warning\">\n\t\t\t\t\t\t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t</div>\n        ")
                                .open();
                        });
                        dashboardUserModalDeleteRef_1.result.then(function (result) {
                            if (result) {
                                var tagCollection = { children: tagList };
                                _this.tagsService.deleteTags(tagCollection).subscribe(function (data) {
                                    _this.translate.get("TR_TAGS_DELETED", { NodeName: data.data }).subscribe(function (res) {
                                        _this.alertService.success(res);
                                    });
                                }, function (error) {
                                    if (error.status == 401) {
                                        _this.authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        _this.alertService.error("TR_ERROR_OBJECT_NOT_DELETED");
                                    }
                                });
                            }
                        }, function () { });
                    }
                };
                DashboardConfigComponent.prototype.read61850Tag = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "Read61850Tag", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_61850_SERVER_READ");
                        }
                    });
                };
                DashboardConfigComponent.prototype.readICCPTag = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "ReadICCPTag", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_ICCP_SERVER_READ");
                        }
                    });
                };
                DashboardConfigComponent.prototype.operateTase2Control = function (objectNameParameter, objectCollectionKind, tagList) {
                    var _this = this;
                    var dashboardConfigTagValueQualityModalRef = this.modal.open(dashboard_config_tag_value_quality_modal_1.DashboardConfigTagValueQualityModal, ngx_modialog_7_1.overlayConfigFactory({ tagList: tagList, tagValueList: {} }, bootstrap_1.BSModalContext));
                    dashboardConfigTagValueQualityModalRef.result.then(function (result) {
                        if (result) {
                            var tagValueList = dashboardConfigTagValueQualityModalRef.context.tagValueList;
                            if (tagValueList != null && tagValueList.tags != null && tagValueList.tags.length != null && tagValueList.tags.length == 1) {
                                _this.editorsService.editorAction("Root", "OperateTase2Control", objectCollectionKind, objectNameParameter, tagValueList.tags[0].tagValue, "", "").subscribe(function (data) {
                                    _this.alertService.success("TR_COMMAND_SUCCESS");
                                }, function (error) {
                                    if (error.status == 401) {
                                        _this.authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        _this.alertService.error("TR_ERROR_TASE2_OPERATE_CONTROL");
                                    }
                                });
                            }
                        }
                    }, function (error) { _this.alertService.debug(error.toString()); });
                };
                DashboardConfigComponent.prototype.disconnectConnectFromOPCServer = function (objectNameParameter, objectCollectionKind, disconnectConnectCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DisconnectConnectFromOPCServer", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_OPC_SERVER_NOT_DISCONNECTED_CONNECTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.disconnectConnectFromOPCAEServer = function (objectNameParameter, objectCollectionKind, disconnectConnectCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DisconnectConnectFromOPCAEServer", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_OPCAE_SERVER_NOT_DISCONNECTED_CONNECTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.disconnectConnectFromOPCUAServer = function (objectNameParameter, objectCollectionKind, disconnectConnectCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DisconnectConnectFromOPCUAServer", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_OPCUA_SERVER_NOT_DISCONNECTED_CONNECTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.getStatusFromOPCUAServer = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "GetOPCUaServerStatus", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        if (data.result) {
                            var OPCServerStatusModalRef_1;
                            _this.translate.get(data.data).subscribe(function (res) {
                                OPCServerStatusModalRef_1 = _this.modal.alert()
                                    .size('lg')
                                    .showClose(true)
                                    .title(_this.translate.instant('TR_MENU_CMD_OPC_UA_SERVER_STATUS'))
                                    .okBtn(_this.translate.instant('TR_CLOSE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t\t      <div class=\"panel panel-primary\">\n\t\t\t\t\t\t      <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-info-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t      </div>\n              ").open();
                            });
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_OPCUA_SERVER_STATUS");
                        }
                    });
                };
                DashboardConfigComponent.prototype.readOPCDAMDO = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "ReadOPCDAMDO", objectCollectionKind, objectNameParameter).subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_READ_OPC_DA_MDO");
                        }
                    });
                };
                DashboardConfigComponent.prototype.disconnectConnectFrom61850Server = function (objectNameParameter, objectCollectionKind, disconnectConnectCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DisconnectConnectFrom61850Server", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_61850_SERVER_NOT_DISCONNECTED_CONNECTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.restart61850Server = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "Restart61850Server", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_61850_SERVER_NOT_RESTARTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.performWriteAction = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "PerformWriteAction", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_PERFORM_WRITE_ACTION");
                        }
                    });
                };
                DashboardConfigComponent.prototype.save61850ModelToFile = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    var fileName = objectNameParameter + ".icd";
                    fileName = fileName.replace(/^.*[\\\/]/, '');
                    this.editorsService.editorAction("Root", "Save61850ModelToFile", objectCollectionKind, objectNameParameter, fileName, "", "").subscribe(function (data) {
                        _this.download.downloadClick(fileName, "CurrentWorkspace");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_61850_MODEL_NOT_SAVED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.saveTase2ServerToFile = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    var fileName = objectNameParameter + ".xml";
                    fileName = fileName.replace(/^.*[\\\/]/, '');
                    this.editorsService.editorAction("Root", "SaveTase2ServerToFile", objectCollectionKind, objectNameParameter, fileName, "", "").subscribe(function (data) {
                        _this.download.downloadClick(fileName, "CurrentWorkspace");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_TASE2_SERVER_NOT_SAVED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.enableDisable61850RCB = function (objectNameParameter, objectCollectionKind, enableDisableCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "EnableDisableRCB", objectCollectionKind, objectNameParameter, enableDisableCommand).subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK");
                        }
                    });
                };
                DashboardConfigComponent.prototype.readDataset = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "ReadDataset", objectCollectionKind, objectNameParameter).subscribe(function (data) {
                        if (data.result)
                            _this.alertService.success("TR_DATASET_READ_SUCCEED");
                        else
                            _this.alertService.error("TR_DATASET_READ_FAILED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_VERIFY_DATASET");
                        }
                    });
                };
                DashboardConfigComponent.prototype.readOPCUAMDO = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "ReadOPCUAMDO", objectCollectionKind, objectNameParameter).subscribe(function (data) {
                        if (data.result)
                            _this.alertService.success("TR_READ_SUCCEED");
                        else
                            _this.alertService.error("TR_READ_FAILED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_VERIFY_DATASET");
                        }
                    });
                };
                DashboardConfigComponent.prototype.enableDisableDSTS = function (objectNameParameter, objectCollectionKind, enableDisableCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "EnableDisableDSTS", objectCollectionKind, objectNameParameter, enableDisableCommand).subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK");
                        }
                    });
                };
                DashboardConfigComponent.prototype.verifyDataset = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "VerifyDataset", objectCollectionKind, objectNameParameter).subscribe(function (data) {
                        if (data.result)
                            _this.alertService.success("TR_DATASET_VERIFICATION_SUCCEED");
                        else
                            _this.alertService.error("TR_DATASET_VERIFICATION_FAILED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_VERIFY_DATASET");
                        }
                    });
                };
                DashboardConfigComponent.prototype.resetAverageMdoUpdateRate = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "ResetAverageMdoUpdateRate", objectCollectionKind, objectNameParameter).subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_RESET_AVERAGE_MDO_UPDATE_RATE");
                        }
                    });
                };
                DashboardConfigComponent.prototype.reset61850RetryConnectCount = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "Reset61850RetryConnectCount", objectCollectionKind, objectNameParameter).subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_RESET_61850_RETRY_CONNECT_COUNT");
                        }
                    });
                };
                DashboardConfigComponent.prototype.deleteNode = function (node, parentObjectNameParameter, objectCollectionKind) {
                    var _this = this;
                    if (node) {
                        var dashboardUserModalDeleteRef_2;
                        var deleteMessage = "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME";
                        if (node.hasChildren)
                            deleteMessage = "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_WITH_CHILDREN_NODENAME";
                        this.translate.get(deleteMessage, { NodeName: node.nodeFullName }).subscribe(function (res) {
                            dashboardUserModalDeleteRef_2 = _this.modal.confirm()
                                .size('lg')
                                .showClose(true)
                                .title(_this.translate.instant('TR_WARNING'))
                                .okBtn(_this.translate.instant('TR_DELETE'))
                                .okBtnClass('btn btn-default')
                                .body("\n\t\t\t\t\t<div class=\"panel panel-warning\">\n\t\t\t\t\t\t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t</div>\n        ")
                                .open();
                        });
                        dashboardUserModalDeleteRef_2.result.then(function (result) {
                            if (result) {
                                var nodeCollection = { children: [node] };
                                if (_this.selectedNode == node)
                                    _this.selectedNode = { nodeFullName: "", nodeCollectionKind: models_1.TreeNodeDTO.NodeCollectionKindEnum.ALL };
                                _this.nodesService.deleteNodes(nodeCollection, parentObjectNameParameter, objectCollectionKind).subscribe(function (data) {
                                    _this.translate.get("TR_OBJECT_NODENAME_DELETED", { NodeName: node.nodeFullName }).subscribe(function (res) {
                                        _this.alertService.success(res);
                                    });
                                }, function (error) {
                                    if (error.status == 401) {
                                        _this.authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        _this.alertService.error("TR_ERROR_OBJECT_NOT_DELETED");
                                    }
                                });
                            }
                        }, function () { });
                    }
                };
                DashboardConfigComponent.prototype.onDropNode = function (dragDropData) {
                    var dragSource = dragDropData[0];
                    var dropTarget = dragDropData[1];
                    if (dragSource == null || dropTarget == null) {
                        this.alertService.error("TR_ERROR_MAPPING");
                        return;
                    }
                    if (dragSource.length != null && dropTarget.nodeClassName == "GTWDataType" && dropTarget.memberClass != "GTWMasterDataObject") {
                        if (dropTarget.nodeCollectionKind === "SDO")
                            this.callEditorModal(models_1.EditorCommandsDTO.MENUCMDADDMAPPINGSDOS.toString(), this.extractFullNameFromTag(dragSource), dropTarget.nodeFullName, null, dropTarget);
                        else
                            this.callEditorModal(models_1.EditorCommandsDTO.MENUCMDADDMAPPINGMDOS.toString(), this.extractFullNameFromTag(dragSource), dropTarget.nodeFullName, null, dropTarget);
                    }
                    else if (dragSource.length != null && dropTarget.nodeClassName == "GTWUserFolder") {
                        this.onNodeAction(models_1.EditorCommandsDTO.MENUCMDDROPONFOLDER.toString(), dropTarget.nodeFullName, this.extractFullNameFromTag(dragSource), dragSource, dropTarget);
                    }
                    else if (dragSource.length != null && (dropTarget.nodeClassName != "GTWDataType" || dropTarget.memberClass == "GTWMasterDataObject")) {
                        if (dropTarget.nodeCollectionKind === "SDO")
                            this.onNodeAction(models_1.EditorCommandsDTO.MENUCMDADDMAPPINGSDO.toString(), dropTarget.nodeFullName, dragSource[0].tagName, dragSource[0], dropTarget);
                        else
                            this.onNodeAction(models_1.EditorCommandsDTO.MENUCMDADDMAPPINGMDO.toString(), dropTarget.nodeFullName, dragSource[0].tagName, dragSource[0], dropTarget);
                    }
                    else {
                        if (dropTarget.nodeCollectionKind === "SDO")
                            this.onNodeAction(models_1.EditorCommandsDTO.MENUCMDADDMAPPINGSDO.toString(), dropTarget.nodeFullName, dragSource.tagName, dragSource, dropTarget);
                        else {
                            if (dropTarget.nodeClassName == "GTWUserFolder") {
                                this.onNodeAction(models_1.EditorCommandsDTO.MENUCMDDROPONFOLDER.toString(), dropTarget.nodeFullName, dragSource.tagName, dragSource, dropTarget);
                            }
                            else {
                                this.onNodeAction(models_1.EditorCommandsDTO.MENUCMDADDMAPPINGMDO.toString(), dropTarget.nodeFullName, dragSource.tagName, dragSource, dropTarget);
                            }
                        }
                    }
                };
                DashboardConfigComponent.prototype.disconnectConnectFromTase2Server = function (objectNameParameter, objectCollectionKind, disconnectConnectCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DisconnectConnectFromTase2Server", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_TASE2_SERVER_NOT_DISCONNECTED_CONNECTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.disconnectRestartTase2Server = function (objectNameParameter, objectCollectionKind, disconnectRestartCommand) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DisconnectRestartTase2Server", objectCollectionKind, objectNameParameter, disconnectRestartCommand, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_TASE2_SERVER_NOT_RESTARTED");
                        }
                    });
                };
                DashboardConfigComponent.prototype.autoCreateTags = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "AutoCreateTags", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_AUTO_CREATE_TAGS");
                        }
                    });
                };
                DashboardConfigComponent.prototype.dropOnFolder = function (folderNameParameter, objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "DropOnFolder", objectCollectionKind, folderNameParameter, objectNameParameter, "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_DROP_ON_FOLDER");
                        }
                    });
                };
                DashboardConfigComponent.prototype.subscribeUnsubscribeGooseStream = function (objectNameParameter, isSubscribing) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "SubscribeUnsubscribeGooseStream", "", objectNameParameter, String(isSubscribing), "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM");
                        }
                    });
                };
                DashboardConfigComponent.prototype.createTHXMLPointFile = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "CreateTHXMLPointFile", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM");
                        }
                    });
                };
                DashboardConfigComponent.prototype.createDTMCSVPointFile = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "CreateDTMCSVPointFile", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM");
                        }
                    });
                };
                DashboardConfigComponent.prototype.switchToRChannel = function (objectNameParameter, objectCollectionKind) {
                    var _this = this;
                    this.editorsService.editorAction("Root", "SwitchToRChannel", objectCollectionKind, objectNameParameter, "", "", "").subscribe(function (data) {
                        _this.alertService.success("TR_COMMAND_SUCCESS");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM");
                        }
                    });
                };
                DashboardConfigComponent.prototype.extractFullNameFromTag = function (tagList) {
                    var tagNameList = [];
                    tagList.forEach(function (tag) {
                        tagNameList.push(tag.tagName);
                    });
                    return tagNameList;
                };
                __decorate([
                    core_1.Input("panel"),
                    __metadata("design:type", panel_1.Panel)
                ], DashboardConfigComponent.prototype, "panel", void 0);
                __decorate([
                    core_1.ViewChild("download", { static: false }),
                    __metadata("design:type", download_component_1.DownloadComponent)
                ], DashboardConfigComponent.prototype, "download", void 0);
                DashboardConfigComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigComponent",
                        styles: ["\n\t\t\t.panel-body{\n\t\t\t\theight: 100%;\n        padding: 0 2px 0 0;\n      }\n      .config {\n        height: 100%;\n\t\t\t\toverflow: auto;\n        background-image: url(../../../images/background_7.svg), linear-gradient(#000000, #c5c5c5);\n        background-size: 20%;\n      }\n      .device-tree {\n\t\t\t\theight: 100%;\n        background-image: linear-gradient(to bottom, rgba(231, 240, 247, 0.85), rgba(231, 240, 247, 0.95));\n      }\n     .tag-grid {\n\t\t\t\theight: 100%;\n        background-image: linear-gradient(to bottom, rgba(231, 240, 247, 0.85), rgba(231, 240, 247, 0.95));\n      }\n      "],
                        template: "\n    <div class=\"panel-body\">\n      <div class=\"config\">\n        <split direction=\"horizontal\">\n          <split-area [size]=\"15\" [minSizePixel]=\"150\" class=\"device-tree\">\n            <dashboardConfigDevicesComponent [panel]=\"panel\" (onSelectedChanged)=\"onSelectNode($event)\" (onDropNode)=\"onDropNode($event)\"></dashboardConfigDevicesComponent>\n          </split-area>\n          <split-area [size]=\"85\" class=\"tag-grid\">\n            <dashboardConfigTagsGridComponent [selectedNode]=\"selectedNode\" [tagPurposeFilter]=\"tagPurposeFilter\" [isRecursive]=\"isTagSelectionRecursive\"></dashboardConfigTagsGridComponent>\n          </split-area>\n        </split>\n      </div>\n    </div>\n    <downloadComponent #download></downloadComponent>\n  "
                    }),
                    __metadata("design:paramtypes", [bootstrap_1.Modal, alert_service_1.AlertService, authentication_service_1.AuthenticationService, api_1.EditorsService, api_1.NodesService, api_1.TagsService, api_1.FileService, core_2.TranslateService])
                ], DashboardConfigComponent);
                return DashboardConfigComponent;
            }());
            exports_1("DashboardConfigComponent", DashboardConfigComponent);
        }
    };
});
//# sourceMappingURL=dashboard.config.component.js.map