﻿import { Injectable } from '@angular/core';
import { SDGConfigDTO, HealthObjectDTO } from "../data/model/models";

@Injectable()

export class GlobalDataService {
  public healthData: HealthObjectDTO

  private config: SDGConfigDTO = null;
  private _isQuickStartVisible: boolean;

	get SDGConfig(): SDGConfigDTO {
			return this.config;
	}
	set SDGConfig(value: SDGConfigDTO) {
		this.config = value;
  }

  get isQuickStartVisible(): boolean {
    return this._isQuickStartVisible;
  }

  set isQuickStartVisible(value: boolean) {
    this._isQuickStartVisible = value;
    localStorage.setItem("SDGIsQuickStartVisible", JSON.stringify(value));
  }

	constructor() {}
}