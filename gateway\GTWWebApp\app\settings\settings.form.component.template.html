﻿<div>
  <form [formGroup]="configForm" novalidate (ngSubmit)="save(configForm.getRawValue(), configForm.valid)">
    <collapsiblePanel [title]="'TR_WEB_SERVER'" [lsName]="'SDGConfigWebPanel'">
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label>{{ 'TR_GATEWAY_API_HOST_AND_HTTP_PORT' | translate }}: </label>
        <div style="display: inline-block;width: 200px;vertical-align:top">
          <input type="text" class="form-control" formControlName="monHost" readonly>
        </div>
        <div style="display: inline-block;width: 4px;font-weight:bold; margin-top:10px">:</div>
        <div [ngClass]="{'has-error': (!configForm.controls.monHttpPort.valid)}" style="display: inline-block;width: 100px;vertical-align:top">
          <input type="number" class="form-control" formControlName="monHttpPort">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.monHttpPort.valid || (configForm.controls.monHttpPort.pristine && !submitted)">
          <br />{{ 'TR_GATEWAY_API_HOST_AND_HTTP_PORT' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL' | translate }}
        </small>
      </div>
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label>{{ 'TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT' | translate }}: </label>
        <div style="display: inline-block;width: 200px;vertical-align:top">
          <input type="text" class="form-control" formControlName="gtwHost" (change)="onChangeGTWHost()">
        </div>
        <div class="asterisk">*</div>
        <div style="display: inline-block;width: 4px;font-weight:bold; margin-top:10px">:</div>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwHttpPort.valid)}" style="display: inline-block;width: 100px;vertical-align:top">
          <input type="number" class="form-control" formControlName="gtwHttpPort">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwHttpPort.valid || (configForm.controls.gtwHttpPort.pristine && !submitted)">
          <br />{{ 'TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL' | translate }}
        </small>
      </div>
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label>{{ 'TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE' | translate }}: </label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwHttpPageBlockSize.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwHttpPageBlockSize">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwHttpPageBlockSize.valid || (configForm.controls.gtwHttpPageBlockSize.pristine && !submitted)">
          {{ 'TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL' | translate }}
        </small>
      </div>
      <div class="form-group">
        <label>{{ 'TR_GATEWAY_WEBSOCKET_UPDATE_RATE' | translate }}: </label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwWsUpdateRate.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwWsUpdateRate">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwWsUpdateRate.valid || (configForm.controls.gtwWsUpdateRate.pristine && !submitted)">
          {{ 'TR_GATEWAY_WEBSOCKET_UPDATE_RATE' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_1' | translate }}
        </small>
      </div>
      <div class="form-group">
        <label>{{ 'TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE' | translate }}: </label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwWsUpdateBlockSize.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwWsUpdateBlockSize">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwWsUpdateBlockSize.valid || (configForm.controls.gtwWsUpdateBlockSize.pristine && !submitted)">
          {{ 'TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_100' | translate }}
        </small>
      </div>
      <div class="form-group">
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="gtwEnableHttpDeflate">
          {{ 'TR_USE_DEFLATE' | translate }}
        </label>
      </div>
      <div class="form-group">
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="gtwUseLocalHostForEngineAndMonitorComms">
          {{ 'TR_USE_LOCALHOST_FOR_COMMS' | translate }}
        </label>
      </div>
    </collapsiblePanel>

    <collapsiblePanel [title]="'TR_SECURITY_PARAMETERS'" [lsName]="'SDGConfigSecurityPanel'">
      <!--<div class="form-group">
      <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
      <label class="form-check-label">
        <input type="checkbox" class="form-check" formControlName="gtwUseWebSSL" (change)="checkboxSSLClick()" [disabled]="(this.authenticationService.role !== 'SU_ROLE')">
        {{ 'TR_USE_WEB_SSL' | translate }}
      </label>
    </div>
    <div class="form-group" *ngIf="isSSLParamVisible">
      <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
      <label>{{ 'TR_GATEWAY_HTTPS_KEY_FILE' | translate }}: </label>
      <div [ngClass]="{'has-error': (!configForm.controls.httpsPrivateKeyFile.valid)}" style="display: inline-block;width: 500px;vertical-align:middle">
        <select class="form-control" formControlName="httpsPrivateKeyFile">
          <option *ngFor="let fileName of httpsPrivateKeyList" [ngValue]="fileName.value">{{fileName.value}}</option>
        </select>
      </div>
      <div class="asterisk">*</div>
    </div>
    <div class="form-group" *ngIf="isSSLParamVisible">
      <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
      <label>{{ 'TR_GATEWAY_HTTPS_KEY_PASS_PHRASE' | translate }}: </label>
      <div [ngClass]="{'has-error': (!configForm.controls.httpsPrivateKeyPassPhrase.valid)}" style="display: inline-block;width: 500px;vertical-align:middle">
        <input type="password" class="form-control" formControlName="httpsPrivateKeyPassPhrase">
      </div>
      <div class="asterisk">*</div>
    </div>
    <div class="form-group" *ngIf="isSSLParamVisible">
      <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
      <label>{{ 'TR_GATEWAY_HTTPS_CERT_FILE' | translate }}: </label>
      <div [ngClass]="{'has-error': (!configForm.controls.httpsCertificateFile.valid)}" style="display: inline-block;width: 500px;vertical-align:middle">
        <select class="form-control" formControlName="httpsCertificateFile">
          <option *ngFor="let fileName of httpsCertificateList" [ngValue]="fileName.value">{{fileName.value}}</option>
        </select>
      </div>
      <div class="asterisk">*</div>
    </div>-->
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label class="form-check-label">{{ 'TR_GATEWAY_ALLOWED_IPS' | translate }}: </label>
        <input type="text" class="form-check" formControlName="gtwAllowedIPs" [disabled]="(this.authenticationService.role !== 'SU_ROLE')">
        <label class="form-check-label">&nbsp;&nbsp;{{ 'TR_GATEWAY_ALLOWED_IPS_HELP' | translate }}</label>
      </div>
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="gtwDoAuth" (change)="checkboxDoAuthClick()" [disabled]="(this.authenticationService.role !== 'SU_ROLE')">
          {{ 'TR_GATEWAY_ENABLE_AUTHENTICATION' | translate }}
        </label>
      </div>
      <div class="form-group">
        <label [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_VIEWER_ROLE' | translate"></label><label>:&nbsp;&nbsp;&nbsp;&nbsp;</label>
        <div style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="checkbox" [checked]="isNoExpirationViewer" (change)="checkboxNoExpirationViewerClick($event)">&nbsp;{{ 'TR_NO_EXPIRATION' | translate }}&nbsp;&nbsp;
        </div>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwAuthExpVIEWER_ROLE.valid)}" style="display: inline-block;width: 110px;vertical-align:middle" [style.visibility]="isNoExpirationViewer ? 'hidden' : 'visible'">
          <input type="number" class="form-control" style="display: inline-block;width: 100px;" formControlName="gtwAuthExpVIEWER_ROLE">
          <div class="asterisk" style="display:inline-block">*</div>
        </div>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpVIEWER_ROLE.valid || (configForm.controls.gtwAuthExpVIEWER_ROLE.pristine && !submitted)" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_VIEWER_ROLE' | translate"></small>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpVIEWER_ROLE.valid || (configForm.controls.gtwAuthExpVIEWER_ROLE.pristine && !submitted)" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small>
      </div>
      <div class="form-group">
        <label [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_OPERATOR_ROLE' | translate"></label><label>:&nbsp;&nbsp;&nbsp;&nbsp;</label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwAuthExpOPERATOR_ROLE.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwAuthExpOPERATOR_ROLE">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpOPERATOR_ROLE.valid || (configForm.controls.gtwAuthExpOPERATOR_ROLE.pristine && !submitted)" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_OPERATOR_ROLE' | translate"></small>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpOPERATOR_ROLE.valid || (configForm.controls.gtwAuthExpOPERATOR_ROLE.pristine && !submitted)" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small>
      </div>
      <div class="form-group">
        <label [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_CONFIGURATOR_ROLE' | translate"></label><label>:&nbsp;&nbsp;&nbsp;&nbsp;</label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwAuthExpCONFIGURATOR_ROLE">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.valid || (configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.pristine && !submitted)" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_CONFIGURATOR_ROLE' | translate"></small>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.valid || (configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.pristine && !submitted)" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small>
      </div>
      <div class="form-group">
        <label [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_SU_ROLE' | translate"></label><label>:&nbsp;&nbsp;&nbsp;&nbsp;</label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwAuthExpSU_ROLE.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwAuthExpSU_ROLE">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpSU_ROLE.valid || (configForm.controls.gtwAuthExpSU_ROLE.pristine && !submitted)" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_SU_ROLE' | translate"></small>
        <small class="text-danger" [hidden]="configForm.controls.gtwAuthExpSU_ROLE.valid || (configForm.controls.gtwAuthExpSU_ROLE.pristine && !submitted)" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small>
      </div>
    </collapsiblePanel>

    <collapsiblePanel [title]="'TR_RUNTIME_PARAMETERS'" [lsName]="'SDGRuntimeParameters'">
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="enableIECFullStackAddressing">
          {{ 'TR_ENABLE_IEC_FULL_STACK_ADDRESSING' | translate }}
        </label>
      </div>
      <div class="form-group">
        <label>{{ 'TR_LIC_GRACE_PERIOD' | translate }}: </label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwLicGracePeriodInMinutes.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwLicGracePeriodInMinutes">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwLicGracePeriodInMinutes.valid || (configForm.controls.gtwLicGracePeriodInMinutes.pristine && !submitted)">
          {{ 'TR_LIC_GRACE_PERIOD' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_1' | translate }}
        </small>
      </div>
    </collapsiblePanel>
    <collapsiblePanel [title]="'TR_LOG_PARAMETERS'" [lsName]="'SDGRuntimeParameters'">
      <div class="form-group">
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="fullLogOnRestart">
          {{ 'TR_GATEWAY_FULL_LOG_ON_RESTART' | translate }}
        </label>
      </div>
      <div class="form-group">
        <div class="glyphicon glyphicon-warning-sign warning-editable" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="mirrorAllToLog">
          {{ 'TR_GATEWAY_MIRROR_ALL_TO_LOG' | translate }}
        </label>
      </div>
      <div class="form-group">
        <label class="form-check-label">
          <input type="checkbox" class="form-check" formControlName="gtwDoAudit">
          {{ 'TR_GATEWAY_ENABLE_AUDIT' | translate }}
        </label>
      </div>
      <div class="form-group">
        <label>{{ 'TR_GATEWAY_MAX_LOG_FILES' | translate }}: </label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwMaxLogFiles.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwMaxLogFiles">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwMaxLogFiles.valid || (configForm.controls.gtwMaxLogFiles.pristine && !submitted)">
          {{ 'TR_GATEWAY_MAX_LOG_FILES' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_10_TO_100' | translate }}
        </small>
      </div>
      <div class="form-group">
        <label>{{ 'TR_GATEWAY_MAX_LOG_BUFFER_ENTRIES' | translate }}: </label>
        <div [ngClass]="{'has-error': (!configForm.controls.gtwMaxLogBufferEntries.valid)}" style="display: inline-block;width: 100px;vertical-align:middle">
          <input type="number" class="form-control" formControlName="gtwMaxLogBufferEntries">
        </div>
        <div class="asterisk">*</div>
        <small class="text-danger" [hidden]="configForm.controls.gtwMaxLogBufferEntries.valid || (configForm.controls.gtwMaxLogBufferEntries.pristine && !submitted)">
          {{ 'TR_GATEWAY_MAX_LOG_BUFFER_ENTRIES' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_0_TO_100000' | translate }}
        </small>
      </div>
    </collapsiblePanel>
    <div>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwHttpPort.valid || (configForm.controls.gtwHttpPort.pristine && !submitted))">{{ 'TR_GATEWAY_API_HOST_AND_HTTP_PORT' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.monHttpPort.valid || (configForm.controls.monHttpPort.pristine && !submitted))">{{ 'TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwHttpPageBlockSize.valid || (configForm.controls.gtwHttpPageBlockSize.pristine && !submitted))">{{ 'TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwWsUpdateRate.valid || (configForm.controls.gtwWsUpdateRate.pristine && !submitted))">{{ 'TR_GATEWAY_WEBSOCKET_UPDATE_RATE' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_1' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwLicGracePeriodInMinutes.valid || (configForm.controls.gtwLicGracePeriodInMinutes.pristine && !submitted))">{{ 'TR_LIC_GRACE_PERIOD' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_1' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwWsUpdateBlockSize.valid || (configForm.controls.gtwWsUpdateBlockSize.pristine && !submitted))">{{ 'TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_100' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpVIEWER_ROLE.valid || (configForm.controls.gtwAuthExpVIEWER_ROLE.pristine && !submitted))" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_VIEWER_ROLE' | translate"></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpVIEWER_ROLE.valid || (configForm.controls.gtwAuthExpVIEWER_ROLE.pristine && !submitted))" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small><br />
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpOPERATOR_ROLE.valid || (configForm.controls.gtwAuthExpOPERATOR_ROLE.pristine && !submitted))" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_OPERATOR_ROLE' | translate"></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpOPERATOR_ROLE.valid || (configForm.controls.gtwAuthExpOPERATOR_ROLE.pristine && !submitted))" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small><br />
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.valid || (configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.pristine && !submitted))" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_CONFIGURATOR_ROLE' | translate"></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.valid || (configForm.controls.gtwAuthExpCONFIGURATOR_ROLE.pristine && !submitted))" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small><br />
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpSU_ROLE.valid || (configForm.controls.gtwAuthExpSU_ROLE.pristine && !submitted))" [innerHTML]="'TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_SU_ROLE' | translate"></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwAuthExpSU_ROLE.valid || (configForm.controls.gtwAuthExpSU_ROLE.pristine && !submitted))" [innerHTML]="'TR_IS_REQUIRED_NUMERICAL_360_TO_36000' | translate"></small><br />
      <small class="text-danger" *ngIf="!(configForm.controls.gtwMaxLogFiles.valid || (configForm.controls.gtwMaxLogFiles.pristine && !submitted))">{{ 'TR_GATEWAY_MAX_LOG_FILES' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_10_TO_100' | translate }}<br /></small>
      <small class="text-danger" *ngIf="!(configForm.controls.gtwMaxLogBufferEntries.valid || (configForm.controls.gtwMaxLogBufferEntries.pristine && !submitted))">{{ 'TR_GATEWAY_MAX_LOG_BUFFER_ENTRIES' | translate }}{{ 'TR_IS_REQUIRED_NUMERICAL_0_TO_100000' | translate }}<br /></small>
    </div>
    <div class="form-group" style="display: inline-block;width:99%; text-align: center;">
      <div style="display: table-cell;">
        <button type="reset" [tooltip]="['TR_CANCEL']" class="btn btn-default btn-sm" (click)="cancel()" [disabled]="configForm.pristine"><img src="../../images/close.svg" class="image-button" />&nbsp;{{ 'TR_CANCEL' | translate }}</button>
      </div>
      <div style="display: table-cell;width:99%"></div>
      <div style="display: table-cell;">
        <div style="white-space: nowrap; padding-right: 6px;">
          <label class="form-check-label">
            <input type="checkbox" class="form-check" formControlName="gtwDoValidateConfig">
            {{ 'TR_VALIDATE_CONFIG' | translate }}
          </label>
        </div>
      </div>
      <div style="display: table-cell;">
        <button type="submit" [tooltip]="['TR_SAVE']" class="btn btn-default btn-sm"><img src="../../images/ok.svg" class="image-button" />&nbsp;{{ 'TR_SAVE' | translate }}</button>
      </div>
    </div>
    <input type="hidden" formControlName="currentWorkSpaceName">
  </form>
</div>