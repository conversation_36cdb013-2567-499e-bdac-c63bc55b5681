import { Output, EventEmitter, HostListener, Directive } from '@angular/core';

@Directive({
  selector: '[myDropTarget]'
})
export class DropTargetDirective {
  constructor() {
  }

  @Output('myDrop') drop = new EventEmitter();

  @HostListener('drop', ['$event'])
  private onDrop(event: any) {
    if (event.dataTransfer.getData('text') != "") {
      event.preventDefault();
      event.stopPropagation();
      const data = JSON.parse(event.dataTransfer.getData('text'));
      this.drop.emit(data);
      if (event && event.currentTarget)
        event.currentTarget.style.background = "";
    }
  }
}