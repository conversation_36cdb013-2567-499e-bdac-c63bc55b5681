System.register(["@angular/core", "./tab.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, tab_component_1, TabsetComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (tab_component_1_1) {
                tab_component_1 = tab_component_1_1;
            }
        ],
        execute: function () {
            TabsetComponent = (function () {
                function TabsetComponent() {
                    this.onSelect = new core_1.EventEmitter();
                }
                TabsetComponent.prototype.ngAfterContentInit = function () {
                    var tabs = this.tabs.toArray();
                    var actives = this.tabs.filter(function (t) { return t.active; });
                    if (actives.length > 1) {
                        console.error("Multiple active tabs set 'active'");
                    }
                    else if (!actives.length && tabs.length) {
                        tabs[0].active = true;
                    }
                };
                TabsetComponent.prototype.tabClicked = function (tab) {
                    var tabs = this.tabs.toArray();
                    tabs.forEach(function (tab) { return tab.active = false; });
                    tab.active = true;
                    this.onSelect.emit(tab);
                };
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", Object)
                ], TabsetComponent.prototype, "onSelect", void 0);
                __decorate([
                    core_1.ContentChildren(tab_component_1.TabComponent),
                    __metadata("design:type", Object)
                ], TabsetComponent.prototype, "tabs", void 0);
                TabsetComponent = __decorate([
                    core_1.Component({
                        selector: "tabset",
                        template: "\n    <ul class=\"nav nav-tabs\">\n      <li *ngFor=\"let tab of tabs\" [class.active]=\"tab.active\">\n        <a (click)=\"tabClicked(tab)\" class=\"btn\" [class.disabled]=\"tab.disabled\">\n          <span>{{tab.title}}</span>\n        </a>\n      </li>\n    </ul>\n    <ng-content></ng-content>\n  ",
                        styles: ["\n    .nav {\n      padding-left: 0;\n      margin-bottom: 0;\n      list-style: none;\n    }\n    .nav-tabs > li {\n      float: left;\n      position: relative;\n      display: block;\n    }\n    .nav-tabs > li > a {\n      position: relative;\n      display: block;\n      padding: 10px 15px;\n    }\n    .nav-tabs {\n      border-bottom:none;\n      margin-bottom: 10px;\n    }\n    .nav-tabs > li.disabled:hover {\n      background: transparent;\n    }\n    .nav-tabs > li.disabled:hover > a {\n      margin-top: 1px;\n    }\n    .nav-tabs > li > a {\n      border: none;\n      color: #000;\n      background: transparent;\n      font-weight: 600;\n      opacity: .5;\n      outline: none;\n      box-shadow: none;\n      cursor:pointer;\n    }\n    .nav-tabs > li > a:hover {\n      border: none;\n      opacity: 1;\n    }\n    .nav-tabs > li > a::after {\n      content: \"\";\n      height: 2px;\n      position: absolute;\n      width: 100%;\n      left: 0px;\n      bottom: -1px;\n      transition: all 250ms ease 0s;\n      transform: scale(0);\n      background-image: linear-gradient(to right, rgba(200, 255, 200, 1), rgba(200, 255, 200, 0.60));\n      color: #fff;\n    }\n    .nav-tabs > li.active > a, .nav-tabs > li.active > a:focus, .nav-tabs > li.active > a:hover {\n      border-width: 0;\n      opacity: 1;\n    }\n    .nav-tabs > li.active > a::after, .nav-tabs > li:hover > a::after {\n      transform: scale(1);\n    }\n\t"]
                    })
                ], TabsetComponent);
                return TabsetComponent;
            }());
            exports_1("TabsetComponent", TabsetComponent);
        }
    };
});
//# sourceMappingURL=tabset.component.js.map