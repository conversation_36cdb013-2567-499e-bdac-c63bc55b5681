System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, KeysPipe;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            KeysPipe = (function () {
                function KeysPipe() {
                }
                KeysPipe.prototype.transform = function (keyPairs, args) {
                    var keyPairsList = [];
                    for (var keyPair in keyPairs) {
                        var valueNameLength = keyPairsList.length;
                        try {
                            if (valueNameLength === 0) {
                                keyPairsList.push({ key: keyPair, value: keyPairs[keyPair].replace(/_/g, ' ') });
                            }
                            else {
                                if (keyPairsList[valueNameLength - 1]["key"] !== keyPair && keyPairsList[valueNameLength - 1]["value"].replace(/_/g, ' ') !== keyPair.replace(/_/g, ' ')) {
                                    keyPairsList.push({ key: keyPair, value: keyPairs[keyPair].replace(/_/g, ' ') });
                                }
                            }
                        }
                        catch (err) { }
                    }
                    return keyPairsList;
                };
                KeysPipe.prototype.transformJson = function (values, args) {
                    var keys = [];
                    if (values)
                        values.sort();
                    try {
                        values.forEach(function (item) {
                            var key = Object.keys(item)[0];
                            keys.push({ key: key, value: item[key] });
                        });
                    }
                    catch (err) { }
                    keys = this.sort(keys);
                    return keys;
                };
                KeysPipe.prototype.transformEditorGroup = function (items, filter) {
                    var result = [];
                    try {
                        if (!items || !filter) {
                            return items;
                        }
                        result = items.filter(function (item) { return item.groupName == filter; });
                    }
                    catch (err) { }
                    return result;
                };
                KeysPipe.prototype.sort = function (jsonObjects) {
                    var clone = jsonObjects;
                    clone.sort(function (a, b) {
                        if (a["key"] && b["key"]) {
                            a = a["key"];
                            b = b["key"];
                        }
                        var reA = /[^a-zA-Z]/g;
                        var reN = /[^0-9]/g;
                        var aA = a.toString().replace(reA, "");
                        var bA = b.toString().replace(reA, "");
                        if (aA === bA) {
                            var aN = parseInt(a.toString().replace(reN, ""), 10);
                            var bN = parseInt(b.toString().replace(reN, ""), 10);
                            return aN === bN ? 0 : aN > bN ? 1 : -1;
                        }
                        else {
                            return aA > bA ? 1 : -1;
                        }
                    });
                    return clone;
                };
                KeysPipe = __decorate([
                    core_1.Pipe({ name: 'keys' })
                ], KeysPipe);
                return KeysPipe;
            }());
            exports_1("KeysPipe", KeysPipe);
        }
    };
});
//# sourceMappingURL=keys.pipe.js.map