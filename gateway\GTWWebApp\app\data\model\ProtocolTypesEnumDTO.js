System.register([], function (exports_1, context_1) {
    "use strict";
    var ProtocolTypesEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (ProtocolTypesEnumDTO) {
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["S101"] = 'GTWTYPES_PROTOCOL_S101'] = "S101";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["S102"] = 'GTWTYPES_PROTOCOL_S102'] = "S102";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["S103"] = 'GTWTYPES_PROTOCOL_S103'] = "S103";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["S104"] = 'GTWTYPES_PROTOCOL_S104'] = "S104";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["SDNP"] = 'GTWTYPES_PROTOCOL_SDNP'] = "SDNP";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["M101"] = 'GTWTYPES_PROTOCOL_M101'] = "M101";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["M102"] = 'GTWTYPES_PROTOCOL_M102'] = "M102";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["M103"] = 'GTWTYPES_PROTOCOL_M103'] = "M103";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["M104"] = 'GTWTYPES_PROTOCOL_M104'] = "M104";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["MDNP"] = 'GTWTYPES_PROTOCOL_MDNP'] = "MDNP";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["MMB"] = 'GTWTYPES_PROTOCOL_MMB'] = "MMB";
                ProtocolTypesEnumDTO[ProtocolTypesEnumDTO["SMB"] = 'GTWTYPES_PROTOCOL_SMB'] = "SMB";
            })(ProtocolTypesEnumDTO || (ProtocolTypesEnumDTO = {}));
            exports_1("ProtocolTypesEnumDTO", ProtocolTypesEnumDTO);
        }
    };
});
//# sourceMappingURL=ProtocolTypesEnumDTO.js.map