System.register(["./audit.service", "./auth.service", "./config.service", "./editorContextMenu.service", "./editors.service", "./manage.service", "./mappings.service", "./nodes.service", "./tags.service", "./file.service", "./misc.service", "./log.service", "./help.service", "./workspace.service"], function (exports_1, context_1) {
    "use strict";
    var audit_service_1, auth_service_1, config_service_1, editorContextMenu_service_1, editors_service_1, manage_service_1, mappings_service_1, nodes_service_1, tags_service_1, file_service_1, misc_service_1, log_service_1, help_service_1, workspace_service_1, APIS;
    var __moduleName = context_1 && context_1.id;
    var exportedNames_1 = {
        "APIS": true
    };
    function exportStar_1(m) {
        var exports = {};
        for (var n in m) {
            if (n !== "default" && !exportedNames_1.hasOwnProperty(n)) exports[n] = m[n];
        }
        exports_1(exports);
    }
    return {
        setters: [
            function (audit_service_2_1) {
                exportStar_1(audit_service_2_1);
                audit_service_1 = audit_service_2_1;
            },
            function (auth_service_2_1) {
                exportStar_1(auth_service_2_1);
                auth_service_1 = auth_service_2_1;
            },
            function (config_service_2_1) {
                exportStar_1(config_service_2_1);
                config_service_1 = config_service_2_1;
            },
            function (editorContextMenu_service_2_1) {
                exportStar_1(editorContextMenu_service_2_1);
                editorContextMenu_service_1 = editorContextMenu_service_2_1;
            },
            function (editors_service_2_1) {
                exportStar_1(editors_service_2_1);
                editors_service_1 = editors_service_2_1;
            },
            function (manage_service_2_1) {
                exportStar_1(manage_service_2_1);
                manage_service_1 = manage_service_2_1;
            },
            function (mappings_service_2_1) {
                exportStar_1(mappings_service_2_1);
                mappings_service_1 = mappings_service_2_1;
            },
            function (nodes_service_2_1) {
                exportStar_1(nodes_service_2_1);
                nodes_service_1 = nodes_service_2_1;
            },
            function (tags_service_2_1) {
                exportStar_1(tags_service_2_1);
                tags_service_1 = tags_service_2_1;
            },
            function (file_service_2_1) {
                exportStar_1(file_service_2_1);
                file_service_1 = file_service_2_1;
            },
            function (misc_service_2_1) {
                exportStar_1(misc_service_2_1);
                misc_service_1 = misc_service_2_1;
            },
            function (log_service_2_1) {
                exportStar_1(log_service_2_1);
                log_service_1 = log_service_2_1;
            },
            function (help_service_2_1) {
                exportStar_1(help_service_2_1);
                help_service_1 = help_service_2_1;
            },
            function (workspace_service_2_1) {
                exportStar_1(workspace_service_2_1);
                workspace_service_1 = workspace_service_2_1;
            }
        ],
        execute: function () {
            exports_1("APIS", APIS = [audit_service_1.AuditService, auth_service_1.AuthService, config_service_1.ConfigService, editorContextMenu_service_1.EditorContextMenuService, editors_service_1.EditorsService, mappings_service_1.MappingsService, manage_service_1.ManageService, nodes_service_1.NodesService, tags_service_1.TagsService, file_service_1.FileService, misc_service_1.MiscService, log_service_1.LogService, help_service_1.HelpService, workspace_service_1.WorkspaceService]);
        }
    };
});
//# sourceMappingURL=api.js.map