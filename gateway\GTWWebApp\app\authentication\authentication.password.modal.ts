﻿import { Component, OnInit } from "@angular/core";
import { Router } from '@angular/router';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { AlertService } from "../modules/alert/alert.service";
import { AuthService } from "../data/api/api";
import { AuthenticationService } from "../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "authenticationPasswordModal",
  providers: [AuthService],
  styles: [`
      .password {
        display:inline-block;
        width:90%;
        margin-bottom: 0px;
        margin-top: 8px;
      }
      .status-bar{
        margin: 2px;
      }
    `],
  template: `
	  <div class="container-fluid">
		  <div class="modal-heading">{{'TR_CHANGE_PASSWORD' | translate}}</div>
		  <div class="modal-body">
		    <form [formGroup]="editPasswordForm" novalidate>
		      <div class="form-group password" [ngClass]="{'has-error': (!editPasswordForm.controls.currentPassword.valid)}">
			      <label>{{'TR_CURRENT_PASSWORD' | translate}}</label>
			      <input type="password" class="form-control" formControlName="currentPassword" show-password>
		      </div>
			    <small class="text-danger" [hidden]="editPasswordForm.controls.currentPassword.valid || (editPasswordForm.controls.currentPassword.pristine && !submitted)">
            {{'TR_THE_CURRENT_PASSWORD_IS_REQUIRED' | translate}}
			    </small>
		      <div class="form-group password" [ngClass]="{'has-error': (!editPasswordForm.controls.newPassword.valid)}">
			      <label>{{'TR_NEW_PASSWORD' | translate}}</label>
            <img class="help-icon" src="../../images/help.svg" title="{{'TR_PASSWORD_COMPLEXITY_REQUIREMENTS' | translate}}">
			      <input type="password" class="form-control" formControlName="newPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@$#!\\-+*_%.])\\S{8,24}$" show-password>
		      </div>
			    <small class="text-danger" [hidden]="editPasswordForm.controls.newPassword.valid || (editPasswordForm.controls.newPassword.pristine && !submitted)" [innerHtml]="'TR_YOUR_PASSWORD_DOESNT_MEET_COMPLEXITY_REQUIREMENTS' | translate"></small>
		      <div class="form-group password" [ngClass]="{'has-error': (!editPasswordForm.controls.newPasswordConfirm.valid)}">
			      <label>{{'TR_CONFIRM_NEW_PASSWORD' | translate}}</label>
			      <input type="password" class="form-control" formControlName="newPasswordConfirm" show-password>
		      </div>
			    <small class="text-danger" [hidden]="!editPasswordForm.errors?.mismatch || (editPasswordForm.controls.newPasswordConfirm.pristine && !submitted)">
            {{'TR_THE_TWO_PASSWORD_FIELDS_DIDN_T_MATCH' | translate}}
			    </small>
		      <div style="display: inline-block;width:99%; text-align: center; margin-top: 20px;">
			      <div style="display: table-cell;" *ngIf="!isPasswordReset">
              <button class="btn btn-default" (click)="cancel()">{{'TR_CANCEL' | translate}}</button>
			      </div>
			      <div style="display: table-cell;width:99%"></div>
			      <div style="display: table-cell;">
			        <button (click)="save(editPasswordForm.value, editPasswordForm.valid)" class="btn btn-default">{{'TR_SAVE' | translate}}</button>
			      </div>
		      </div>
          <br>
		      <alertStatusBarComponent class="status-bar"></alertStatusBarComponent>
		    </form>
      </div>
	</div>`
})
export class AuthenticationPasswordModal implements CloseGuard, ModalComponent<PasswordModalContext> {
  public submitted: boolean; // keep track on whether form is submitted
  private context: PasswordModalContext;
  private editPasswordForm: FormGroup; // our model driven form
  private authenticationService: AuthenticationService = null;
  private username: string = "";
  private isPasswordReset: boolean = false;

  constructor(public dialog: DialogRef<PasswordModalContext>, private router: Router, private alertService: AlertService, private authService: AuthService, private translate: TranslateService) {
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-sm";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }

  public ngOnInit(): void {
    this.authenticationService = this.dialog.context.authenticationService;
    this.username = this.dialog.context.username;
    this.authService.configuration = this.authenticationService.userApiConfig;
    this.isPasswordReset = this.dialog.context.isPasswordReset;

    this.editPasswordForm = new FormGroup({
      currentPassword: new FormControl("", [<any>Validators.required]),
      newPassword: new FormControl("", [<any>Validators.required, <any>Validators.minLength(6)]),
      newPasswordConfirm: new FormControl("", [<any>Validators.required, <any>Validators.minLength(6)]),
    }, this.passwordMatchValidator);
  }

  private passwordMatchValidator(formGroup: FormGroup) {
    return formGroup.get("newPassword").value === formGroup.get("newPasswordConfirm").value ? null : { "mismatch": true };
  }

  beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private cancel() {
    this.alertService.clearMessage();
    this.dialog.close();
  }

  private save(passwordForm: passwordFormModel, isValid: boolean) {
    this.submitted = true; // set form submit to true
    if (isValid) {
      this.authService.changeUserPassword(this.username, this.editPasswordForm.get("currentPassword").value, this.editPasswordForm.get("newPassword").value).subscribe(
        data => {
          this.alertService.success("TR_DATA_SAVED");
          this.authService.logoffUser()
            .subscribe(
              data => {
                this.authenticationService.logoff();
              },
              error => {
                this.alertService.debug(error.message.toString());
              },
              () => location.reload()
            );
        },
        error => {
          if (error.status == 401) {
            this.authenticationService.onLoginFailed("/");
            this.dialog.close();
          }
          else if (error.status == 403) {
            this.translate.get("TR_CURRENT_PASSWORD_DOESNT_MATCH").subscribe(res => {
              this.alertService.error(res);
            }); 
          }
          else {
            this.translate.get("TR_ERROR", { error: error.error }).subscribe(res => {
              this.alertService.error(res);
            }); 
          }
        }
      );
    }
  }
}
export interface passwordFormModel {
  currentPassword?: string;
  newPassword?: string;
  newPasswordConfirm?: Array<string>;
}

export class PasswordModalContext extends BSModalContext {
  authenticationService: AuthenticationService = null;
  username: string = "";
  isPasswordReset: boolean = false;
}
