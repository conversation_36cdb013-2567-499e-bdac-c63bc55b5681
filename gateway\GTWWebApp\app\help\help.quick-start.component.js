System.register(["@angular/core", "../global/global.data.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, global_data_service_1, HelpQuickStartComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            }
        ],
        execute: function () {
            HelpQuickStartComponent = (function () {
                function HelpQuickStartComponent(globalDataService) {
                    this.globalDataService = globalDataService;
                    this.isQuickStartVisible = false;
                }
                HelpQuickStartComponent.prototype.ngOnInit = function () {
                    if (localStorage.getItem("SDGIsQuickStartVisible") === "false") {
                        this.globalDataService.isQuickStartVisible = false;
                    }
                    else {
                        this.globalDataService.isQuickStartVisible = true;
                        this.isQuickStartVisible = true;
                    }
                };
                HelpQuickStartComponent.prototype.show = function () {
                    this.isQuickStartVisible = true;
                };
                HelpQuickStartComponent.prototype.close = function () {
                    this.isQuickStartVisible = false;
                };
                HelpQuickStartComponent.prototype.quickStartShowHideOnChange = function () {
                    this.globalDataService.isQuickStartVisible = !this.globalDataService.isQuickStartVisible;
                    if (!this.globalDataService.isQuickStartVisible)
                        this.isQuickStartVisible = false;
                };
                HelpQuickStartComponent = __decorate([
                    core_1.Component({
                        selector: "helpQuickStartComponent",
                        styles: ["\n      .panel {\n        position: absolute;\n        z-index: 999;\n        max-width: 500px;\n        margin: auto;\n        width: 50%;\n        top: 80px;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n      .panel-heading{\n        padding: 4px;\n      }\n      .panel-body{\n        padding: 8px;\n      }\n      .div-image{\n        max-width: 220px;\n        display:inline-block;\n        text-align: center;\n      }\n      .image-gif{\n        max-width: 50%;\n        max-height: 50%;\n        border: solid 1px gray;\n        display: block;\n        margin-right: auto;\n        margin-left: auto;\n      }\n      .context-menu-image-gif:hover{\n        background: url('../images/help/contextMenuPlaying.gif');\n        background-size: auto;\n        background-repeat: no-repeat;\n        padding: 440px 470px 0px 0px;\n        position: absolute;\n      }\n      .mapping-image-gif:hover{\n        background: url(../images/help/mappingPlaying.gif);\n        background-size: auto;\n        background-repeat: no-repeat;\n        padding: 440px 470px 0px 0px;\n        position: absolute;\n      }\n      .dashboard-image-gif:hover{\n        background: url(../images/help/dashboardPlaying.gif);\n        background-size: 100%;\n        background-repeat: no-repeat;\n        padding: 600px 787px 0px 0px;\n        margin:-200px;\n        position: absolute;\n      }\n      .search-tags-image-gif:hover{\n        background: url(../images/help/searchTagsPlaying.gif);\n        background-size: 100%;\n        background-repeat: no-repeat;\n        padding: 600px 787px 0px 0px;\n        margin:-200px;\n        position: absolute;\n      }\n      .button-close {\n        position: absolute;\n        top: 6px;\n        right: 6px;\n      }\n      "
                        ],
                        template: "\n      <div class=\"panel-main panel panel-default\" *ngIf=\"isQuickStartVisible\" style=\"min-width:400px\">\n        <div class=\"panel-heading\">\n          {{'TR_QUICK_START' | translate}}\n          <div class=\"button-close round-button\" title=\"{{'TR_CLOSE' | translate}}\" (click)=\"close()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n        </div>\n        <div class=\"panel-body\">\n          <h4>{{'TR_QUICK_START_TOPICS' | translate}}</h4>\n          <ul>\n            <li><a href=\"../../help/Content/QUICK-START/Dashboard-Management.htm\" target=\"_blank\">{{'TR_QUICK_START_DASHBOARD_MANAGEMENT' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/Default-Dashboard.htm\" target=\"_blank\">{{'TR_QUICK_START_DEFAULT_DASHBOARD' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/Device-Tree-View.htm\" target=\"_blank\">{{'TR_QUICK_START_DEVICE_TREE_VIEW' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/Tags-Grid.htm\" target=\"_blank\">{{'TR_QUICK_START_TAGS_GRIDS' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/Mapping.htm\" target=\"_blank\">{{'TR_QUICK_START_MAPPING' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/Editor.htm\" target=\"_blank\">{{'TR_QUICK_START_EDITOR' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/SDG-Workspaces.htm\" target=\"_blank\">{{'TR_QUICK_START_WORKSPACES' | translate}}</a></li>\n            <li><a href=\"../../help/Content/QUICK-START/SDG-Settings.htm\" target=\"_blank\">{{'TR_QUICK_START_SYSTEM_SETTINGS' | translate}}</a></li>\n            <li><a href=\"../../help/Content/USERS/Enable-Users.htm\" target=\"_blank\">{{'TR_QUICK_START_USERS_MANAGEMENT' | translate}}</a></li>\n            <li><a href=\"../../help/Content/LICENSING/Trial-License.htm\" target=\"_blank\">{{'TR_QUICK_START_LICENSES_MANAGEMENT' | translate}}</a></li>\n          </ul>\n          <div class=\"div-image\">\n            <h4>{{'TR_HOW_TO_ADD_A_DEVICE' | translate}}</h4>\n            <img class=\"image-gif context-menu-image-gif\" src=\"../images/help/contextMenuPaused.gif\">\n          </div>\n          <div class=\"div-image\">\n            <h4>{{'TR_HOW_TO_MAP_POINTS' | translate}}</h4>\n            <img class=\"image-gif mapping-image-gif\" src=\"../images/help/mappingPaused.gif\">\n          </div>\n          <div class=\"div-image\">\n            <h4>{{'TR_HOW_ADD_VIEWS_TO_DASHBOARD' | translate}}</h4>\n            <img class=\"image-gif dashboard-image-gif\" src=\"../images/help/dashboardPaused.gif\">\n          </div>\n          <div class=\"div-image\">\n            <h4>{{'TR_HOW_SEARCH_TAGS' | translate}}</h4>\n            <img class=\"image-gif search-tags-image-gif\" src=\"../images/help/searchTagsPaused.gif\">\n          </div>\n          <br>\n          <br>\n          <label class=\"form-check-label\">\n            <input type=\"checkbox\" class=\"form-check\" [checked]=\"this.globalDataService.isQuickStartVisible\" (change)=\"quickStartShowHideOnChange()\" />\n            {{'TR_SHOW_QUICK_START_AT_STARTUP' | translate}}\n          </label>\n        </div>\n      </div>"
                    }),
                    __metadata("design:paramtypes", [global_data_service_1.GlobalDataService])
                ], HelpQuickStartComponent);
                return HelpQuickStartComponent;
            }());
            exports_1("HelpQuickStartComponent", HelpQuickStartComponent);
        }
    };
});
//# sourceMappingURL=help.quick-start.component.js.map