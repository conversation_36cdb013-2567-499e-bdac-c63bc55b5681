System.register(["@angular/core", "ngx-modialog-7/plugins/bootstrap", "ngx-modialog-7", "./authentication/authentication.password.modal", "./authentication/authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, bootstrap_1, ngx_modialog_7_1, authentication_password_modal_1, authentication_service_1, AppPasswordComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (authentication_password_modal_1_1) {
                authentication_password_modal_1 = authentication_password_modal_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            AppPasswordComponent = (function () {
                function AppPasswordComponent(modal, authenticationService) {
                    this.modal = modal;
                    this.authenticationService = authenticationService;
                }
                AppPasswordComponent.prototype.onChangePassword = function () {
                    this.modal.open(authentication_password_modal_1.AuthenticationPasswordModal, ngx_modialog_7_1.overlayConfigFactory({ authenticationService: this.authenticationService, username: this.authenticationService.userApiConfig.username }, bootstrap_1.BSModalContext));
                };
                AppPasswordComponent = __decorate([
                    core_1.Component({
                        selector: "appPasswordComponent",
                        template: "\n\t\t\t<div (click)=\"onChangePassword()\">{{'TR_CHANGE_PASSWORD' | translate}}</div>"
                    }),
                    __metadata("design:paramtypes", [bootstrap_1.Modal, authentication_service_1.AuthenticationService])
                ], AppPasswordComponent);
                return AppPasswordComponent;
            }());
            exports_1("AppPasswordComponent", AppPasswordComponent);
        }
    };
});
//# sourceMappingURL=app.password.component.js.map