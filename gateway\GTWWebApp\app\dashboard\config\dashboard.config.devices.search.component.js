System.register(["@angular/core", "../../modules/alert/alert.service", "../../authentication/authentication.service", "../../data/api/api"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, alert_service_1, authentication_service_1, api_1, DashboardConfigDevicesSearchComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            }
        ],
        execute: function () {
            DashboardConfigDevicesSearchComponent = (function () {
                function DashboardConfigDevicesSearchComponent(alertService, nodesService, authenticationService) {
                    this.alertService = alertService;
                    this.nodesService = nodesService;
                    this.authenticationService = authenticationService;
                    this.onSearchChanged = new core_1.EventEmitter();
                    this.searchValue = "";
                }
                DashboardConfigDevicesSearchComponent.prototype.onSearchNode = function () {
                    var _this = this;
                    try {
                        this.nodesService.getNodes(null, this.searchValue, false, true, "ALL").subscribe(function (data) {
                            var isNodeTreeFromSearch = false;
                            if (_this.searchValue != "")
                                isNodeTreeFromSearch = true;
                            _this.onSearchChanged.emit({ data: data, isNodeTreeFromSearch: isNodeTreeFromSearch });
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                            }
                        });
                    }
                    catch (err) {
                        return null;
                    }
                };
                DashboardConfigDevicesSearchComponent.prototype.onClearSearchNode = function () {
                    this.searchValue = "";
                    this.onSearchNode();
                };
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDevicesSearchComponent.prototype, "onSearchChanged", void 0);
                DashboardConfigDevicesSearchComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigDevicesSearchComponent",
                        styles: ["      \n      .small-input-text {\n        height: 24px !important;\n        font-size: 14px !important;\n        color: #337ab7;\n        font-style: italic;\n        font-weight: bolder;\n        margin-top: -1px;\n      }\n      .small-input-button {\n        margin-top: -2px;\n        display: inline-block;\n      }      \n      .search-menu {\n        border: 1px solid #f5f5f5;\n        border-radius: 3px;\n        padding: 2px;\n        width:100%\n      }\n  "],
                        template: "\n    <div class=\"shadow-bg search-menu\">\n        <div style=\"display:table-cell;width:99%\">\n          <input placeholder=\"{{'TR_NODE_NAME' | translate}}\" type=\"text\" title=\"{{'TR_ENTER_A_PARTIAL_OR_COMPLETE_NODE_NAME' | translate}}\" class=\"form-control small-input-text\" [(ngModel)]=\"searchValue\">\n        </div>\n        <div style=\"display: table-cell;padding-left: 4px;vertical-align:middle;min-width:56px\">\n          <div class=\"round-button\" title=\"{{'TR_SEARCH' | translate}}\" (click)=\"onSearchNode()\"><img [src]=\"'../../images/magnifier.svg'\" class=\"image-button\"/></div>\n          <div class=\"round-button\" title=\"{{'TR_CLEAR_SEARCH' | translate}}\" (click)=\"onClearSearchNode()\"><img [src]=\"'../../images/delete.svg'\" class=\"image-button\"/></div>\n        </div>\n    </div>\n  "
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, api_1.NodesService, authentication_service_1.AuthenticationService])
                ], DashboardConfigDevicesSearchComponent);
                return DashboardConfigDevicesSearchComponent;
            }());
            exports_1("DashboardConfigDevicesSearchComponent", DashboardConfigDevicesSearchComponent);
        }
    };
});
//# sourceMappingURL=dashboard.config.devices.search.component.js.map