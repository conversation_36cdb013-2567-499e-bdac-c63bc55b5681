System.register(["@angular/core", "rxjs", "../configuration", "../../modules/alert/alert.service", "@ngx-translate/core", "rxjs/operators"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, rxjs_1, configuration_1, alert_service_1, core_2, operators_1, NodesWSApi;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            }
        ],
        execute: function () {
            NodesWSApi = (function () {
                function NodesWSApi(alertService, translate) {
                    this.alertService = alertService;
                    this.translate = translate;
                    this.basePath = "ws://localhost/rest";
                    this.configuration = new configuration_1.Configuration();
                }
                NodesWSApi.prototype.openWebsocket = function () {
                    var _this = this;
                    var websocket = new WebSocket(this.basePath + "/getNodes?token=" + this.configuration.apiKeys["Authorization"]);
                    return rxjs_1.Observable.create(function (observer) {
                        websocket.onopen = function (openEvent) {
                            _this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getNodes" }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                            observer.next(websocket);
                            observer.complete();
                        };
                    });
                };
                NodesWSApi.prototype.getNodesData = function (websocket) {
                    var _this = this;
                    return rxjs_1.Observable.create(function (observer) {
                        websocket.onmessage = function (evt) {
                            observer.next(evt);
                        };
                        websocket.onclose = function (evt) {
                            if (evt.reason === "panelClose") {
                                _this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getNodes", reason: "panelClose" }).subscribe(function (res) {
                                    _this.alertService.debug(res);
                                });
                            }
                            else {
                                _this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getNodes", reason: evt.type }).subscribe(function (res) {
                                    _this.alertService.debug(res);
                                });
                                observer.next(evt);
                            }
                            observer.complete();
                        };
                    })
                        .pipe(operators_1.share());
                };
                NodesWSApi = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, core_2.TranslateService])
                ], NodesWSApi);
                return NodesWSApi;
            }());
            exports_1("NodesWSApi", NodesWSApi);
        }
    };
});
//# sourceMappingURL=NodesWSApi.js.map