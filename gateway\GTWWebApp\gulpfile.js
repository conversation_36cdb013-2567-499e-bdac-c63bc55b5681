// include plug-ins


//-----------------------------------
//TODO: Change for runtime & config
//-----------------------------------

const
  gulp = require('gulp'),
  concat = require('gulp-concat'),
  uglify = require('gulp-uglify'),
  del = require('del'),
  path = require('path'),
  rename = require('gulp-rename'),
  tsc = require('gulp-typescript'),
  SystemJSBuilder = require('systemjs-builder');

function preClean() {
  return del('dist');
}

function compile() {
  var tsProject = tsc.createProject('tsconfig.json');
  return gulp.src('app/**/*.ts')
    .pipe(tsProject())
    .pipe(gulp.dest('dist'));
}

function bundleTS() {
  var builder = new SystemJSBuilder();
  return builder.loadConfig('systemjs.dist.js').then(() => builder.buildStatic('dist', path.join('dist/js', 'main.js'), { minify: true }));
}

function bundleModule() {
  return gulp.src([
    'node_modules/zone.js/dist/zone.js',
    'node_modules/reflect-metadata/Reflect.js',
    'node_modules/systemjs/dist/system.src.js'
  ])
    .pipe(concat('bundleModule.js'))
    .pipe(uglify())
    .pipe(gulp.dest(path.join('dist', 'js')));
}

function cleanCompileJS() {
  return del(['dist/**', '!dist/js/**']);
}

function copyAssets() {
  return gulp.src(['bootstrap.min.css', 'bootstrap.min.css.map', 'styles.css', 'favicon.ico', 'images/**', 'fonts/**', 'i18n/**', 'certificate/**', 'app/**/*.html'], { base: '.' })
    .pipe(gulp.dest('dist'));
}

function copyRedirect() {
  return gulp.src('redirect-dist.html')
    .pipe(rename('redirect.html'))
    .pipe(gulp.dest('dist'));
}

function copyIndex() {
  return gulp.src('index-dist.html')
    .pipe(rename('index.html'))
    .pipe(gulp.dest('dist'));
}

function copyHelp() {
  return gulp.src('../help/**')
    .pipe(gulp.dest('dist/help'));
}

var buildDist = gulp.series(preClean, compile, gulp.parallel(bundleTS, bundleModule), cleanCompileJS, copyAssets, copyRedirect, copyIndex, copyHelp);
var buildCopyHelp = gulp.series(copyHelp);

exports.buildDist = buildDist;