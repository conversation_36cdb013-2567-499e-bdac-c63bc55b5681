﻿import { Component, OnInit, AfterViewInit, Input, ElementRef, ViewChild, Renderer2 } from "@angular/core";
import { Observable } from "rxjs";
import { FormGroup, FormControl, FormBuilder, Validators } from "@angular/forms";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { DashboardConfigTagOptionsModal } from "./dashboard.config.tag.options.modal";
import { BitmaskEditorComponent } from "../../modules/bitmask/bitmask.editor.component";
import { EditorSpecificationObjectDTO, EditorFieldObjectDTO, EditorCommandDTO} from "../../data/model/models";
import { DashboardConfigTagEditorLogic } from "./dashboard.config.tag.editor.logic";
import { FileService, EditorsService } from "../../data/api/api";
import { AuthenticationService } from "../../authentication/authentication.service";
import { AlertService } from "../../modules/alert/alert.service";
import { KeysPipe } from "../../global/keys.pipe";
import { CollapsiblePanel } from "../../modules/collapsible-panel/collapsible-panel";
import { TranslateService } from "@ngx-translate/core";
import { Column } from "../../modules/grid/column";
import { Node } from "../../modules/treeview/node";
import { NodeSelect } from "../../modules/treeview-select/node-select";
import { TagOption } from "./dashboard.config.tag.options.modal";
import { AdvanceParameter } from "./dashboard.config.tag.editor.advance.component";
import { DownloadComponent } from "../../modules/download/download.component";


@Component({
  selector: "dashboardConfigTagEditorComponent",
  styles: [`
    .options-button {
      margin-left: 4px;
    }
    .form-group {
      margin-bottom: 8px;
      margin-left: 0px;
    }
    input[type=checkbox] {
      transform: scale(1.2);
    }
    .radioCheckbox{
      width: 13px;
      height: 13px;
      background-color: white;
      border-radius: 50%;
      vertical-align: middle;
      border: 1px solid #ddd;
      appearance: none;
      -webkit-appearance: none;
      outline: none;
      cursor: pointer;
    }
    .radioCheckbox:checked {
      background-color: #0066ff;
      outline: 5px auto white;
      outline-offset: -2px;
    }
    .radioCheckbox:focus {
      outline: 5px auto white;
      outline-offset: -2px;
    }
    input, textarea, select{
      background-color: rgba(255, 255, 255, 0.7);
    }
    .warning-editable{
      color: #ff4242;
      z-index: 1;
      float: right;
      margin-right: 99%;
      margin-bottom: -12px;
      position: relative;
    }
    .disabled-field {
      pointer-events: none;
      opacity: 0.7;
    }
    .hide-element{
      display: none;
    }
    .help{
      word-break: break-word;
      margin-bottom: 6px !important;
      background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      border: 1px solid #bbbbbb;
      border-radius: 4px;
      max-width: 96%;
      white-space: pre-wrap;
    }
    .control-info{
      word-break: break-word;
      margin-bottom: 6px !important;
      background-color: rgb(225, 225, 225);
      border: 1px solid #c8c7c7;
      border-radius: 4px;
      padding: 3px;
    }
    .flex{
      display: flex;
    }
    .form-group {
        margin-right: -8px;
    }
    .asterisk {
      float: right;
      margin-top: -38px;
      color: red;
      font-weight: 900;
    }
    .grid-tree{
      overflow: auto;
      max-height: 300px;
      padding-left: 0px;
      margin-bottom:15px;
      width:99%
    }
    textarea{
      background: url(../../../images/arrowTopBottom.svg)no-repeat rgba(71, 108, 193, 0.52) 100% 100%;
      background-size: 18px;
      resize: vertical;
      overflow: hidden;
      background-color: #eee;
    }
    .actionHorizontal{
      float:right;
      padding-bottom:8px;
    }
  `],
  providers: [KeysPipe, FileService],
  template: `
	<div [formGroup]="tagForm" [ngClass]="{'actionHorizontal' : editorField.controlType === controlTypeEnum.Action}">
    <div class="glyphicon glyphicon-warning-sign warning-editable" *ngIf="editorField.isEditableAtRuntime==='No' && editorField.controlType != controlTypeEnum.Hidden" title="{{'TR_REQUIRES_RESTART' | translate}}"></div>
    <div *ngIf="(editorField.controlType === controlTypeEnum.Text || editorField.controlType === controlTypeEnum.TextAction)" class="form-group row" [ngClass]="{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>  
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br>{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>      
      <div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <ng-container *ngIf="editorField.controlType !== controlTypeEnum.TextAction"><input type="text" #currentInput class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()"></ng-container>
			  <ng-container *ngIf="editorField.controlType === controlTypeEnum.TextAction"><input type="text" #currentInput class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName"></ng-container>
        <div *ngIf="editorField.controlType === controlTypeEnum.TextAction" class="round-button" (click)="onChange()"><img src="../../images/validation.svg" class="image-button"/></div>
      </div>
		</div>
    <div *ngIf="(editorField.controlType === controlTypeEnum.TextId)" class="form-group row" [ngClass]="{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>  
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br>{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>      
      <div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <ng-container><input type="text" #currentInput class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()" (keypress)="omitSpecialChar($event, editorField)" (paste)="onPaste($event)"></ng-container>
      </div>
		</div>
		<div *ngIf="(editorField.controlType === controlTypeEnum.TextMultiLine)" class="form-group row">
      <label class="col-lg-12 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-12">
        <div><textarea [attr.disabled]="editorField.isEditable==='No' ? '' : null" style="height:80px; overflow:auto;" class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()"></textarea></div>
      </div>
		</div>
		<div *ngIf="(editorField.controlType === controlTypeEnum.TextPassword)"  class="form-group row">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <div style="width: 90%; display: inline-block;"><input type="password" #currentInput class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()" show-password /></div>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Number" class="form-group row" [ngClass]="{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <input type="number" class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()" min="{{rangeMin}}" max="{{rangeMax}}" (keypress)="onlyNumericChar($event, editorField)"/>
		  </div>
		</div>
		<div *ngIf="(editorField.controlType === controlTypeEnum.OptionsEditor)"  class="form-group row">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <div><textarea class="form-control input-sm" style="overflow:auto;" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" cols="60" rows="5"></textarea></div>
        <div class="round-button" style="vertical-align: top;margin-left: 2px;" (click)="openOptionsEditor(editorField.schemaName)" title="{{ 'TR_EDIT' | translateKey:'Edit':translate }}"><img src="../../images/edit.svg" class="image-button"/></div>
        <div class="round-button" style="vertical-align: top;margin-left: 2px;" (click)="clearOptionsEditor(editorField.schemaName)" title="{{ 'TR_DELETE' | translateKey:'Delete':translate }}"><img src="../../images/delete.svg" class="image-button"/></div>      
    </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.MaskEditor" class="form-group row">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <bitmaskEditorComponent [editorField]="editorField" [editorFieldcontrol]="this.tagForm.controls[editorField.schemaName]" (bitmaskOnChange)="onChange()"></bitmaskEditorComponent>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Checkbox" class="form-group row">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
      <div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
			  <input type="checkbox" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()"/>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.RadioButton" class="form-group row">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
      <div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
			  <input class="radioCheckbox" type="checkbox" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()"/>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Combobox" class="form-group row" [ngClass]="{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
			  <select class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()" [ngClass]="{'placeholder':this.tagForm.controls[editorField.schemaName].value==''}">
            <option [selected]="this.tagForm.controls[editorField.schemaName].value==''" disabled>{{'TR_SELECT' | translate}}</option>
				    <option *ngFor="let item of componentData" [ngValue]="item.key">{{item.value}}</option>
			  </select>
			</div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.FileManagement" class="form-group row" [ngClass]="{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <div style="width: 82%; display: inline-block;">
			    <select class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()" [ngClass]="{'placeholder':this.tagForm.controls[editorField.schemaName].value==''}">
            <option [selected]="this.tagForm.controls[editorField.schemaName].value==''" disabled>{{'TR_SELECT' | translate}}</option>
            <option value=""></option>
				    <option *ngFor="let item of componentData.files" [ngValue]="item.key">{{item.value}}</option>
			    </select>
	      </div>
        <div style="width: 18%; display: inline-block;">
          <label class="round-button" style="margin-left:2px;" title="{{'TR_UPLOAD_NEW_FILE' | translate}}"><input style="display: none;" type="file" accept="{{componentData.extensions}}" (change)="onFileUploadChange($event, editorField.schemaName, componentData.fileType)"><img src="../../images/upload.svg" class="image-button"/></label>
          <div class="round-button" style="margin-left:2px;" title="{{'TR_DOWNLOAD_FILE' | translate}}" (click)="onFileDownloadClick(editorField.schemaName, componentData.fileType)"><img src="../../images/download.svg" class="image-button"/></div>
        </div>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.FileManagementCombobox" class="form-group row" [ngClass]="{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}">
      <label class="col-lg-6 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
			<div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
			  <select class="form-control input-sm" [id]="editorField.schemaName" [formControlName]="editorField.schemaName" (change)="onChange()" [ngClass]="{'placeholder':this.tagForm.controls[editorField.schemaName].value==''}">
          <option [selected]="this.tagForm.controls[editorField.schemaName].value==''" disabled>{{'TR_SELECT' | translate}}</option>
          <option value=""></option>
				  <option *ngFor="let item of componentData.files" [ngValue]="item.key">{{item.value}}</option>
			  </select>
			</div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Grid || editorField.controlType === controlTypeEnum.GridNoDefault" class="form-group">
      <label class="col-lg-12 col-form-label" [ngClass]="{'hide-element':showHelp}" [attr.for]="editorField.name" title="{{editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 help" [ngClass]="{'hide-element':!showHelp}"><label class="col-form-label" [attr.for]="editorField.name">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>
      <div class="col-lg-12 flex" style="background-image: linear-gradient(to bottom, rgba(220, 220, 220, 0.85), rgba(234, 231, 247, 0.25));" class="grid-tree" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <gridComponent style="width: 100%;" [rows]="componentData" [columns]="gridColumns" [selectedIndex]="selectedIndex" (onClickActionGrid)="onChange($event)"></gridComponent>
		  </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Treeview" class="form-group">
			<label class="col-lg-12 col-form-label" style="margin-left:-15px"[attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 flex" style="background-color:white;" class="grid-tree" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <treeviewComponent style="width: 100%;" [isCheckbox]=false [node]="componentData" (selectedNodeFullNameChange)="onChange($event)"></treeviewComponent>
		  </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.TreeviewMutliChoice" class="form-group">
			<label class="col-lg-12 col-form-label" style="margin-left:-15px"[attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 flex" style="background-color:white;" class="grid-tree" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <treeviewComponent style="width: 100%;" [isCheckbox]=true [node]="componentData" (change)="onChange()"></treeviewComponent>
		  </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.TreeviewDynamic" class="form-group">
			<label class="col-lg-12 col-form-label" style="margin-left:-15px"[attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 flex" style="background-color:white;" class="grid-tree" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <dynamicTreeviewComponent style="width: 100%;" [isCheckbox]=false [node]="componentData" (selectedNodeFullNameChange)="onChange($event)" (selectedNodeLoadChildren)="onChange($event)"></dynamicTreeviewComponent>
		  </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.TreeviewDynamicMutliChoice" class="form-group">
			<label class="col-lg-12 col-form-label" style="margin-left:-15px"[attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 flex" style="background-color:white;" class="grid-tree" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <dynamicTreeviewComponent style="width: 100%;" [isCheckbox]=true [node]="componentData" (change)="onChange()" (selectedNodeLoadChildren)="onChange($event)"></dynamicTreeviewComponent>
		  </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.TreeviewDynamicSelectMutliChoice" class="form-group">
			<label class="col-lg-12 col-form-label" style="margin-left:-15px"[attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-12 flex" style="background-color:white;" class="grid-tree" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
        <dynamicTreeviewSelectComponent style="width: 100%;" [node]="componentData" (change)="onChange()" (selectedNodeLoadChildren)="onChange($event)"></dynamicTreeviewSelectComponent>
		  </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Label" class="form-group row">
			<label class="col-lg-12 flex col-form-label" [attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Info" class="form-group row">
			<label class="col-lg-6 col-form-label" [attr.for]="editorField.name" title="{{ editorField.help | translateKey:editorField.defaultHelp:translate }}">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>
      <div class="col-lg-6 flex" [ngClass]="{'disabled-field':editorField.isEditable==='No'}">
			  <div class="control-info" [innerHTML]="this.tagForm.controls[editorField.schemaName].value"></div>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.Action" class="form-group row">
      <div class="col-lg-12">
        <button type="button" class="btn btn-default btn-sm" style="float: right;" (click)="onChange(); $event.preventDefault();"><img src="../../images/check.svg" class="image-button"/>&nbsp;{{ editorField.name | translateKey:editorField.defaultName:translate }}</button>
      </div>
		</div>
		<div *ngIf="editorField.controlType === controlTypeEnum.AdvanceParameters" class="form-group row">
      <div class="col-lg-12 flex">
        <dashboardConfigTagEditorAdvanceComponent style="width: 100%;" [editorField]="editorField" [editorFieldcontrol]="this.tagForm.controls[editorField.schemaName]" (onChange)="onChange()"></dashboardConfigTagEditorAdvanceComponent>
      </div>
		</div>
    <div *ngIf="editorField.isRequired" class="asterisk">*</div>
		<ng-container *ngIf="editorField.controlType === controlTypeEnum.Hidden">
      <input type="hidden" [id]="editorField.schemaName" [formControlName]="editorField.schemaName"/>
		</ng-container>
	  <div [hidden]="this.tagForm.controls[editorField.schemaName].errors == null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)" style="text-align:right; margin-top:-8px;">
      <small class="text-danger" *ngIf="this.tagForm.controls[editorField.schemaName].hasError('required')">
	      {{editorField.name | translateKey:editorField.defaultName:translate}}{{ 'TR_IS_REQUIRED' | translate }}
      </small>
      <small class="text-danger" *ngIf="this.tagForm.controls[editorField.schemaName].hasError('incorrect')">
	      {{editorField.name | translateKey:editorField.defaultName:translate}}{{ 'TR_IS_INCORRECT' | translate }}: {{customError | translate}}
      </small>
	  </div>
	</div>
  <downloadComponent #download></downloadComponent>`
})

export class DashboardConfigTagEditorComponent implements OnInit, AfterViewInit {
  @Input() editorField: EditorFieldObjectDTO;
  @Input() tagForm: any;
  @Input() submitted: boolean = false;
  @Input() collapsiblePanels: CollapsiblePanel[];
  @Input() editorType: string;
  @Input() objectCollectionKind: string;
  @Input() editorsService: EditorsService;
  @Input() editorCommand: string;
  @Input() showHelp: boolean;
  private _componentData: any;
  private selectedIndex: number
  private gridColumns: Array<Column>;
  private controlTypeEnum = EditorFieldObjectDTO.ControlTypeEnum;
  private customError: string;
  private rangeMin;
  private rangeMax;
  private inputElement: ElementRef;

  @ViewChild("currentInput", { static: false }) el: ElementRef;
  @ViewChild("download", { static: false }) download: DownloadComponent;

  constructor(private alertService: AlertService, private authenticationService: AuthenticationService, private fileService: FileService, private modal: Modal, private keysPipe: KeysPipe,
    private DashboardRuntimeTagEditorLogic: DashboardConfigTagEditorLogic, private translate: TranslateService, private renderer: Renderer2) { }

  public ngOnInit(): void {
    if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.Combobox) {
      this.listComponentData(this.editorField.controlSource);
      this.fileService.configuration = this.authenticationService.userApiConfig;
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.FileManagement || this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.FileManagementCombobox) {
      this.listComponentDataFile(this.editorField.controlSource);
      this.fileService.configuration = this.authenticationService.userApiConfig;
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.Number) {
      if (this.editorField.range != "" ) {
        let range = JSON.parse(this.editorField.range);
        this.rangeMin = range.min;
        this.rangeMax = range.max;
      }
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.Grid) {
      this.setGridData(this.editorField.controlSource);
      if (this.tagForm.controls[this.editorField.schemaName].value == "")
        setTimeout(() => {
          this.selectGridDataMember(0);
        }, 500);
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.GridNoDefault) {
      this.setGridData(this.editorField.controlSource);
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.Treeview || this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewMutliChoice
      || this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamic || this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicMutliChoice) {
      this.setTreeviewData(this.editorField.controlSource, false);
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicSelectMutliChoice) {
      this.setTreeviewData(this.editorField.controlSource, true);
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.OptionsEditor) {
      this.tagForm.controls[this.editorField.schemaName].value = TagOption.formatTagOptionValue(this.editorField.controlSource, this.editorField.value);
    }
    this.onInitChange();
    this.tagForm.controls[this.editorField.schemaName].component = this;
  }

  ngAfterViewInit(): void {
    if (this.inputElement != null) {
      this.renderer.listen(this.inputElement.nativeElement, 'paste', (event) => {
        // handle the event
      });
    }
  }

  get inputNativeElement(): any {
    return this.el.nativeElement;
  }

  get componentData(): any {
    return this._componentData;
  }
  set componentData(componentData) {
    this._componentData = componentData;
  }

  public selectLastGridDataMember(): void {
    try {
      if (this.componentData !== "") {
        this.selectGridDataMember(this.componentData.length - 1);
      }
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  public selectGridDataMember(selectedIndex: number): void {
    try {
      if (this.componentData !== "" && this.componentData != null) {
        let gridSelectedValue: any = {};
        gridSelectedValue.item = this.componentData[selectedIndex];
        gridSelectedValue.index = selectedIndex;
        this.onChange(gridSelectedValue);
      }
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  public CheckGridDataMember(): boolean {
    try {
      if (this.componentData !== "" && this.componentData != null)
        return true;
      else
        return false;
    }
    catch (error) {
      this.alertService.debug(error.toString());
      return false;
    }
  }

  public clearSelected(): void {
    try {
      this.tagForm.controls[this.editorField.schemaName].setValue("");
      this.selectedIndex = -1;
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  public setGridData(controlSource: any): void {
    try {
      if (!controlSource)
        return;

      let jsonSource;
      if (typeof controlSource === "string")
        jsonSource = JSON.parse(controlSource);
      else
        jsonSource = controlSource;

      if (controlSource != null) {
        this.gridColumns = [];
        jsonSource.columns.forEach((column) => {
          if (column["header"] != "HIDDEN") {
            if (column["field"] == "checkbox") 
              this.gridColumns.push(new Column(column["field"], column["header"], "action$checkbox", ""));
            else
              this.gridColumns.push(new Column(column["field"], column["header"], "action$draggable", ""));
          }
        });
        this.componentData = jsonSource.data;
      }
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  public setTreeviewData(controlSource: any, isSelect: boolean): void {
    try {
      let jsonSource;
      let node;

      if (typeof controlSource === "string")
        jsonSource = JSON.parse(controlSource);
      else
        jsonSource = controlSource;

      if (controlSource != null) {
        if (isSelect)
          node = new NodeSelect(jsonSource[0].nodeName, jsonSource[0].nodeFullName, null, jsonSource[0].checked, jsonSource[0].displayCheckbox);
        else
          node = new Node(jsonSource[0].nodeName, jsonSource[0].nodeFullName, null, jsonSource[0].checked, jsonSource[0].displayCheckbox);

        node.children = this.setTreeviewNode(jsonSource[0], node, isSelect);
        if (jsonSource[0].hasChildren || node.children.length > 0)
          node.hasChildren = true;
        this.componentData = node;
      }
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  public listComponentData(controlSource: any): void {
    try {
      if (controlSource != null && controlSource != "") {
        let jsonSource;
        if (typeof controlSource === "string")
          jsonSource = JSON.parse(controlSource);
        else
          jsonSource = controlSource;

        this.componentData = this.keysPipe.transformJson(jsonSource);
      }
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  public listComponentDataFile(controlSource: any): void {
    try {
      let jsonSource = JSON.parse(controlSource);
      this.componentData = {};
      if (jsonSource.extensions && jsonSource.extensions != "") 
        this.componentData.extensions = jsonSource.extensions.replace(/ /g, ", ");
      this.componentData.fileType = jsonSource.fileType;
      this.componentData.files = this.keysPipe.transformJson(jsonSource.files);
    }
    catch (error) {
      this.alertService.debug(error.toString());
    }
  }

  private setTreeviewNode(parentSource: any, parent: any, isSelect: boolean): Array<Node> {
    let children = []
    let child;
    if (parentSource.children != null) {
      parentSource.children.forEach((childJson) => {
        if (isSelect)
          child = new NodeSelect(childJson.nodeName, childJson.nodeFullName, parent, childJson.checked, childJson.displayCheckbox);
        else
          child = new Node(childJson.nodeName, childJson.nodeFullName, parent, childJson.checked, childJson.displayCheckbox);
        child.children = this.setTreeviewNode(childJson, child, isSelect);
        if (childJson.hasChildren || child.children.length > 0)
          child.hasChildren = true;
        children.push(child);
      });
    }
    return children;
  }

  private openOptionsEditor(schemaName: string): void {
    const dashboardConfigTagOptionModalRef = this.modal.open(DashboardConfigTagOptionsModal, overlayConfigFactory({ controlSource: this.editorField.controlSource, tagOptionsValue: this.tagForm.controls[schemaName].value }, BSModalContext));
    dashboardConfigTagOptionModalRef.result.then(
      (data) => {
        if (data != null) {
          let optionText: string = "";
          data.forEach((option) => {
            if (option[1] != "") 
              optionText += option[0] + " " + option[1] + "\n";
            else
              optionText += option[0] + "\n";
          });
          if (optionText.length > 1) 
            optionText = optionText.substring (0, optionText.length - 1);
          this.tagForm.controls[schemaName].setValue(optionText);
        }
      },
      (error) => { this.alertService.debug(error.toString()); }
    );
  }

  private clearOptionsEditor(schemaName: string): void {
    let result = true;
    this.translate.get("TR_DELETE_ALL_THE_OPTIONS").subscribe(res => {
      result = confirm(res);
    });
    if (!result) return;
    this.tagForm.controls[schemaName].setValue("");
  }

  private onInitChange(): void {
    this.customError = this.DashboardRuntimeTagEditorLogic.manageForm(true, this.modal, this.tagForm, this.editorField, this.editorType, this.objectCollectionKind, this.collapsiblePanels, this.editorsService, this.editorCommand, this.authenticationService, this.alertService, this.translate);
  }

  private onChange(selectedObject?: any): void {
    if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.Grid || this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.GridNoDefault) {
      this.tagForm.controls[this.editorField.schemaName].setValue(selectedObject);
      if (selectedObject != null)
        this.selectedIndex = selectedObject.index;
      else
        this.selectedIndex = null;
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.Treeview || this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamic) {
      this.tagForm.controls[this.editorField.schemaName].setValue(selectedObject);
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewMutliChoice ) {
      this.tagForm.controls[this.editorField.schemaName].setValue(this.componentData);
    }
    else if (this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicMutliChoice  ||  this.editorField.controlType == EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicSelectMutliChoice) {
      this.tagForm.controls[this.editorField.schemaName].setValue(selectedObject);
    }
    this.customError = this.DashboardRuntimeTagEditorLogic.manageForm(false, this.modal, this.tagForm, this.editorField, this.editorType, this.objectCollectionKind, this.collapsiblePanels, this.editorsService, this.editorCommand, this.authenticationService, this.alertService, this.translate);
  }

  private omitSpecialChar(event, editorField): boolean{
    if (editorField.schemaName !== "objectName" && editorField.controlType !== this.controlTypeEnum.TextId)
      return true;

    let input = String.fromCharCode(event.keyCode);
    if (!((event.key == "-") || (event.key == "_") || (event.key == "$") || (/[a-zA-Z0-9]/.test(input)))) {
      event.preventDefault();
      return false;
    }
  }

  private onlyNumericChar(event, editorField): boolean {
    if (editorField.controlType !== this.controlTypeEnum.Number)
      return true;

    let input = String.fromCharCode(event.keyCode);
    if ((/[^-.0-9]/.test(input))) {
      event.preventDefault();
      return false;
    }
  }

  private onPaste(event: ClipboardEvent) {
    event.preventDefault();
  }
    
  private onFileUploadChange(event: any, schemaName: string, fileType: string): void {
    let fileList: FileList = event.target.files;
    if (fileList.length > 0) {
      let file: File = fileList[0];
      let result = true;
      if (this.componentData.files && this.componentData.files.some(item => item.key === file.name)) {
        this.translate.get("TR_FILE_ALREADY_EXISTS_OVERWRITE_IT").subscribe(res => {
          result = confirm(res);
        }); 
      }
      if (!result) return;
      //let formData: FormData = new FormData();
      //formData.append("uploadFile", file, file.name);
      this.fileService.filePost(file, "" , "").subscribe(
        data => {
          this.alertService.success("TR_FILE_SAVED");
          if (this.componentData.files && !this.componentData.files.some(item => item.key === file.name)) {
            if (data.fileDescription && data.fileDescription !="")
              this.componentData.files.splice(1, 0, { key: file.name, value: data.fileDescription });
            else
              this.componentData.files.splice(1, 0, { key: file.name, value: file.name });
          }
          this.tagForm.controls[schemaName].setValue(file.name);
          this.onChange();
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_SAVED"); }
        }
      );
    }
  }

  private onFileDownloadClick(schemaName: string, fileType: string): void {
    if (this.tagForm.controls[schemaName].value != "") {
      let fileName = this.tagForm.controls[schemaName].value.replace(/^.*[\\\/]/, '');
      if (fileName != "")
      { 
        this.download.downloadClick(fileName, "CurrentWorkspace");
      }
    }
    else {
      this.alertService.warning("TR_NO_FILE_TO_DOWNLOAD");
    }
  }
}