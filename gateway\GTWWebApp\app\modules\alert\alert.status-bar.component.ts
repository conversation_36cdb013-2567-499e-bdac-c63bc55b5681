﻿import { Component, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { AlertService } from './alert.service';

@Component({
  selector: 'alertStatusBarComponent',
  template: `
				<div *ngIf="message" [ngClass]="{ 'alert-success': message.type === 'success', 'alert-danger': message.type === 'error', 'alert-info': message.type === 'info' , 'alert-warning': message.type === 'warning'}">{{message.text | translate}}</div>
			`
})

export class AlertStatusBarComponent implements OnDestroy {
  private subscription: Subscription;
  private message: any;

  constructor(private alertService: AlertService) { 
    // subscribe to alert messages
    this.subscription = alertService.getMessage().subscribe(message => { this.message = message; });
  }

  ngOnDestroy(): void {
    // unsubscribe on destroy to prevent memory leaks
    this.subscription.unsubscribe();
  }
}