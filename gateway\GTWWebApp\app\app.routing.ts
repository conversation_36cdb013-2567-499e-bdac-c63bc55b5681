﻿import { ModuleWithProviders }  from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { DashboardComponent } from "./dashboard/dashboard.component";
import { AppBlankComponent } from "./app.blank.component";
import { SettingsComponent } from "./settings/settings.component";
import { UserGridComponent } from "./user/user.grid.component";
import { LicenseComponent } from "./license/license.component";
import { LogMonitorComponent } from "./log/log.monitor.component";
import { LogSoeComponent } from "./log/log.soe.component";
import { LogAuditGridComponent } from "./log/log.audit.grid.component";
import { HelpAboutComponent } from "./help/help.about.component";
import { HelpUpdateComponent } from "./help/help.update.component";
import { HelpSupportComponent } from "./help/help.support.component";

const appRoutes: Routes = [
	{
		path: "",
		component: DashboardComponent
	},
	{
		path: "dashboard",
		component: DashboardComponent
	},
	{
		path: "settings",
		component: SettingsComponent
  },
  {
    path: "license",
    component: LicenseComponent
  },
	{
		path: "users",
		component: UserGridComponent
	},
	{
		path: "support",
		component: HelpSupportComponent
	},
  {
    path: "monitorLog",
    component: LogMonitorComponent
  },
  {
    path: "auditLog",
    component: LogAuditGridComponent
	},
	{
		path: "soeLog",
		component: LogSoeComponent
	},
  {
    path: "about",
    component: HelpAboutComponent
	},
	{
		path: "update",
		component: HelpUpdateComponent
	},
	{
		path: "blank",
		component: AppBlankComponent
	},
  {
    path: "**",  // otherwise route.
		component: DashboardComponent
  }
];
declare module "@angular/core" {
	interface ModuleWithProviders<T = any> {
		ngModule: Type<T>;
		providers?: Provider[];
	}
}
export const Routing: ModuleWithProviders<any> = RouterModule.forRoot(appRoutes);