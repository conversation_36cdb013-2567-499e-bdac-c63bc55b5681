﻿export class Node {
  nodeName: string;
  nodeFullName: string;
  parent: Node;
  children: Array<Node> = [];
  hasChildren: boolean = false;
  isExpanded: boolean = false;
  checked: boolean = false;
  displayCheckbox: boolean = false;

  constructor(nodeName: string, nodeFullName: string, parent: Node, checked: boolean = false, displayCheckbox: boolean = false) {
    this.nodeName = nodeName;
    this.nodeFullName = nodeFullName;
    this.parent = parent;
    this.checked = checked;
    this.displayCheckbox = displayCheckbox
    //Auto expand first level
    if (parent == null) {
      this.isExpanded = true;
    }
  }
  public toggle(): void {
    this.isExpanded = !this.isExpanded;
  }
  public check(): void {
    let newState: boolean = !this.checked;
    this.checked = newState;
    this.checkRecursive(newState);
    this.checkParent();
  }
  public checkRecursive(state: boolean): void {
    if (this.children == null)
      return;
    this.children.forEach(child => {
      child.checked = state;
      child.checkRecursive(state);
    })
  }
  public checkParent(): void {
    let checkCount = 0;
    if (this.parent != null) {
      for (var i = 0; i < this.parent.children.length; i++)
        if (this.parent.children[i].checked === true)
          checkCount++;

      if (checkCount === this.parent.children.length)
        this.parent.checked = true;
      else
        this.parent.checked = false;
    }

  }
}