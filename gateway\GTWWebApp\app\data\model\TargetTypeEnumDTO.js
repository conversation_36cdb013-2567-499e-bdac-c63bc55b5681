System.register([], function (exports_1, context_1) {
    "use strict";
    var TargetTypeEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (TargetTypeEnumDTO) {
                TargetTypeEnumDTO[TargetTypeEnumDTO["_232"] = 'TMWTARGIO_TYPE_232'] = "_232";
                TargetTypeEnumDTO[TargetTypeEnumDTO["MODEMPOOLCHANNEL"] = 'TMWTARGIO_TYPE_MODEM_POOL_CHANNEL'] = "MODEMPOOLCHANNEL";
                TargetTypeEnumDTO[TargetTypeEnumDTO["MODEMPOOL"] = 'TMWTARGIO_TYPE_MODEM_POOL'] = "MODEMPOOL";
                TargetTypeEnumDTO[TargetTypeEnumDTO["MODEM"] = 'TMWTARGIO_TYPE_MODEM'] = "MODEM";
                TargetTypeEnumDTO[TargetTypeEnumDTO["TCP"] = 'TMWTARGIO_TYPE_TCP'] = "TCP";
                TargetTypeEnumDTO[TargetTypeEnumDTO["UDPTCP"] = 'TMWTARGIO_TYPE_UDP_TCP'] = "UDPTCP";
                TargetTypeEnumDTO[TargetTypeEnumDTO["MBP"] = 'TMWTARGIO_TYPE_MBP'] = "MBP";
                TargetTypeEnumDTO[TargetTypeEnumDTO["MON"] = 'TMWTARGIO_TYPE_MON'] = "MON";
            })(TargetTypeEnumDTO || (TargetTypeEnumDTO = {}));
            exports_1("TargetTypeEnumDTO", TargetTypeEnumDTO);
        }
    };
});
//# sourceMappingURL=TargetTypeEnumDTO.js.map