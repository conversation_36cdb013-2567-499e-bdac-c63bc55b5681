import { Injectable } from "@angular/core";
import { Subject } from "rxjs";

@Injectable() 
export class LoaderService {
  public isLoading: boolean;

  constructor() {
    this.isLoadingChange.subscribe((value) => {
      this.isLoading = value
    });
  }

  isLoadingChange: Subject<boolean> = new Subject<boolean>();

  public toggleIsLoading(isLoading: boolean): void {
    this.isLoadingChange.next(isLoading);
  }
}