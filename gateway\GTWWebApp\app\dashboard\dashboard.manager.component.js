System.register(["@angular/core", "../modules/panel/panel.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, panel_service_1, DashboardManagerComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (panel_service_1_1) {
                panel_service_1 = panel_service_1_1;
            }
        ],
        execute: function () {
            DashboardManagerComponent = (function () {
                function DashboardManagerComponent(panelService) {
                    this.panelService = panelService;
                }
                DashboardManagerComponent.prototype.ngOnInit = function () {
                    if (this.panelService.panelList.length == 0) {
                        var lsDashboardLayout = localStorage.getItem("SDGDashboardLayout");
                        if (lsDashboardLayout == null) {
                            this.isPanelConfigOpen = true;
                        }
                    }
                };
                DashboardManagerComponent.prototype.updatePanelList = function (eventPanel, event) {
                    if (this.panelService.panelLayoutDirection != 0) {
                        this.panelService.toggleLayoutDasboard(this.panelService.panelLayoutDirection);
                    }
                    else {
                        if (eventPanel.visible) {
                            eventPanel.zindex = 1;
                        }
                    }
                };
                DashboardManagerComponent = __decorate([
                    core_1.Component({
                        selector: 'dashboardManagerComponent',
                        host: { "style": "height: 100%" },
                        styles: ["\n\t\t\t.layoutButton {\n        background-color: transparent;\n        border: none;\n        color: white;\n\t\t\t}\n      .panel-config-icon{\n        width: 20px;\n        height: 20px;\n        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n        vertical-align: top;\n        margin: 3px\n      }\n      .title{\n        text-align: center;\n        font-weight: bold;\n        font-size: 14px;\n        margin: -4px 0px 6px 0px;\n      }\n      .btn-panel{\n        text-align: center;\n        margin: 14px 6px -12px 6px;\n        background-color: black !important;\n      }\n      .btn-direction{\n        margin: 0px 2px 0px 2px;\n      }\n      .btn-direction:hover{\n        background-color: #cdcdcd !important;\n      }\n      "
                        ],
                        template: "\n\t\t<div class=\"layoutButton panel panel-default\">\n      <div class=\"title\">{{'TR_DASHBOARD_MANAGER' | translate}}</div>\n\t\t\t<div *ngFor=\"let panel of this.panelService.panelList\" >\n\t\t\t\t\t<input type=\"checkbox\" name=\"options\" class=\"form-check\" value=\"{{panel.lsName}}\" [(ngModel)]=\"panel.visible\" (change)=\"updatePanelList(panel, $event)\"/>\n\t\t\t\t\t<img [src]=\"'../../images/' + panel.icon\" class=\"panel-config-icon\" *ngIf=\"panel.icon!=''\"/> {{panel.title | translate}}\n\t\t\t</div>\n\t\t\t<div class=\"btn-panel\">\n\t\t\t  <button type=\"button\"\tclass=\"btn-xs btn-default btn-direction\" [ngStyle]=\"{'background-color': this.panelService.panelLayoutDirection == 0 ? '#65e577' : 'transparent'}\" (click)=\"this.panelService.toggleLayoutDasboardCustom()\" title=\"{{'TR_CUSTOM_DIPLAY' | translate}}\"><img src=\"../../images/customSplit.svg\" class=\"panel-config-icon\" /></button>\n\t\t\t  <button type=\"button\"\tclass=\"btn-xs btn-default btn-direction\" [ngStyle]=\"{'background-color': this.panelService.panelLayoutDirection == 1 ? '#65e577' : 'transparent'}\" (click)=\"this.panelService.toggleLayoutDasboard(1)\" title=\"{{'TR_VERTICAL_DISPLAY' | translate}}\"><img src=\"../../images/verticalSplit.svg\" class=\"panel-config-icon\" /></button>\n\t\t\t  <button type=\"button\" class=\"btn-xs btn-default btn-direction\" [ngStyle]=\"{'background-color': this.panelService.panelLayoutDirection == 2 ? '#65e577' : 'transparent'}\" (click)=\"this.panelService.toggleLayoutDasboard(2)\" title=\"{{'TR_HORIZONTAL_DISPLAY' | translate}}\"><img src=\"../../images/horizontalSplit.svg\" class=\"panel-config-icon\" /></button>\n      </div>\t\t\n    </div>\n\t\t"
                    }),
                    __metadata("design:paramtypes", [panel_service_1.PanelService])
                ], DashboardManagerComponent);
                return DashboardManagerComponent;
            }());
            exports_1("DashboardManagerComponent", DashboardManagerComponent);
        }
    };
});
//# sourceMappingURL=dashboard.manager.component.js.map