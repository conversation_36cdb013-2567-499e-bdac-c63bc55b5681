﻿import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { HelpService } from "../data/api/api";
import { Subscription } from 'rxjs';
import { INIParamNodeDTO } from "../data/model/models";
import { Column } from "../modules/grid/column";
import { AlertService } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { TranslateKeyPipe } from "../global/translateKey.pipe";
import { GlobalJsonUtil } from '../global/global.json.util';
import { LoaderService } from "../modules/loader/loader.service";

@Component({
  selector: "helpIniComponent",
  providers: [TranslateKeyPipe],
  styles: [`
      .panel {
        position: absolute;
        z-index: 999;
        margin: auto;
        width: 50%;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
      }
      .panel-heading{
        padding: 4px;
      }
      .panel-body{
        padding: 8px;
        
      }
      .button-close {
        position: absolute;
        top: 6px;
        right: 6px;
      }
      `
    ],
    template: `
      <div class="panel-main panel panel-default" *ngIf="isHelpIniVisible" style="min-width:400px">
        <div class="panel-heading">
          {{'TR_INI_FILE_PARAMETER_HELP' | translate}}
          <div class="button-close round-button" title="{{'TR_CLOSE' | translate}}" (click)="close()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
        </div>
	      <div class="panel-body" style="overflow-y: scroll; height:80vh;">
		      <gridComponent [rows]='helpIniContent' [columns]='columns'></gridComponent>
	      </div>
 
      </div>`
})

export class HelpIniComponent implements OnInit, OnDestroy{
  private columns: Array<Column>;
  private isHelpIniVisible: boolean = false;
  private helpIniContent: Array<HelpIniContentDTO> = [];
  private isLoadingChangeSubscriptionShow: Subscription;
  private isLoading: boolean = false;

  constructor(private helpService: HelpService, private alertService: AlertService, private authenticationService: AuthenticationService, private translate: TranslateService, private translateKeyPipe: TranslateKeyPipe, private loaderService: LoaderService) {
    this.columns = this.getColumns();
  }

  public ngOnInit(): void {
    this.isLoadingChangeSubscriptionShow = this.loaderService.isLoadingChange.subscribe((value) => {
      this.isLoading = value;
    });
  }

  public ngOnDestroy(): void {
    this.isLoadingChangeSubscriptionShow.unsubscribe();
  }

  public show() {
    this.isHelpIniVisible = true;
    this.getIniParametersHelp();
  }

  private close() {
    this.isHelpIniVisible = false;
  }

  private getIniParametersHelp() {
    if (this.helpIniContent.length == 0) {
      this.loaderService.toggleIsLoading(true);
      this.helpService.getINIhelp().subscribe(
        data => {
          if (data) {
            data = GlobalJsonUtil.sortByProperty(data, "paramName");
            data.forEach((iniParam: INIParamNodeDTO) => {
              let helpIniContentTxt = "";
              helpIniContentTxt += "<b>" + iniParam.paramName + " - " + this.translateKeyPipe.transform(iniParam.paramLabelKey, iniParam.paramLabel, this.translate)  + "</b><br>";
              helpIniContentTxt += "<i>" + iniParam.paramSection + "</i><br>";
              helpIniContentTxt += this.translateKeyPipe.transform(iniParam.paramHelpKey, iniParam.paramHelp, this.translate) + "<br>";
              helpIniContentTxt += iniParam.paramRange ? iniParam.paramRange + "<br>" : "";
              this.translate.get("TR_DEFAULT").subscribe(res => {
                helpIniContentTxt += res + ": " + iniParam.paramDefault + "<br><br>";
              });
              this.helpIniContent.push(new HelpIniContentDTO(helpIniContentTxt));
            });
          }
        },
        error => {
          this.loaderService.toggleIsLoading(false);
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("LICENSE.ERROR_CAN_T_READ_LICENSE"); }
        },
        () => {
          this.loaderService.toggleIsLoading(false);
        }
      );
    }
  }

  private getColumns(): Array<Column> {
    return [
      new Column("helpIniContentTxt", "TR_SEARCH", "action$innerHTML", ""),
    ];
  }
}

export class HelpIniContentDTO {
  constructor(
    public helpIniContentTxt?: string
  ) { }
}