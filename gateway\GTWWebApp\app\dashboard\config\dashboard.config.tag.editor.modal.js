System.register(["@angular/core", "@angular/forms", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "../../data/model/models", "../../modules/alert/alert.service", "../../authentication/authentication.service", "../../modules/collapsible-panel/collapsible-panel", "../../modules/alert/alert.log.modal"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, forms_1, ngx_modialog_7_1, bootstrap_1, models_1, alert_service_1, authentication_service_1, collapsible_panel_1, alert_log_modal_1, DashboardConfigTagEditorModal, TagModalContext;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (forms_1_1) {
                forms_1 = forms_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (collapsible_panel_1_1) {
                collapsible_panel_1 = collapsible_panel_1_1;
            },
            function (alert_log_modal_1_1) {
                alert_log_modal_1 = alert_log_modal_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagEditorModal = (function () {
                function DashboardConfigTagEditorModal(zone, dialog, authenticationService, alertService, modal) {
                    this.zone = zone;
                    this.dialog = dialog;
                    this.authenticationService = authenticationService;
                    this.alertService = alertService;
                    this.modal = modal;
                    this.controlTypeEnum = models_1.EditorFieldObjectDTO.ControlTypeEnum;
                    this.editorKindEnum = models_1.EditorSpecificationObjectDTO.EditorKind;
                    this.isDataLoaded = false;
                    this.submitted = false;
                    this.panelGroupNames = [];
                    this.showHelp = false;
                    this.panelX = 0;
                    this.panelY = 0;
                    this.context = dialog.context;
                    this.context.dialogClass = "modal-dialog modal-lg";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                }
                DashboardConfigTagEditorModal.prototype.ngOnInit = function () {
                    this.initEditorData();
                };
                DashboardConfigTagEditorModal.prototype.beforeDismiss = function () {
                    return true;
                };
                DashboardConfigTagEditorModal.prototype.mouseDown = function (event) {
                    var _this = this;
                    this.draggingWindow = true;
                    this.px = event.clientX;
                    this.py = event.clientY;
                    this.zone.runOutsideAngular(function () {
                        window.document.addEventListener('mousemove', _this.onWindowDrag.bind(_this));
                    });
                };
                DashboardConfigTagEditorModal.prototype.onWindowDrag = function (event) {
                    if (!this.draggingWindow)
                        return;
                    if (event.clientX >= window.innerWidth || event.clientX <= 0)
                        return;
                    if (event.clientY >= window.innerHeight || event.clientY <= 0)
                        return;
                    var offsetX = event.clientX - this.px;
                    var offsetY = event.clientY - this.py;
                    this.panelX += offsetX;
                    this.panelY += offsetY;
                    this.px = event.clientX;
                    this.py = event.clientY;
                    if (event.stopPropagation)
                        event.stopPropagation();
                    if (event.preventDefault)
                        event.preventDefault();
                    event.cancelBubble = true;
                    event.returnValue = false;
                };
                DashboardConfigTagEditorModal.prototype.onCornerRelease = function (event) {
                    this.draggingWindow = false;
                };
                DashboardConfigTagEditorModal.prototype.cancel = function () {
                    this.dialog.close(null);
                };
                DashboardConfigTagEditorModal.prototype.close = function () {
                    this.dialog.close(null);
                    if (this.editorSpecification.editorKind === this.editorKindEnum.CloseSave) {
                        this.save(this.tagForm.getRawValue(), this.tagForm.valid);
                    }
                };
                DashboardConfigTagEditorModal.prototype.onChangeShowHelp = function () {
                    this.showHelp = !this.showHelp;
                };
                DashboardConfigTagEditorModal.prototype.initEditorData = function () {
                    var _this = this;
                    var currentGroupPanelName = "";
                    var currentFieldsetName = "";
                    var objectClassName = "";
                    if (this.dialog.context.node != null) {
                        if (this.dialog.context.node.memberClass != "" && this.dialog.context.node.memberClass)
                            objectClassName = this.dialog.context.node.memberClass;
                        else
                            objectClassName = this.dialog.context.node.nodeClassName;
                        this.objectCollectionKind = this.dialog.context.node.nodeCollectionKind.toString();
                    }
                    else if (this.dialog.context.tag != null) {
                        objectClassName = this.dialog.context.tag.tagClassName;
                        this.objectCollectionKind = models_1.TagObjectDTO.getTagPropertyMaskStringValue(this.dialog.context.tag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
                    }
                    this.dialog.context.editorsService.getEditorData(this.dialog.context.editorCommand, this.dialog.context.objectName, this.dialog.context.parentObjectName, true, objectClassName, this.objectCollectionKind).subscribe(function (data) {
                        _this.isDataLoaded = true;
                        _this.editorSpecification = data;
                        var formControls = {};
                        _this.editorType = _this.editorSpecification.editorType;
                        _this.editorSpecification.children.forEach(function (editorField) {
                            if ((editorField.controlType == _this.controlTypeEnum.Checkbox || editorField.controlType == _this.controlTypeEnum.RadioButton) && (editorField.value == "1" || editorField.value.toUpperCase() == "TRUE"))
                                formControls[editorField.schemaName] = new forms_1.FormControl(true);
                            else if ((editorField.controlType == _this.controlTypeEnum.Checkbox || editorField.controlType == _this.controlTypeEnum.RadioButton) && (editorField.value == "0" || editorField.value.toUpperCase() == "FALSE"))
                                formControls[editorField.schemaName] = new forms_1.FormControl(false);
                            else if (editorField.controlType == _this.controlTypeEnum.OptionsEditor)
                                formControls[editorField.schemaName] = new forms_1.FormControl({ value: editorField.value, disabled: true });
                            else
                                formControls[editorField.schemaName] = new forms_1.FormControl(editorField.value);
                            formControls[editorField.schemaName].editorField = editorField;
                            formControls[editorField.schemaName].enumJson = "";
                            if (editorField.isRequired)
                                formControls[editorField.schemaName].setValidators([forms_1.Validators.required]);
                            editorField.isGroupPanelHeader = false;
                            if (editorField.groupPanelName != "" && currentGroupPanelName != editorField.groupPanelName)
                                editorField.isGroupPanelHeader = true;
                            else if (editorField.groupPanelName == "")
                                currentGroupPanelName = "";
                            currentGroupPanelName = editorField.groupPanelName;
                            editorField.isFieldsetHeader = false;
                            if (editorField.fieldsetName != "" && currentFieldsetName != editorField.fieldsetName)
                                editorField.isFieldsetHeader = true;
                            else if (editorField.fieldsetName == "")
                                currentFieldsetName = "";
                            currentFieldsetName = editorField.fieldsetName;
                        });
                        _this.tagForm = new forms_1.FormGroup(formControls);
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                        _this.dialog.close(false);
                    });
                };
                DashboardConfigTagEditorModal.prototype.save = function (model, isValid) {
                    var _this = this;
                    this.submitted = true;
                    if (isValid) {
                        for (var k in model) {
                            if (model.hasOwnProperty(k)) {
                                if (model[k] && model[k].checkedItem) {
                                    model[k] = model[k].checkedItem;
                                }
                                else {
                                    var tempString = String(model[k]);
                                    model[k] = tempString.replace(/(\r\n|\n|\r)/gm, " ");
                                }
                            }
                        }
                        var objectClassName = "";
                        var objectCollectionKind = "";
                        if (this.dialog.context.node != null) {
                            objectClassName = this.dialog.context.node.nodeClassName;
                            objectCollectionKind = this.dialog.context.node.nodeCollectionKind.toString();
                        }
                        else if (this.dialog.context.tag != null) {
                            objectClassName = this.dialog.context.tag.tagClassName;
                            objectCollectionKind = models_1.TagObjectDTO.getTagPropertyMaskStringValue(this.dialog.context.tag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
                        }
                        var editorResult = {
                            objectName: model.objectName,
                            parentObjectName: this.dialog.context.parentObjectName,
                            objectClassName: objectClassName,
                            objectCollectionKind: objectCollectionKind.toString(),
                            oldObjectDataJson: this.editorSpecification.objectDataJson,
                            objectDataJson: JSON.stringify(model),
                            arg1: "",
                            arg2: ""
                        };
                        this.dialog.context.editorsService.createOrUpdateEditorObject(this.dialog.context.editorCommand, editorResult).subscribe(function (data) {
                            _this.alertService.success("TR_DATA_SAVED");
                            _this.dialog.close(data);
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                if (error.error.messages && error.error.messages.length > 0)
                                    _this.modal.open(alert_log_modal_1.AlertLogModal, ngx_modialog_7_1.overlayConfigFactory({ messages: error.error.messages, title: "TR_ERROR" }, bootstrap_1.BSModalContext));
                                _this.alertService.error("TR_ERROR_DATA_NOT_SAVED");
                            }
                        });
                    }
                    else {
                        try {
                            Object.keys(this.tagForm.controls).forEach(function (key) {
                                var controlErrors = _this.tagForm.get(key).errors;
                                if (controlErrors != null) {
                                    Object.keys(controlErrors).forEach(function (keyError) {
                                        var formControl = _this.tagForm.controls[key];
                                        var groupPanelName = formControl.editorField.groupPanelName;
                                        if (groupPanelName != "") {
                                            var collapsiblePanelsfiltered = _this.collapsiblePanels.filter(function (item) { return item.lsName == groupPanelName; });
                                            if (collapsiblePanelsfiltered.length > 0)
                                                collapsiblePanelsfiltered[0].isOpen = true;
                                        }
                                    });
                                }
                            });
                        }
                        catch (err) {
                        }
                    }
                };
                __decorate([
                    core_1.ViewChildren(collapsible_panel_1.CollapsiblePanel),
                    __metadata("design:type", core_1.QueryList)
                ], DashboardConfigTagEditorModal.prototype, "collapsiblePanels", void 0);
                __decorate([
                    core_1.HostListener('document:mousemove', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [MouseEvent]),
                    __metadata("design:returntype", void 0)
                ], DashboardConfigTagEditorModal.prototype, "onWindowDrag", null);
                __decorate([
                    core_1.HostListener('document:mouseup', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [MouseEvent]),
                    __metadata("design:returntype", void 0)
                ], DashboardConfigTagEditorModal.prototype, "onCornerRelease", null);
                DashboardConfigTagEditorModal = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagEditorModal",
                        styles: ["\n      .tag-modal::before {\n        content: \"\";\n        opacity: 0.1;\n        background-image: url(images/background_7.svg), linear-gradient(#000000, #c5c5c5);\n        background-size: 30%;\n        position: absolute;\n        height: 100%;\n        width: 100%;\n        z-index: -999;\n      }\n      .button-close {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n      }\n      .tag-modal {\n        position: absolute;\n        z-index: -998;\n        -webkit-box-shadow: 0 5px 15px rgba(0,0,0,.5);\n        box-shadow: 0 5px 15px rgba(0,0,0,.5);\n        background-image: linear-gradient(to bottom, rgba(218, 218, 218, 0.06), rgba(0, 0, 0, 0.13));\n        background-color: #fff;\n        background-clip: padding-box;\n        border: 1px solid #999;\n        border: 1px solid rgba(0,0,0,.2);\n        border-radius: 6px;\n        -webkit-box-shadow: 0 3px 9px rgba(0,0,0,.5);\n        box-shadow: 0 3px 9px rgba(0,0,0,.5);\n        outline: 0;\n        min-width: 400px;\n        width: 66vw;\n        max-width: 1200px;\n      }\n      .tag-modal-heading {\n        background-color: #d8d8d8;\n        padding: 8px 10px 6px 10px;\n        font-size: 22px;\n        font-weight: bold;\n        border-bottom: 1px solid #a31c3f;\n        overflow-wrap: break-word;\n\t\t\t  cursor: move;\n        border-top-left-radius: 6px;\n        border-top-right-radius: 6px;\n      }\n      .tag-modal-content {\n        padding: 10px\n      }\n      .panel-heading {\n        padding: 5px 5px;\n      }\n      fieldset {\n        border-radius: 6px;\n        border: 1px #a0a0a0 solid;\n        padding: 0px 6px 0px 6px;\n        margin-bottom: 10px;\n        width: 100%\n      }\n      legend{\n        width: auto;\n        padding: 0 10px;\n        border-bottom: none;\n        margin-bottom: 4px;\n        font-weight: bold;\n        font-size: 15px;\n      }\n      .help-icon{\n        position: absolute;\n        top: 12px;\n        right: 40px;\n        cursor: pointer;\n        height: 22px;\n        width: 22px;\n      }\n  "],
                        template: "\n\t<div novalidate class=\"container-fluid\" *ngIf=\"isDataLoaded\">\n    <div [style.top.px]=\"this.panelY\" [style.left.px]=\"this.panelX\" class=\"tag-modal\" #modalPopup>\n      <div class=\"tag-modal-heading\" (mousedown)=\"mouseDown($event)\">\n        <ng-container *ngIf=\"this.dialog.context.editorCommand == 'MENU_CMD_EDIT'\" >\n          {{ 'TR_EDIT' | translate }} {{this.dialog.context.objectName}}\n        </ng-container>\n        <ng-container *ngIf=\"this.dialog.context.editorCommand.includes('MENU_CMD_SHOW')\" >\n          {{ this.dialog.context.addTitle | translate }} {{this.dialog.context.objectName}}\n        </ng-container>\n        <ng-container *ngIf=\"this.dialog.context.editorCommand != 'MENU_CMD_EDIT' && !this.dialog.context.editorCommand.includes('MENU_CMD_SHOW')\">\n          {{ this.dialog.context.addTitle | translate }}\n        </ng-container>\n        <img src=\"../../../images/help.svg\" class=\"help-icon\" (click)=\"onChangeShowHelp()\" alt=\"{{'TR_HIDE_HELP' | translate}}\" title=\"{{'TR_SHOW_HELP' | translate}}\"/>\n        <div class=\"button-close round-button\" title=\"{{'TR_CLOSE' | translate}}\" (click)=\"close()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n\t    </div>\n      <div class=\"tag-modal-content\">\n\t      <form (ngSubmit)=\"save(tagForm.getRawValue(), tagForm.valid)\" [formGroup]=\"tagForm\" (keydown.enter)=\"$event.preventDefault()\">\n          <ng-container *ngFor=\"let editorField of editorSpecification.children\">\n            <dashboardConfigTagEditorComponent [editorCommand]=\"this.dialog.context.editorCommand\" [editorsService]=\"this.dialog.context.editorsService\" [editorField]=\"editorField\" [objectCollectionKind]=\"objectCollectionKind\" [tagForm]=\"tagForm\" [submitted]=\"submitted\" [editorType]=\"editorType\" *ngIf=\"(editorField.groupPanelName=='' && editorField.fieldsetName=='')\" [collapsiblePanels]=\"collapsiblePanels\" [showHelp]=\"showHelp\"></dashboardConfigTagEditorComponent>\n            <ng-container *ngIf=\"(editorField.fieldsetName != '' && editorField.groupPanelName == '')\">\n              <fieldset *ngIf=\"editorField.isFieldsetHeader\"><legend>{{editorField.fieldsetName | translate}}</legend>\n                <ng-container *ngFor=\"let editorField of editorSpecification.children | editorGroupPipe: null:editorField.fieldsetName\">\n                  <dashboardConfigTagEditorComponent [editorCommand]=\"this.dialog.context.editorCommand\" [editorsService]=\"this.dialog.context.editorsService\" [editorField]=\"editorField\" [objectCollectionKind]=\"objectCollectionKind\" [tagForm]=\"tagForm\" [submitted]=\"submitted\" [collapsiblePanels]=\"collapsiblePanels\" [editorType]=\"editorType\" [showHelp]=\"showHelp\"></dashboardConfigTagEditorComponent>\n                </ng-container>\n              </fieldset>\n            </ng-container>\n            <collapsiblePanel [title]=\"editorField.groupPanelName| translate\" [lsName]=\"editorField.groupPanelName\" *ngIf=\"editorField.isGroupPanelHeader && editorField.groupPanelName != ''\" [islocalStorageEnabled]=false>\n              <ng-container *ngFor=\"let editorField of editorSpecification.children | editorGroupPipe:editorField.groupPanelName:null\">\n                <dashboardConfigTagEditorComponent [editorCommand]=\"this.dialog.context.editorCommand\" [editorsService]=\"this.dialog.context.editorsService\" [editorField]=\"editorField\" [objectCollectionKind]=\"objectCollectionKind\" [tagForm]=\"tagForm\" [submitted]=\"submitted\" *ngIf=\"(editorField.fieldsetName == '')\" [collapsiblePanels]=\"collapsiblePanels\" [editorType]=\"editorType\" [showHelp]=\"showHelp\"></dashboardConfigTagEditorComponent>\n                <ng-container *ngIf=\"(editorField.fieldsetName != '')\">\n                  <fieldset *ngIf=\"editorField.isFieldsetHeader\"><legend>{{editorField.fieldsetName | translate}}</legend>\n                    <ng-container *ngFor=\"let editorField of editorSpecification.children | editorGroupPipe: editorField.groupPanelName:editorField.fieldsetName\">\n                      <dashboardConfigTagEditorComponent [editorCommand]=\"this.dialog.context.editorCommand\" [editorsService]=\"this.dialog.context.editorsService\" [editorField]=\"editorField\" [objectCollectionKind]=\"objectCollectionKind\" [tagForm]=\"tagForm\" [submitted]=\"submitted\" [collapsiblePanels]=\"collapsiblePanels\" [editorType]=\"editorType\" [showHelp]=\"showHelp\"></dashboardConfigTagEditorComponent>\n                    </ng-container>\n                  </fieldset>\n                </ng-container>\n              </ng-container>\n            </collapsiblePanel>\n          </ng-container>\n          <div *ngIf=\"!tagForm.valid && submitted\" style=\"color: #a94442;margin-left: 14px;}\">\n            {{\"TR_MANDATORY_FIELDS_ARE_EMPTY_OR_INCORRECT\" | translate}}\n          </div>\n\t\t      <div *ngIf=\"!this.editorSpecification.editorKind\" class=\"form-group\" style=\"display: inline-block;width:99%; text-align: center; margin-top: 20px;\">\n\t\t        <div style=\"display: table-cell;\">\n\t\t          <button type=\"button\" class=\"btn btn-default\" (click)=\"cancel(); $event.preventDefault();\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CANCEL' | translate}}</button>\n\t\t        </div>\n\t\t        <div style=\"display: table-cell;width:99%\"></div>\n\t\t        <div style=\"display: table-cell;\">\n\t\t          <button type=\"submit\" class=\"btn btn-default\"><img src=\"../../images/ok.svg\" class=\"image-button\"/>&nbsp;{{'TR_OK' | translate}}</button>\n\t\t        </div>\n\t\t      </div>\n\t\t      <div *ngIf=\"(this.editorSpecification.editorKind === this.editorKindEnum.AddItem)\" class=\"form-group\" style=\"display: inline-block;width:99%; text-align: center; margin-top: 20px;\">\n\t\t        <div style=\"display: table-cell;\">\n\t\t          <button type=\"button\" (click)=\"close(); $event.preventDefault();\" class=\"btn btn-default\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CLOSE' | translate}}</button>\n\t\t        </div>\n\t\t      </div>\n\t\t      <div *ngIf=\"(this.editorSpecification.editorKind === this.editorKindEnum.CloseSave)\" class=\"form-group\" style=\"display: inline-block;width:99%; text-align: center; margin-top: 20px;\">\n\t\t        <div style=\"display: table-cell;\">\n\t\t          <button type=\"submit\" class=\"btn btn-default\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CLOSE' | translate}}</button>\n\t\t        </div>\n\t\t      </div>\n\t\t      <alertStatusBarComponent></alertStatusBarComponent>\n\t      </form>\n      </div>\n    </div>\n\t</div>"
                    }),
                    __metadata("design:paramtypes", [core_1.NgZone, ngx_modialog_7_1.DialogRef, authentication_service_1.AuthenticationService, alert_service_1.AlertService, bootstrap_1.Modal])
                ], DashboardConfigTagEditorModal);
                return DashboardConfigTagEditorModal;
            }());
            exports_1("DashboardConfigTagEditorModal", DashboardConfigTagEditorModal);
            TagModalContext = (function (_super) {
                __extends(TagModalContext, _super);
                function TagModalContext() {
                    var _this = _super !== null && _super.apply(this, arguments) || this;
                    _this.editorCommand = "";
                    _this.objectName = "";
                    _this.addTitle = "";
                    _this.parentObjectName = "";
                    _this.tag = null;
                    _this.node = null;
                    _this.editorsService = null;
                    return _this;
                }
                return TagModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("TagModalContext", TagModalContext);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.editor.modal.js.map