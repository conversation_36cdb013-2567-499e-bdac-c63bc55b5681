﻿import { Directive, Input, ViewContainerRef, ComponentFactoryResolver } from "@angular/core";
import { ContextMenuService } from "../../modules/context-menu/context-menu.service";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { CheckRolePipe } from "../../authentication/check.role.pipe";
import { EditorCommandsDTO } from "../../data/model/models";
import { EditorContextMenuService } from "../../data/api/api";
import { ContextMenuItem, ContextMenuType } from "../../modules/context-menu/context-menu-item";
import { DashboardConfigComponent } from "./dashboard.config.component";
import { DashboardConfigDevicesComponent } from "./dashboard.config.devices.component";
import { DashboardConfigGridComponent } from "./dashboard.config.grid.component";
import { VirtualNode } from "./dashboard.config.device.virtual-node.component";
import { TreeNodeDTO,TagObjectDTO } from "../../data/model/models";
import { Subject } from 'rxjs';


@Directive({
  selector: "[dashboardConfigContextMenuDirective]",
  host: { "(contextmenu)": "rightClicked($event)" },
  providers: [DashboardConfigDevicesComponent, DashboardConfigGridComponent]
})

export class DashboardConfigContextMenuDirective {
  @Input("dashboardConfigContextMenuDirective") targetObject: any;
  @Input("rootNode") rootNode: TreeNodeDTO;
  @Input("node") node: TreeNodeDTO;
  @Input("tag") tag: TagObjectDTO;
  @Input("selectedRows") selectedRows: Array<any>;
  @Input("rows") rows: Array<any>;

  private contextMenuType = ContextMenuType;

  constructor(private contextMenuService: ContextMenuService, private dashboardConfigComponent: DashboardConfigComponent, private dashboardConfigDevicesComponent: DashboardConfigDevicesComponent,
    private dashboardConfigGridComponent: DashboardConfigGridComponent, private alertService: AlertService, private authenticationService: AuthenticationService,
    private editorContextMenuService: EditorContextMenuService, private checkRolePipe: CheckRolePipe, private vcRef: ViewContainerRef, private resolver: ComponentFactoryResolver) {}

  private rightClicked(event: MouseEvent): void {
    this.loadContextMenuItem(event, this.targetObject);
    event.preventDefault();
  }

  private instanceOfVirtualNode(targetObject: any): targetObject is VirtualNode {
    return (<VirtualNode>targetObject).name !== undefined;
  }

  private loadAPIContextMenuItems(event: MouseEvent, contextMenuItems: Array<ContextMenuItem>, targetObject?: any):void {
    let APIContextMenuItems: Array<ContextMenuItem> = [];
    let objectClassName: string = "";
    let objectCollectionKind: any = "";

    if (this.node != null && this.rootNode != null && typeof targetObject === "string") {
      objectClassName = this.node.nodeClassName
      objectCollectionKind = this.node.nodeCollectionKind.toString();
    }
    else if (this.tag != null && typeof targetObject === "string") {
      objectClassName = this.tag.tagClassName
      objectCollectionKind = TagObjectDTO.getTagPropertyMaskStringValue(this.tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
    }
    else if (contextMenuItems.length > 0) {
      this.contextMenuService.show.next({ event: event, obj: contextMenuItems });
    }

    this.editorContextMenuService.getEditorContextMenu(targetObject, objectClassName, objectCollectionKind).subscribe(
      data => {
        if (data != null && data.length > 0) {
          data.forEach((contextMenuItem) => {
            if (contextMenuItem.command == EditorCommandsDTO.MENUCMDSEPARATOR.toString())
              APIContextMenuItems.push(new ContextMenuItem("MENU_CMD_SEPARATOR", null, this.contextMenuType.SEPARATOR));
            else
              APIContextMenuItems.push(new ContextMenuItem(contextMenuItem.menuText, new Subject().subscribe(val => this.dashboardConfigComponent.onNodeAction(contextMenuItem.command, targetObject, targetObject, this.tag, this.node)), this.contextMenuType.ACTION, null, contextMenuItem.isLicensed));
          })
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      },
      () => {
        this.loadExtraContextMenuItem(event, APIContextMenuItems, targetObject);
        if (APIContextMenuItems.length > 0) {
          //Add to node to filter log
          if (this.node != null && this.node.isFilterTarget) {
            APIContextMenuItems.push(new ContextMenuItem(EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
            APIContextMenuItems.push(new ContextMenuItem("TR_ADD_NODE_TO_FILTER_LOG", new Subject().subscribe(val => this.dashboardConfigDevicesComponent.addNodeToLogFilter(this.node.nodeFullName)), this.contextMenuType.ACTION));
          }
          //Import Export
          if (this.node != null && this.node.nodeName == "Gateway" && this.node.nodeClassName.toString() == "GatewayRootNode") {
            APIContextMenuItems.push(new ContextMenuItem(EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
            APIContextMenuItems.push(new ContextMenuItem("TR_IMPORT_EXPORT_POINTS", new Subject().subscribe(val => this.dashboardConfigDevicesComponent.importExportCSV("")), this.contextMenuType.ACTION));
          }
          else if (this.node != null && this.node.nodeCollectionKind == TreeNodeDTO.NodeCollectionKindEnum.MDO) {
            APIContextMenuItems.push(new ContextMenuItem(EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
            APIContextMenuItems.push(new ContextMenuItem("TR_EXPORT_POINTS", new Subject().subscribe(val => this.dashboardConfigDevicesComponent.importExportCSV(this.node.nodeFullName)), this.contextMenuType.ACTION));
          }
          // Tree Menu
          if (this.node != null && this.node.nodeName == "Gateway" && this.node.nodeClassName.toString() == "GatewayRootNode") {
            APIContextMenuItems.unshift(new ContextMenuItem(EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
            APIContextMenuItems.unshift(new ContextMenuItem("TR_DISPLAY_WARNING", new Subject().subscribe(val => this.dashboardConfigDevicesComponent.onWarningOnRootActiveChange(this.node)), this.contextMenuType.CHECKBOX, this.node.isWarningActive));
            APIContextMenuItems.unshift(new ContextMenuItem("TR_SAVE_WORKSPACE", new Subject().subscribe(val => this.dashboardConfigDevicesComponent.saveConfig()), this.contextMenuType.ACTION_BOLD, null, true, "save.svg"));
          }
          if (contextMenuItems.length > 0) {
            contextMenuItems.unshift(new ContextMenuItem(EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
            this.contextMenuService.show.next({ event: event, obj: APIContextMenuItems.concat(contextMenuItems) });
          }
          else {
            this.contextMenuService.show.next({ event: event, obj: APIContextMenuItems });
          }
        }
        else if (contextMenuItems.length > 0) {
          this.contextMenuService.show.next({ event: event, obj: APIContextMenuItems.concat(contextMenuItems) });
        }
      }
    );
  }

  private loadContextMenuItem(event: MouseEvent, targetObject?: any): void {
    let contextMenuItems: Array<ContextMenuItem> = [];

    // Warning Tree Menu
    if (this.instanceOfVirtualNode(targetObject) && targetObject.name != "warning")
      return;

    if (this.instanceOfVirtualNode(targetObject) && targetObject.name === "warning" && this.checkRole("OPERATOR_ROLE")) {
      contextMenuItems.push(new ContextMenuItem("TR_RESET_COUNTER", new Subject().subscribe(val => this.dashboardConfigComponent.resetWarningTags())));
      this.contextMenuService.show.next({ event: event, obj: contextMenuItems });
      return;
    }

    // Grid and Tree Menu
    if (this.checkRole("OPERATOR_ROLE")) {
      this.loadAPIContextMenuItems(event, contextMenuItems, targetObject);
    }
    else if (contextMenuItems.length > 0) {
      this.contextMenuService.show.next({ event: event, obj: contextMenuItems });
    }
  }

  private loadExtraContextMenuItem(event: MouseEvent, contextMenuItems: Array<ContextMenuItem>, targetObject?: any): void {
    if (this.node != null && this.node.hasChildren && this.node.nodeName != "Gateway") {
      let dashboardConfigDevicesComponent: DashboardConfigDevicesComponent = this.getDashboardConfigDevicesComponentInstance(this.vcRef['_view']);
      if (dashboardConfigDevicesComponent != null) {
        contextMenuItems.push(new ContextMenuItem("TR_EXPAND_CHILDREN", new Subject().subscribe(val => dashboardConfigDevicesComponent.expandChild(this.node, this.rootNode))));
        contextMenuItems.push(new ContextMenuItem("TR_COLLAPSE_CHILDREN", new Subject().subscribe(val => this.dashboardConfigDevicesComponent.collapseChild(this.node))));
      }
    }

    // Grid Menu
    if (this.checkRole("CONFIGURATOR_ROLE")) {
      if (this.selectedRows && this.selectedRows.length > 1) {
        let canTagBeDeleted: boolean;
        this.selectedRows.forEach((tag) => {
          if (TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_DELETE)) {
            canTagBeDeleted = true;
          }
        });
        if (canTagBeDeleted) {
          let contextMenuItemIndex: number = contextMenuItems.findIndex(x => x.title == "TR_MENU_CMD_DELETE");
          if (contextMenuItemIndex != -1)
            contextMenuItems.splice(contextMenuItemIndex, 1);
          contextMenuItems.push(new ContextMenuItem("TR_DELETE_SELECTED", new Subject().subscribe(val => this.dashboardConfigComponent.deleteAllTags(this.selectedRows))));
        }

        let canTagValueBeChanged: boolean;
        this.selectedRows.forEach((tag) => {
          if (TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_CHANGE_VALUE)) {
            canTagValueBeChanged = true;
          }
        });
        if (canTagValueBeChanged){
          let contextMenuItemIndex: number = contextMenuItems.findIndex(x => x.title == "TR_MENU_CMD_CHANGE_VALUE");
          if (contextMenuItemIndex != -1)
            contextMenuItems.splice(contextMenuItemIndex, 1);
          contextMenuItems.push(new ContextMenuItem("TR_CHANGE_VALUE_OF_SELECTED", new Subject().subscribe(val => this.dashboardConfigComponent.changeAllValueTag(this.selectedRows))));
        }

        contextMenuItems.push(new ContextMenuItem(EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
        contextMenuItems.push(new ContextMenuItem("TR_UNSELECT_ALL", new Subject().subscribe(val => this.dashboardConfigGridComponent.unSelectedAllRows(this.selectedRows))));
      }
      if (this.tag != null) {
        contextMenuItems.push(new ContextMenuItem("TR_SELECT_ALL", new Subject().subscribe(val => this.dashboardConfigGridComponent.selectedAllRows(this.selectedRows, this.rows))));
      }
    }
  }

  private checkRole(minRole: string): boolean {
    return this.checkRolePipe.transform(this.authenticationService.role, minRole);
  }

  private getDashboardConfigDevicesComponentInstance(view: any): any {
    if (view.component.constructor != null &&
      view.component.constructor.__annotations__ != null &&
      view.component.constructor.__annotations__.length > 0 &&
      view.component.constructor.__annotations__[0].selector == "dashboardConfigDevicesComponent")
      return view.parent.component;
    else
      return this.getParentInstance(view.parent);
  }

  private getParentInstance(viewParent: any): any {
    if (viewParent.component.constructor != null &&
      viewParent.component.constructor.__annotations__ != null &&
      viewParent.component.constructor.__annotations__.length > 0 &&
      viewParent.component.constructor.__annotations__[0].selector == "dashboardConfigDevicesComponent")
      return viewParent.component;
    else if (viewParent.parent != null)
      return this.getParentInstance(viewParent.parent);
    else
      return null;
  }
}