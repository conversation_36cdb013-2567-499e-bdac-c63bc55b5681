﻿import { Component, OnInit } from "@angular/core";
import { HttpClient } from '@angular/common/http';
import { MiscService } from "../data/api/api";
import { SDGAboutDTO } from "../data/model/models";
import { AlertService } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";
import { Observable } from 'rxjs'

@Component({
  selector: "helpUpdateComponent",
  styles: [`
   .center{
      text-align: center;
      font-weight: bold;
    }
	`],
  template: `
	<div>
    <div class="panel panel-default panel-main" style="width:99%; margin-left:6px; margin-top:60px">
      <div class="panel-heading"><img src="../../images/about.svg" class="module-icon" />{{'TR_UPDATE' | translate}}</div>
      <div class="panel-body center">
        <div *ngIf="updateResult===updateResultEnum.Failed">{{'TR_FAILED_CONTACT_UPDATE_SERVER' | translate}}</div>
        <div *ngIf="updateResult===updateResultEnum.Wait">{{'TR_PLEASE_WAIT_UPDATE_SDG' | translate}}</div>
        <div *ngIf="updateResult===updateResultEnum.NoLicense">{{'TR_NO_LICENSE_UPDATE' | translate}}</div>
        <div *ngIf="updateResult===updateResultEnum.OutOffMaintenance" >{{'TR_LICENSE_OUT_OF_MAINTENANCE' | translate}}</div>
        <div *ngIf="updateResult===updateResultEnum.UpToDate">{{'TR_VERSION_UP_TO_DATE' | translate: { currentVersion: currentVersion } }}</div>
        <div *ngIf="updateResult===updateResultEnum.NeedUpdate">{{'TR_NEW_VERSION_AVAILABLE' | translate: { currentVersion: currentVersion, newVersion: updateVersion } }}</div>
        <div *ngIf="updateResult===updateResultEnum.NeedUpdate" [innerHTML]="'TR_GO_TO_TMW_TO_DOWNLOAD_UPDATE' | translate"></div>
      </div>
	</div>` 
})

export class HelpUpdateComponent implements OnInit {
  private currentVersion: string = "";
  private updateVersion: string = "";
  private updateResult: UpdateResult = UpdateResult.Wait;
  private updateResultEnum = UpdateResult;

  constructor(private miscService: MiscService, private alertService: AlertService, private authenticationService: AuthenticationService, private http: HttpClient) {
  }

  public ngOnInit(): void { 
    try {
      this.miscService.getAbout().subscribe(
        dataAbout => {
          let aboutDTO: SDGAboutDTO;
          aboutDTO = dataAbout;
          this.currentVersion = aboutDTO.currentVersion;
          let mpExpires = aboutDTO.mpExpires;
          if (mpExpires == null || this.currentVersion == null) {
            this.updateResult = UpdateResult.NoLicense;
            return;
          }
          let updateDetails: SDGUpdateDetailsDTO = (<any>aboutDTO).releaseDetails;
          if (updateDetails != null) {
            if (this.isMPValidForUpdate(mpExpires, updateDetails.ReleaseDate)) {
              this.updateVersion = updateDetails.Version
              if (this.isNewerVersion(this.currentVersion, updateDetails.Version)) {
                this.updateResult = UpdateResult.NeedUpdate;
              }
              else {
                this.updateResult = UpdateResult.UpToDate;
              }
            }
            else {
              this.updateResult = UpdateResult.OutOffMaintenance;
            }
          }
          else {
            this.updateResult = UpdateResult.Failed;
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_READ_UPDATE"); }
          this.updateResult = UpdateResult.Failed;
        }
      );
    } catch (e) {
      this.alertService.error("TR_FAILED_CONTACT_UPDATE_SERVER");

    }
  }

  private isNewerVersion(oldVer, newVer): boolean {
    const oldParts = oldVer.split('.')
    const newParts = newVer.split('.')
    for (let i = 0; i < newParts.length; i++) {
      const a = ~~newParts[i] // parse int
      const b = ~~oldParts[i] // parse int
      if (a > b) return true
      if (a < b) return false
    }
    return false
  }

  private isMPValidForUpdate(mpExpires: string, versionRelease: string): boolean {
    if (this.isValidDate(mpExpires) && this.isValidDate(versionRelease)) {
      let mpExpiresDate = new Date(mpExpires);
      let versionReleaseDate = new Date(versionRelease);
      if (versionReleaseDate <= mpExpiresDate)
        return true
    }
    return false
  }


  private isValidDate(d): boolean {
    const date = Date.parse(d);
    return !isNaN(date)
  }
}

export interface SDGUpdateDetailsDTO {
  Version?: string;
  Name?: string;
  ReleaseDate?: string;
}

enum UpdateResult {
  Failed,             //"TR_PLEASE_WAIT_UPDATE_SDG"
  Wait,               //"TR_PLEASE_WAIT_UPDATE_SDG"
  NoLicense,          //"TR_NO_LICENSE_UPDATE"
  OutOffMaintenance,  //"TR_LICENSE_OUT_OF_MAINTENANCE"
  UpToDate,           //TR_VERSION_UP_TO_DATE
  NeedUpdate          //"TR_NEW_VERSION_AVAILABLE" + "TR_GO_TO_TMW_TO_DOWNLOAD_UPDATE"
}

