﻿<div *ngIf="this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'">
  <div class="panel-main panel panel-default" style="width:99%; margin-left:6px; margin-top:60px">
    <div class="panel-heading"><img src="../../images/config.svg" class="module-icon" />{{ 'TR_SYSTEM_SETTINGS' | translate }} - {{ globalDataService?.SDGConfig?.currentWorkSpaceName }}</div>
    <div class="panel-body">

      <collapsiblePanel [title]="'TR_WORKSPACE_MANAGEMENT'" [lsName]="'SDGConfigWorkspaceManagementPanel'">
        <fieldset>
          <legend>{{ 'TR_CURRENT_WORKSPACE' | translate }}:&nbsp;&nbsp;<i>{{ globalDataService?.SDGConfig?.currentWorkSpaceName }}</i></legend>
          <div class="form-group">
            <ng-container *ngIf="(this.globalDataService?.healthData?.engineState === EngineStateEnumDTO.RUNNING.toString() || this.globalDataService?.healthData?.engineState === EngineStateEnumDTO.ERRORININI.toString())">
              <button type="button" [tooltip]="['TR_SAVE_CURRENT_WORKSPACE']" id="saveGTW" (click)="onManageGTWClick($event)" class="btn btn-info"><img src="../../images/save.svg" class="image-button" />&nbsp;{{ 'TR_SAVE_CURRENT_WORKSPACE' | translate }}</button>
              <div class="div-spacer"></div>
              <button type="button" [tooltip]="['TR_FORCE_SAVE_CURRENT_WORKSPACE']" id="forceSaveGTW" (click)="onManageGTWClick($event)" class="btn btn-info"><img src="../../images/save.svg" class="image-button" />&nbsp;{{ 'TR_FORCE_SAVE_CURRENT_WORKSPACE' | translate }}</button>
              <div class="div-spacer"></div>
              <button type="button" [tooltip]="['TR_SAVE_AS_CURRENT_WORKSPACE']" id="saveAsGTW" (click)="saveAsDashboard()" class="btn btn-info"><img src="../../images/save.svg" class="image-button" />&nbsp;{{ 'TR_SAVE_AS_CURRENT_WORKSPACE' | translate }}</button>
              <div class="div-spacer"></div>
            </ng-container>
          </div>
        </fieldset>
        <br />
        <fieldset>
          <legend>{{ 'TR_WORKSPACE_MANAGEMENT' | translate }}</legend>
          <table>
            <tr>
              <td width="150px"><label>{{ 'TR_WORKSPACE' | translate }}:</label></td>
              <td>
                <select class="input-sm" style="width: 99%;" [(ngModel)]="selectedWorkSpaceName">
                  <option *ngFor="let fileName of workSpaceNameList" [ngValue]="fileName.value">{{fileName.value}}</option>
                </select>
              </td>
              <td>
                <button type="button" [tooltip]="['TR_RUN_SELECTED_WORKSPACE']" id="useFile" (click)="runSelectedWorkSpace($event)" class="btn btn-success"><img src="../../images/play.svg" class="image-button" />&nbsp;{{ 'TR_RUN_SELECTED_WORKSPACE' | translate }}</button>
                <div class="div-spacer"></div>
                <button type="button" [tooltip]="['TR_DELETE_WORSPACE']" id="deleteWorkspace" (click)="deleteWorkspace()" class="btn btn-warning"><img src="../../images/delete.svg" class="image-button" />&nbsp;{{ 'TR_DELETE_WORSPACE' | translate }}</button>
                <div class="div-spacer"></div>
                <button type="button" [tooltip]="['TR_RUN_NEW_WORKSPACE']" id="newWorkSpace" (click)="onManageGTWClick($event)" class="btn btn-success"><img src="../../images/play.svg" class="image-button" />&nbsp;{{ 'TR_RUN_NEW_WORKSPACE' | translate }}</button>
              </td>
            </tr>
            <tr>
              <td width="150px">&nbsp;</td>
              <td>
                <button type="button" [tooltip]="['TR_DOWNLOAD_WORSPACE']" id="downloadWorkspace" (click)="onWorkspaceDownloadClick($event)" class="btn btn-default btn-sm"><img src="../../images/download.svg" class="image-button" />&nbsp;{{ 'TR_DOWNLOAD_WORSPACE' | translate }}</button>
                <div class="div-spacer"></div>
                <label class="btn btn-default btn-sm" [tooltip]="['TR_IMPORT_WORKSPACE']"><img src="../../images/upload.svg" class="image-button" />&nbsp;{{ 'TR_IMPORT_WORKSPACE' | translate }}<input #uploadWorkspace style="display: none;" type="file" (change)="onWorkspaceUploadChange($event)" placeholder="Restore Workspace" accept=".gws"></label>
              </td>
              <td>&nbsp;</td>
            </tr>
          </table>
          <br>
        </fieldset>
        <br>
        <fieldset>
          <legend>{{ 'TR_MANAGE_WORKSPACE_FILES' | translate }}</legend>
          <div class="form-group">
            <table>
              <tr>
                <td width="150px"><label>{{ 'TR_WORKSPACE' | translate }}:</label></td>
                <td>
                  <select class="input-sm" style="width: 99%;" [(ngModel)]="selectedWorkSpaceName" (change)="getWorkSpaceFileList()">
                    <option *ngFor="let fileName of workSpaceNameList" [ngValue]="fileName.value">{{fileName.value}}</option>
                  </select>
                </td>
              </tr>
              <tr>
                <td width="150px"><label>{{ 'TR_FILE' | translate }}:</label></td>
                <td>
                  <select class="input-sm" style="width: 99%;" [(ngModel)]="selectedWorkSpaceFile" [ngClass]="{'placeholder':selectedWorkSpaceFile==''}">
                    <option value="" disabled selected>{{'TR_SELECT' | translate}}</option>
                    <option *ngFor="let fileName of workSpaceFileList" [ngValue]="fileName.value">{{fileName.value}}</option>
                  </select>
                </td>
              </tr>
              <tr>
                <td width="150px">
                  &nbsp;
                </td>
                <td>
                  <button type="button" [tooltip]="['TR_DOWNLOAD_FILE']" id="downloadFile" (click)="onFileDownloadClick($event)" class="btn btn-default btn-sm"><img src="../../images/download.svg" class="image-button" />&nbsp;{{ 'TR_DOWNLOAD_FILE' | translate }}</button>
                  <div class="div-spacer"></div>
                  <label class="btn btn-default btn-sm" [tooltip]="['TR_UPLOAD_NEW_FILE']"><img src="../../images/upload.svg" class="image-button" />&nbsp;{{ 'TR_UPLOAD_NEW_FILE' | translate }}<input #uploadFile style="display: none;" type="file" (change)="onFileUploadChange($event)" placeholder="Upload file" accept="*"></label>
                </td>
              </tr>
            </table>
          </div>
        </fieldset>
      </collapsiblePanel>

      <collapsiblePanel [title]="'TR_SYSTEM_MANAGEMENT'" [lsName]="'SDGConfigSystemManagementPanel'">
        <div class="form-group">
          <button type="button" [tooltip]="['TR_RE_START_BOTH']" id="stopMonitorEngine" (click)="onManageGTWClick($event)" class="btn btn-warning"><img src="../../images/powerSwitch.svg" class="image-button" />&nbsp;{{ 'TR_RE_START_BOTH' | translate }}</button>
          <div class="div-spacer"></div>
          <button type="button" [tooltip]="['TR_RE_START_MONITOR']" id="stopMonitor" (click)="onManageGTWClick($event)" class="btn btn-warning"><img src="../../images/powerSwitch.svg" class="image-button" />&nbsp;{{ 'TR_RE_START_MONITOR' | translate }}</button>
          <div class="div-spacer"></div>
          <button type="button" [tooltip]="['TR_RE_START_GATEWAY']" id="stopEngine" (click)="onManageGTWClick($event)" class="btn btn-warning" *ngIf="(this.globalDataService?.healthData?.engineState === EngineStateEnumDTO.RUNNING.toString() || this.globalDataService?.healthData?.engineState === EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString() || this.globalDataService?.healthData?.engineState === EngineStateEnumDTO.RUNNING_NO_LICENSE.toString())">
            <img src="../../images/powerSwitch.svg" class="image-button" />&nbsp;{{ 'TR_RE_START_GATEWAY' | translate }}
          </button>
          <button type="button" [tooltip]="['TR_START_GATEWAY']" id="stopEngine" (click)="onManageGTWClick($event)" class="btn btn-success" *ngIf="(this.globalDataService?.healthData == null || (this.globalDataService.healthData?.engineState != EngineStateEnumDTO.RUNNING.toString() && this.globalDataService.healthData?.engineState != EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString() && this.globalDataService.healthData?.engineState != EngineStateEnumDTO.RUNNING_NO_LICENSE.toString()))">
            <img src="../../images/powerSwitch.svg" class="image-button" />&nbsp;{{ 'TR_START_GATEWAY' | translate }}
          </button>
        </div>
      </collapsiblePanel>

      <div *ngIf="this.authenticationService.role | checkRole:'SU_ROLE'">
        <collapsiblePanel [title]="'TR_LANGUAGE'" [lsName]="'SDGConfigLanguagePanel'">
          <div class="form-group">
            <label>
              {{ 'TR_SELECT_LANGUAGE' | translate }}:
            </label>
            <div style="display: inline-block;width: 200px;">
              <select #langSelect (change)="onLanguageChange(langSelect.value);" class="form-control">
                <option *ngFor="let language of supportedLanguages" [value]="language.value" [selected]="language.value === translate.currentLang">{{ language.display }}</option>
              </select>
            </div>
          </div>
        </collapsiblePanel>
        <settingsFormComponent></settingsFormComponent>
        <downloadComponent #download></downloadComponent>
      </div>
    </div>
  </div>
</div>