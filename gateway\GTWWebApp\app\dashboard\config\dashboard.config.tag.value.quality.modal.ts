﻿import { <PERSON>mpo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON>ementRef, <PERSON>derer2 } from "@angular/core";
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { TagObjectDTO, TagValuesDTO, TagValueDTO, TagPurposeFilterEnumDTO } from "../../data/model/models";
import { Bitmask } from "../../modules/bitmask/bitmask.component";

@Component({
  selector: "dashboardConfigTagValueQualityModal",
  styles: [`
      .tag-modal-container {
		    padding: 10px;
      }
      fieldset {
        border-radius: 6px;
        border: 1px LightGray solid;
        margin-bottom: 10px;
      }
      .tag-modal-heading {
        background-color: #d8d8d8;
        padding: 8px 10px 6px 10px;
        font-size: 22px;
        font-weight: bold;
        border-bottom: 1px solid #a31c3f;
        overflow-wrap: break-word;
        margin-top: -20px
      }
    `],
  template: `
    <div class="container-fluid tag-modal-container">
      <ng-container *ngFor="let tag of this.tagList">
        <fieldset><h3><legend class="tag-modal-heading">{{'TR_CHANGE_VALUE_AND_QUALITY_OF' | translate}}&nbsp;{{tag.tagName}}</legend></h3>
          <div class="form-group" style="padding:4px">
            <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Bool)" class="form-group">
              <label class="form-check-label">
                <input type="checkbox" class="form-check" [checked]="tag.tagValue === 'On' || tag.tagValue === '1' || tag.tagValue === 'True'" (change)="tagValueBoolOnChange(tag, tag.tagValue)" (paste)="onPaste($event)"/>
                {{'TR_VALUE' | translate}}
              </label>
            </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Char)" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="-128" max="127" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum['Unsigned Char'])" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="0" max="255" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Short)" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="-32768" max="32767" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum['Unsigned Short'])" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="0" max="65535" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Long)" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="-2147483648" max="2147483647" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum['Unsigned Int'])" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="0" max="4294967295" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum['Int 64'])" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="-9223372036854775808" max="9223372036854775807" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum['Unsigned Int 64'])" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" min="0" max="18446744073709551615" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Float)" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Double)" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="number" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (keypress)="onlyNumericChar($event, editorField)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType===TagValueTypeTextEnum.Unknown || tag.tagValueType===TagValueTypeTextEnum.String || tag.tagValueType===TagValueTypeTextEnum.Time)" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>
		          <input type="text" class="form-control" [ngModel]="tag.tagValue" (ngModelChange)="tagValueOnChange(tag, $event)" (paste)="onPaste($event)"/>
	          </div>
	          <div *ngIf="(tag.tagValueType==='Reset To Zero')" class="form-group">
		          <label>{{'TR_VALUE' | translate}}:</label>&nbsp;
              <label>{{tag.tagValue}}</label>&nbsp;&nbsp;&nbsp;
              <button class="btn btn-default" (click)="reset(tag)"><img src="../../images/reset.svg" class="image-button"/>&nbsp;{{'TR_RESET' | translate}}</button>
	          </div>
          </div>
          <div class="form-group" *ngIf="(tag.tagClassName.indexOf('GTWInternalUserDataObject') > -1)">
	          <label>{{'TR_QUALITY' | translate}}:</label>
	          <bitmaskComponent [bitmaskList]="tag.tagQualityList" [bitmaskString]="tag.tagQuality" (bitmaskOnChange)="tagQualityOnChange(tag, $event)"></bitmaskComponent>
          </div>
          <div class="form-group" *ngIf="(tag.tagClassName.indexOf('GTWDnpAnalogInputMdo') > -1 || tag.tagClassName.indexOf('GTWDnpBinaryInputMdo') > -1)">
	          <label>{{'TR_QUALITY' | translate}}:</label>
		        <input type="text" class="form-control" [ngModel]="tag.tagQuality" (ngModelChange)="tagQualityOnChange(tag, $event)" (paste)="onPaste($event)"/>
          </div>
        </fieldset>
      </ng-container>
      <div class="form-group" style="display:inline-block; width:99%; text-align:center;">
	      <div style="display: table-cell;">
		      <button class="btn btn-default" (click)="cancel()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CANCEL' | translate}}</button>
	      </div>
	      <div style="display: table-cell;width:99%"></div>
		    <div style="display: table-cell;">
			    <button class="btn btn-default" (click)="save()"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_SAVE' | translate}}</button>
		    </div>
	    </div>
	    <alertStatusBarComponent></alertStatusBarComponent>
    </div>`
})

export class DashboardConfigTagValueQualityModal implements CloseGuard, ModalComponent<TagQualityModalContext> {
  private context: TagQualityModalContext;
  private tagList: Array<TagObjectDTO> = [];
  private TagValueTypeTextEnum = TagObjectDTO.TagValueTypeTextEnum;
  private inputElement: ElementRef;

  constructor(public dialog: DialogRef<TagQualityModalContext>, private renderer: Renderer2) {
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-md";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }

  public ngOnInit(): void {
    this.tagList = this.dialog.context.tagList.map(x => Object.assign({}, x));
    this.tagList.forEach((tag) => {
      let tagQualityList: Bitmask[] = [];
      tagQualityList.push({ name: "QUALITY_ENUM_GOOD", value: 0x0000, description: "TR_QUALITY_ENUM_GOOD_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_INVALID", value: 0x80, description: "TR_QUALITY_ENUM_INVALID_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_INVALID_TIME", value: 0x08, description: "TR_QUALITY_ENUM_INVALID_TIME_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_BLOCKED", value: 0x010, description: "TR_QUALITY_ENUM_BLOCKED_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_SUBSTITUTED", value: 0x20, description: "TR_QUALITY_ENUM_SUBSTITUTED_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_NOT_TOPICAL", value: 0x40, description: "TR_QUALITY_ENUM_NOT_TOPICAL_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_REF_ERROR", value: 0x0100, description: "TR_QUALITY_ENUM_REF_ERROR_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_IN_TRANSIT", value: 0x0200, description: "TR_QUALITY_ENUM_IN_TRANSIT_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_UNINITIALIZED", value: 0x0400, description: "TR_QUALITY_ENUM_UNINITIALIZED_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_TEST", value: 0x0800, description: "TR_QUALITY_ENUM_TEST_DESC", isChecked: false });
      tagQualityList.push({ name: "QUALITY_ENUM_OVERFLOW", value: 0x01, description: "TR_QUALITY_ENUM_OVERFLOW_DESC", isChecked: false });

      tag["tagQualityList"] = tagQualityList;
      tag["tagValueType"] = TagObjectDTO.getTagPropertyMaskStringValue(tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.VALUE_TYPE);

      if (tag.tagPurposeMask & TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO) {
        tag["tagValueType"] = "Reset To Zero"
      }
    });
  }

  public beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private reset(tag: TagObjectDTO) {
    tag.tagValue = "0";
  }

  private cancel() {
    this.dialog.close({ result: false });
  }

  private tagQualityOnChange(tag: TagObjectDTO, tagQualityString:string): void {
    tag.tagQuality = tagQualityString;
  }

  private tagValueOnChange(tag: TagObjectDTO, tagValue: string): void {
    tag.tagValue = tagValue;
  }

  private tagValueBoolOnChange(tag: TagObjectDTO, tagValue: string): void {
    tag.tagValue = ((tagValue === "On" || tagValue === "1" || tagValue === "True") ? "false" : "true");
  }

  private save() {
    this.dialog.context.tagValueList.tags = [];
    this.tagList.forEach((tag) => {
      let tagValueString: string = tag.tagValue.toString();
      let tagValueDTO: TagValueDTO = { tagName: tag.tagName, tagValue: tagValueString, tagQuality: tag.tagQuality };
      this.dialog.context.tagValueList.tags.push(tagValueDTO);
    });

    this.dialog.close({ result: true });
  }

  private onlyNumericChar(event, editorField): boolean {
    let input = String.fromCharCode(event.keyCode);
    if ((/[^-e+E.0-9]/.test(input))) {
      event.preventDefault();
      return false;
    }
  }

  private onPaste(event: ClipboardEvent) {
    event.preventDefault();
  }
}

export class TagQualityModalContext extends BSModalContext {
  public tagList: Array<TagObjectDTO> = [];
  public tagValueList: TagValuesDTO = {};
}