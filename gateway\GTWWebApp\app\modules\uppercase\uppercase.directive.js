System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, UppercaseDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            UppercaseDirective = (function () {
                function UppercaseDirective() {
                }
                UppercaseDirective.prototype.onInput = function (event) {
                    var input = event.target;
                    input.value = input.value.toUpperCase();
                };
                __decorate([
                    core_1.HostListener('input', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [KeyboardEvent]),
                    __metadata("design:returntype", void 0)
                ], UppercaseDirective.prototype, "onInput", null);
                UppercaseDirective = __decorate([
                    core_1.Directive({
                        selector: '[uppercase]',
                    })
                ], UppercaseDirective);
                return UppercaseDirective;
            }());
            exports_1("UppercaseDirective", UppercaseDirective);
        }
    };
});
//# sourceMappingURL=uppercase.directive.js.map