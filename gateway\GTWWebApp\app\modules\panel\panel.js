System.register([], function (exports_1, context_1) {
    "use strict";
    var Panel;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            Panel = (function () {
                function Panel(lsName, icon, title, titleToolTip, x, y, width, height, zindex, component, visible, parameters, panelHeadingCSS) {
                    if (titleToolTip === void 0) { titleToolTip = ""; }
                    if (parameters === void 0) { parameters = ""; }
                    if (panelHeadingCSS === void 0) { panelHeadingCSS = "panel-heading-bg"; }
                    this.lsName = lsName;
                    this.icon = icon;
                    this.title = title;
                    this.titleToolTip = titleToolTip;
                    this.x = x;
                    this.y = y;
                    this.width = width;
                    this.height = height;
                    this.zindex = zindex;
                    this.component = component;
                    this.visible = visible;
                    this.parameters = parameters;
                    this.panelHeadingCSS = panelHeadingCSS;
                }
                return Panel;
            }());
            exports_1("Panel", Panel);
        }
    };
});
//# sourceMappingURL=panel.js.map