﻿import { <PERSON>mponent, On<PERSON>nit, On<PERSON>roy, ViewChild, ElementRef } from "@angular/core";
import { Subscription } from 'rxjs';
import { ConfigService, FileService, ManageService, WorkspaceService } from "../data/api/api";
import { Health<PERSON>Api } from "../data/wsApi/wsApi";
import { KeysPipe } from "../global/keys.pipe";
import { AlertService, messageLogMask } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { GlobalDataService } from "../global/global.data.service";
import { SDGConfigDTO, BroadcastEventDTO, BroadcastEventTypeEnumDTO, EngineStateEnumDTO, HealthObjectDTO } from "../data/model/models";
import { Modal } from "ngx-modialog-7/plugins/bootstrap";
import { LoaderService } from "../modules/loader/loader.service";
import { Router } from '@angular/router';
import { DownloadComponent } from "../modules/download/download.component";


@Component({
  selector: "settingsComponent",
  templateUrl: "app/settings/settings.component.template.html",
  providers: [KeysPipe],
  styles: [`
		.div-spacer {
			display: inline-block;
			width:20px;
      margin-top: 22px;
		}
		.div-inline {
			display: inline-block;
		}
    fieldset {
      border-radius: 6px;
      border: 1px #a0a0a0 solid;
      padding: 0px 6px 0px 6px;
      margin-bottom: 10px;
    }
    legend{
      padding: 0 10px;
      border-bottom: none;
      margin-bottom: 4px;
      font-weight: bold;
      font-size: 15px;
      width: auto;
    }
	`]
})
export class SettingsComponent implements OnInit, OnDestroy {
  private role: string = "";
  private counter: number;
  private timer: any;
  private currentConfig: SDGConfigDTO = {};
  private workSpaceNameList: string[];
  private workSpaceFileList: string[];
  private selectedWorkSpaceName: string = "";
  private selectedWorkSpaceFile: string = "";
  private isLoading: boolean = false;
  private EngineStateEnumDTO = EngineStateEnumDTO;
  private isLoadingChangeSubscriptionShow: Subscription;
  private healthServiceSubscription: Subscription;

  @ViewChild("download", { static: false }) download: DownloadComponent;
  @ViewChild("uploadWorkspace", { static: false }) uploadWorkspace: ElementRef;
  @ViewChild("uploadFile", { static: false }) uploadFile: ElementRef;

  public supportedLanguages: any[];

  constructor(private alertService: AlertService, private authenticationService: AuthenticationService, private configService: ConfigService, private fileService: FileService, private workspaceService: WorkspaceService,
    private manageService: ManageService, private healthWSApi: HealthWSApi, private translate: TranslateService, private modal: Modal, private globalDataService: GlobalDataService,
    private keysPipe: KeysPipe, private loaderService: LoaderService, private router: Router) {
    this.supportedLanguages = [
      { display: "English", value: "en" },
      //{ display: "Français", value: "fr" },
      //{ display: "Español", value: "es" },
      //{ display: "Português", value: "pt" },
    ];
  }

  public ngOnInit(): void {
    this.getConfig();
    this.getHealth();
    this.isLoadingChangeSubscriptionShow = this.loaderService.isLoadingChange.subscribe((value) => {
      this.isLoading = value;
    });
  }

  public ngOnDestroy(): void {
    if (this.isLoadingChangeSubscriptionShow)
      this.isLoadingChangeSubscriptionShow.unsubscribe();
    if (this.healthServiceSubscription != null)
      this.healthServiceSubscription.unsubscribe();
  }

  private getHealth(): void {
    this.healthServiceSubscription = this.healthWSApi.getHealthData().subscribe(
      event => {
        if (event.type === 'message') {
          let healthData: HealthObjectDTO = JSON.parse(event.data);
          if (healthData != null && healthData.engineState == EngineStateEnumDTO.STARTUPSTARTING.toString()) {
            this.configService.getConfig().subscribe(
              data => {
                this.getConfig();
              }
            );
          }
        }
      }
    );
  }

  private getConfig(): void {
    this.configService.getConfig().subscribe(
      data => {
        this.globalDataService.SDGConfig = data;
        this.getWorkSpacesList();
      },
      error => {
        this.alertService.debug(error.toString());
      }
    );
  }

  private getWorkSpacesList(): void {
    this.fileService.workSpacesGet().subscribe(
      (data: any) => {
        if (data.result) {
          this.workSpaceNameList = this.keysPipe.transformJson(data.workspaces);
          this.getWorkSpaceFileList();
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_AVAILABLE"); }
      }
    );
  }

  private getWorkSpaceFileList(): void {
    if (this.selectedWorkSpaceName !== "") {
      this.fileService.filesGet("Workspaces", "", this.selectedWorkSpaceName).subscribe(
        (data: any) => {
          if (data.result) {
            this.workSpaceFileList = this.keysPipe.transformJson(data.files);
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_WORKSPACE_FILE_NOT_AVAILABLE"); }
        }
      );
    }
  }

  private onLanguageChange(langSelectvalue): void {
    this.translate.use(langSelectvalue)
    localStorage.setItem("SDGLanguage", langSelectvalue);
  }

  private onStopMonClick(both: boolean): void {
    this.manageLoading(true);
    this.configService.stopMon(both).subscribe(
      data => this.alertService.success("TR_MONITOR_IS_RE_STARTING"),
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); }
        this.manageLoading(false);
      },
      () => {
        this.healthWSApi.websocket.close(3000, "restart");
        this.refreshBrowser();
      }
    );
  }

  private refreshBrowser(): void {
    this.counter = 60;
    window.clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.counter--;
      this.translate.get("TR_YOUR_BROWSER_WILL_REFRESH_IN_X_SECONDS", { value: this.counter.toString() }).subscribe(res => {
        this.alertService.warning(res, false, messageLogMask.statusBar, 0);
      });
      if (this.counter === 0) {
        window.clearInterval(this.timer);
        /*After counter value is 0 means 10000 is completed */
        location.reload();
        this.alertService.clearMessage();
      }
    }, 1000);
  }

  private onManageGTWClick(event): void {
    if (this.getIdWithEvent(event) != undefined) {
      if (this.getIdWithEvent(event) == "stopMonitorEngine") {
        this.confirmSaveModal("stopMonitorEngine");
      }
      if (this.getIdWithEvent(event) == "stopMonitor") {
        this.confirmSaveModal("stopMonitor");
      }
      if (this.getIdWithEvent(event) == "stopEngine") {
        this.confirmSaveModal("stopEngine");
      }
      else if (this.getIdWithEvent(event) == "newWorkSpace") {
        this.confirmSaveModal("newWorkSpace");
      }
      else if (this.getIdWithEvent(event) == "saveGTW") {
        this.confirmSaveModal("saveGTW");
      }
      else if (this.getIdWithEvent(event) == "forceSaveGTW") {
        this.confirmSaveModal("forceSaveGTW");
      }
      
    }
  }

  private manageGTW(event: string): void {
    if (event == "stopEngine") {
      this.manageLoading(true);
      this.configService.stopEngine().subscribe(
        data => this.alertService.success("TR_ENGINE_IS_RE_STARTING"),
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
          this.manageLoading(false);
        }
      );
    }
    else if (event == "newWorkSpace") {
      let today = new Date();
      let date = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate();
      let time = today.getHours() + "-" + today.getMinutes() + "-" + today.getSeconds();
      let tmwgtwayDefaultFileName = "tmwgtway_" + date + "_" + time;

      let promptNewWorkSpaceModalRef;
      this.translate.get("TR_WHAT_NAME_DO_YOU_TO_USE_FOR_YOUR_NEW_WORKSPACE").subscribe(res => {
        promptNewWorkSpaceModalRef = this.modal.prompt()
          .defaultValue(tmwgtwayDefaultFileName)
          .size('lg')
          .showClose(false)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_OK'))
          .okBtnClass('btn btn-default')
          .body(`
				<div class="panel panel-warning">
					<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				</div>
        `)
          .open()
      });
      promptNewWorkSpaceModalRef.result.then(
        (result) => {
          var regForbiddenCharacters = /^[^\\/:\*\?"<>\|]+$/; // forbidden characters \ / : * ? " < > |
          if (!regForbiddenCharacters.test(result)) {
            this.alertService.error("TR_ERROR_INVALID_FILENAME");
            return;
          }
          this.manageLoading(true);
          this.configService.newWorkSpace(result).subscribe(
            data => this.alertService.success("TR_ENGINE_IS_RE_STARTING"),
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
              this.manageLoading(false);
            },
            () => {
              this.getConfig();
            }
          );
        },
        () => { }
      );
    }
    else if (event == "selectedWorkSpace") {
      this.manageLoading(true);
      this.configService.selectWorkSpace(this.selectedWorkSpaceName).subscribe(
        (data: any) => this.alertService.success("TR_ENGINE_IS_RE_STARTING"),
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_RE_STARTING_ENGINE"); }
          this.manageLoading(false);
        },
        () => {
          this.getConfig();
        }
      );
    }
    else if (event == "stopMonitorEngine") {
      this.onStopMonClick(true);
    }
    else if (event == "stopMonitor") {
      this.onStopMonClick(false);
    }
    else if (event == "workspaceDownload") {
      this.onWorkspaceDownloadClick();
    }
    else if (event == "saveAsDashboard") {
      this.saveAsDashboardConfig();
    }
  }

  private runSelectedWorkSpace(event): void {
    if (this.selectedWorkSpaceName != "")
      this.confirmSaveModal("selectedWorkSpace");
  }

  private onFileUploadChange(event): void {
    if (this.selectedWorkSpaceName != undefined && this.selectedWorkSpaceName != "") {
      let fileList: FileList = event.target.files;
      if (fileList.length > 0) {
        let file: File = fileList[0];
        //let formData: FormData = new FormData();
        //formData.append("uploadFile", file, file.name);
        this.fileService.filePost(file, "", this.selectedWorkSpaceName).subscribe(
          data => {
            this.uploadFile.nativeElement.value = "";
            this.alertService.success("TR_FILE_SAVED");
            this.getWorkSpaceFileList();
          },
          error => {
            if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_SAVED"); }
          }
        );
      }
    }
    else {
      let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_PLEASE_SELECT_A_WORKSPACE", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageWarning };
      this.alertService.show(broadcastEvent, false);
    }
  }

  private onFileDownloadClick(event): void {
    if (this.selectedWorkSpaceFile != undefined && this.selectedWorkSpaceFile != "") {
      let fileName: string = this.selectedWorkSpaceName + "/" + this.selectedWorkSpaceFile;
      this.download.downloadClick(fileName, "Workspaces");
    }
    else {
      let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_PLEASE_SELECT_A_FILE", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageWarning };
      this.alertService.show(broadcastEvent, false);
    }
  }

  private getIdWithEvent(event): any {
    let target = event.target || event.srcElement || event.currentTarget;
    return target.attributes.id.value;
  }

  private confirmSaveModal(event: string): void {
    if (this.globalDataService != null && this.globalDataService.healthData != null &&
      (this.globalDataService.healthData.engineState === EngineStateEnumDTO.RUNNING.toString()
        || this.globalDataService.healthData.engineState === EngineStateEnumDTO.ERRORININI.toString()
        || this.globalDataService.healthData.engineState === EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString()
        || this.globalDataService.healthData.engineState === EngineStateEnumDTO.RUNNING_NO_LICENSE.toString()
      )) {
      let confirmSaveModalRef;
      this.translate.get("TR_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_WORKSPACE").subscribe(res => {
        confirmSaveModalRef = this.modal.confirm()
          .size('lg')
          .showClose(false)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_YES'))
          .cancelBtn(this.translate.instant('TR_NO'))
          .okBtnClass('btn btn-default')
          .body(`
				<div class="panel panel-warning">
					<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				</div>
        `)
          .open()
      });
      confirmSaveModalRef.result.then(
        (result) => {
          if (event == "forceSaveGTW")
            this.saveDashboardConfig(event, true);
          else
            this.saveDashboardConfig(event);
        },
        () => {
          this.manageGTW(event);
        }
      );
    }
    else {
      this.manageGTW(event);
    }
  }

  private saveDashboardConfig(event: string, overrideLoadError: boolean = false): void {
    this.loaderService.toggleIsLoading(true);
    this.manageService.saveFile(overrideLoadError).subscribe(
      data => this.alertService.success("TR_FILE_SAVED"),
      error => {
        if (error.status == 401) {
          this.authenticationService.onLoginFailed("/");
        }
        else {
          if (!overrideLoadError)
            this.alertService.show({ messageKey: "TR_THERE_WERE_PROBLEMS_SAVING_THE_INI_CSV_FILES", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError }, false);
          this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
        }
        this.loaderService.toggleIsLoading(false);
      },
      () => {
        this.loaderService.toggleIsLoading(false);
        this.manageGTW(event);
      }
    );
  }

  private saveAsDashboard(): void {
    this.confirmSaveModal("saveAsDashboard");
  }


  private saveAsDashboardConfig(): void {
    let today = new Date();
    let date = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate();
    let time = today.getHours() + "-" + today.getMinutes() + "-" + today.getSeconds();
    let newWorkSpaceName: string = this.globalDataService?.SDGConfig?.currentWorkSpaceName + "_" + date + "_" + time;
    this.translate.get("TR_PLEASE_ENTER_A_NEW_WORKSPACE_NAME").subscribe(res => {
      newWorkSpaceName = prompt(res, newWorkSpaceName);
      if (newWorkSpaceName == null) {
        return;
      }
      if (newWorkSpaceName == "") {
        alert(res);
        return;
      }
      this.loaderService.toggleIsLoading(true);
      this.manageService.saveFileCopyAs(newWorkSpaceName).subscribe(
        data => {
          this.getWorkSpacesList();
          this.alertService.success("TR_FILE_SAVED");
        },
        error => {
          if (error.status == 401) {
            this.authenticationService.onLoginFailed("/");
          }
          else if (error.error) {
            this.alertService.error(error.error);
          }
          else {
            this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
          }
          this.loaderService.toggleIsLoading(false);
        },
        () => {
          this.loaderService.toggleIsLoading(false);
        }
      );
    });
  }

  private manageLoading(isLoading: boolean): void {
    this.loaderService.toggleIsLoading(isLoading);
    if (this.isLoading) {
      let loadingInterval = setInterval(() => {
        if (this.globalDataService != null && this.globalDataService.healthData != null && this.globalDataService.healthData.engineState === EngineStateEnumDTO.RUNNING.toString()) {
          this.loaderService.toggleIsLoading(false);
          clearInterval(loadingInterval);
          this.ngOnInit();
        }
      }, 10000);
    }
  }

  private onWorkspaceUploadChange(event): void {
    let fileList: FileList = event.target.files;
    if (fileList.length > 0 && fileList[0].name.includes(".gws")) {
      let file: File = fileList[0];
      this.uploadWorkspace.nativeElement.value = "";
      this.workspaceService.workspacePost(file).subscribe(
        data => {
          this.uploadWorkspace.nativeElement.value = "";
          this.alertService.success("TR_WORKSPACE_IMPORTED");
          this.getWorkSpacesList();
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); }
          else if (error.status == 409) {
            let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_ERROR_WORKSPACE_ALREADY_PRESENT", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError, parameters: { WORKSPACE: file.name.replace(".gws", "") } };
            this.alertService.show(broadcastEvent, false);
          }
          else { this.alertService.error("TR_ERROR_WORKSPACE_NOT_IMPORTED"); }
        }
      );
    }
  }

  private onWorkspaceDownloadClick(): void {
    if (this.selectedWorkSpaceName != undefined && this.selectedWorkSpaceName != "") {
      this.workspaceService.workspaceGet(this.selectedWorkSpaceName).subscribe(
        (data: any) => {
          this.download.saveData(data, this.selectedWorkSpaceName + ".gws");
          this.alertService.success("TR_FILE_SAVED");
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_SAVED"); }
        }
      );
    }
    else {
      let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_PLEASE_SELECT_A_WORKSPACE", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageWarning };
      this.alertService.show(broadcastEvent, false);
    }
  }

  private deleteWorkspace(fileName: string): void {
    if (this.selectedWorkSpaceName != undefined && this.selectedWorkSpaceName != "") {
      let workspaceModalDeleteRef;
      this.translate.get("TR_ARE_YOU_SURE_TO_DELETE_WORKSPACE", { WORKSPACE: this.selectedWorkSpaceName }).subscribe(res => {
        workspaceModalDeleteRef = this.modal.confirm()
          .size('lg')
          .showClose(true)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_DELETE'))
          .okBtnClass('btn btn-default')
          .body(`
					<div class="panel panel-warning">
						<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					</div>
        `)
          .open()
      });
      workspaceModalDeleteRef.result.then(
        (result) => {
          if (result) {
            this.workspaceService.workspaceDelete(this.selectedWorkSpaceName).subscribe(
              (data: any) => {
                this.translate.get("TR_WORKSPACE_DELETED").subscribe(res => {
                  this.alertService.success(res);
                  this.getWorkSpacesList();
                });
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); }
                else if (error.status == 409) {
                  let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_ERROR_WORKSPACE_IS_CURRENTLY_RUNNING", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError };
                  this.alertService.show(broadcastEvent, false);
                }
                else { this.alertService.error("TR_ERROR_WORKSPACE_NOT_DELETED"); }
              }
            );
          }
        },
        () => { } //needed
      );
    }
    else {
      let broadcastEvent: BroadcastEventDTO = { messageKey: "TR_PLEASE_SELECT_A_WORKSPACE", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageWarning };
      this.alertService.show(broadcastEvent, false);
    }
  }
}