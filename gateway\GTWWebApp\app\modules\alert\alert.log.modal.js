System.register(["@angular/core", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ngx_modialog_7_1, bootstrap_1, AlertLogModal, MessagesModalContext;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            }
        ],
        execute: function () {
            AlertLogModal = (function () {
                function AlertLogModal(dialog) {
                    this.dialog = dialog;
                    this.messages = [];
                    this.context = dialog.context;
                    this.messages = this.context.messages;
                    this.title = this.context.title;
                    this.context.dialogClass = "modal-dialog modal-md";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                }
                AlertLogModal.prototype.ngOnInit = function () {
                    this.scrollToBottom();
                };
                AlertLogModal.prototype.ngAfterViewChecked = function () {
                    this.scrollToBottom();
                };
                AlertLogModal.prototype.close = function () {
                    this.dialog.close({ result: true });
                };
                AlertLogModal.prototype.beforeDismiss = function () {
                    return true;
                };
                AlertLogModal.prototype.scrollToBottom = function () {
                    try {
                        if ((this.myScrollContainer.nativeElement.scrollHeight - this.myScrollContainer.nativeElement.scrollTop) < 200)
                            this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
                    }
                    catch (err) { }
                };
                __decorate([
                    core_1.ViewChild("scrollMe", { static: false }),
                    __metadata("design:type", core_1.ElementRef)
                ], AlertLogModal.prototype, "myScrollContainer", void 0);
                AlertLogModal = __decorate([
                    core_1.Component({
                        selector: "alertLogModal",
                        template: "<div class=\"container-fluid tag-options-modal-container\">\n                <div class=\"tag-modal-heading\">\n                 {{this.title | translate}}\n\t              </div>\n                <div #scrollMe style=\"max-height:550px;width:550px;overflow: auto; margin:20px\">\n                  <table style=\"width:100%;background-image: linear-gradient(to bottom, rgba(231, 240, 247, 0.85), rgba(231, 240, 247, 0.95));\">\n\t\t\t\t\t\t\t\t\t  <tr *ngFor=\"let message of messages;\">\n                      <td *ngIf=\"message.type=='information'\" class=\"td-icon\"><div class=\"glyphicon glyphicon-info-sign icon\" style=\"color: blue;\"></div></td>\n                      <td *ngIf=\"message.type=='warning'\" class=\"td-icon\"><div class=\"glyphicon glyphicon-exclamation-sign icon\" style=\"color: orange;\"></div></td>\n                      <td *ngIf=\"message.type=='error'\" class=\"td-icon\"><div class=\"glyphicon glyphicon-warning-sign icon\" style=\"color: red;\"></div></td>\n                      <td *ngIf=\"message.type=='success'\" class=\"td-icon\"><div class=\"glyphicon glyphicon-ok-sign icon\" style=\"color: green;\"></div></td>\n                      <td *ngIf=\"message.type=='debug'\" class=\"td-icon\"><div class=\"glyphicon glyphicon-bell icon\" style=\"color: yellow;\"></div></td>\n\t\t\t\t\t\t\t\t\t\t  <td>{{message.type | translate }}</td>\n                      <td>{{message.text | translate: { arg1: message.arg1, arg2: message.arg2, arg3: message.arg3 } }}</td>\n\t\t\t\t\t\t\t\t\t  </tr>\n\t\t\t\t\t\t\t  </table>\n              </div>\n              <div style=\"margin-left: 480px;margin-bottom: 16px;\">\n\t\t            <button type=\"reset\" class=\"btn btn-default\" (click)=\"close()\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CLOSE' | translate}}</button>\n\t\t          </div>\n            </div>",
                        styles: ["\n      td {\n\t\t\t\tpadding-right: 6px;\n\t\t\t\tpadding-left: 6px;\n        padding-top: 6px;\n      }\n      tr:nth-of-type(odd) {\n          background-color: rgba(125, 125, 0, 0.05) !important;\n      }\n      .td-icon {\n        width: 10px;\n      }\n      .icon {\n        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n      }\n      .tag-modal-heading {\n        background-color: #d8d8d8;\n        padding: 8px 10px 6px 10px;\n        font-size: 22px;\n        font-weight: bold;\n        border-bottom: 1px solid #a31c3f;\n        overflow-wrap: break-word;\n        border-top-left-radius: 6px;\n        border-top-right-radius: 6px;\n      }\n      "]
                    }),
                    __metadata("design:paramtypes", [ngx_modialog_7_1.DialogRef])
                ], AlertLogModal);
                return AlertLogModal;
            }());
            exports_1("AlertLogModal", AlertLogModal);
            MessagesModalContext = (function (_super) {
                __extends(MessagesModalContext, _super);
                function MessagesModalContext() {
                    var _this = _super !== null && _super.apply(this, arguments) || this;
                    _this.messages = [];
                    return _this;
                }
                return MessagesModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("MessagesModalContext", MessagesModalContext);
        }
    };
});
//# sourceMappingURL=alert.log.modal.js.map