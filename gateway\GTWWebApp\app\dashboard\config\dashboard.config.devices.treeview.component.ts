﻿import { Component, Input, Output, EventEmitter } from "@angular/core";
import { TreeNodeDTO } from "../../data/model/models";
import { NodesService } from "../../data/api/api";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { GlobalJsonUtil } from '../../global/global.json.util';

@Component({
  selector: "dashboardConfigDevicesTreeViewComponent",
  styles: [`
		  .icon-button {
        font-size: 8px;
			  display: inline-block; 
			  margin-right: 5px;
			  cursor: pointer;
			  color:#fffaaa;
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
		  }
		  ul {
			  padding-left: 6px;
			  list-style-type: none;
		  }
		  li{
			  margin-top: 4px;
		  }
		  ol, ul {
			  margin-top: 0;
		  }
      .leaf{
        white-space: nowrap;
      }
      .leaf:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .leaf-text{
        cursor: pointer;
        display: inline-block;
        width: 90%;
      }
      .glyphicon-none {
        padding-right: 8px;  
        color: transparent !important;
      }
      .node-text{
        cursor: pointer;
        color:black;
        text-decoration:none;
        vertical-align: bottom;
      }
      .search-text {
        cursor: pointer;
        color: #337ab7;
        font-style: italic;
        text-decoration:none;
        vertical-align: bottom;
      }`
  ],
  template: `<ul>
								<li>
                  <div class="leaf" [class.is-selected]="selectedNode === deviceNode && isDevicesTreeViewSelected" [dashboardConfigContextMenuDirective]="deviceNode?.nodeFullName" [node]=deviceNode [rootNode]=deviceRootNode>
										  <div *ngIf="deviceNode?.hasChildren == false" style="cursor:not-allowed;" class="glyphicon-none icon-button"></div>
										  <div *ngIf="deviceNode?.hasChildren == true && deviceNode?.isExpanded" class="glyphicon glyphicon-triangle-bottom icon-button" (click)="toggleNode(deviceNode)" title="{{TR_COLLAPSE_CHILDREN | translate}}"></div>
										  <div *ngIf="deviceNode?.hasChildren == true && !deviceNode?.isExpanded" class="glyphicon glyphicon-triangle-right icon-button" (click)="toggleNode(deviceNode)" title="{{TR_EXPAND_CHILDREN | translate}}"></div>
                      <div (click)="onSelectNode(deviceNode)" class="leaf-text" myDropTarget (myDrop)="onDrop($event)" (dragover)="onDragover($event)"(dragleave)="onDragleave($event)" >
                        <img [src]="'../../images/gtw_objects/' + (deviceNode?.nodeIcon | gtwImage)" [ngClass]="{'blinking-red' : (!deviceNode?.isHealthy && deviceRootNode?.isWarningActive)}" class="image"/>
									      <a  [tooltip]="[deviceNode?.nodeDescription]" [className]="isNodeTreeFromSearch ? 'search-text' : 'node-text'">{{deviceNode?.nodeLabel}}</a>
                      </div>
                  </div>
									<ng-container *ngFor="let childrenDeviceNode of deviceNode?.children">
										<dashboardConfigDevicesTreeViewComponent *ngIf="deviceNode?.isExpanded" [(selectedNode)]="selectedNode" (onSelectedChanged)="onSelectNode($event)" [isDevicesTreeViewSelected]="isDevicesTreeViewSelected" [deviceNode]="childrenDeviceNode" [deviceRootNode]="deviceRootNode" [isNodeTreeFromSearch]="isNodeTreeFromSearch" (onToggleNode)="onToggleNodeRecursive($event)" (onDropNode)="onDropNodeRecursive($event)"></dashboardConfigDevicesTreeViewComponent>
									</ng-container>
								</li>
							</ul>`
})

export class DashboardConfigDevicesTreeViewComponent {
  @Input() deviceNode: TreeNodeDTO;
  @Input() deviceRootNode: TreeNodeDTO;
  @Input() selectedNode: TreeNodeDTO;
  @Input() isDevicesTreeViewSelected: boolean;
  @Input() isNodeTreeFromSearch: boolean; 
  @Output() onSelectedChanged: EventEmitter<TreeNodeDTO> = new EventEmitter();
  @Output() onDropNode: EventEmitter<any> = new EventEmitter();
  @Output() onToggleNode: EventEmitter<any> = new EventEmitter();

  constructor(private alertService: AlertService, private nodesService: NodesService, private authenticationService: AuthenticationService) { }

  private onSelectNode(node: TreeNodeDTO): void {
    this.onSelectedChanged.emit(node);
  }

  private toggleNode(node: TreeNodeDTO): void {
    if (!node.isExpanded && this.deviceNode.hasChildren && this.deviceNode.nodeClassName != "GatewayRootNode") {
      this.loadTagsTreeview();
    }
    node.isExpanded = !node.isExpanded;
  }

  private onToggleNodeRecursive(node: TreeNodeDTO) {
    this.onToggleNode.emit(node);
  }

  private onDrop(dragData: any): void {
    if (this.deviceNode.isMappingTarget) {
      let dropData: Array<any> = [dragData, this.deviceNode];
      this.onDropNode.emit(dropData);
    }
  }

  private onDragleave(event: any) {
    if (event && event.currentTarget)
      event.currentTarget.style.background = "";
  }

  private onDragover(event: any): void {
    if (this.deviceNode.isMappingTarget) {
      if (event && event.currentTarget)
        event.currentTarget.style.background = "rgba(251, 253, 180, 0.95)";
      event.preventDefault();
    }
  } 

  private onDropNodeRecursive(dropData: any) {
    this.onDropNode.emit(dropData);
  }

  private loadTagsTreeview(): void {
    try {
      this.nodesService.getNodes(this.deviceNode.nodeFullName, null, false, true, <any>this.deviceNode.nodeCollectionKind.toString()).subscribe(
        data => {
          //this.deviceNode.children = GlobalJsonUtil.sortByProperty(data.children, "nodeName");
          this.deviceNode.children = data.children;
          this.onToggleNodeRecursive(this.deviceNode);
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        }
      );
    }
    catch (err) {
      return null;
    }
  }
}

