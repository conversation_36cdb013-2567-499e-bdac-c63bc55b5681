System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, DropdownDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            DropdownDirective = (function () {
                function DropdownDirective() {
                    this.isOpen = false;
                }
                Object.defineProperty(DropdownDirective.prototype, "opened", {
                    get: function () {
                        return this.isOpen;
                    },
                    enumerable: false,
                    configurable: true
                });
                DropdownDirective.prototype.open = function () {
                    this.isOpen = true;
                };
                DropdownDirective.prototype.close = function () {
                    this.isOpen = false;
                };
                __decorate([
                    core_1.HostBinding('class.open'),
                    __metadata("design:type", Object),
                    __metadata("design:paramtypes", [])
                ], DropdownDirective.prototype, "opened", null);
                __decorate([
                    core_1.HostListener('mouseover'),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", []),
                    __metadata("design:returntype", void 0)
                ], DropdownDirective.prototype, "open", null);
                __decorate([
                    core_1.HostListener('mouseleave'),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", []),
                    __metadata("design:returntype", void 0)
                ], DropdownDirective.prototype, "close", null);
                DropdownDirective = __decorate([
                    core_1.Directive({
                        selector: '[dropdown]'
                    }),
                    __metadata("design:paramtypes", [])
                ], DropdownDirective);
                return DropdownDirective;
            }());
            exports_1("DropdownDirective", DropdownDirective);
        }
    };
});
//# sourceMappingURL=dropdown.directive.js.map