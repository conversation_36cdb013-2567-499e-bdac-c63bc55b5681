System.register(["@angular/core", "./modules/alert/alert.service", "./authentication/authentication.service", "@ngx-translate/core", "./global/translateKey.pipe"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, alert_service_1, authentication_service_1, core_2, translateKey_pipe_1, AppInfoComponent, InfoTypeEnum;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (translateKey_pipe_1_1) {
                translateKey_pipe_1 = translateKey_pipe_1_1;
            }
        ],
        execute: function () {
            AppInfoComponent = (function () {
                function AppInfoComponent(alertService, authenticationService, translate, translateKeyPipe) {
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.translate = translate;
                    this.translateKeyPipe = translateKeyPipe;
                    this.isVisible = false;
                    this.infoTypeEnumEnum = InfoTypeEnum;
                    this.isHttpsWarningAtStartupEnabled = false;
                }
                AppInfoComponent.prototype.hideHttpsWarningAtStartup = function () {
                    if (this.isHttpsWarningAtStartupEnabled) {
                        localStorage.setItem("SDGHideHTTPSWarningAtStartup", JSON.stringify(false));
                    }
                    else {
                        localStorage.setItem("SDGHideHTTPSWarningAtStartup", JSON.stringify(true));
                    }
                    this.isHttpsWarningAtStartupEnabled = !this.isHttpsWarningAtStartupEnabled;
                };
                Object.defineProperty(AppInfoComponent.prototype, "setInfoType", {
                    set: function (value) {
                        this.infoType = value;
                    },
                    enumerable: false,
                    configurable: true
                });
                AppInfoComponent.prototype.show = function () {
                    this.isVisible = true;
                };
                AppInfoComponent.prototype.close = function () {
                    this.isVisible = false;
                };
                AppInfoComponent = __decorate([
                    core_1.Component({
                        selector: "appInfoComponent",
                        providers: [translateKey_pipe_1.TranslateKeyPipe],
                        styles: ["\n      .panel {\n        position: absolute;\n        z-index: 999;\n        margin: auto;\n        width: 50%;\n        top: 80px;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n      .panel-heading{\n        padding: 4px;\n      }\n      .panel-body{\n        padding: 8px;\n        \n      }\n      .button-close {\n        position: absolute;\n        top: 6px;\n        right: 6px;\n      }\n      "
                        ],
                        template: "\n      <div class=\"modal-backdrop fade show\" *ngIf=\"isVisible\"></div>\n      <div class=\"panel-main panel panel-default\" *ngIf=\"isVisible\" style=\"width: auto; z-index: 1042; filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .8));\">\n        <ng-container *ngIf=\"infoType === infoTypeEnumEnum.HTTPS_WARNING\">\n          <div class=\"panel-heading\">\n            {{'TR_HTTPS_WARNING_TITLE' | translate}}\n            <div class=\"button-close round-button\" title=\"{{'TR_CLOSE' | translate}}\" (click)=\"close()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n          </div>\n          <div class=\"panel-body\">\n            <div [innerHtml]=\"'TR_HTTPS_WARNING_DESCRIPTION' | translate\"></div>\n            <br>\n            <label class=\"form-check-label\" >\n              <input type=\"checkbox\" class=\"form-check\" [checked]=\"isHttpsWarningAtStartupEnabled\" (change)=\"hideHttpsWarningAtStartup()\"/>\n                {{ 'TR_HTTPS_WARNING_AT_STARTUP' | translate }}\n            </label>\n          </div>\n        </ng-container>\n        <ng-container *ngIf=\"infoType === infoTypeEnumEnum.HTTPS_TMW_CERT\">\n          <div class=\"panel-heading\">\n            {{'TR_HTTPS_TMW_CERT_TITLE' | translate}}\n            <div class=\"button-close round-button\" title=\"{{'TR_CLOSE' | translate}}\" (click)=\"close()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n          </div>\n          <div class=\"panel-body\">\n            <div [innerHtml]=\"'TR_HTTPS_TMW_CERT_DESCRIPTION' | translate\"></div>\n          </div>\n        </ng-container>\n        <ng-container *ngIf=\"infoType === infoTypeEnumEnum.BROWSER_WARNING\">\n          <div class=\"panel-heading\">\n            {{'TR_BROWSER_WARNING_TITLE' | translate}}\n            <div class=\"button-close round-button\" title=\"{{'TR_CLOSE' | translate}}\" (click)=\"close()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n          </div>\n          <div class=\"panel-body\">\n            <div [innerHtml]=\"'TR_HTTPS_BROWSER_DESCRIPTION' | translate\"></div>\n          </div>\n        </ng-container>\n      </div>\n"
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, authentication_service_1.AuthenticationService, core_2.TranslateService, translateKey_pipe_1.TranslateKeyPipe])
                ], AppInfoComponent);
                return AppInfoComponent;
            }());
            exports_1("AppInfoComponent", AppInfoComponent);
            (function (InfoTypeEnum) {
                InfoTypeEnum[InfoTypeEnum["HTTPS_WARNING"] = 0] = "HTTPS_WARNING";
                InfoTypeEnum[InfoTypeEnum["HTTPS_TMW_CERT"] = 1] = "HTTPS_TMW_CERT";
                InfoTypeEnum[InfoTypeEnum["BROWSER_WARNING"] = 2] = "BROWSER_WARNING";
            })(InfoTypeEnum || (InfoTypeEnum = {}));
            exports_1("InfoTypeEnum", InfoTypeEnum);
        }
    };
});
//# sourceMappingURL=app.info.component.js.map