System.register(["@angular/core", "@angular/common/http", "../encoder", "../variables", "../configuration"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, http_1, encoder_1, variables_1, configuration_1, AuthService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (encoder_1_1) {
                encoder_1 = encoder_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            }
        ],
        execute: function () {
            AuthService = (function () {
                function AuthService(httpClient, basePath, configuration) {
                    this.httpClient = httpClient;
                    this.basePath = 'http://localhost/rest';
                    this.defaultHeaders = new http_1.HttpHeaders();
                    this.configuration = new configuration_1.Configuration();
                    if (basePath) {
                        this.basePath = basePath;
                    }
                    if (configuration) {
                        this.configuration = configuration;
                        this.basePath = basePath || configuration.basePath || this.basePath;
                    }
                }
                AuthService.prototype.canConsumeForm = function (consumes) {
                    var form = 'multipart/form-data';
                    for (var _i = 0, consumes_1 = consumes; _i < consumes_1.length; _i++) {
                        var consume = consumes_1[_i];
                        if (form === consume) {
                            return true;
                        }
                    }
                    return false;
                };
                AuthService.prototype.addUser = function (username, password, isactive, role, email, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling addUser.');
                    }
                    if (password === null || password === undefined) {
                        throw new Error('Required parameter password was null or undefined when calling addUser.');
                    }
                    if (isactive === null || isactive === undefined) {
                        throw new Error('Required parameter isactive was null or undefined when calling addUser.');
                    }
                    if (role === null || role === undefined) {
                        throw new Error('Required parameter role was null or undefined when calling addUser.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (password !== undefined) {
                        formParams = formParams.append('password', password) || formParams;
                    }
                    if (email !== undefined) {
                        formParams = formParams.append('email', email) || formParams;
                    }
                    if (isactive !== undefined) {
                        formParams = formParams.append('isactive', isactive) || formParams;
                    }
                    if (role !== undefined) {
                        formParams = formParams.append('role', role) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/user/" + encodeURIComponent(String(username)), convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.changeUserPassword = function (username, oldpassword, password, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling changeUserPassword.');
                    }
                    if (oldpassword === null || oldpassword === undefined) {
                        throw new Error('Required parameter oldpassword was null or undefined when calling changeUserPassword.');
                    }
                    if (password === null || password === undefined) {
                        throw new Error('Required parameter password was null or undefined when calling changeUserPassword.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (username !== undefined) {
                        formParams = formParams.append('username', username) || formParams;
                    }
                    if (oldpassword !== undefined) {
                        formParams = formParams.append('oldpassword', oldpassword) || formParams;
                    }
                    if (password !== undefined) {
                        formParams = formParams.append('password', password) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/change_user_password", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.checkAuth = function (token, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (token === null || token === undefined) {
                        throw new Error('Required parameter token was null or undefined when calling checkAuth.');
                    }
                    var headers = this.defaultHeaders;
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (token !== undefined) {
                        formParams = formParams.append('token', token) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/check_auth", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.deleteUser = function (username, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling deleteUser.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'text/plain'
                    ];
                    return this.httpClient.delete(this.basePath + "/user/" + encodeURIComponent(String(username)), {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.getUser = function (username, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling getUser.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'text/plain'
                    ];
                    return this.httpClient.get(this.basePath + "/user/" + encodeURIComponent(String(username)), {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.getUsers = function (userFilter, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (userFilter !== undefined && userFilter !== null) {
                        queryParameters = queryParameters.set('userFilter', userFilter);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/users", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.isTokenValid = function (token, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (token === null || token === undefined) {
                        throw new Error('Required parameter token was null or undefined when calling isTokenValid.');
                    }
                    var headers = this.defaultHeaders;
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'text/plain'
                    ];
                    return this.httpClient.get(this.basePath + "/is_token_valid/" + encodeURIComponent(String(token)), {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.loginUser = function (username, password, isForcingUsersLogoff, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling loginUser.');
                    }
                    if (password === null || password === undefined) {
                        throw new Error('Required parameter password was null or undefined when calling loginUser.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (username !== undefined) {
                        formParams = formParams.append('username', username) || formParams;
                    }
                    if (password !== undefined) {
                        formParams = formParams.append('password', password) || formParams;
                    }
                    if (isForcingUsersLogoff !== undefined) {
                        formParams = formParams.append('isForcingUsersLogoff', isForcingUsersLogoff) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/login_user", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.logoffRemoteUser = function (remoteUsername, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (remoteUsername === null || remoteUsername === undefined) {
                        throw new Error('Required parameter remoteUsername was null or undefined when calling logoffRemoteUser.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (remoteUsername !== undefined) {
                        formParams = formParams.append('remoteUsername', remoteUsername) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/logoff_remote_user", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.logoffUser = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    return this.httpClient.post(this.basePath + "/logoff_user", null, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.logoffUserForce = function (session, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (session === null || session === undefined) {
                        throw new Error('Required parameter session was null or undefined when calling logoffUserForce.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (session !== undefined) {
                        formParams = formParams.append('session', session) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/logoff_user_force", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.resetUserPassword = function (username, password, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling resetUserPassword.');
                    }
                    if (password === null || password === undefined) {
                        throw new Error('Required parameter password was null or undefined when calling resetUserPassword.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (username !== undefined) {
                        formParams = formParams.append('username', username) || formParams;
                    }
                    if (password !== undefined) {
                        formParams = formParams.append('password', password) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/reset_user_password", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService.prototype.updateUser = function (username, isactive, role, email, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (username === null || username === undefined) {
                        throw new Error('Required parameter username was null or undefined when calling updateUser.');
                    }
                    if (isactive === null || isactive === undefined) {
                        throw new Error('Required parameter isactive was null or undefined when calling updateUser.');
                    }
                    if (role === null || role === undefined) {
                        throw new Error('Required parameter role was null or undefined when calling updateUser.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (email !== undefined) {
                        formParams = formParams.append('email', email) || formParams;
                    }
                    if (isactive !== undefined) {
                        formParams = formParams.append('isactive', isactive) || formParams;
                    }
                    if (role !== undefined) {
                        formParams = formParams.append('role', role) || formParams;
                    }
                    return this.httpClient.put(this.basePath + "/user/" + encodeURIComponent(String(username)), convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                AuthService = __decorate([
                    core_1.Injectable(),
                    __param(1, core_1.Optional()),
                    __param(1, core_1.Inject(variables_1.BASE_PATH)),
                    __param(2, core_1.Optional()),
                    __metadata("design:paramtypes", [http_1.HttpClient, String, configuration_1.Configuration])
                ], AuthService);
                return AuthService;
            }());
            exports_1("AuthService", AuthService);
        }
    };
});
//# sourceMappingURL=auth.service.js.map