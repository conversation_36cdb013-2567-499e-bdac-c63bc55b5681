{"version": 3, "file": "bitmask.editor.component.js", "sourceRoot": "", "sources": ["bitmask.editor.component.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;gBA4EE;oBAPU,oBAAe,GAAuB,IAAI,mBAAY,EAAE,CAAC;oBAC3D,gBAAW,GAAc,EAAE,CAAC;oBAC5B,kBAAa,GAAW,GAAG,CAAC;oBAC5B,kBAAa,GAAW,CAAC,CAAC;oBAE1B,YAAO,GAAW,GAAG,CAAC;gBAEf,CAAC;gBAET,yCAAQ,GAAf;oBAEE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBAChD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC9D,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAErD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI;wBAC5B,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;;wBAEhD,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;oBAEnB,IAAI,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;oBAEnE,KAAK,IAAI,CAAC,IAAI,iBAAiB,EAAE;wBAC/B,IAAI,gBAAgB,GAAW,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;wBACxE,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE;4BACjE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;yBACxK;6BACI;4BACH,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO;gCAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;;gCAEvK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;yBAC3K;qBACF;gBACJ,CAAC;gBAEQ,iDAAgB,GAAxB,UAAyB,KAAiB,EAAE,MAAe;oBACzD,IAAI,kBAAkB,GAAQ,KAAK,CAAC,aAAa,CAAC;oBAClD,IAAI,kBAAkB,IAAI,IAAI,IAAI,kBAAkB,CAAC,SAAS,IAAI,IAAI,IAAI,kBAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC;wBACjI,OAAO;oBAET,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;oBAC3B,KAAK,CAAC,cAAc,EAAE,CAAC;gBAC1B,CAAC;gBAEQ,oDAAmB,GAA3B,UAA4B,OAAgB;oBAC1C,OAAO,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;oBAEvC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;wBACnD,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW;4BAC5B,IAAI,CAAC,GAAG,GAAG;gCACT,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;wBAC1C,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;qBACpB;yBACI,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE;wBAC/C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;qBACvC;oBAED,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC/B,IAAI,OAAO,CAAC,SAAS;wBACpB,WAAW,GAAG,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;;wBAE/C,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;oBAErD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;oBAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC9D,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;oBAC5B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACxD,CAAC;gBAEQ,sDAAqB,GAA7B,UAA8B,WAAmB;oBAC/C,IAAI,GAAG,GAAG,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;oBACnC,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;gBACnF,CAAC;gBA3ES;oBAAR,YAAK,EAAE;;2EAAmC;gBAClC;oBAAR,YAAK,EAAE;8CAAqB,mBAAW;kFAAC;gBAC/B;oBAAT,aAAM,EAAE;8CAAkB,mBAAY;+EAA4B;gBAHxD,sBAAsB;oBA9DlC,gBAAS,CAAC;wBACT,QAAQ,EAAE,wBAAwB;wBAClC,MAAM,EAAE,CAAC,60BAiCV,CAAC;wBACD,QAAQ,EAAE,oxCAuBJ;qBACN,CAAC;;mBAEW,sBAAsB,CA6ElC;gBAAD,6BAAC;aAAA,AA7ED;;QAoFC,CAAC"}