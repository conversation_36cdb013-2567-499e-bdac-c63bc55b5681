﻿import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { EditorFieldObjectDTO } from "../../data/model/models";
import { FormControl } from "@angular/forms";
import { KeysPipe } from "../../global/keys.pipe";

@Component({
  selector: "dashboardConfigTagEditorAdvanceComponent",
  styles: [`
    .table{
      margin-bottom: 0px;
      width: 90%;
      max-width: 90%;
      margin-left: 10px;
    }
    .table>tr>td,
    .table>tr>th{
	    padding: 2px !important;
      line-height: 4px;
      border-bottom: gray solid 1px;
      vertical-align: baseline !important;
    }
    .input-text-xs {
      height: 20px;
      font-size: 11px;
      line-height: 14px;
    }
    .input-number-xs{
      height: 20px;
      width: 100px;
      font-size: 11px;
      line-height: 14px;
    }
    .select-xs{
      height: 20px;
      font-size: 11px;
      line-height: 14px;
      padding: 0;
    }
    .inline {
      display: inline-block;
    }
    fieldset {
      border-radius: 6px;
      border: 1px LightGray solid;
      padding: 0px 6px 0px 6px;
      margin-bottom: 10px;
    }
    legend{
      width: inherit;
      font-size: 11px !important;
      padding: 0 10px;
      border-bottom: none;
      margin-bottom: 4px;
      font-weight: bold;
      font-size: 15px;
    }
  `],
  template: `
	<div *ngIf="isDataLoaded" class="container-fluid" style="margin-left: -12px">
    <table class="table">
      <tr *ngFor="let parameter of parameterList" title="{{parameter.helpTR | translateKey:parameter.helpText:translate}}">
        <td>
          <label>{{parameter.name}}</label>
        </td>
        <td *ngIf="(parameter.controlType===controlTypeEnum.Text)">
          <input type="text" class="form-control input-text-xs" [(ngModel)]="parameter.value" (change)="parameterOnChange()"/>
        </td>
        <td *ngIf="(parameter.controlType===controlTypeEnum.Number)">
          <input type="number" style="display: inline;" class="form-control input-number-xs" min="{{parameter.range.min}}" max="{{parameter.range.max}}" [(ngModel)]="parameter.value" (change)="parameterOnChange()" (keypress)="onlyNumericCharInt($event, parameter.value, parameter.range.min, parameter.range.max)"/>
          <div style="display: inline; padding-left: 4px;" *ngIf="(parameter.range!=='')">{{'TR_MINIMUM_MAXIMUM_RANGE' | translate}} {{parameter.range.min}} - {{parameter.range.max}}</div>
        </td>
        <td *ngIf="(parameter.controlType===controlTypeEnum.Checkbox)">
          <input type="checkbox" class="form-check" [(ngModel)]="parameter.value" (change)="parameterOnChange()"/>
        </td>
        <td *ngIf="(parameter.controlType===controlTypeEnum.Combobox) || (parameter.controlType===controlTypeEnum.FileManagement)">
			    <select class="form-control select-xs" [(ngModel)]="parameter.value" (change)="parameterOnChange()">
            <option *ngFor="let item of parameter.choices" [ngValue]="item.key">{{item.value}}</option>
			    </select>
        </td>
		    <td *ngIf="(parameter.controlType===controlTypeEnum.MaskEditor)">
          <bitmaskAdvanceEditorComponent (bitmaskOnChange)="parameterOnChange()" [(parameter)]="parameter"></bitmaskAdvanceEditorComponent>
        </td>
        <td *ngIf="(parameter.isGlobalParam===true)">
          <div style="color: #ff4242;" class="glyphicon glyphicon-exclamation-sign warning-editable"></div>
        </td>
      </tr>
    </table>
    <fieldset style="background-color: rgba(256, 256,256, 0.3) !important; margin: 8px;">
      <div style="display: inline;font-weight: bold; margin: 12px"><div style="color: #ff4242;" class="glyphicon glyphicon-exclamation-sign warning-editable"></div> {{'TR_GLOBAL_PARAMATER_APPLY_TO_THE_WORKSPACE' | translate}}</div>
      <br>
      <div style="display: inline;font-weight: bold; margin: 12px"><div style="color: #ff4242;" class="glyphicon glyphicon-warning-sign warning-editable"></div> {{'TR_CHANGING_ADVANCE_PARAMATERS_REQUIRE_A_RESTART' | translate}}</div>
    </fieldset>
    <alertStatusBarComponent></alertStatusBarComponent>
  </div>`
})

export class DashboardConfigTagEditorAdvanceComponent implements OnInit {
  @Input() editorField: EditorFieldObjectDTO;
  @Input() editorFieldcontrol: FormControl;
  @Output() onChange: EventEmitter<null> = new EventEmitter();
  private parameterList: AdvanceParameter[] = [];
  private isDataLoaded: boolean = false;
  private controlTypeEnum = EditorFieldObjectDTO.ControlTypeEnum;

  constructor(private alertService: AlertService, private translate: TranslateService, private keysPipe: KeysPipe) {
  }

  public ngOnInit(): void {
    if (this.editorField?.controlSource) {
      this.parameterList = AdvanceParameter.initParameterValueList(this.editorField.controlSource, this.keysPipe );
      this.isDataLoaded = true;
    }
  }

  private parameterOnChange(): void {
    type ParameterItem = [string];
    let parameterItemList: ParameterItem[] = [];
    try {
      this.parameterList.forEach((parameter) => {
        if (parameter.value != null) {
          parameterItemList.push(["{\"name\":\"" + parameter.name + "\",\"value\":\"" + parameter.value + "\"}"]);
        }
      });
    } catch (e) {}

    this.onChange.emit();
    this.editorFieldcontrol.setValue("[" + parameterItemList + "]");
  }

  private onlyNumericCharInt(event, value: number ,rangeMin: number, rangeMax: number): boolean {
    let input = String.fromCharCode(event.keyCode);
    if ((/[^0-9]/.test(input))) {
      event.preventDefault();
      return false;
    }
    if (rangeMin != null && rangeMax != null && value != null) {
      if (value > rangeMax || value < rangeMin) {
        event.preventDefault();
        return false;
      }
    }
  }
}

export class AdvanceParameter {
  public name: string;
  public helpTR: string;
  public helpText: string;
  public controlType: string;
  public isGlobalParam: boolean;
  public choices: any;
  public range: any;
  public value: any;

  constructor(name: string, helpTR: string, helpText: string, controlType: string, isGlobalParam: boolean, choices: any = null, range: any, value: any = null) {
    this.name = name;
    this.helpTR = helpTR;
    this.helpText = helpText;
    this.controlType = controlType;
    this.isGlobalParam = isGlobalParam;
    this.choices = choices;
    this.range = range;
    this.value = value;
  }

  public static initParameterValueList(parameterListSource: string, keysPipe: KeysPipe): AdvanceParameter[] {
    let parameterValueList: AdvanceParameter[] = [];
    let parameterList = JSON.parse(parameterListSource);

    try {
      if (parameterList) {
        parameterList.forEach((parameter: AdvanceParameter) => {
          let helpText = unescape(parameter.helpText);
          if (parameter.controlType == EditorFieldObjectDTO.ControlTypeEnum.Combobox.toString()) {
            let jsonSource = JSON.parse(parameter.choices);
            parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, keysPipe.transformJson(jsonSource), "",parameter.value));
          }
          else if (parameter.controlType == EditorFieldObjectDTO.ControlTypeEnum.Checkbox.toString()) {
            let valueCheckbox = false;
            if (parameter.value==="1")
              valueCheckbox = true;
            parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, "", "", valueCheckbox));
          }
          else if (parameter.controlType == EditorFieldObjectDTO.ControlTypeEnum.Number.toString() && parameter.range != "") {
            let range = JSON.parse(parameter.range);
            parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam,  "", range, parameter.value));
          }
          else if (parameter.controlType == EditorFieldObjectDTO.ControlTypeEnum.MaskEditor.toString()) {
            parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, parameter.choices, "", parameter.value));
          }
          else {
            parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, "", "",parameter.value));
          }
        });
      }
    }
    catch (e) {}
    return parameterValueList;
  }
}