﻿import { EditorFieldObjectDTO, EditorCommandsDTO, TargetTypeEnumDTO, ProtocolTypesEnumDTO, TargetTCPModeEnumDTO, TreeNodeDTO } from "../../data/model/models";
import { EditorsService } from "../../data/api/api";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { FormGroup, FormControl, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { CollapsiblePanel } from "../../modules/collapsible-panel/collapsible-panel";
import { KeysPipe } from "../../global/keys.pipe";
import { Column } from "../../modules/grid/column";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { DashboardConfigTagEditorModal } from "./dashboard.config.tag.editor.modal";
import { TranslateService } from "@ngx-translate/core";
import { Node } from "../../modules/treeview/node";
import { NodeSelect } from "../../modules/treeview-select/node-select";

export class DashboardConfigTagEditorLogic {
  public manageForm(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService): string {
    let customError: string = "";
    let selectValue = tagForm.controls[editorField.schemaName].value;
    try {
      customError = this[editorType](isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue);
      return customError;
    }
    catch (err) {
      if (err.message != "this[editorType] is not a function")
        console.log(err);
      return "";
    }
  }

  // #region Editor Function
  private GTWOPCUAClientEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "OpcUaTrustRejectedCertificate") {
        let rejectedCert = tagForm.controls["OpcUaCertificateRejectedList"].value;
        editorsService.editorAction(editorType, "TrustOPCUaClientCertificate", objectCollectionKind, rejectedCert).subscribe(
          (data: any) => {
            (<any>tagForm.controls["OpcUaCertificateRejectedList"]).component.listComponentDataFile(data.data);
            alertService.success("TR_DATA_SAVED");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_UA_TRUST_CERTIFICATE_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "refreshList") {
        let serverName = tagForm.controls["serverName"].value;
        editorsService.editorAction(editorType, "RefreshOPCUAServerList", objectCollectionKind, serverName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["serverList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_UA_SERVER_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "selectServer") {
        let gidItem = (<any>tagForm.controls["serverList"]).value;
        tagForm.controls["OpcUaClientServerUrl"].setValue(gidItem.item.name);
      }
    }
    return "";
  }

  private GTWModbusSessionActionEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "pointType") {
        let pointType: string = tagForm.controls["pointType"].value;
        if (pointType == "COIL") {
          tagForm.controls["actionValuesExample"].setValue("[{\"start\":0,\"values\":[4,7,3]},{\"start\":3,\"values\":[5,2]}]");
        }
        else {
          tagForm.controls["actionValuesExample"].setValue("[{\"start\":0,\"values\":[true,false,true]},{\"start\":3,\"values\":[false,true]}]");
        }
      }
    }
    return "";
  }

  private GTWOPCUAServerEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "OpcUaTrustRejectedCertificate") {
        let rejectedCert = tagForm.controls["OpcUaCertificateRejectedList"].value;
        editorsService.editorAction(editorType, "TrustOPCUaCertificate", objectCollectionKind, rejectedCert).subscribe(
          (data: any) => {
            (<any>tagForm.controls["OpcUaCertificateRejectedList"]).component.listComponentDataFile(data.data);
            alertService.success("TR_DATA_SAVED");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_UA_TRUST_CERTIFICATE_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "OpcUaServerEditUserSecurity") {
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDEDIT;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        let node: TreeNodeDTO = { nodeClassName: "GTWOPCUAUserSecurityEditor", nodeCollectionKind: TreeNodeDTO.NodeCollectionKindEnum.ALL };
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "", parentObjectName: "", addTitle: addTitle, editorsService: editorsService, node: node }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.DSName != "") {
            alertService.success("TR_DATA_SAVED");
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error); }
        );
      }
      if (editorField.schemaName == "OpcUaServerSecurityModeNone" || editorField.schemaName == "OpcUaServerSecurityModeSign" || editorField.schemaName == "OpcUaServerSecurityModeSignAndEncrypt") {
        if (tagForm.controls["OpcUaServerSecurityModeNone"].value == false && tagForm.controls["OpcUaServerSecurityModeSign"].value == false && tagForm.controls["OpcUaServerSecurityModeSignAndEncrypt"].value == false) {
          tagForm.controls["OpcUaServerSecurityModeNone"].setValue(true);
        }
      }
      if (editorField.schemaName == "OpcUaServerSecurityTokenAllowAnonymousUsers" || editorField.schemaName == "OpcUaServerSecurityTokenAllowUserNames" || editorField.schemaName == "OpcUaServerSecurityTokenAllowUserCerts") {
        if (tagForm.controls["OpcUaServerSecurityTokenAllowAnonymousUsers"].value == false && tagForm.controls["OpcUaServerSecurityTokenAllowUserNames"].value == false && tagForm.controls["OpcUaServerSecurityTokenAllowUserCerts"].value == false) {
          tagForm.controls["OpcUaServerSecurityTokenAllowAnonymousUsers"].setValue(true);
        }
      }
    }
    return "";
  }

  private GTWOPCUAUserSecurityEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "OpcUaClientAddUser") {
        let newUsername: string = tagForm.controls["OpcUaClientNewUsername"].value;
        let password: string = tagForm.controls["OpcUaClientPassword"].value;
        let userListControlSource: any = (<any>tagForm.controls["OpcUaClientUserList"]).component.componentData;
        let maxUserPerOPCUaServer: number = tagForm.controls["OPCUaMaxUserPerServer"].value;
        if (newUsername == "") {
          alertService.error("TR_ERROR_USERNAME_IS_NOT_SPECIFIED");
          return "";
        }
        else if (password == "") {
          alertService.error("TR_ERROR_PASSWORD_IS_NOT_SPECIFIED");
          return "";
        }
        else if (userListControlSource != null && userListControlSource.filter(p => p.username == newUsername).length > 0) {
          alertService.error("TR_ERROR_USERNAME_ALREADY_PRESENT");
          return "";
        }
        else if (userListControlSource != null && userListControlSource.length >= maxUserPerOPCUaServer) {
          alertService.error("TR_ERROR_CANNOT_ADD_ANOTHER_USER_THE_MAX_NUMBER_OF_SUPPORTED_USERS_HAS_BEEN_REACHED");
          return "";
        }
        else {
          editorsService.editorAction(editorType, "AddDeleteOPCUaUser", objectCollectionKind, "", "ADD", newUsername, password).subscribe(
            (data: any) => {
              (<any>tagForm.controls["OpcUaClientUserList"]).component.setGridData(data.data);
              tagForm.controls["OpcUaClientNewUsername"].setValue("");
              tagForm.controls["OpcUaClientPassword"].setValue("");
              (<any>tagForm.controls["OpcUaClientUserList"]).component.clearSelected();
              alertService.success("TR_SUCCESS");
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_UA_ADD_USER_FAILED"); }
            }
          );
        }
      }
      if (editorField.schemaName == "OpcUaClientDeleteUser") {
        let usernameGrid: any = tagForm.controls["OpcUaClientUserList"].value;
        if (usernameGrid != null && usernameGrid.item != null && usernameGrid.item.username != "") {
          let username: string = usernameGrid.item.username;
          let ModalDeleteRef;
          translateService.get("TR_ARE_YOU_SURE_TO_DELETE_USER_", { username: username }).subscribe(res => {
            ModalDeleteRef = modal.confirm()
              .size('lg')
              .showClose(true)
              .title(translateService.instant('TR_WARNING'))
              .okBtn(translateService.instant('TR_DELETE'))
              .okBtnClass('btn btn-default')
              .body(`
				      <div class="panel panel-warning">
					      <div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				      </div>
            `).open()
          });
          ModalDeleteRef.result.then(
            (result) => {
              if (result) {
                editorsService.editorAction(editorType, "AddDeleteOPCUaUser", objectCollectionKind, "", "DELETE", username, "").subscribe(
                  (data: any) => {
                    (<any>tagForm.controls["OpcUaClientUserList"]).component.setGridData(data.data);
                    (<any>tagForm.controls["OpcUaClientUserList"]).component.clearSelected();
                    alertService.success("TR_SUCCESS");
                  },
                  error => {
                    if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_UA_DELETE_USER_FAILED"); }
                  }
                );
              }
            },
            () => { } //needed
          );
        }
      }
      if (editorField.schemaName == "OpcUaClientModifyUser") {
        let newUsername: string = tagForm.controls["OpcUaClientNewUsername"].value;
        let password: string = tagForm.controls["OpcUaClientPassword"].value;
        let userListControlSource: any = (<any>tagForm.controls["OpcUaClientUserList"]).component.componentData;
        let maxUserPerOPCUaServer: number = tagForm.controls["OPCUaMaxUserPerServer"].value;
        if (newUsername == "") {
          alertService.error("TR_ERROR_USERNAME_IS_NOT_SPECIFIED");
          return "";
        }
        else if (password == "") {
          alertService.error("TR_ERROR_PASSWORD_IS_NOT_SPECIFIED");
          return "";
        }
        else if (userListControlSource != null && userListControlSource.filter(p => p.username == newUsername).length == 0) {
          alertService.error("TR_ERROR_USERNAME_NOT_PRESENT");
          return "";
        }
        else if (userListControlSource != null && userListControlSource.length >= maxUserPerOPCUaServer) {
          alertService.error("TR_ERROR_CANNOT_ADD_ANOTHER_USER_THE_MAX_NUMBER_OF_SUPPORTED_USERS_HAS_BEEN_REACHED");
          return "";
        }
        else {
          editorsService.editorAction(editorType, "AddDeleteOPCUaUser", objectCollectionKind, "", "MODIFY", newUsername, password).subscribe(
            (data: any) => {
              (<any>tagForm.controls["OpcUaClientUserList"]).component.setGridData(data.data);
              tagForm.controls["OpcUaClientNewUsername"].setValue("");
              tagForm.controls["OpcUaClientPassword"].setValue("");
              (<any>tagForm.controls["OpcUaClientUserList"]).component.clearSelected();
              alertService.success("TR_SUCCESS");
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_UA_MODIFY_USER_FAILED"); }
            }
          );
        }
      }
      if (editorField.schemaName == "OpcUaClientUserList") {
        if (true) {
          let newUsername: string = tagForm.controls["OpcUaClientUserList"].value.item.username;
          tagForm.controls["OpcUaClientNewUsername"].setValue(newUsername);
        }
      }
    }
    return "";
  }

  private GTWOPCClientItemEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "itemName")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
      if (editorField.schemaName == "itemId")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
      if (editorField.schemaName == "itemDescription")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
      if (editorField.schemaName == "itemType")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
    }
    else {
      if (editorField.schemaName == "refreshOPCItemParent") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "RefreshOPCItemParent", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["itemParentBrowser"]).component.setTreeviewData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "") {
        let currentNode: any = tagForm.controls["itemParentBrowser"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (currentNode instanceof Node) {
          if (currentNode.children.length === 0) {
            editorsService.editorAction(editorType, "LoadChildrenOPCClientItem", objectCollectionKind, objectName, currentNode.nodeFullName).subscribe(
              (data: any) => {
                let jsonSource = data.data;
                currentNode.children = this.setTreeviewNode(jsonSource, currentNode);
                tagForm.controls["itemParentBrowser"].setValue(null);
                if (currentNode.children.length > 0)
                  currentNode.isExpanded = true;
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
              }
            );
          }
          else {
            currentNode.toggle();
          }
        }
        else {
          alertService.clearMessage();
          tagForm.controls["itemName"].setValue("");
          editorsService.editorAction(editorType, "ValidateOPCItem", objectCollectionKind, objectName, currentNode).subscribe(
            (data: any) => {
              if (data.result)
                tagForm.controls["itemName"].setValue(currentNode);
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED"); }
            }
          );
        }
      }
      if (editorField.schemaName == "itemList" && tagForm.controls["itemList"].value != "" && tagForm.controls["itemParentBrowser"].value != "") {
        let opcItemName: any = tagForm.controls["itemList"].value;
        let parenOPCItemName: string = tagForm.controls["itemParentBrowser"].value;
        tagForm.controls["itemName"].setValue(parenOPCItemName + "." + opcItemName.item.i);
      }
      if (editorField.schemaName == "AddOPCItem" && tagForm.controls["itemName"].value != "" && tagForm.controls["itemParentBrowser"].value != "") {
        let opcItemName: string = tagForm.controls["itemName"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        let opcItemValueType: string = tagForm.controls["itemValueType"].value;
        if (opcItemName == "") {
          alertService.error("TR_OPC_INVALID_ITEM_NAME");
          return "";
        }
        editorsService.editorAction(editorType, "AddOPCItem", objectCollectionKind, objectName, opcItemName, opcItemValueType, "0").subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_ADD_ITEM_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "AddOPCProperty" && tagForm.controls["daItemPropertyList"].value != "") {
        let daItemProperty: any = tagForm.controls["daItemPropertyList"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (daItemProperty.item.PD == "") {
          alertService.error("TR_PLEASE_SELECT_A_PROPERTY");
          return "";
        }
        editorsService.editorAction(editorType, "AddOPCItem", objectCollectionKind, objectName, objectName, daItemProperty.item.TYPE, daItemProperty.item.ID).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_ADD_ITEM_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "RefreshOPCDAProperties") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "RefreshOPCDAProperties", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["daItemPropertyList"]).component.setGridData(data.data);
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_REFRESH_OPC_PROPERTIES_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "ItemAttributeIsActive") {
        let objectName: string = tagForm.controls["objectName"].value;
        let itemAttributeIsActive: string = tagForm.controls["ItemAttributeIsActive"].value;
        editorsService.editorAction(editorType, "ActivateOPCItem", objectCollectionKind, objectName, itemAttributeIsActive).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_ACTIVATE_OPC_ITEM_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "ReadOPCItem") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "ReadOPCItem", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_READ_OPC_ITEM_FAILED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWOPCClientMultipleItemsEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "refreshOPCItemParent") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "RefreshOPCItemParent", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["itemParentBrowser"]).component.setTreeviewData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "") {
        let currentNode: any = tagForm.controls["itemParentBrowser"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (currentNode instanceof Node) {
          if (currentNode.children.length === 0) {
            editorsService.editorAction(editorType, "LoadChildrenOPCClientItem", objectCollectionKind, objectName, currentNode.nodeFullName).subscribe(
              (data: any) => {
                let jsonSource = data.data;
                currentNode.children = this.setTreeviewNode(jsonSource, currentNode);
                tagForm.controls["itemParentBrowser"].setValue(null);
                if (currentNode.children.length > 0) {
                  currentNode.isExpanded = true;
                  currentNode.checkRecursive(currentNode.checked);
                }
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
              }
            );
          }
          else {
            currentNode.toggle();
          }
        }
      }
      if (editorField.schemaName == "AddOPCItem") {
        let objectName: string = tagForm.controls["objectName"].value;
        let checkedNodes: any = this.getCheckedItems((<any>tagForm.controls["itemParentBrowser"]).component.componentData, true);
        editorsService.editorAction(editorType, "AddOPCMultipleItems", objectCollectionKind, objectName, JSON.stringify(checkedNodes)).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_ADD_MULTIPLE_ITEMS_FAILED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWOPCUAClientMultipleItemsEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "refreshOPCUAItemParent") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "RefreshOPCUAItemParent", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["itemParentBrowser"]).component.setTreeviewData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "" && !isRunDuringInit) {
        let currentNode = tagForm.controls["itemParentBrowser"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (currentNode instanceof NodeSelect) {
          if (currentNode.children.length === 0) {
            editorsService.editorAction(editorType, "LoadChildrenOPCUAClientItem", objectCollectionKind, objectName, currentNode.nodeFullName).subscribe(
              (data: any) => {
                let jsonSource = data.data;
                currentNode.children = this.setTreeviewNodeSelect(jsonSource, currentNode);
                tagForm.controls["itemParentBrowser"].setValue(null);
                if (currentNode.children.length > 0) {
                  currentNode.isExpanded = true;
                  currentNode.checkRecursive(currentNode.checked, currentNode);
                }
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
              }
            );
          }
          else {
            currentNode.toggle();
          }
        }
      }
      if (editorField.schemaName == "AddOPCUAItem" && !isRunDuringInit) {
        let objectName: string = tagForm.controls["objectName"].value;
        let checkedNodes: any = this.getCheckedItems((<any>tagForm.controls["itemParentBrowser"]).component.componentData, true);
        editorsService.editorAction(editorType, "AddOPCUAMultipleItems", objectCollectionKind, objectName, JSON.stringify(checkedNodes)).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_ADD_MULTIPLE_ITEMS_FAILED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWOPCUAClientItemEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "itemName")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
      if (editorField.schemaName == "itemId")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
      if (editorField.schemaName == "itemType")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
    }
    if (!isRunDuringInit) {
      if (editorField.schemaName == "refreshOPCUAItemParent") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "RefreshOPCUAItemParent", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["itemParentBrowser"]).component.setTreeviewData(data.data);
            (<any>tagForm.controls["itemName"]).setValue("");
            (<any>tagForm.controls["itemId"]).setValue("");
            (<any>tagForm.controls["itemDescription"]).setValue("");
            (<any>tagForm.controls["itemType"]).setValue("");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "" && !isRunDuringInit) {
        let currentNode: any = tagForm.controls["itemParentBrowser"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (currentNode instanceof Node) {
          if (currentNode.children.length === 0) {
            editorsService.editorAction(editorType, "LoadChildrenOPCUAClientItem", objectCollectionKind, objectName, currentNode.nodeFullName).subscribe(
              (data: any) => {
                let jsonSource = data.data;
                currentNode.children = this.setTreeviewNode(jsonSource, currentNode);
                tagForm.controls["itemParentBrowser"].setValue(null);
                if (currentNode.children.length > 0)
                  currentNode.isExpanded = true;
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
              }
            );
          }
          else {
            currentNode.toggle();
          }
        }
        else {
          editorsService.editorAction(editorType, "ValidateOPCUAItem", objectCollectionKind, objectName, currentNode).subscribe(
            (data: any) => {
              tagForm.controls["itemName"].setValue(data.data.itemName);
              tagForm.controls["itemId"].setValue(data.data.itemId);
              tagForm.controls["itemDescription"].setValue(data.data.itemDescription);
              tagForm.controls["itemType"].setValue(data.data.itemType);
              alertService.clearMessage();
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED"); }
            }
          );
        }
      }
      if (editorField.schemaName == "AddOPCUAItem" && !isRunDuringInit) {
        let itemName: string = tagForm.controls["itemName"].value;
        let itemId: string = tagForm.controls["itemId"].value;
        let itemType: string = tagForm.controls["itemType"].value;
        let itemDescription: string = tagForm.controls["itemDescription"].value;
        let objectName: string = tagForm.controls["objectName"].value;

        if (itemName == "") {
          alertService.error("TR_OPC_INVALID_ITEM_NAME");
          return "";
        }
        if (itemId == "") {
          alertService.error("TR_OPC_INVALID_ITEM_ID");
          return "";
        }
        if (itemType == "") {
          alertService.error("TR_OPC_INVALID_ITEM_TYPE");
          return "";
        }
        editorsService.editorAction(editorType, "AddOPCUAItem", objectCollectionKind, objectName, itemName, itemId, itemType, itemDescription).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_ADD_ITEM_FAILED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWOPCClientEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
 
    if (!isRunDuringInit) {
      if (editorField.schemaName == "refreshList" || editorField.schemaName == "availableServers") {
        let availableServers = tagForm.controls["availableServers"].value;
        let nodeName = tagForm.controls["nodeName"].value;
        editorsService.editorAction(editorType, "RefreshOPCServerList", objectCollectionKind, availableServers, nodeName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["serverList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "selectServer") {
        let gidItem = (<any>tagForm.controls["serverList"]).value;
        let nodeName = tagForm.controls["nodeName"].value;
        tagForm.controls["OPCserverProgID"].setValue(gidItem.item.progID);
        tagForm.controls["OPCserverNode"].setValue(nodeName);
      }
      if (editorField.schemaName == "OPCserverType") {
        let OPCserverType = (<any>tagForm.controls["OPCserverType"]).value;
        if (OPCserverType =="XML DA 1.0") {
          tagForm.controls["OPCClientDataRetrievalMode"].setValue("Opc_DataRetrievalMode_SYNCREAD");
        }
      }
      //if (editorField.schemaName == "isXmlClient" && !isRunDuringInit) {
      //  let isXmlClientValue: boolean = (<any>tagForm.controls["isXmlClient"]).value;
      //}
    }
    return "";
  }

  private GTWOPCAEClientEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "refreshList") {
        let nodeName = tagForm.controls["nodeName"].value;
        editorsService.editorAction(editorType, "RefreshOPCAEServerList", objectCollectionKind, nodeName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["serverList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_AE_SERVER_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "selectServer") {
        let gidItem = (<any>tagForm.controls["serverList"]).value;
        let nodeName = tagForm.controls["nodeName"].value;
        tagForm.controls["OPCAEserverProgID"].setValue(gidItem.item.progID);
        tagForm.controls["OPCAEserverNode"].setValue(nodeName);
      }
    }
    return "";
  }

  private GTWOPCAEClientItemEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
    }
    else {
      if (editorField.schemaName == "refreshOPCAEAreaApace") {
        let objectName: string = tagForm.controls["objectName"].value;
        editorsService.editorAction(editorType, "RefreshOPCAEAreaApace", objectCollectionKind, objectName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["areaSpace"]).component.setTreeviewData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_AE_ITEM_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "areaSpace") {
        let currentNode: any = tagForm.controls["areaSpace"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (currentNode instanceof Node) {
          if (currentNode.children.length === 0) {
            editorsService.editorAction(editorType, "LoadChildrenOPCAEAreaApace", objectCollectionKind, objectName, currentNode.nodeFullName).subscribe(
              (data: any) => {
                let jsonSource = data.data;
                currentNode.children = this.setTreeviewNode(jsonSource, currentNode);
                tagForm.controls["areaSpace"].setValue(null);
                if (currentNode.children.length > 0)
                  currentNode.isExpanded = true;
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
              }
            );
          }
          else {
            currentNode.toggle();
          }
        }
        else {
          editorsService.editorAction(editorType, "ValidateOPCItem", objectCollectionKind, objectName, currentNode).subscribe(
            (data: any) => {
              if (data.result) {
                tagForm.controls["itemName"].setValue(currentNode);
              }
              else {
                tagForm.controls["itemName"].setValue("");
              }
              alertService.clearMessage();
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED"); }
            }
          );
        }
      }
    }
    return "";
  }

  private GTWOPCAEClientAttrEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "itemName")
        editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
    }
    else {
      if (editorField.schemaName == "eventSpace") {
        let attributepath: string = tagForm.controls["eventSpace"].value;
        if (attributepath.indexOf("_TMWOPCAEAT_")) {
          tagForm.controls["eventName"].setValue(attributepath.substring(0, attributepath.indexOf('_TMWOPCAEAT_')));
          tagForm.controls["attributePath"].setValue(attributepath);
        }
      }
    }
    return "";
  }


  private GTWTASE2MappingDataAttributeEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "addNewDA")
        tagForm.controls["addNewDA"].setValue(false);
      if (editorField.schemaName == "destinationMemberList")
        this.enableControl(tagForm.controls["destinationMemberList"], true);
      if (editorField.schemaName == "domainName")
        this.enableControl(tagForm.controls["domainName"], false);
      if (editorField.schemaName == "destinationMemberList")
        this.enableControl(tagForm.controls["controlBlockName"], false);
      if (editorField.schemaName == "controlBlockName")
        this.enableControl(tagForm.controls["dataAttributeName"], false);
      if (editorField.schemaName == "dataType")
        this.enableControl(tagForm.controls["dataType"], false);
      if (editorField.schemaName == "SBO")
        this.enableControl(tagForm.controls["SBO"], false);
    }
    else {
      if (editorField.schemaName == "destinationMemberList") {
        let destinationMember: any = tagForm.controls["destinationMemberList"].value;
        tagForm.controls["destinationMemberName"].setValue(destinationMember.item.tagName);
      }
      if (editorField.schemaName == "dataType") {
        let dataType: any = tagForm.controls["dataType"].value;
        editorsService.editorAction(editorType, "ChangeDataTypeTase2", objectCollectionKind, dataType).subscribe(
          (data: any) => {
            this.enableControl(tagForm.controls["SBO"], data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_DATA_TYPE"); }
          }
        );
      }
      if (editorField.schemaName == "addNewDA") {
        if (tagForm.controls["addNewDA"].value) {
          this.enableControl(tagForm.controls["destinationMemberList"], false);
          this.enableControl(tagForm.controls["domainName"], true);
          this.enableControl(tagForm.controls["controlBlockName"], true);
          this.enableControl(tagForm.controls["dataAttributeName"], true);
          this.enableControl(tagForm.controls["dataType"], true);
        }
        else {
          this.enableControl(tagForm.controls["destinationMemberList"], true);
          this.enableControl(tagForm.controls["domainName"], false);
          this.enableControl(tagForm.controls["controlBlockName"], false);
          this.enableControl(tagForm.controls["dataAttributeName"], false);
          this.enableControl(tagForm.controls["dataType"], false);
          tagForm.controls["dataType"].setValue("");
          this.enableControl(tagForm.controls["SBO"], false);
        }
      }
    }
    return "";
  }

  private GTWMdoMappingEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "destinationMemberList") {
        let destinationMember: any = tagForm.controls["destinationMemberList"].value;
        tagForm.controls["destinationMemberName"].setValue(destinationMember.item.tagName);
      }
    }
    return "";
  }

  private GTWMDOEquationEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "includeSDO") {
        let includeSDO = tagForm.controls["includeSDO"].value
        editorsService.editorAction(editorType, "RefreshEquationArgument", objectCollectionKind, includeSDO).subscribe(
          (data: any) => {
            (<any>tagForm.controls["mdoList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_MDO_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "validateEquation") {
        let expression = tagForm.controls["expression"].value
        editorsService.editorAction(editorType, "ValidateEquation", objectCollectionKind, expression.replace(/\+/gi, '%2B')).subscribe(
          (data: any) => {
            alertService.success("TR_VALID_EQUATION_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_INVALID_EQUATION"); }
          }
        );
      }
      if (editorField.schemaName == "expression") {
        (<any>tagForm.controls["objectName"]).component.inputNativeElement.focus();
      }
      if (editorField.schemaName == "mdoList") {
        let expression: string = tagForm.controls["expression"].value
        if (tagForm.controls["mdoList"].value && tagForm.controls["mdoList"].value.item) {
          let item: string = tagForm.controls["mdoList"].value.item.draggableField
          tagForm.controls["expression"].setValue(expression + item);
        }
      }
      if (editorField.schemaName == "operationList") {
        let expression: string = tagForm.controls["expression"].value
        if (tagForm.controls["operationList"].value && tagForm.controls["operationList"].value.item) {
          let item: string = tagForm.controls["operationList"].value.item.draggableField
          tagForm.controls["expression"].setValue(expression + item);
        }
      }
    }
    return "";
  }

  private GTWChannelEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      this.enableControl(tagForm.controls["IsRedundantChannel"], false);
      this.enableControl(tagForm.controls["IsRedundancyGroup"], false);
      if (editorField.schemaName == "IsRedundantChannel") {
        if (tagForm.controls["IsRedundantChannel"].value == true)
          this.enableControl(tagForm.controls["PhysComProtocol"], false);
      }
    }

    if (editorField.schemaName == "ChanTLSEnable") {
      let collapsiblePanelsfiltered = collapsiblePanels.filter(item => item.lsName == "TR_TLS_CONFIG");
      if (collapsiblePanelsfiltered.length > 0)
        collapsiblePanelsfiltered[0].isOpen = tagForm.controls["ChanTLSEnable"].value;
    }

    if (tagForm.controls["PhysComType"].value == TargetTypeEnumDTO.MBP.toString()) {
      if (editorField.schemaName == "PhysComProtocol" && selectValue == ProtocolTypesEnumDTO.MMB) {
        let channelMBPRouteAddress = tagForm.controls["physComMBPRoutePath"];
        this.enableControl(channelMBPRouteAddress, false);

        let channelMBPSlavePath = tagForm.controls["physComMBPSlavePath"];
        this.enableControl(channelMBPSlavePath);
      }
      else if (editorField.schemaName == "PhysComProtocol" && selectValue == ProtocolTypesEnumDTO.SMB) {
        let channelMBPRouteAddress = tagForm.controls["physComMBPRoutePath"];
        this.enableControl(channelMBPRouteAddress);

        let channelMBPSlavePath = tagForm.controls["physComMBPSlavePath"];
        this.enableControl(channelMBPSlavePath, false);
      }
    }
    else if (tagForm.controls["PhysComType"].value == TargetTypeEnumDTO.MODEMPOOLCHANNEL.toString()) {
      let physComModbusRTU = tagForm.controls["physComModbusRTU"];
      this.enableControl(physComModbusRTU, false);
      if (tagForm.controls["PhysComProtocol"].value == ProtocolTypesEnumDTO.MMB || tagForm.controls["PhysComProtocol"].value == ProtocolTypesEnumDTO.SMB)
        this.enableControl(physComModbusRTU, true);
    }
    else if (tagForm.controls["PhysComType"].value == TargetTypeEnumDTO._232.toString()) {
      let offlinePollPeriod = tagForm.controls["PhysOfflinePollPeriod"];
      this.enableControl(offlinePollPeriod, false);
      if (tagForm.controls["PhysComProtocol"].value == ProtocolTypesEnumDTO.MMB || tagForm.controls["PhysComProtocol"].value == ProtocolTypesEnumDTO.MDNP)
        this.enableControl(offlinePollPeriod, true);
    }
    else if (tagForm.controls["PhysComType"].value == TargetTypeEnumDTO.UDPTCP.toString()) {
      if (editorField.schemaName == "PhysComProtocol" && selectValue == ProtocolTypesEnumDTO.MDNP) {
        tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
      }
      else if (editorField.schemaName == "PhysComProtocol" && selectValue == ProtocolTypesEnumDTO.SDNP) {
        tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
      }
    }
    else if (tagForm.controls["PhysComType"].value == TargetTypeEnumDTO.TCP.toString()) {
      if (editorField.schemaName == "ChanTLSEnable") {
        this.enableControl(tagForm.controls["ChanTLSCertAuthChainingVerDepth"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSCertificateAuthorityFile"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSCertificateRevocationFile"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSCommonName"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSRenegotiationCount"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSRenegotiationSeconds"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSHandshakeTimeout"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSDhFile"], selectValue);
        //this.enableControl(tagForm.controls["ChanTLSDSAPrivateKeyFile"], selectValue);
        //this.enableControl(tagForm.controls["ChanTLSDSAPrivateKeyPassPhrase"], selectValue);
        //this.enableControl(tagForm.controls["ChanTLSDSACertificateFile"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSRSAPrivateKeyFile"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSRSAPrivateKeyPassPhrase"], selectValue);
        this.enableControl(tagForm.controls["ChanTLSRSACertificateFile"], selectValue);

        let collapsiblePanelsfiltered = collapsiblePanels.filter(item => item.lsName == "TR_TLS_CONFIG");
        if (collapsiblePanelsfiltered.length > 0)
          collapsiblePanelsfiltered[0].isOpen = selectValue;
      }
      if (editorField.schemaName == "PhysComProtocol") {
        this.enableControl(tagForm.controls["IsRedundantChannel"], false);
        this.enableControl(tagForm.controls["IsRedundancyGroup"], false);
        this.enableControl(tagForm.controls["IsRedundancyGroup"], false);
        tagForm.controls["IsRedundancyGroup"].setValue(false);
        tagForm.controls["IsRedundancyGroup"].setValue(false);
        if (editorCommand != EditorCommandsDTO.MENUCMDEDIT.toString()) {
          switch (selectValue) {
            case ProtocolTypesEnumDTO.M101:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              break;
            case ProtocolTypesEnumDTO.M102:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              break;
            case ProtocolTypesEnumDTO.M103:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              break;
            case ProtocolTypesEnumDTO.M104:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              this.enableControl(tagForm.controls["IsRedundancyGroup"], true);
              break;
            case ProtocolTypesEnumDTO.MDNP:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
              tagForm.controls["PhysComIpPort"].setValue("20000");
              break;
            case ProtocolTypesEnumDTO.MMB:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.CLIENT);
              tagForm.controls["PhysComIpPort"].setValue("502");
              break;
            case ProtocolTypesEnumDTO.S101:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              break;
            case ProtocolTypesEnumDTO.S102:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              break;
            case ProtocolTypesEnumDTO.S103:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
              tagForm.controls["PhysComIpPort"].setValue("2404");
              break;
            case ProtocolTypesEnumDTO.S104:
              tagForm.controls["PhysComIpPort"].setValue("2404");
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
              this.enableControl(tagForm.controls["IsRedundancyGroup"], true);
              this.enableControl(tagForm.controls["PhysComIpMode"], false);
              break;
            case ProtocolTypesEnumDTO.SDNP:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
              tagForm.controls["PhysComIpPort"].setValue("20000");
              break;
            case ProtocolTypesEnumDTO.SMB:
              tagForm.controls["PhysComIpMode"].setValue(TargetTCPModeEnumDTO.SERVER);
              tagForm.controls["PhysComIpPort"].setValue("502");
              break;
          }
        }
      }
    }
    return "";
  }

  private GTWSessionEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    let customError: string;
    if (!isRunDuringInit) {
      if (tagForm.controls["linkMode"].value == "GTWTYPES_LINK_UNBALANCED" && tagForm.controls["linkSizeAddress"].value == "0") { // linkSizeAddress -> Not Present=0
        tagForm.controls["linkSizeAddress"].setErrors({ "incorrect": true });
        customError = "TR_LINK_SIZE_ADDRESS_0_NOT_ALLOW_IN_UNBALANCED_LINK";
      }
      else {
        tagForm.controls["linkSizeAddress"].setErrors(null);
        customError = "";
      }
    }
    else {
      if (editorField.schemaName == "SessionLinkAddress") {
        if (editorCommand != EditorCommandsDTO.MENUCMDEDIT.toString()) {
          switch (tagForm.controls["protocol"].value) {
            case ProtocolTypesEnumDTO.M101:
              tagForm.controls["SessionLinkAddress"].setValue("3");
              break;
            case ProtocolTypesEnumDTO.M103:
              tagForm.controls["SessionLinkAddress"].setValue("3");
              break;
            case ProtocolTypesEnumDTO.M104:
              tagForm.controls["SessionLinkAddress"].setValue("4");
              break;
            case ProtocolTypesEnumDTO.MMB:
              tagForm.controls["SessionLinkAddress"].setValue("1");
              break;
            case ProtocolTypesEnumDTO.MDNP:
              tagForm.controls["SessionLinkAddress"].setValue("4");
              tagForm.controls["SessionLocalAddress"].setValue("3");
              break;
            case ProtocolTypesEnumDTO.S101:
              tagForm.controls["SessionLinkAddress"].setValue("3");
              break;
            case ProtocolTypesEnumDTO.S104:
              tagForm.controls["SessionLinkAddress"].setValue("4");
              break;
            case ProtocolTypesEnumDTO.SDNP:
              tagForm.controls["SessionLinkAddress"].setValue("3");
              tagForm.controls["SessionLocalAddress"].setValue("4");
              break;
            case ProtocolTypesEnumDTO.SMB:
              tagForm.controls["SessionLinkAddress"].setValue("1");
              break;
          }
        }
      }
    }
    return customError;
  }

  private GTWSectorEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "m14AuthEnable") {
        this.enableControl(tagForm.controls["m14AuthHMACAlgorithm"], selectValue);
        this.enableControl(tagForm.controls["m14AuthReplyTimeout"], selectValue);
        this.enableControl(tagForm.controls["m14AuthKeyChangeInterval"], selectValue);
        this.enableControl(tagForm.controls["m14AuthMaxKeyChangeCount"], selectValue);
        this.enableControl(tagForm.controls["m14AuthRandomChallengeDataLength"], selectValue);
        this.enableControl(tagForm.controls["m14AuthSecurityStatsIOA"], selectValue);
        this.enableControl(tagForm.controls["m14AuthExtraDiags"], selectValue);
        this.enableControl(tagForm.controls["m14AuthUserKey"], selectValue);
        this.enableControl(tagForm.controls["m14AuthUserNumber"], selectValue);
        this.enableControl(tagForm.controls["m14AuthUserName"], selectValue);

        let collapsiblePanelsfiltered = collapsiblePanels.filter(item => item.lsName == "AUTH_CONFIG");
        if (collapsiblePanelsfiltered.length > 0)
          collapsiblePanelsfiltered[0].isOpen = selectValue;
      }
    }
    return "";
  }

  private GTW61850ServerNetworkParamter(data: any, tagForm: FormGroup): void {
    tagForm.controls["Isrv61850ServerTransportAddress"].setValue(data.data["Isrv61850ServerTransportAddress"]);
    tagForm.controls["Isrv61850ServerSessionAddress"].setValue(data.data["Isrv61850ServerSessionAddress"]);
    tagForm.controls["Isrv61850ServerPresentationAddress"].setValue(data.data["Isrv61850ServerPresentationAddress"]);
    tagForm.controls["Isrv61850ServerAppID"].setValue(data.data["Isrv61850ServerAppID"]);
    tagForm.controls["Isrv61850ServerAPInvokeID"].setValue(data.data["Isrv61850ServerAPInvokeID"]);
    tagForm.controls["Isrv61850ServerAEQualifier"].setValue(data.data["Isrv61850ServerAEQualifier"]);
    tagForm.controls["Isrv61850ServerAEInvokeID"].setValue(data.data["Isrv61850ServerAEInvokeID"]);
    tagForm.controls["Isrv61850ServerIPPort"].setValue(data.data["Isrv61850ServerIPPort"]);
    tagForm.controls["Isrv61850ServerIPAddress"].setValue(data.data["Isrv61850ServerIPAddress"]);
    if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["Isrv61850ServerAuthMechanism"].value != "Certificate")
      tagForm.controls["Isrv61850ServerIPPort"].setValue("102");
    else if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate")
      tagForm.controls["Isrv61850ServerIPPort"].setValue("3782");
    if (data.data["Isrv61850ServerIPAddress"] == "")
      tagForm.controls["Isrv61850ServerIPAddress"].setValue("127.0.0.1");
  }

  private GTW61850ServerEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "Isrv61850ServerICDFile" && tagForm.controls["Isrv61850ServerICDFile"].value != "") {
        editorsService.editorAction(editorType, "GetAvailableIEDFromFile", objectCollectionKind, tagForm.controls["Isrv61850ServerICDFile"].value, "true").subscribe(
          (data: any) => {
            if (data.result) {
              (<any>tagForm.controls["I61850ServerSCLFileIEDName"]).component.listComponentData(data.data);
              if ((<any>tagForm.controls["I61850ServerSCLFileIEDName"]).component.componentData.length > 0) {
                (<any>tagForm.controls["I61850ServerSCLFileIEDName"]).setValue((<any>tagForm.controls["I61850ServerSCLFileIEDName"]).component.componentData[0].value);
              }
              editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["Isrv61850ServerICDFile"].value, tagForm.controls["I61850ServerSCLFileIEDName"].value).subscribe(
                (data: any) => {
                  this.GTW61850ServerNetworkParamter(data, tagForm);
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
                }
              );
            }
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "I61850ServerSCLFileIEDName" && tagForm.controls["I61850ServerSCLFileIEDName"].value != "") {
        editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["Isrv61850ServerICDFile"].value, tagForm.controls["I61850ServerSCLFileIEDName"].value).subscribe(
          (data: any) => {
            this.GTW61850ServerNetworkParamter(data, tagForm);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
          }
        );
      }
    }
    if (editorField.schemaName == "I61850ServerUseMMSOnly") {
      if (tagForm.controls["I61850ServerUseMMSOnly"].value == true && tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate") {
        this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], true);
      }
      else {
        if (tagForm.controls["I61850ServerUseTLSOnly"].value == false) {
          tagForm.controls["I61850ServerUseMMSOnly"].setValue(true);
          return;
        }

        this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], false);
      }
    }
    else if (editorField.schemaName == "I61850ServerUseTLSOnly") {
      if (tagForm.controls["I61850ServerUseTLSOnly"].value == true && tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate") {
        this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], true);
        //this.enableControl(tagForm.controls["Isrv61850ServerUseSiscoCompatability"], true);
      }
      else {
        if (tagForm.controls["I61850ServerUseMMSOnly"].value == false) {
          tagForm.controls["I61850ServerUseTLSOnly"].setValue(true);
          return;
        }
        this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], false);
        //this.enableControl(tagForm.controls["Isrv61850ServerUseSiscoCompatability"], false);
      }
    }
    else if (editorField.schemaName == "Isrv61850ServerAuthMechanism") {
      if (tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate") {
        this.enableControl(tagForm.controls["Isrv61850ServerCertAuthChainingVerDepth"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerAuthPassword"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityFile"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityRevokeListFile"], true);
        this.enableControl(tagForm.controls["Isrv61850ServerDirectoryToCertificateAuthority"], true);
        this.enableControl(tagForm.controls["I61850ServerUseTLSOnly"], true);
        this.enableControl(tagForm.controls["I61850ServerUseMMSOnly"], true);
        if (tagForm.controls["Isrv61850ServerIPPort"].value == "102") {
          translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 3782 }).subscribe(res => {
            if (confirm(res) == true) {
              tagForm.controls["Isrv61850ServerIPPort"].setValue("3782");
            }
          });
        }
        if (tagForm.controls["I61850ServerUseMMSOnly"].value == true) {
          this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], true);
        }
        else {
          this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], false);
        }
        if (tagForm.controls["I61850ServerUseTLSOnly"].value == true) {
          this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], true);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], true);
          //this.enableControl(tagForm.controls["Isrv61850ServerUseSiscoCompatability"], true);
        }
        else {
          this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], false);
          this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], false);
          //this.enableControl(tagForm.controls["Isrv61850ServerUseSiscoCompatability"], false);
        }
      }
      else {
        if (tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Password") {
          this.enableControl(tagForm.controls["Isrv61850ServerAuthPassword"], true);
        }
        else {
          this.enableControl(tagForm.controls["Isrv61850ServerAuthPassword"], false);
          if (tagForm.controls["Isrv61850ServerIPPort"].value == "3782") {
            translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 102 }).subscribe(res => {
              if (confirm(res) == true) {
                tagForm.controls["Isrv61850ServerIPPort"].setValue("102");
              }
            });
          }
        }
        this.enableControl(tagForm.controls["Isrv61850ServerCertAuthChainingVerDepth"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityRevokeListFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerDirectoryToCertificateAuthority"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], false);
        //this.enableControl(tagForm.controls["Isrv61850ServerUseSiscoCompatability"], false);
        this.enableControl(tagForm.controls["I61850ServerUseTLSOnly"], false);
        this.enableControl(tagForm.controls["I61850ServerUseMMSOnly"], false);
      }
    }
    return "";
  }

  private GTW62351I61850SecurityEditor(isRunDuringInit: boolean, tagForm: FormGroup, editorField: EditorFieldObjectDTO) {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "isCertificateAuthorityFileEnabled") {
        tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(true);
        tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(false);
      }
      else if (editorField.schemaName == "isCertificateAuthorityFolderEnabled") {
        tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(false);
        tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(true);
      } 
    }

    if (tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
      if (tagForm.controls["isCertificateAuthorityFileEnabled"].value == true) {
        this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], true);
        this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], false);
        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValue("");

        tagForm.controls["I61850ClientCertificateAuthorityFile"].setValidators([Validators.required]);
        tagForm.controls["I61850ClientCertificateAuthorityFile"].updateValueAndValidity();
        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValidators(null);
        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].updateValueAndValidity();
      }
      else if (tagForm.controls["isCertificateAuthorityFolderEnabled"].value == true) {
        this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], false);
        this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], true);
        tagForm.controls["I61850ClientCertificateAuthorityFile"].setValue("");

        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValidators([Validators.required]);
        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].updateValueAndValidity();
        tagForm.controls["I61850ClientCertificateAuthorityFile"].setValidators(null);
        tagForm.controls["I61850ClientCertificateAuthorityFile"].updateValueAndValidity();
      }
    }
    else {
      tagForm.controls["I61850ClientCertificateAuthorityFile"].setValidators(null);
      tagForm.controls["I61850ClientCertificateAuthorityFile"].updateValueAndValidity();
      tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValidators(null);
      tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].updateValueAndValidity();
    }
  }

  private GTW61850ClientNetworkParamterServer(data: any, tagForm: FormGroup): void {
    tagForm.controls["I61850ServerTransportAddress"].setValue(data.data["Isrv61850ServerTransportAddress"]);
    tagForm.controls["I61850ServerSessionAddress"].setValue(data.data["Isrv61850ServerSessionAddress"]);
    tagForm.controls["I61850ServerPresentationAddress"].setValue(data.data["Isrv61850ServerPresentationAddress"]);
    tagForm.controls["I61850ServerAppID"].setValue(data.data["Isrv61850ServerAppID"]);
    tagForm.controls["I61850ServerAPInvokeID"].setValue(data.data["Isrv61850ServerAPInvokeID"]);
    tagForm.controls["I61850ServerAEQualifier"].setValue(data.data["Isrv61850ServerAEQualifier"]);
    tagForm.controls["I61850ServerAEInvokeID"].setValue(data.data["Isrv61850ServerAEInvokeID"]);
    tagForm.controls["I61850ServerIPPort"].setValue(data.data["Isrv61850ServerIPPort"]);
    tagForm.controls["I61850ServerIPAddress"].setValue(data.data["Isrv61850ServerIPAddress"]);
    if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["I61850AuthMechanism"].value != "Certificate")
      tagForm.controls["I61850ServerIPPort"].setValue("102");
    else if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["I61850AuthMechanism"].value == "Certificate")
      tagForm.controls["I61850ServerIPPort"].setValue("3782");
    if (data.data["Isrv61850ServerIPAddress"] == "")
      tagForm.controls["I61850ServerIPAddress"].setValue("127.0.0.1");
  }

  private GTW61850ClientNetworkParamterClient(data: any, tagForm: FormGroup): void {
    tagForm.controls["I61850ClientIPAddress"].setValue(data.data["Isrv61850ServerIPAddress"]);
    tagForm.controls["I61850ClientAEInvokeID"].setValue(data.data["Isrv61850ServerAEInvokeID"]); 
    tagForm.controls["I61850ClientAPInvokeID"].setValue(data.data["Isrv61850ServerAPInvokeID"]);
    tagForm.controls["I61850ClientAEQualifier"].setValue(data.data["Isrv61850ServerAEQualifier"]);
    tagForm.controls["I61850ClientAppID"].setValue(data.data["Isrv61850ServerAppID"]);
    tagForm.controls["I61850ClientPresentationAddress"].setValue(data.data["Isrv61850ServerPresentationAddress"]);
    tagForm.controls["I61850ClientSessionAddress"].setValue(data.data["Isrv61850ServerSessionAddress"]);
    tagForm.controls["I61850ClientTransportAddress"].setValue(data.data["Isrv61850ServerTransportAddress"]);
  }

  private GTW61850ClientEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "I61850SCLFileName" && tagForm.controls["I61850SCLFileName"].value != "") {
        tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(true);
        editorsService.editorAction(editorType, "GetAvailableIEDFromFile", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, "true").subscribe(
          (data: any) => {
            (<any>tagForm.controls["I61850SCLFileIEDName"]).component.listComponentData(data.data);
            if ((<any>tagForm.controls["I61850SCLFileIEDName"]).component.componentData.length > 0) {
              (<any>tagForm.controls["I61850SCLFileIEDName"]).setValue((<any>tagForm.controls["I61850SCLFileIEDName"]).component.componentData[0].value);
              editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, tagForm.controls["I61850SCLFileIEDName"].value).subscribe(
                (data: any) => {
                  this.GTW61850ClientNetworkParamterServer(data, tagForm);
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
                }
              );
            }
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
          }
        );
        editorsService.editorAction(editorType, "GetAvailableIEDFromFile", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, "false").subscribe(
          (data: any) => {
            (<any>tagForm.controls["I61850SCLClientIEDName"]).component.listComponentData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
          }
        );
      }

      if (editorField.schemaName == "I61850SCLClientIEDName" && tagForm.controls["I61850SCLClientIEDName"].value != "") {
        editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, tagForm.controls["I61850SCLClientIEDName"].value).subscribe(
          (data: any) => {
            this.GTW61850ClientNetworkParamterClient(data, tagForm);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "I61850SCLFileIEDName" && tagForm.controls["I61850SCLFileIEDName"].value != "") {
        editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, tagForm.controls["I61850SCLFileIEDName"].value).subscribe(
          (data: any) => {
            this.GTW61850ClientNetworkParamterServer(data, tagForm);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "I61850SCLFileName" && tagForm.controls["I61850SCLFileName"].value == "") {
        tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(false);
        (<any>tagForm.controls["I61850SCLFileIEDName"]).component.listComponentData(null);
        (<any>tagForm.controls["I61850SCLFileIEDName"]).component.clearSelected();
        (<any>tagForm.controls["I61850SCLClientIEDName"]).component.listComponentData(null);
        (<any>tagForm.controls["I61850SCLClientIEDName"]).component.clearSelected();
      }
    }
    if (editorField.schemaName == "isCertificateAuthorityFileEnabled" || editorField.schemaName == "isCertificateAuthorityFolderEnabled") this.GTW62351I61850SecurityEditor(isRunDuringInit, tagForm, editorField);
    if (editorField.schemaName == "generateFileFromDiscovery") {
      if (tagForm.controls["generateFileFromDiscovery"].value == false) {
        if (tagForm.controls["I61850LoadModelFromFileEnabled"].value == false && tagForm.controls["I61850LoadModelFromFileEnabledNOT"].value == false)
          tagForm.controls["generateFileFromDiscovery"].setValue(true);
      }
      else {
        tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(false);
        tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(false);
        this.enableControl(tagForm.controls["I61850SCLFileIEDName"], false);
        this.enableControl(tagForm.controls["I61850SCLFileName"], false);
        this.enableControl(tagForm.controls["I61850SCLClientIEDName"], false);
      }
    }
    if (editorField.schemaName == "I61850LoadModelFromFileEnabledNOT") {
      if (tagForm.controls["I61850LoadModelFromFileEnabledNOT"].value == false) {
        if (tagForm.controls["generateFileFromDiscovery"].value == false && tagForm.controls["I61850LoadModelFromFileEnabled"].value == false)
          tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(true);
        else {
          this.enableControl(tagForm.controls["I61850SCLFileIEDName"], true);
          this.enableControl(tagForm.controls["I61850SCLFileName"], true);
          this.enableControl(tagForm.controls["I61850SCLClientIEDName"], true);
        }
      }
      else {
        tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(false);
        tagForm.controls["generateFileFromDiscovery"].setValue(false);
        this.enableControl(tagForm.controls["I61850SCLFileIEDName"], false);
        this.enableControl(tagForm.controls["I61850SCLFileName"], false);
        this.enableControl(tagForm.controls["I61850SCLClientIEDName"], false);
      }
    }
    if (editorField.schemaName == "I61850LoadModelFromFileEnabled") {
      if (tagForm.controls["I61850LoadModelFromFileEnabled"].value == false) {
        if (tagForm.controls["generateFileFromDiscovery"].value == false && tagForm.controls["I61850LoadModelFromFileEnabledNOT"].value == false)
          tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(true);
        else {
          tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(true);
          this.enableControl(tagForm.controls["I61850SCLFileIEDName"], false);
          this.enableControl(tagForm.controls["I61850SCLFileName"], false);
          this.enableControl(tagForm.controls["I61850SCLClientIEDName"], false);
        }
      }
      else {
        tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(false);
        tagForm.controls["generateFileFromDiscovery"].setValue(false);
        this.enableControl(tagForm.controls["I61850SCLFileIEDName"], true);
        this.enableControl(tagForm.controls["I61850SCLFileName"], true);
        this.enableControl(tagForm.controls["I61850SCLClientIEDName"], true);
      }
    }
    if (editorField.schemaName == "I61850ClientUseMMSOnly") {
      if (tagForm.controls["I61850ClientUseMMSOnly"].value == true && tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
        this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], true);
        this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], true);
        this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], true);
        this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], true);
      }
      else {
        if (tagForm.controls["I61850ClientUseTLSOnly"].value == false) {
          tagForm.controls["I61850ClientUseMMSOnly"].setValue(true);
          return;
        }

        this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], false);
      }
    }
    else if (editorField.schemaName == "I61850ClientUseTLSOnly") {
      if (tagForm.controls["I61850ClientUseTLSOnly"].value == true && tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
        this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], true);
        this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], true);
        this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], true);
        this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], true);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], true);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], true);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], true);
      }
      else {
        if (tagForm.controls["I61850ClientUseMMSOnly"].value == false) {
          tagForm.controls["I61850ClientUseTLSOnly"].setValue(true);
          return;
        }
        this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], false);
      }
    }
    else if (editorField.schemaName == "I61850AuthMechanism") {
      if (tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
        this.enableControl(tagForm.controls["I61850ClientCertAuthChainingVerDepth"], true);
        this.enableControl(tagForm.controls["I61850AuthPassword"], false);
        this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], true);
        this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityRevokeListFile"], true);
        this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], true);
        this.enableControl(tagForm.controls["I61850ClientUseTLSOnly"], true);
        this.enableControl(tagForm.controls["I61850ClientUseMMSOnly"], true);
        if (tagForm.controls["I61850ServerIPPort"].value == "102") {
          translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 3782 }).subscribe(res => {
            if (confirm(res) == true) {
              tagForm.controls["I61850ServerIPPort"].setValue("3782");
            }
          });
        }
        if (tagForm.controls["I61850ClientUseMMSOnly"].value == true) {
          this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], true);
          this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], true);
          this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], true);
          this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], true);
        }
        else {
          this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], false);
          this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], false);
          this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], false);
          this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], false);
        }

        if (tagForm.controls["I61850ClientUseTLSOnly"].value == true) {
          this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], true);
          this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], true);
          this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], true);
          this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], true);
          this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], true);
          this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], true);
          this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], true);
        }
        else {
          this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], false);
          this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], false);
          this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], false);
          this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], false);
          this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], false);
          this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], false);
          this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], false);
        }
      }
      else {
        if (tagForm.controls["I61850AuthMechanism"].value == "Password") {
          this.enableControl(tagForm.controls["I61850AuthPassword"], true);
        }
        else {
          this.enableControl(tagForm.controls["I61850AuthPassword"], false);
          if (tagForm.controls["I61850ServerIPPort"].value == "3782") {
            translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 102 }).subscribe(res => {
              if (confirm(res) == true) {
                tagForm.controls["I61850ServerIPPort"].setValue("102");
              }
            });
          }
        }

        this.enableControl(tagForm.controls["I61850ClientCertAuthChainingVerDepth"], false);
        this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], false);
        this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityRevokeListFile"], false);
        this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], false);
        this.enableControl(tagForm.controls["I61850ClientUseTLSOnly"], false);
        this.enableControl(tagForm.controls["I61850ClientUseMMSOnly"], false);
      }
    }
    return "";
  }

  private GTW61850ReportEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "RCBName")
        this.enableControl(tagForm.controls["RCBName"], false);
      if (editorField.schemaName == "currentEntryID")
        this.enableControl(tagForm.controls["currentEntryID"], false);
    }
    else {
      if (editorField.schemaName == "RCBList" && tagForm.controls["61850ClientName"].value != "") {
        let RCBListValue: any = tagForm.controls["RCBList"].value;
        tagForm.controls["RCBName"].setValue(RCBListValue.item.reportName);
        tagForm.controls["DSName"].setValue(RCBListValue.item.dataSet);
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, RCBListValue.item.reportName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "DSName" && tagForm.controls["61850ClientName"].value != "") {
        let reportName: any
        let RCBListValue: any = tagForm.controls["RCBList"].value;
        if (RCBListValue != null && RCBListValue != "")
          reportName = RCBListValue.item.reportName;
        else
          reportName = (<any>tagForm.controls["RCBList"]).component.componentData[0].reportName;

        let clientName61850 = tagForm.controls["61850ClientName"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDCHANGE61850DATASET;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: reportName, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName, dataModal.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["RCBList"]).component.setGridData(data.dataDS);
                editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName).subscribe(
                  (data: any) => {
                    (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
                  },
                  error => {
                    if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                  }
                );
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error); }
        );
      }
      if (editorField.schemaName == "createDS" && tagForm.controls["61850ClientName"].value != "") {
        let reportName: any
        let RCBListValue: any = tagForm.controls["RCBList"].value;
        if (RCBListValue != null && RCBListValue != "")
          reportName = RCBListValue.item.reportName;
        else
          reportName = (<any>tagForm.controls["RCBList"]).component.componentData[0].reportName;

        let clientName61850 = tagForm.controls["61850ClientName"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADD61850DATASET;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: reportName, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName, dataModal.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["RCBList"]).component.setGridData(data.dataDS);
                editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName).subscribe(
                  (data: any) => {
                    (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
                  },
                  error => {
                    if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                  }
                );
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
      if (editorField.schemaName == "readConfigFromServer") {
        editorsService.editorAction(editorType, "ReadConfigFromServer", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["RCBName"].value).subscribe(
          (data: any) => {
            tagForm.controls["integrityPeriod"].setValue(data.data.integrityPeriod);
            tagForm.controls["bufferTime"].setValue(data.data.bufferTime);
            tagForm.controls["purgeBefore1stEnable"].setValue(this.convertBool(data.data.purgeBefore1stEnable));
            tagForm.controls["purgeBeforeEnableOnReconnect"].setValue(this.convertBool(data.data.purgeBeforeEnableOnReconnect));
            tagForm.controls["integrityPeriodMonitored"].setValue(this.convertBool(data.data.integrityPeriodMonitored));
            tagForm.controls["dataUpdateChange"].setValue(this.convertBool(data.data.dataUpdateChange));
            tagForm.controls["qualityChange"].setValue(this.convertBool(data.data.qualityChange));
            tagForm.controls["dataChange"].setValue(this.convertBool(data.data.dataChange));
            tagForm.controls["generalInterrogation"].setValue(this.convertBool(data.data.generalInterrogation));
            tagForm.controls["bufferOverflow"].setValue(this.convertBool(data.data.bufferOverflow));
            tagForm.controls["configurationRevision"].setValue(this.convertBool(data.data.configurationRevision));
            tagForm.controls["dataReference"].setValue(this.convertBool(data.data.dataReference));
            tagForm.controls["dataSetName"].setValue(this.convertBool(data.data.dataSetName));
            tagForm.controls["entryID"].setValue(this.convertBool(data.data.entryID));
            tagForm.controls["reasonForInclusion"].setValue(this.convertBool(data.data.reasonForInclusion));
            tagForm.controls["sequenceNumber"].setValue(this.convertBool(data.data.sequenceNumber));
            tagForm.controls["timeStamp"].setValue(this.convertBool(data.data.timeStamp));
            tagForm.controls["retryEnableCount"].setValue(data.data.retryEnableCount);
            tagForm.controls["retryEnablePeriod"].setValue(data.data.retryEnablePeriod);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_SERVER_READ"); }
          }
        );
      }
    }
    return "";
  }

  private GTW61850GOOSEEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "GCBName")
        this.enableControl(tagForm.controls["GCBName"], false);
    }
    else {
      if (editorField.schemaName == "GCBList" && tagForm.controls["61850ClientName"].value != "") {
        let GCBListValue: any = tagForm.controls["GCBList"].value;
        tagForm.controls["GCBName"].setValue(GCBListValue.item.GOOSEName);
        tagForm.controls["DSName"].setValue(GCBListValue.item.dataSet);
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, GCBListValue.item.GOOSEName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "DSName" && tagForm.controls["61850ClientName"].value != "") {
        let GOOSEName: any
        let GCBListValue: any = tagForm.controls["GCBList"].value;
        if (GCBListValue != null && GCBListValue != "")
          GOOSEName = GCBListValue.item.GOOSEName;
        else
          GOOSEName = (<any>tagForm.controls["GCBList"]).component.componentData[0].GOOSEName;

        let clientName61850 = tagForm.controls["61850ClientName"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDCHANGE61850DATASET;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: GOOSEName, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName, dataModal.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["GCBList"]).component.setGridData(data.dataDS);
                editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName).subscribe(
                  (data: any) => {
                    (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
                  },
                  error => {
                    if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE"); }
                  }
                );
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error); }
        );
      }
      if (editorField.schemaName == "createDS" && tagForm.controls["61850ClientName"].value != "") {
        let GOOSEName: any
        let GCBListValue: any = tagForm.controls["GCBList"].value;
        if (GCBListValue != null && GCBListValue != "")
          GOOSEName = GCBListValue.item.GOOSEName;
        else
          GOOSEName = (<any>tagForm.controls["GCBList"]).component.componentData[0].GOOSEName;

        let clientName61850 = tagForm.controls["61850ClientName"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADD61850DATASET;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: GOOSEName, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName, dataModal.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["GCBList"]).component.setGridData(data.dataDS);
                editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName).subscribe(
                  (data: any) => {
                    (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
                  },
                  error => {
                    if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE"); }
                  }
                );
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
      if (editorField.schemaName == "readConfigFromServer") {
        editorsService.editorAction(editorType, "ReadConfigFromServer", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["RCBName"].value).subscribe(
          (data: any) => {
            tagForm.controls["integrityPeriod"].setValue(data.data.integrityPeriod);
            tagForm.controls["bufferTime"].setValue(data.data.bufferTime);
            tagForm.controls["purgeBefore1stEnable"].setValue(this.convertBool(data.data.purgeBefore1stEnable));
            tagForm.controls["purgeBeforeEnableOnReconnect"].setValue(this.convertBool(data.data.purgeBeforeEnableOnReconnect));
            tagForm.controls["integrityPeriodMonitored"].setValue(this.convertBool(data.data.integrityPeriodMonitored));
            tagForm.controls["dataUpdateChange"].setValue(this.convertBool(data.data.dataUpdateChange));
            tagForm.controls["qualityChange"].setValue(this.convertBool(data.data.qualityChange));
            tagForm.controls["dataChange"].setValue(this.convertBool(data.data.dataChange));
            tagForm.controls["generalInterrogation"].setValue(this.convertBool(data.data.generalInterrogation));
            tagForm.controls["bufferOverflow"].setValue(this.convertBool(data.data.bufferOverflow));
            tagForm.controls["configurationRevision"].setValue(this.convertBool(data.data.configurationRevision));
            tagForm.controls["dataReference"].setValue(this.convertBool(data.data.dataReference));
            tagForm.controls["dataSetName"].setValue(this.convertBool(data.data.dataSetName));
            tagForm.controls["entryID"].setValue(this.convertBool(data.data.entryID));
            tagForm.controls["reasonForInclusion"].setValue(this.convertBool(data.data.reasonForInclusion));
            tagForm.controls["sequenceNumber"].setValue(this.convertBool(data.data.sequenceNumber));
            tagForm.controls["timeStamp"].setValue(this.convertBool(data.data.timeStamp));
            tagForm.controls["retryEnableCount"].setValue(data.data.retryEnableCount);
            tagForm.controls["retryEnablePeriod"].setValue(data.data.retryEnablePeriod);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_SERVER_READ"); }
          }
        );
      }
    }
    return "";
  }

  private GTW61850PolledDataSetEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "I61850PolledDataSetName")
        this.enableControl(tagForm.controls["I61850PolledDataSetName"], false);
      if (editorField.schemaName == "DSList" && tagForm.controls["61850ClientName"].value != "") {
        let DSListValue: any = tagForm.controls["DSList"].value;
        tagForm.controls["DSList"].setValue(DSListValue.item.dataSet);
        tagForm.controls["I61850PolledDataSetName"].setValue(DSListValue.item.dataSet);
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, DSListValue.item.dataSet).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "createDS" && tagForm.controls["61850ClientName"].value != "") {
        let clientName61850 = tagForm.controls["61850ClientName"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADD61850DATASET;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "NewDS", parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(data => {
          if (data != null && data.result && data.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, data.DSName).subscribe(
              (datac: any) => {
                (<any>tagForm.controls["DSList"]).component.setGridData(datac.dataDS);
                (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
                editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, data.DSName).subscribe(
                  (datam: any) => {
                    (<any>tagForm.controls["DSMemberList"]).component.setGridData(datam.result);
                  },
                  error => {
                    if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE"); }
                  }
                );
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (data == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
      if (editorField.schemaName == "deleteDS" && tagForm.controls["61850ClientName"].value != "") {
        let DSListValue = tagForm.controls["DSList"].value
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue }).subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_DELETE'))
            .okBtnClass('btn btn-default')
            .body(`
					<div class="panel panel-warning">
						<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					</div>
        `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["61850ClientName"].value, DSListValue).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectGridDataMember(0);
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
    }
    return "";
  }

  private GTW61850ChangeDataSetEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "DSName") {
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["DSName"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
    }
    return "";
  }

  private GTW61850DatasetEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "changeFCDisplay") {
        editorsService.editorAction(editorType, "ChangeFCDS", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["changeFCDisplay"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["nodeList"]).component.setTreeviewData(data.data);
            if (tagForm.controls["changeFCDisplay"].value === "true") {
              tagForm.controls["changeFCDisplay"].setValue("false");
              (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
              editorField.name = 'TR_SHOW_ALL_FCS';
            }
            else {
              tagForm.controls["changeFCDisplay"].setValue("true");
              (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
              editorField.name = 'TR_SHOW_ONLY_ST_MX';
            }
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_FC_DATASET_NOT_CHANGED"); }
          }
        );
      }
      if (editorField.schemaName == "nodeList") {
        let checkedNodes: any = this.getCheckedItems(tagForm.controls["nodeList"].value);
        let DSGridData = "{ \"columns\": [{ \"field\": \"item\", \"header\": \"ITEM\" }], \"data\":" + JSON.stringify(checkedNodes) + "}";
        (<any>tagForm.controls["DSMemberList"]).component.setGridData(DSGridData);
        tagForm.controls["DSMemberList"].setValue(JSON.stringify(checkedNodes));
      }
    }
    return "";
  }

  private GTW61850DataAttributeMDOEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "Add61850Item") {
        let RCBListComponentData: any = (<any>tagForm.controls["DAPointList"]).component.componentData;
        let DAPointListChecked: Array<string> = [];
        RCBListComponentData.forEach(item => {
          if (item.checkbox === true) {
            DAPointListChecked.push(item.itemName);
          };
        });
        editorsService.editorAction(editorType, "Add61850MultipleItems", objectCollectionKind, tagForm.controls["61850ControlBlock"].value, JSON.stringify(DAPointListChecked), tagForm.controls["autoMapQualityTime"].value).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_ADD_MULTIPLE_ITEMS_FAILED"); }
          }
        );
      }
      if ((editorField.schemaName == "autoMapQualityTime")) {
        editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850ControlBlock"].value, tagForm.controls["autoMapQualityTime"].value, tagForm.controls["functionalConstraint"].value, tagForm.controls["DASearch"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DAPointList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED"); }
          }
        );
      }
      if ((editorField.schemaName == "BTNSearch")) {
        editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850ControlBlock"].value, tagForm.controls["autoMapQualityTime"].value, tagForm.controls["functionalConstraint"].value, tagForm.controls["DASearch"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DAPointList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED"); }
          }
        );
      }
      if ((editorField.schemaName == "tagQuality" || editorField.schemaName == "tagTime")) {
        let clientName61850 = tagForm.controls["controlBlock"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDSELECTDATAATTRIBUTE;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        let selectDA = "SelectDAT";
        if (editorField.schemaName == "tagQuality")
          selectDA = "SelectDAQ";
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: selectDA, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(data => {
          if (data != null && data.result && data.DAName != "") {
            if (editorField.schemaName == "tagQuality")
              tagForm.controls["tagQuality"].setValue(data.DAName);
            else
              tagForm.controls["tagTime"].setValue(data.DAName);
          }
          else if (data == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
    }
    return "";
  }

  private GTW61850WriteablePointEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "DAPointList") {
        let RCBListComponentData: any = (<any>tagForm.controls["DAPointList"]).component.componentData
        let DAPointListChecked: Array<string> = [];
        RCBListComponentData.forEach(item => {
          if (item.checkbox === true) {
            DAPointListChecked.push(item.itemName);
          };
        });
        tagForm.controls["DAPointListChecked"].setValue(JSON.stringify(DAPointListChecked));
      }
      if ((editorField.schemaName == "FC_SP" || editorField.schemaName == "FC_CF" || editorField.schemaName == "FC_SV" || editorField.schemaName == "DASearch")) {

        let functionalConstraint: string;
        if (tagForm.controls["FC_SP"].value === true)
          functionalConstraint += "FC_SP;";
        if (tagForm.controls["FC_CF"].value === true)
          functionalConstraint += "FC_CF;";
        if (tagForm.controls["FC_SV"].value === true)
          functionalConstraint += "FC_SV;";

        editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850Client"].value, functionalConstraint, tagForm.controls["DASearch"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DAPointList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED"); }
          }
        );
      }
    }
    return "";
  }

  private GTW61850SelectDataAttributeEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      (<any>tagForm.controls["DAPointList"]).component.selectGridDataMember(-1);
    }
    else {
      if (editorField.schemaName == "DAPointList") {
        let DAPointListValue: any = tagForm.controls["DAPointList"].value;
        tagForm.controls["DAName"].setValue(DAPointListValue.item.itemName);
      }
    }
    return "";
  }

  private GTW61850CommandPointEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "objectName")
        this.enableControl(tagForm.controls["objectName"], false);
    }
    else {
      if (editorField.schemaName == "controlpointList") {
        let controlpointListValue: any = tagForm.controls["controlpointList"].value;
        tagForm.controls["objectName"].setValue(controlpointListValue.item.itemName);
      }
    }
    return "";
  }

  private GTW61850SdoEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "objectName")
        this.enableControl(tagForm.controls["objectName"], false);
      if (editorField.schemaName == "dropMemberName")
        this.enableControl(tagForm.controls["dropMemberName"], false);
    }
    else {
      if (editorField.schemaName == "DAPointList") {
        let DAListValue: any = tagForm.controls["DAPointList"].value;
        tagForm.controls["DAValue"].setValue(DAListValue.item.itemName);
        tagForm.controls["DATime"].setValue(DAListValue.item.time);
        tagForm.controls["DAQuality"].setValue(DAListValue.item.quality);
        if ((<any>tagForm.controls["objectName"]).component.editorField.value == "") {
          let DAValueName: any = DAListValue.item.itemName.replace(/[.+\/]/g, "_");
          tagForm.controls["objectName"].setValue(DAValueName);
        }
      }
      if ((editorField.schemaName == "autoMapQualityTime" || editorField.schemaName == "functionalConstraint" || editorField.schemaName == "DASearch" || editorField.schemaName == "onlyExtRef" || editorField.schemaName == "DAPointsToSearch")) {
        editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850Server"].value, tagForm.controls["autoMapQualityTime"].value,  tagForm.controls["functionalConstraint"].value, tagForm.controls["DASearch"].value, tagForm.controls["DAPointsToSearch"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DAPointList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWOPC61850ServerControlEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if ((editorField.schemaName == "OPCSelectRequestPoint" ||
        editorField.schemaName == "OPCSelectResponsePoint" ||
        editorField.schemaName == "OPCOperateRequestPoint" ||
        editorField.schemaName == "OPCOperateResponsePoint" ||
        editorField.schemaName == "OPCCancelRequestPoint" ||
        editorField.schemaName == "OPCCancelResponsePoint" ||
        editorField.schemaName == "OPCaddCausePoint" ||
        editorField.schemaName == "OPCCommandTermPoint")) {
        let OPCClientName = tagForm.controls["OPCClientName"].value
        if (OPCClientName == "") {
          alertService.error("TR_OPC_CLIENT_IS_EMPTY");
          return "";
        }
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADD61850CONTROLTOOPCMAPPINGITEM;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: OPCClientName, parentObjectName: OPCClientName, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result) {
            tagForm.controls[editorField.schemaName].setValue(dataModal.itemName);
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_NO_OPC_ITEM_SELECTED");
          }
        });
      }
    }
    return "";
  }

  private GTWOPC61850ServerControlItemEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (editorField.schemaName == "refreshOPCItemParent") {
      let objectName: string = tagForm.controls["objectName"].value;
      editorsService.editorAction(editorType, "RefreshOPCItemParent", objectCollectionKind, objectName).subscribe(
        (data: any) => {
          (<any>tagForm.controls["itemParentBrowser"]).component.setTreeviewData(data.data);
        },
        error => {
          if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE"); }
        }
      );
    }
    if (!isRunDuringInit) {
      if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "") {
        let currentNode: any = tagForm.controls["itemParentBrowser"].value;
        let objectName: string = tagForm.controls["objectName"].value;
        if (currentNode instanceof Node) {
          if (currentNode.children.length === 0) {
            editorsService.editorAction(editorType, "LoadChildrenOPCClientItem", objectCollectionKind, objectName, currentNode.nodeFullName).subscribe(
              (data: any) => {
                let jsonSource = data.data;
                currentNode.children = this.setTreeviewNode(jsonSource, currentNode);
                tagForm.controls["itemParentBrowser"].setValue(null);
                if (currentNode.children.length > 0)
                  currentNode.isExpanded = true;
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE"); }
              }
            );
          }
          else {
            currentNode.toggle();
          }
        }
        else {
          alertService.clearMessage();
          tagForm.controls["itemName"].setValue("");
          editorsService.editorAction(editorType, "ValidateOPCItem", objectCollectionKind, objectName, currentNode).subscribe(
            (data: any) => {
              if (data.result)
                tagForm.controls["itemName"].setValue(currentNode);
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED"); }
            }
          );
        }
      }
    }
    return "";
  }

  private GTW62351TASE2SecurityEditor(isRunDuringInit: boolean, tagForm: FormGroup, editorField: EditorFieldObjectDTO) {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "isCertificateAuthorityFileEnabled") {
        tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(true);
        tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(false);
      }
      else if (editorField.schemaName == "isCertificateAuthorityFolderEnabled") {
        tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(false);
        tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(true);
      }
    }

    if (tagForm.controls["TASE2SecurityOn"].value == true) {
      if (tagForm.controls["isCertificateAuthorityFileEnabled"].value == true) {
        this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], true);
        this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], false);
        tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValue("");

        tagForm.controls["TASE2CertificateAuthorityFile"].setValidators([Validators.required]);
        tagForm.controls["TASE2CertificateAuthorityFile"].updateValueAndValidity();
        tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValidators(null);
        tagForm.controls["TASE2DirectoryToCertificateAuthority"].updateValueAndValidity();
      }
      else if (tagForm.controls["isCertificateAuthorityFolderEnabled"].value == true) {
        this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], false);
        this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], true);
        tagForm.controls["TASE2CertificateAuthorityFile"].setValue("");

        tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValidators([Validators.required]);
        tagForm.controls["TASE2DirectoryToCertificateAuthority"].updateValueAndValidity();
        tagForm.controls["TASE2CertificateAuthorityFile"].setValidators(null);
        tagForm.controls["TASE2CertificateAuthorityFile"].updateValueAndValidity();
      }
    }
    else {
      tagForm.controls["TASE2CertificateAuthorityFile"].setValidators(null);
      tagForm.controls["TASE2CertificateAuthorityFile"].updateValueAndValidity();
      tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValidators(null);
      tagForm.controls["TASE2DirectoryToCertificateAuthority"].updateValueAndValidity();
    }
  }

  private GTWTASE2ClientEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (editorField.schemaName == "TASE2SecurityOn") {
      if (tagForm.controls["TASE2SecurityOn"].value == true) {
        this.enableControl(tagForm.controls["TASE2CertAuthChainingVerDepth"], true);
        this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], true);
        this.enableControl(tagForm.controls["TASE2CertificateAuthorityRevokeListFile"], true);
        this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], true);
        this.enableControl(tagForm.controls["TASE2MMSCommonName"], true);
        this.enableControl(tagForm.controls["TASE2MMSPrivateKeyFile"], true);
        this.enableControl(tagForm.controls["TASE2MMSPrivateKeyPassPhrase"], true);
        this.enableControl(tagForm.controls["TASE2MMSPublicCertificateFile"], true);
        this.enableControl(tagForm.controls["TASE2TLSCommonName"], true);
        this.enableControl(tagForm.controls["TASE2TLSMaxPDUs"], true);
        this.enableControl(tagForm.controls["TASE2TLSMaxRenegotiationWaitTime"], true);
        this.enableControl(tagForm.controls["TASE2TLSRenegotiation"], true);
        this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyFile"], true);
        this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyPassPhrase"], true);
        this.enableControl(tagForm.controls["TASE2TLSRSAPublicCertFile"], true);
        this.enableControl(tagForm.controls["TASE2UseSiscoCompatability"], true);
        this.enableControl(tagForm.controls["isCertificateAuthorityFileEnabled"], true);
        this.enableControl(tagForm.controls["isCertificateAuthorityFolderEnabled"], true);
      }
      else {
        this.enableControl(tagForm.controls["TASE2CertAuthChainingVerDepth"], false);
        this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], false);
        this.enableControl(tagForm.controls["TASE2CertificateAuthorityRevokeListFile"], false);
        this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], false);
        this.enableControl(tagForm.controls["TASE2MMSCommonName"], false);
        this.enableControl(tagForm.controls["TASE2MMSPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["TASE2MMSPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["TASE2MMSPublicCertificateFile"], false);
        this.enableControl(tagForm.controls["TASE2TLSCommonName"], false);
        this.enableControl(tagForm.controls["TASE2TLSMaxPDUs"], false);
        this.enableControl(tagForm.controls["TASE2TLSMaxRenegotiationWaitTime"], false);
        this.enableControl(tagForm.controls["TASE2TLSRenegotiation"], false);
        this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyFile"], false);
        this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyPassPhrase"], false);
        this.enableControl(tagForm.controls["TASE2TLSRSAPublicCertFile"], false);
        this.enableControl(tagForm.controls["TASE2UseSiscoCompatability"], false);
        this.enableControl(tagForm.controls["isCertificateAuthorityFileEnabled"], false);
        this.enableControl(tagForm.controls["isCertificateAuthorityFolderEnabled"], false);
      }
    }
    if (editorField.schemaName == "isCertificateAuthorityFileEnabled" || editorField.schemaName == "isCertificateAuthorityFolderEnabled" || editorField.schemaName == "TASE2SecurityOn") this.GTW62351TASE2SecurityEditor(isRunDuringInit, tagForm, editorField);
    return "";
  }

  private GTWTASE2CommandPointEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (isRunDuringInit) {
      if (editorField.schemaName == "tagName")
        this.enableControl(tagForm.controls["tagName"], false);
    }
    else {
      if (editorField.schemaName == "controlpointList") {
        let controlpointListValue: any = tagForm.controls["controlpointList"].value;
        tagForm.controls["tagName"].setValue(controlpointListValue.item.itemName);
        tagForm.controls["tagTase2Type"].setValue(controlpointListValue.item.datatype);
        tagForm.controls["tagOptions"].setValue(controlpointListValue.item.options);
      }
      if (editorField.schemaName == "addTASE2CommandPoint") {
        let TASE2ControlBlockName = tagForm.controls["TASE2ControlBlockName"].value;
        let tagName = tagForm.controls["tagName"].value;
        let tagOptions = tagForm.controls["tagOptions"].value;
        let tagDescription = tagForm.controls["tagDescription"].value;
        let userTagName = tagForm.controls["userTagName"].value;
        let tagTase2Type = tagForm.controls["tagTase2Type"].value;

        editorsService.editorAction(editorType, "AddTASE2CommandPoint", objectCollectionKind, TASE2ControlBlockName, tagName, tagOptions, tagDescription, userTagName, tagTase2Type).subscribe(
          (data: any) => {
            alertService.success("TR_DATA_SAVED");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_DATA_NOT_SAVED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWTASE2PolledDataSetEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "DSList" && tagForm.controls["TASE2ClientName"].value != "") {
        let DSListValue: any = tagForm.controls["DSList"].value;
        tagForm.controls["DSList"].setValue(DSListValue.item.dataSetName);
        (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
        tagForm.controls["polledDSDSID"].setValue(DSListValue.item.dataSetName);
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue.item.dataSetName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "createDS" && tagForm.controls["TASE2ClientName"].value != "") {
        let clientNameTASE2 = tagForm.controls["TASE2ClientName"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADDTASE2DATASET
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "NewDS", parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(data => {
          if (data != null && data.result && data.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (data == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
      if (editorField.schemaName == "manageDS" && tagForm.controls["TASE2ClientName"].value != "") {
        let clientNameTASE2 = tagForm.controls["TASE2ClientName"].value
        let DSListValue = tagForm.controls["DSList"].value
        if (DSListValue != "") {
          let editorCommandsDTOString = EditorCommandsDTO.MENUCMDMANAGETASE2DATASET
          let addTitle: string = "TR_" + editorCommandsDTOString;
          const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
          dashboardConfigTagEditorModalRef.result.then(data => {
            if (data != null && data.result && data.DSName != "") {
              alertService.success("TR_DATA_SAVED");
              editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
                }
              );
            }
            else if (data == "") {
              alertService.error("TR_ERROR_DATA_NOT_SAVED");
            }
          },
            (error) => { alertService.debug(error.toString()); }
          );
        }
        else {
          alertService.error("TR_TASE2_DATASET_IS_NOT_SELECTED");
          return "";
        }
      }
      if (editorField.schemaName == "deleteDS" && tagForm.controls["TASE2ClientName"].value != "") {
        let DSListValue = tagForm.controls["DSList"].value
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue }).subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_DELETE'))
            .okBtnClass('btn btn-default')
            .body(`
						<div class="panel panel-warning">
							<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
						</div>
          `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectGridDataMember(0);
                  (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
      if (editorField.schemaName == "addTASE2DSPD" && tagForm.controls["TASE2ClientName"].value != "") {
        let tase2ClientName: any = tagForm.controls["TASE2ClientName"].value;
        let polledDSID: any = tagForm.controls["polledDSID"].value;
        let tase2CltReportedDSIntegrityPeriod: any = tagForm.controls["TASE2CltReportedDSIntegrityPeriod"].value;
        let dsName: any = tagForm.controls["DSList"].value;
        if (tase2ClientName != "" && polledDSID != "" && tase2CltReportedDSIntegrityPeriod != "" && dsName != "") {
          editorsService.editorAction(editorType, "AddTASE2DSPD", objectCollectionKind, tase2ClientName, polledDSID, tase2CltReportedDSIntegrityPeriod, dsName).subscribe(
            (data: any) => {
              alertService.success("TR_DATA_SAVED");
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_CANNOT_ADD_DSTS"); }
            }
          );
        }
        else {
          alertService.error("TR_ERROR_THE_REQUEST_IS_MISSING_ONE_OR_MORE_REQUIRED_FIELDS");
        }
      }
    }
    return "";
  }

  private GTWTASE2CltReportedDataSetEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "DSList" && tagForm.controls["TASE2ClientName"].value != "") {
        let DSListValue: any = tagForm.controls["DSList"].value;
        tagForm.controls["DSList"].setValue(DSListValue.item.dataSetName);
        tagForm.controls["RCBDataSetName"].setValue(DSListValue.item.dataSetName);
        tagForm.controls["DSMemberList"].setValue(DSListValue.item.dataSetName);
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue.item.dataSetName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "createDS" && tagForm.controls["TASE2ClientName"].value != "") {
        let clientNameTASE2 = tagForm.controls["TASE2ClientName"].value
        let DSListValue = tagForm.controls["DSList"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADDTASE2DATASET
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(data => {
          if (data != null && data.result && data.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (data == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
      if (editorField.schemaName == "manageDS" && tagForm.controls["TASE2ClientName"].value != "") {
        let clientNameTASE2 = tagForm.controls["TASE2ClientName"].value
        let DSListValue = tagForm.controls["DSList"].value
        if (DSListValue != "") {
          let editorCommandsDTOString = EditorCommandsDTO.MENUCMDMANAGETASE2DATASET
          let addTitle: string = "TR_" + editorCommandsDTOString;
          const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
          dashboardConfigTagEditorModalRef.result.then(data => {
            if (data != null && data.result && data.DSName != "") {
              alertService.success("TR_DATA_SAVED");
              editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
                }
              );
            }
            else if (data == "") {
              alertService.error("TR_ERROR_DATA_NOT_SAVED");
            }
          },
            (error) => { alertService.debug(error.toString()); }
          );
        }
        else {
          alertService.error("TR_TASE2_DATASET_IS_NOT_SELECTED");
          return "";
        }
      }
      if (editorField.schemaName == "deleteDS" && tagForm.controls["TASE2ClientName"].value != "") {
        let DSListValue = tagForm.controls["DSList"].value
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue }).subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_DELETE'))
            .okBtnClass('btn btn-default')
            .body(`
					    <div class="panel panel-warning">
						    <div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					    </div>
            `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectGridDataMember(0);
                  (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
      if (editorField.schemaName == "addTASE2DSTS" && tagForm.controls["TASE2ClientName"].value != "") {
        let tase2ClientName: any = tagForm.controls["TASE2ClientName"].value;
        let tase2CltReportedDSName: any = tagForm.controls["TASE2CltReportedDSName"].value;
        let tase2CltReportedDSDomainName: any = tagForm.controls["TASE2CltReportedDSDomainName"].value;
        let dsName: any = tagForm.controls["DSList"].value;
        if (tase2ClientName != "" && tase2CltReportedDSName != "" && tase2CltReportedDSDomainName != "" && dsName != "") {
          editorsService.editorAction(editorType, "AddTASE2DSTS", objectCollectionKind, tase2ClientName, tase2CltReportedDSName, tase2CltReportedDSDomainName, dsName).subscribe(
            (data: any) => {
              alertService.success("TR_DATA_SAVED");
            },
            error => {
              if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_CANNOT_ADD_DSTS"); }
            }
          );
        }
        else {
          alertService.error("TR_ERROR_THE_REQUEST_IS_MISSING_ONE_OR_MORE_REQUIRED_FIELDS");
        }
      }
    }
    return "";
  }

  private GTWTASE2DatasetEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "nodeList") {
        let checkedNodes: any = this.getCheckedItems(tagForm.controls["nodeList"].value);
        let DSGridData = "{ \"columns\": [{ \"field\": \"item\", \"header\": \"ITEM\" }], \"data\":" + JSON.stringify(checkedNodes) + "}";
        (<any>tagForm.controls["DSMemberList"]).component.setGridData(DSGridData);
        tagForm.controls["DSMemberList"].setValue(JSON.stringify(checkedNodes));
      }
    }
    return "";
  }

  private GTWTase2ConfigEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "domainsList") {
        let domainListValue: any = tagForm.controls["domainsList"].value;
        editorsService.editorAction(editorType, "ShowConfigInfoTase2", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue.item.itemName).subscribe(
          (data: any) => {
            tagForm.controls["domainInfo"].setValue(data.data.join("\r\n"));
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_CONFIG_INFO_UNAVAILABLE"); }
          }
        );
      }
    }
    return "";
  }

  private GTWTASE2DataAttributeMDOEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "DAPointList") {
        let RCBListComponentData: any = (<any>tagForm.controls["DAPointList"]).component.componentData
        let DAPointListChecked: Array<string> = [];
        RCBListComponentData.forEach(item => {
          if (item.checkbox === true) {
            DAPointListChecked.push(item.itemName);
          };
        });
        tagForm.controls["DAPointListChecked"].setValue(JSON.stringify(DAPointListChecked));
      }
    }
    return "";
  }

  private GTWTASE2ClientModelEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "domainsList") {
        let domainListValue: any = tagForm.controls["domainsList"].value;
        editorsService.editorAction(editorType, "ChangeSelectedDomain", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue.item.itemName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["dataAttributesList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "DSList") {
        let DSListValue: any = tagForm.controls["DSList"].value;
        tagForm.controls["DSList"].setValue(DSListValue.item.dataSetName);
        tagForm.controls["DSMemberList"].setValue(DSListValue.item.dataSetName);
        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["objectName"].value, DSListValue.item.dataSetName).subscribe(
          (data: any) => {
            (<any>tagForm.controls["DSMemberList"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "addDomain") {
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADDTASE2DOMAIN;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        let node: TreeNodeDTO = { nodeClassName: "GTWTASE2DomainEditor", nodeCollectionKind: TreeNodeDTO.NodeCollectionKindEnum.ALL };
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "", parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService, node: node }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.data != "") {
            (<any>tagForm.controls["domainsList"]).component.setGridData(dataModal.data);
            alertService.success("TR_DATA_SAVED");
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error); }
        );
      }
      if (editorField.schemaName == "editDomain") {
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDEDIT;
        let addTitle: string = "TR_" + editorCommandsDTOString;
        let domainListValue: any = tagForm.controls["domainsList"].value;
        if (domainListValue == null || domainListValue == "") {
          alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
          return "";
        }
        let node: TreeNodeDTO = { nodeClassName: "GTWTASE2DomainEditor", nodeCollectionKind: TreeNodeDTO.NodeCollectionKindEnum.ALL };
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: domainListValue.item.itemName, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService, node: node }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.data != "") {
            (<any>tagForm.controls["domainsList"]).component.setGridData(dataModal.data);
            alertService.success("TR_DATA_SAVED");
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error); }
        );
      }
      if (editorField.schemaName == "deleteDomain") {
        let domainListValue: any = tagForm.controls["domainsList"].value;
        if (domainListValue == null || domainListValue == "") {
          alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
          return "";
        }
        if (domainListValue.item.itemName == "VCC") {
          alertService.error("TR_TASE2_CANNOT_DELETE_THE_VCC_DOMAIN");
          return "";
        }
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DOMAIN").subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_DELETE'))
            .okBtnClass('btn btn-default')
            .body(`
				    <div class="panel panel-warning">
					    <div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				    </div>
          `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "DeleteTase2Domain", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue.item.itemName).subscribe(
                (dataModal: any) => {
                  if (dataModal != null && dataModal.result && dataModal.data != "") {
                    (<any>tagForm.controls["domainsList"]).component.setGridData(dataModal.data);
                    (<any>tagForm.controls["domainsList"]).component.selectGridDataMember(null);
                    alertService.success("TR_DATA_DELETED");
                  }
                  else if (dataModal == "") {
                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                  }
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_CANNOT_DELETE_DOMAIN"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
      if (editorField.schemaName == "addDataAttribute") {
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADDTASE2DATAATTRIBUTE
        let addTitle: string = "TR_" + editorCommandsDTOString;
        let domainListValue: any = tagForm.controls["domainsList"].value;
        if (domainListValue == null || domainListValue == "") {
          alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
          return "";
        }
        let node: TreeNodeDTO = { nodeClassName: "GTWTASE2DomainEditor", nodeCollectionKind: TreeNodeDTO.NodeCollectionKindEnum.ALL };
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: domainListValue.item.itemName, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService, node: node }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(dataModal => {
          if (dataModal != null && dataModal.result && dataModal.data != "") {
            (<any>tagForm.controls["dataAttributesList"]).component.setGridData(dataModal.data);
            alertService.success("TR_DATA_SAVED");
          }
          else if (dataModal == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error); }
        );
      }
      if (editorField.schemaName == "deleteDataAttribute") {
        let domainListValue: any = tagForm.controls["domainsList"].value;
        let dataAttributesListValue: any = tagForm.controls["dataAttributesList"].value;
        if (domainListValue == null || domainListValue == "") {
          alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
          return "";
        }
        if (domainListValue == "VCC") {
          alertService.error("TR_TASE2_CANNOT_DELETE_THE_VCC_DOMAIN");
          return "";
        }
        if (dataAttributesListValue == "") {
          alertService.error("TR_TASE2_DATA_ATTRIBUTE_IS_NOT_SELECTED");
          return "";
        }
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATA_ATTRIBUTE").subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_DELETE'))
            .okBtnClass('btn btn-default')
            .body(`
				    <div class="panel panel-warning">
					    <div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				    </div>
          `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "DeleteTase2DataAttribute", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue.item.itemName, dataAttributesListValue.item.itemName).subscribe(
                (dataModal: any) => {
                  if (dataModal != null && dataModal.result && dataModal.data != "") {
                    (<any>tagForm.controls["dataAttributesList"]).component.setGridData(dataModal.data);
                    (<any>tagForm.controls["dataAttributesList"]).component.selectGridDataMember(null);
                    alertService.success("TR_DATA_DELETED");
                  }
                  else if (dataModal == "") {
                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                  }
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_CANNOT_DELETE_DATA_ATTRIBUTE"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
      if (editorField.schemaName == "createDS") {
        let DSListValue = tagForm.controls["DSList"].value
        let editorCommandsDTOString = EditorCommandsDTO.MENUCMDADDTASE2DATASET
        let addTitle: string = "TR_" + editorCommandsDTOString;
        const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
        dashboardConfigTagEditorModalRef.result.then(data => {
          if (data != null && data.result && data.DSName != "") {
            alertService.success("TR_DATA_SAVED");
            editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["objectName"].value, data.DSName).subscribe(
              (data: any) => {
                (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          }
          else if (data == "") {
            alertService.error("TR_ERROR_DATA_NOT_SAVED");
          }
        },
          (error) => { alertService.debug(error.toString()); }
        );
      }
      if (editorField.schemaName == "manageDS") {
        let DSListValue = tagForm.controls["DSList"].value
        if (DSListValue != "") {
          let editorCommandsDTOString = EditorCommandsDTO.MENUCMDMANAGETASE2DATASETFULLEDIT
          let addTitle: string = "TR_" + editorCommandsDTOString;
          const dashboardConfigTagEditorModalRef = modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService }, BSModalContext));
          dashboardConfigTagEditorModalRef.result.then(data => {
            if (data != null && data.result && data.DSName != "") {
              alertService.success("TR_DATA_SAVED");
              editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["objectName"].value, data.DSName).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectLastGridDataMember();
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
                }
              );
            }
            else if (data == "") {
              alertService.error("TR_ERROR_DATA_NOT_SAVED");
            }
          },
            (error) => { alertService.debug(error.toString()); }
          );
        }
        else {
          alertService.error("TR_TASE2_DATASET_IS_NOT_SELECTED");
          return "";
        }
      }
      if (editorField.schemaName == "deleteDS") {
        let DSListValue = tagForm.controls["DSList"].value
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue }).subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_DELETE'))
            .okBtnClass('btn btn-default')
            .body(`
					    <div class="panel panel-warning">
						    <div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					    </div>
            `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["objectName"].value, DSListValue).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                  (<any>tagForm.controls["DSList"]).component.selectGridDataMember(0);
                  (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
      if (editorField.schemaName == "loadModel" && tagForm.controls["tase2CSVFileName"].value != "") {
        editorsService.editorAction(editorType, "LoadModel", objectCollectionKind, tagForm.controls["objectName"].value, tagForm.controls["tase2CSVFileName"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["domainsList"]).component.setGridData(data.data);
            if ((<any>tagForm.controls["domainsList"]).component.CheckGridDataMember) {
              (<any>tagForm.controls["domainsList"]).component.selectGridDataMember(0);
            }
            editorsService.editorAction(editorType, "GetDataSet", objectCollectionKind, tagForm.controls["objectName"].value).subscribe(
              (data: any) => {
                (<any>tagForm.controls["DSList"]).component.setGridData(data.dataDS);
                if ((<any>tagForm.controls["DSList"]).component.CheckGridDataMember) {
                  (<any>tagForm.controls["DSList"]).component.selectGridDataMember(0);
                }
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED"); }
              }
            );
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_LOAD_MODEL"); }
          }
        );
      }
      if (editorField.schemaName == "exportModel" && tagForm.controls["exportFileName"].value != "") {
        editorsService.editorAction(editorType, "ExportModel", objectCollectionKind, tagForm.controls["objectName"].value, tagForm.controls["exportFileName"].value).subscribe(
          (data: any) => {
            let csvFileName: any = tagForm.controls["tase2CSVFileName"].value;
            editorsService.editorAction(editorType, "LoadEditorFiles", objectCollectionKind, tagForm.controls["objectName"].value).subscribe(
              (data: any) => {
                (<any>tagForm.controls["tase2CSVFileName"]).component.listComponentData(data.data);
              },
              error => {
                if (error.status == 401) { authenticationService.onLoginFailed("/"); }
              }
            );

            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_EXPORTMODEL_FAILED"); }
          }
        );
      }
      if (editorField.schemaName == "clearModel") {
        let ModalDeleteRef;
        translateService.get("TR_ARE_YOU_SURE_TO_CLEAR_MODEL").subscribe(res => {
          ModalDeleteRef = modal.confirm()
            .size('lg')
            .showClose(true)
            .title(translateService.instant('TR_WARNING'))
            .okBtn(translateService.instant('TR_CLEAR_MODEL'))
            .okBtnClass('btn btn-default')
            .body(`
				  <div class="panel panel-warning">
				  	<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
				  </div>
        `).open()
        });
        ModalDeleteRef.result.then(
          (result) => {
            if (result) {
              editorsService.editorAction(editorType, "ClearModel", objectCollectionKind, tagForm.controls["objectName"].value).subscribe(
                (data: any) => {
                  (<any>tagForm.controls["domainsList"]).component.componentData = null;
                  (<any>tagForm.controls["dataAttributesList"]).component.componentData = null;
                  (<any>tagForm.controls["DSList"]).component.componentData = null;
                  (<any>tagForm.controls["DSMemberList"]).component.componentData = null;
                },
                error => {
                  if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE"); }
                }
              );
            }
          },
          () => { } //needed
        );
      }
    }
    return "";
  }

  private GTWTASE2DataAttributeEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "dataType") {
        let dataType: any = tagForm.controls["dataType"].value;
        editorsService.editorAction(editorType, "ChangeDataTypeTase2", objectCollectionKind, dataType).subscribe(
          (data: any) => {
            this.enableControl(tagForm.controls["SBO"], data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_TASE2_DATA_TYPE"); }
          }
        );
      }
    }
    return "";
  }

  private GTWODBCClientEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {

      if (editorField.schemaName == "TestODBCConnectionString" && tagForm.controls["ODBCConnectionString"].value != "") {
        editorsService.editorAction(editorType, "TestODBCConnectionString", objectCollectionKind, tagForm.controls["ODBCConnectionString"].value).subscribe(
          (data: any) => {
            alertService.success("TR_SUCCESS");
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ODBC_OPENDB_FAILED"); }
          }
        );
      }
    }
    return "";
  }

  private GTWODBCQueryEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "tableList") {
        let tableValue: any = tagForm.controls["tableList"].value;
        editorsService.editorAction(editorType, "ChangeTable", objectCollectionKind, tagForm.controls["ODBCClient"].value, tableValue).subscribe(
          (data: any) => {
            (<any>tagForm.controls["tableInfo"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "ODBCQuery") {
        (<any>tagForm.controls["objectName"]).component.inputNativeElement.focus();
      }
      if (editorField.schemaName == "executeSql") {
        let queryValue: any = tagForm.controls["ODBCQuery"].value;
        let queryAlias: any = tagForm.controls["ODBCQueryAliasName"].value;

        editorsService.editorAction(editorType, "ExecuteSql", objectCollectionKind, tagForm.controls["ODBCClient"].value, queryValue.replace(/\?/gi, '%3F'), queryAlias).subscribe(
          (data: any) => {
            (<any>tagForm.controls["queryResults"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE"); }
          }
        );
      }
    }
    return "";
  }

  private GTWGooseMonitorEditor(isRunDuringInit: boolean, modal: Modal, tagForm: FormGroup, editorField: EditorFieldObjectDTO, editorType: string, objectCollectionKind: any, collapsiblePanels: CollapsiblePanel[], editorsService: EditorsService, editorCommand: string, authenticationService: AuthenticationService, alertService: AlertService, translateService: TranslateService, selectValue: any): string {
    if (!isRunDuringInit) {
      if (editorField.schemaName == "GOOSEMonitorSCLFile") {
        editorsService.editorAction(editorType, "GetGOOSEMonitorStreams", objectCollectionKind, tagForm.controls["GOOSEMonitorSCLFile"].value).subscribe(
          (data: any) => {
            (<any>tagForm.controls["GOOSEMonitorStream"]).component.setGridData(data.data);
          },
          error => {
            if (error.status == 401) { authenticationService.onLoginFailed("/"); } else { alertService.error("TR_ERROR_GOOSE_MONITOR_STREAMS_UNAVAILABLE"); }
          }
        );
      }
      if (editorField.schemaName == "GOOSEMonitorStream") {
        let gooseMonitorStreamComponentData: any = (<any>tagForm.controls["GOOSEMonitorStream"]).component.componentData;
        let gooseMonitorStreamThreshold: number = tagForm.controls["GOOSEMonitorStreamThreshold"].value;
        let currentRow: number = tagForm.controls["GOOSEMonitorStream"].value.index;
        if (gooseMonitorStreamComponentData[currentRow].checkbox == true)
          gooseMonitorStreamComponentData[currentRow].threshold = gooseMonitorStreamThreshold.toString();
        else
          gooseMonitorStreamComponentData[currentRow].threshold = "60";
      }
      if (editorField.schemaName == "objectName") {
        let gooseMonitorStreamComponentData: any = (<any>tagForm.controls["GOOSEMonitorStream"]).component.componentData
        let checkedItem: Array<string> = [];
        let GOOSEMonitorStreamValue: any = {};
        gooseMonitorStreamComponentData.forEach(rowItem => {
          if (rowItem.checkbox == true) {
            checkedItem.push(rowItem);
          };
        });
        GOOSEMonitorStreamValue.checkedItem = checkedItem
        tagForm.controls["GOOSEMonitorStream"].setValue(GOOSEMonitorStreamValue);
      }
    }
    return "";
  }
  // #endregion

  // #region Helper
  private setTreeviewNode(parentChildrenSource: any, parent: any): Array<Node> {
    let children: Array<Node> = []
    if (parentChildrenSource != null && parentChildrenSource != "") {
      parentChildrenSource.forEach((childJson) => {
        let child = new Node(childJson.nodeName, childJson.nodeFullName, parent, false, childJson.displayCheckbox);
        child.hasChildren = childJson.hasChildren;
        children.push(child);
      });
    }
    return children;
  }

  private setTreeviewNodeSelect(parentChildrenSource: any, parent: any): Array<NodeSelect> {
    let children: Array<NodeSelect> = []
    if (parentChildrenSource != null && parentChildrenSource != "") {
      parentChildrenSource.forEach((childJson) => {
        let child = new NodeSelect(childJson.nodeName, childJson.nodeFullName, parent, false, childJson.displayCheckbox);
        child.hasChildren = childJson.hasChildren;
        children.push(child);
      });
    }
    return children;
  }

  private convertBool(value: string): Boolean {
    if (value == "1" || value.toUpperCase() == "TRUE")
      return true;
    if (value == "0" || value.toUpperCase() == "FALSE")
      return false;
  }

  private getCheckedItems(source, checkIfNodeHasChildren: Boolean = false): any {
    let nodes: any = source.children;
    let nodePaths: Array<any> = [];
    this.getCheckedNodes(nodes, nodePaths, 0, checkIfNodeHasChildren);
    return nodePaths;
  }

  private getCheckedNodes(nodes: any, nodePaths: Array<any>, nodeDepth: number, checkIfNodeHasChildren: Boolean): any {
    let node, childCheckedNodes;
    let checkedNodes = [];

    for (let i = 0; i < nodes.length; i++) {
      node = nodes[i];
      if (node.checked && (!node.hasChildren || checkIfNodeHasChildren)) {
        checkedNodes.push(node);
        nodePaths.push({ "item": node.nodeFullName });
      }
      if (node.children.length > 0) {
        nodeDepth++;
        childCheckedNodes = this.getCheckedNodes(node.children, nodePaths, nodeDepth, checkIfNodeHasChildren);
        if (childCheckedNodes.length > 0)
          checkedNodes = checkedNodes.concat(childCheckedNodes);
      }
    }
    return checkedNodes;
  }

  private enableControl(abstractControl: any, enable: boolean = true): void {
    if (enable == true) {
      if (abstractControl.editorField.isRequired) {
        abstractControl.setValidators([Validators.required]);
        abstractControl.updateValueAndValidity();
      }
      abstractControl.editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.Yes;
      abstractControl.enable();
    }
    else {
      abstractControl.setValidators(null);
      abstractControl.updateValueAndValidity();
      abstractControl.editorField.isEditable = EditorFieldObjectDTO.IsEditableEnum.No;
      abstractControl.disable();
    }
  }
  // #endregion
}