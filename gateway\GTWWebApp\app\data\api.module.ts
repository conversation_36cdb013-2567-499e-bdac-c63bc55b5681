import { NgModule, ModuleWithProviders, Ski<PERSON><PERSON>elf, Optional } from '@angular/core';
import { Configuration } from './configuration';
import { HttpClient } from '@angular/common/http';


import { AuthService } from './api/auth.service';
import { ConfigService } from './api/config.service';
import { EditorContextMenuService } from './api/editorContextMenu.service';
import { EditorsService } from './api/editors.service';
import { HelpService } from './api/help.service';
import { LogService } from './api/log.service';
import { ManageService } from './api/manage.service';
import { MappingsService } from './api/mappings.service';
import { NodesService } from './api/nodes.service';
import { TagsService } from './api/tags.service';

@NgModule({
  imports: [],
  declarations: [],
  exports: [],
  providers: [
    AuthService,
    ConfigService,
    EditorContextMenuService,
    EditorsService,
    HelpService,
    LogService,
    ManageService,
    MappingsService,
    NodesService,
    TagsService]
})
export class ApiModule {
  public static forRoot(configurationFactory: () => Configuration): ModuleWithProviders {
    return {
      ngModule: ApiModule,
      providers: [{ provide: Configuration, useFactory: configurationFactory }]
    };
  }

  constructor(@Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
        'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
