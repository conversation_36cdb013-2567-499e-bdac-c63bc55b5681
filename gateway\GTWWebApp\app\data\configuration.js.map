{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["configuration.ts"], "names": [], "mappings": ";;;;;;;YASA;gBAQE,uBAAY,uBAAqD;oBAArD,wCAAA,EAAA,4BAAqD;oBAC/D,IAAI,CAAC,OAAO,GAAG,uBAAuB,CAAC,OAAO,CAAC;oBAC/C,IAAI,CAAC,QAAQ,GAAG,uBAAuB,CAAC,QAAQ,CAAC;oBACjD,IAAI,CAAC,QAAQ,GAAG,uBAAuB,CAAC,QAAQ,CAAC;oBACjD,IAAI,CAAC,WAAW,GAAG,uBAAuB,CAAC,WAAW,CAAC;oBACvD,IAAI,CAAC,QAAQ,GAAG,uBAAuB,CAAC,QAAQ,CAAC;oBACjD,IAAI,CAAC,eAAe,GAAG,uBAAuB,CAAC,eAAe,CAAC;gBACjE,CAAC;gBASM,+CAAuB,GAA9B,UAA+B,YAAsB;oBAArD,iBAUC;oBATC,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;wBAC5B,OAAO,SAAS,CAAC;qBAClB;oBAED,IAAI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAC;oBACtD,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;qBACxB;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBASM,0CAAkB,GAAzB,UAA0B,OAAiB;oBAA3C,iBAUC;oBATC,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;wBACvB,OAAO,SAAS,CAAC;qBAClB;oBAED,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAC;oBACjD,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;qBACnB;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAYM,kCAAU,GAAjB,UAAkB,IAAY;oBAC5B,IAAM,QAAQ,GAAW,IAAI,MAAM,CAAC,+DAA+D,EAAE,GAAG,CAAC,CAAC;oBAC1G,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,6BAA6B,CAAC,CAAC;gBACvG,CAAC;gBACH,oBAAC;YAAD,CAAC,AArED,IAqEC;;QACD,CAAC"}