System.register(["@angular/core", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "../../data/model/models"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ngx_modialog_7_1, bootstrap_1, models_1, DashboardConfigTagValueQualityModal, TagQualityModalContext;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagValueQualityModal = (function () {
                function DashboardConfigTagValueQualityModal(dialog, renderer) {
                    this.dialog = dialog;
                    this.renderer = renderer;
                    this.tagList = [];
                    this.TagValueTypeTextEnum = models_1.TagObjectDTO.TagValueTypeTextEnum;
                    this.context = dialog.context;
                    this.context.dialogClass = "modal-dialog modal-md";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                }
                DashboardConfigTagValueQualityModal.prototype.ngOnInit = function () {
                    this.tagList = this.dialog.context.tagList.map(function (x) { return Object.assign({}, x); });
                    this.tagList.forEach(function (tag) {
                        var tagQualityList = [];
                        tagQualityList.push({ name: "QUALITY_ENUM_GOOD", value: 0x0000, description: "TR_QUALITY_ENUM_GOOD_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_INVALID", value: 0x80, description: "TR_QUALITY_ENUM_INVALID_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_INVALID_TIME", value: 0x08, description: "TR_QUALITY_ENUM_INVALID_TIME_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_BLOCKED", value: 0x010, description: "TR_QUALITY_ENUM_BLOCKED_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_SUBSTITUTED", value: 0x20, description: "TR_QUALITY_ENUM_SUBSTITUTED_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_NOT_TOPICAL", value: 0x40, description: "TR_QUALITY_ENUM_NOT_TOPICAL_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_REF_ERROR", value: 0x0100, description: "TR_QUALITY_ENUM_REF_ERROR_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_IN_TRANSIT", value: 0x0200, description: "TR_QUALITY_ENUM_IN_TRANSIT_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_UNINITIALIZED", value: 0x0400, description: "TR_QUALITY_ENUM_UNINITIALIZED_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_TEST", value: 0x0800, description: "TR_QUALITY_ENUM_TEST_DESC", isChecked: false });
                        tagQualityList.push({ name: "QUALITY_ENUM_OVERFLOW", value: 0x01, description: "TR_QUALITY_ENUM_OVERFLOW_DESC", isChecked: false });
                        tag["tagQualityList"] = tagQualityList;
                        tag["tagValueType"] = models_1.TagObjectDTO.getTagPropertyMaskStringValue(tag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.VALUE_TYPE);
                        if (tag.tagPurposeMask & models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO) {
                            tag["tagValueType"] = "Reset To Zero";
                        }
                    });
                };
                DashboardConfigTagValueQualityModal.prototype.beforeDismiss = function () {
                    return true;
                };
                DashboardConfigTagValueQualityModal.prototype.reset = function (tag) {
                    tag.tagValue = "0";
                };
                DashboardConfigTagValueQualityModal.prototype.cancel = function () {
                    this.dialog.close({ result: false });
                };
                DashboardConfigTagValueQualityModal.prototype.tagQualityOnChange = function (tag, tagQualityString) {
                    tag.tagQuality = tagQualityString;
                };
                DashboardConfigTagValueQualityModal.prototype.tagValueOnChange = function (tag, tagValue) {
                    tag.tagValue = tagValue;
                };
                DashboardConfigTagValueQualityModal.prototype.tagValueBoolOnChange = function (tag, tagValue) {
                    tag.tagValue = ((tagValue === "On" || tagValue === "1" || tagValue === "True") ? "false" : "true");
                };
                DashboardConfigTagValueQualityModal.prototype.save = function () {
                    var _this = this;
                    this.dialog.context.tagValueList.tags = [];
                    this.tagList.forEach(function (tag) {
                        var tagValueString = tag.tagValue.toString();
                        var tagValueDTO = { tagName: tag.tagName, tagValue: tagValueString, tagQuality: tag.tagQuality };
                        _this.dialog.context.tagValueList.tags.push(tagValueDTO);
                    });
                    this.dialog.close({ result: true });
                };
                DashboardConfigTagValueQualityModal.prototype.onlyNumericChar = function (event, editorField) {
                    var input = String.fromCharCode(event.keyCode);
                    if ((/[^-e+E.0-9]/.test(input))) {
                        event.preventDefault();
                        return false;
                    }
                };
                DashboardConfigTagValueQualityModal.prototype.onPaste = function (event) {
                    event.preventDefault();
                };
                DashboardConfigTagValueQualityModal = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagValueQualityModal",
                        styles: ["\n      .tag-modal-container {\n\t\t    padding: 10px;\n      }\n      fieldset {\n        border-radius: 6px;\n        border: 1px LightGray solid;\n        margin-bottom: 10px;\n      }\n      .tag-modal-heading {\n        background-color: #d8d8d8;\n        padding: 8px 10px 6px 10px;\n        font-size: 22px;\n        font-weight: bold;\n        border-bottom: 1px solid #a31c3f;\n        overflow-wrap: break-word;\n        margin-top: -20px\n      }\n    "],
                        template: "\n    <div class=\"container-fluid tag-modal-container\">\n      <ng-container *ngFor=\"let tag of this.tagList\">\n        <fieldset><h3><legend class=\"tag-modal-heading\">{{'TR_CHANGE_VALUE_AND_QUALITY_OF' | translate}}&nbsp;{{tag.tagName}}</legend></h3>\n          <div class=\"form-group\" style=\"padding:4px\">\n            <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Bool)\" class=\"form-group\">\n              <label class=\"form-check-label\">\n                <input type=\"checkbox\" class=\"form-check\" [checked]=\"tag.tagValue === 'On' || tag.tagValue === '1' || tag.tagValue === 'True'\" (change)=\"tagValueBoolOnChange(tag, tag.tagValue)\" (paste)=\"onPaste($event)\"/>\n                {{'TR_VALUE' | translate}}\n              </label>\n            </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Char)\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"-128\" max=\"127\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum['Unsigned Char'])\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"0\" max=\"255\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Short)\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"-32768\" max=\"32767\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum['Unsigned Short'])\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"0\" max=\"65535\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Long)\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"-2147483648\" max=\"2147483647\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum['Unsigned Int'])\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"0\" max=\"4294967295\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum['Int 64'])\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"-9223372036854775808\" max=\"9223372036854775807\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum['Unsigned Int 64'])\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" min=\"0\" max=\"18446744073709551615\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Float)\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Double)\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"number\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (keypress)=\"onlyNumericChar($event, editorField)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType===TagValueTypeTextEnum.Unknown || tag.tagValueType===TagValueTypeTextEnum.String || tag.tagValueType===TagValueTypeTextEnum.Time)\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>\n\t\t          <input type=\"text\" class=\"form-control\" [ngModel]=\"tag.tagValue\" (ngModelChange)=\"tagValueOnChange(tag, $event)\" (paste)=\"onPaste($event)\"/>\n\t          </div>\n\t          <div *ngIf=\"(tag.tagValueType==='Reset To Zero')\" class=\"form-group\">\n\t\t          <label>{{'TR_VALUE' | translate}}:</label>&nbsp;\n              <label>{{tag.tagValue}}</label>&nbsp;&nbsp;&nbsp;\n              <button class=\"btn btn-default\" (click)=\"reset(tag)\"><img src=\"../../images/reset.svg\" class=\"image-button\"/>&nbsp;{{'TR_RESET' | translate}}</button>\n\t          </div>\n          </div>\n          <div class=\"form-group\" *ngIf=\"(tag.tagClassName.indexOf('GTWInternalUserDataObject') > -1)\">\n\t          <label>{{'TR_QUALITY' | translate}}:</label>\n\t          <bitmaskComponent [bitmaskList]=\"tag.tagQualityList\" [bitmaskString]=\"tag.tagQuality\" (bitmaskOnChange)=\"tagQualityOnChange(tag, $event)\"></bitmaskComponent>\n          </div>\n          <div class=\"form-group\" *ngIf=\"(tag.tagClassName.indexOf('GTWDnpAnalogInputMdo') > -1 || tag.tagClassName.indexOf('GTWDnpBinaryInputMdo') > -1)\">\n\t          <label>{{'TR_QUALITY' | translate}}:</label>\n\t\t        <input type=\"text\" class=\"form-control\" [ngModel]=\"tag.tagQuality\" (ngModelChange)=\"tagQualityOnChange(tag, $event)\" (paste)=\"onPaste($event)\"/>\n          </div>\n        </fieldset>\n      </ng-container>\n      <div class=\"form-group\" style=\"display:inline-block; width:99%; text-align:center;\">\n\t      <div style=\"display: table-cell;\">\n\t\t      <button class=\"btn btn-default\" (click)=\"cancel()\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CANCEL' | translate}}</button>\n\t      </div>\n\t      <div style=\"display: table-cell;width:99%\"></div>\n\t\t    <div style=\"display: table-cell;\">\n\t\t\t    <button class=\"btn btn-default\" (click)=\"save()\"><img src=\"../../images/ok.svg\" class=\"image-button\"/>&nbsp;{{'TR_SAVE' | translate}}</button>\n\t\t    </div>\n\t    </div>\n\t    <alertStatusBarComponent></alertStatusBarComponent>\n    </div>"
                    }),
                    __metadata("design:paramtypes", [ngx_modialog_7_1.DialogRef, core_1.Renderer2])
                ], DashboardConfigTagValueQualityModal);
                return DashboardConfigTagValueQualityModal;
            }());
            exports_1("DashboardConfigTagValueQualityModal", DashboardConfigTagValueQualityModal);
            TagQualityModalContext = (function (_super) {
                __extends(TagQualityModalContext, _super);
                function TagQualityModalContext() {
                    var _this = _super !== null && _super.apply(this, arguments) || this;
                    _this.tagList = [];
                    _this.tagValueList = {};
                    return _this;
                }
                return TagQualityModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("TagQualityModalContext", TagQualityModalContext);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.value.quality.modal.js.map