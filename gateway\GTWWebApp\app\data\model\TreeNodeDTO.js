System.register([], function (exports_1, context_1) {
    "use strict";
    var TreeNodeDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (TreeNodeDTO) {
                var NodeCollectionKindEnum;
                (function (NodeCollectionKindEnum) {
                    NodeCollectionKindEnum[NodeCollectionKindEnum["MDO"] = 'MDO'] = "MDO";
                    NodeCollectionKindEnum[NodeCollectionKindEnum["SDO"] = 'SDO'] = "SDO";
                    NodeCollectionKindEnum[NodeCollectionKindEnum["ALL"] = 'ALL'] = "ALL";
                })(NodeCollectionKindEnum = TreeNodeDTO.NodeCollectionKindEnum || (TreeNodeDTO.NodeCollectionKindEnum = {}));
            })(TreeNodeDTO || (TreeNodeDTO = {}));
            exports_1("TreeNodeDTO", TreeNodeDTO);
        }
    };
});
//# sourceMappingURL=TreeNodeDTO.js.map