System.register(["@angular/core", "../../authentication/authentication.service", "../../modules/panel/panel", "./dashboard.log.grid.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, authentication_service_1, panel_1, dashboard_log_grid_component_1, DashboardLogComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (panel_1_1) {
                panel_1 = panel_1_1;
            },
            function (dashboard_log_grid_component_1_1) {
                dashboard_log_grid_component_1 = dashboard_log_grid_component_1_1;
            }
        ],
        execute: function () {
            DashboardLogComponent = (function () {
                function DashboardLogComponent(authenticationService) {
                    this.authenticationService = authenticationService;
                    this.logEntryFilter = {};
                    this.isWSPaused = false;
                }
                DashboardLogComponent.prototype.ngOnInit = function () {
                    this.logEntryFilter = { timeStamp: "", source: "", category: "", severity: "", message: "" };
                };
                DashboardLogComponent.prototype.ngAfterViewChecked = function () {
                    if (!this.isWSPaused)
                        this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
                };
                DashboardLogComponent.prototype.extraPanelToggle = function () {
                    this.isExtraPanelOpen = !this.isExtraPanelOpen;
                };
                DashboardLogComponent.prototype.copyToCSV = function () {
                    this.dashboardLogGridComponent.copyToCSV();
                };
                DashboardLogComponent.prototype.clearDisplayLog = function () {
                    this.dashboardLogGridComponent.clearLog();
                };
                DashboardLogComponent.prototype.onClickPause = function (isWSPaused) {
                    try {
                        this.isWSPaused = isWSPaused;
                        if (!this.isWSPaused)
                            this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
                    }
                    catch (err) { }
                };
                __decorate([
                    core_1.ViewChild("scrollMe", { static: false }),
                    __metadata("design:type", core_1.ElementRef)
                ], DashboardLogComponent.prototype, "myScrollContainer", void 0);
                __decorate([
                    core_1.ViewChild(dashboard_log_grid_component_1.DashboardLogGridComponent, { static: false }),
                    __metadata("design:type", dashboard_log_grid_component_1.DashboardLogGridComponent)
                ], DashboardLogComponent.prototype, "dashboardLogGridComponent", void 0);
                __decorate([
                    core_1.Input("panel"),
                    __metadata("design:type", panel_1.Panel)
                ], DashboardLogComponent.prototype, "panel", void 0);
                DashboardLogComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardLogComponent",
                        styles: ["\n\t\t\t.panel-log{\n\t\t\t\theight: 100%;\n        background-image: url(../../../images/background_7.svg), linear-gradient(#000000, #c5c5c5);\n        background-size: 20%;\n        margin: 0 2px 0 0;\n        overflow-x: auto;\n        overflow-y: hidden;\n      }\n      .panel-log-bg-color {\n        height: 100%;\n        background-image: linear-gradient(to bottom, rgba(200, 200, 175, 0.96), rgba(250, 250, 250, 0.90));\n        min-width: 960px;\n      }\n      .panel-scroll{\n        height: calc(100% - 82px);\n        overflow-x: auto;\n      }\n\t\t\t.layoutOptionsButton {\n        position: absolute;\n        width: 99%;\n        padding: 6px;\n        margin: 0px !important;\n        background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(230, 230, 230, 0.8));\n\t\t\t}\n\t\t\t.header-log-grid {\n        padding: 40px 0px 0px 0px;\n        margin: 0px;\n        background-color: transparent;\n\t\t\t}\n      .grid-header {\n        font-weight:bold;\n      }\n      .input-xs {\n        height: 22px;\n        padding: 2px 5px;\n        font-size: 12px;\n        line-height: 1.5; /* If Placeholder of the input is moved up, rem/modify this. */\n        border-radius: 3px;\n      }\n      "],
                        template: "\n    <div class=\"panel-log\">\n      <div class=\"panel-log-bg-color\">\n        <div class=\"layoutOptionsButton panel panel-default\">\n          <div style=\"vertical-align:bottom;\">\n            <div (click)=\"extraPanelToggle()\" style=\"display: inline-block;\">\n              <div *ngIf=\"isExtraPanelOpen\" class=\"glyphicon glyphicon-chevron-down glyphicon-size cell\" title=\"{{'TR_COLLAPSE' | translate}}\"></div>\n              <div *ngIf=\"!isExtraPanelOpen\" class=\"glyphicon glyphicon-chevron-right glyphicon-size cell\" title=\"{{'TR_EXPAND' | translate}}\"></div>\n              <span style=\"font-size:16px;font-weight: bold;margin: 2px;\">{{'TR_LOG_PARAMETERS' | translate}}</span>\n            </div>\n            <div style=\"float: right;margin: -4px\">\n              <button class=\"btn btn-default btn-sm\" style=\"margin-right: 4px;\" (click)=\"copyToCSV()\"><img src=\"../../images/download.svg\" class=\"image-button\"/>&nbsp;{{'TR_DOWNLOAD_BUFFERED_LOG_ENTRIES' | translate}}</button>\n              <button class=\"btn btn-default btn-sm\" style=\"margin-right: 4px;\" (click)=\"clearDisplayLog()\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CLEAR_DISPLAY' | translate}}</button>\n              <button class=\"btn btn-default btn-sm\" style=\"margin-right: 4px;\" *ngIf=\"!isWSPaused\" (click)=\"onClickPause(true)\"><img src=\"../../images/pause.svg\" class=\"image-button\"/>&nbsp;{{'TR_PAUSE' | translate}}</button>\n              <button class=\"btn btn-default btn-sm\" style=\"margin-right: 4px;width: 74px\" *ngIf=\"isWSPaused\" (click)=\"onClickPause(false)\"><img src=\"../../images/play.svg\" class=\"image-button\"/>&nbsp;{{'TR_RUN' | translate}}</button>\n            </div>\n          </div>\n          <dashboardLogExtraComponent *ngIf=\"isExtraPanelOpen\"></dashboardLogExtraComponent>\n        </div>\n        <div class=\"header-log-grid panel panel-default\">\n          <table style=\"table-layout:fixed; width:100%\">\n            <tr>\n              <td style=\"width: 15%\" class=\"grid-header\">{{'TR_TIME_STAMP' | translate}}<br/><input type=\"text\" class=\"form-control input-xs\" [(ngModel)]=\"logEntryFilter.timeStamp\" placeholder=\"{{'TR_ENTER_FILTER' | translate}}\"/></td>\n              <td style=\"width: 10%\" class=\"grid-header\">{{'TR_SOURCE' | translate}}<br/><input type=\"text\" class=\"form-control input-xs \" [(ngModel)]=\"logEntryFilter.source\" placeholder=\"{{'TR_ENTER_FILTER' | translate}}\"/></td>\n              <td style=\"width: 10%\" class=\"grid-header\">{{'TR_DEVICE' | translate}}<br/><input type=\"text\" class=\"form-control input-xs \" [(ngModel)]=\"logEntryFilter.name\" placeholder=\"{{'TR_ENTER_FILTER' | translate}}\"/></td>\n              <td style=\"width: 10%\" class=\"grid-header\">{{'TR_CATEGORY' | translate}}<br/><input type=\"text\" class=\"form-control input-xs\" [(ngModel)]=\"logEntryFilter.category\" placeholder=\"{{'TR_ENTER_FILTER' | translate}}\"/></td>\n              <td style=\"width: 10%\" class=\"grid-header\">{{'TR_SEVERITY' | translate}}<br/><input type=\"text\" class=\"form-control input-xs\" [(ngModel)]=\"logEntryFilter.severity\" placeholder=\"{{'TR_ENTER_FILTER' | translate}}\"/></td>\n              <td style=\"width: 45%\" class=\"grid-header\">{{'TR_MESSAGE' | translate}}<br/><input type=\"text\" class=\"form-control input-xs\" [(ngModel)]=\"logEntryFilter.message\" placeholder=\"{{'TR_ENTER_FILTER' | translate}}\"/></td>\n            </tr>\n          </table>\n        </div>\n        <div #scrollMe class=\"panel-scroll\">\n          <div>\n            <dashboardLogGridComponent [logEntryFilter]=\"logEntryFilter\" [isWSPaused]=\"isWSPaused\" ></dashboardLogGridComponent>\n          </div>\n        </div>\n      </div>\n    </div>\n    "
                    }),
                    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService])
                ], DashboardLogComponent);
                return DashboardLogComponent;
            }());
            exports_1("DashboardLogComponent", DashboardLogComponent);
        }
    };
});
//# sourceMappingURL=dashboard.log.component.js.map