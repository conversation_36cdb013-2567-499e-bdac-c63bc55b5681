{"version": 3, "file": "loader-interceptor.service.js", "sourceRoot": "", "sources": ["loader-interceptor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQE,kCAAoB,aAA4B;oBAA5B,kBAAa,GAAb,aAAa,CAAe;oBAChD,oBAAe,GAAW,CAAC,CAAC;gBADwB,CAAC;gBAGrD,4CAAS,GAAT,UAAU,OAAyB,EAAE,IAAiB;oBAAtD,iBA4BC;oBA3BC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC;wBACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC;wBACpC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EACnD;wBACA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;qBAC1B;yBACI;wBACH,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;wBACzC,IAAI,CAAC,eAAe,EAAE,CAAC;qBACxB;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;yBACxB,IAAI,CAAC,sBAAU,CAAC,UAAC,GAAG;wBACnB,KAAI,CAAC,eAAe,GAAG,CAAC,CAAC;wBACzB,KAAI,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBAC1C,OAAO,iBAAU,CAAC,GAAG,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC;yBACF,IAAI,CAAC,eAAG,CAAsB,UAAC,GAAmB;wBACjD,IAAI,GAAG,YAAY,mBAAY,EAAE;4BAC/B,IAAI,IAAI,EAAE;gCACR,KAAI,CAAC,eAAe,EAAE,CAAC;gCACvB,IAAI,KAAI,CAAC,eAAe,IAAI,CAAC;oCAC3B,KAAI,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gCAC5C,KAAI,CAAC,eAAe,GAAG,CAAC,CAAC;6BAC1B;yBACF;wBACD,OAAO,GAAG,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC;gBAhCU,wBAAwB;oBADpC,iBAAU,EAAE;qDAEwB,8BAAa;mBADrC,wBAAwB,CAiCpC;gBAAD,+BAAC;aAAA,AAjCD;;QAiCC,CAAC"}