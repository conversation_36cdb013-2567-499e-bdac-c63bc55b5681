System.register(["@angular/core", "../../modules/context-menu/context-menu.service", "../../modules/alert/alert.service", "../../authentication/authentication.service", "../../authentication/check.role.pipe", "../../data/model/models", "../../data/api/api", "../../modules/context-menu/context-menu-item", "./dashboard.config.component", "./dashboard.config.devices.component", "./dashboard.config.grid.component", "rxjs"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, context_menu_service_1, alert_service_1, authentication_service_1, check_role_pipe_1, models_1, api_1, context_menu_item_1, dashboard_config_component_1, dashboard_config_devices_component_1, dashboard_config_grid_component_1, models_2, rxjs_1, DashboardConfigContextMenuDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (context_menu_service_1_1) {
                context_menu_service_1 = context_menu_service_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (check_role_pipe_1_1) {
                check_role_pipe_1 = check_role_pipe_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
                models_2 = models_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (context_menu_item_1_1) {
                context_menu_item_1 = context_menu_item_1_1;
            },
            function (dashboard_config_component_1_1) {
                dashboard_config_component_1 = dashboard_config_component_1_1;
            },
            function (dashboard_config_devices_component_1_1) {
                dashboard_config_devices_component_1 = dashboard_config_devices_component_1_1;
            },
            function (dashboard_config_grid_component_1_1) {
                dashboard_config_grid_component_1 = dashboard_config_grid_component_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
            }
        ],
        execute: function () {
            DashboardConfigContextMenuDirective = (function () {
                function DashboardConfigContextMenuDirective(contextMenuService, dashboardConfigComponent, dashboardConfigDevicesComponent, dashboardConfigGridComponent, alertService, authenticationService, editorContextMenuService, checkRolePipe, vcRef, resolver) {
                    this.contextMenuService = contextMenuService;
                    this.dashboardConfigComponent = dashboardConfigComponent;
                    this.dashboardConfigDevicesComponent = dashboardConfigDevicesComponent;
                    this.dashboardConfigGridComponent = dashboardConfigGridComponent;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.editorContextMenuService = editorContextMenuService;
                    this.checkRolePipe = checkRolePipe;
                    this.vcRef = vcRef;
                    this.resolver = resolver;
                    this.contextMenuType = context_menu_item_1.ContextMenuType;
                }
                DashboardConfigContextMenuDirective.prototype.rightClicked = function (event) {
                    this.loadContextMenuItem(event, this.targetObject);
                    event.preventDefault();
                };
                DashboardConfigContextMenuDirective.prototype.instanceOfVirtualNode = function (targetObject) {
                    return targetObject.name !== undefined;
                };
                DashboardConfigContextMenuDirective.prototype.loadAPIContextMenuItems = function (event, contextMenuItems, targetObject) {
                    var _this = this;
                    var APIContextMenuItems = [];
                    var objectClassName = "";
                    var objectCollectionKind = "";
                    if (this.node != null && this.rootNode != null && typeof targetObject === "string") {
                        objectClassName = this.node.nodeClassName;
                        objectCollectionKind = this.node.nodeCollectionKind.toString();
                    }
                    else if (this.tag != null && typeof targetObject === "string") {
                        objectClassName = this.tag.tagClassName;
                        objectCollectionKind = models_2.TagObjectDTO.getTagPropertyMaskStringValue(this.tag.tagPropertyMask, models_2.TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
                    }
                    else if (contextMenuItems.length > 0) {
                        this.contextMenuService.show.next({ event: event, obj: contextMenuItems });
                    }
                    this.editorContextMenuService.getEditorContextMenu(targetObject, objectClassName, objectCollectionKind).subscribe(function (data) {
                        if (data != null && data.length > 0) {
                            data.forEach(function (contextMenuItem) {
                                if (contextMenuItem.command == models_1.EditorCommandsDTO.MENUCMDSEPARATOR.toString())
                                    APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem("MENU_CMD_SEPARATOR", null, _this.contextMenuType.SEPARATOR));
                                else
                                    APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem(contextMenuItem.menuText, new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigComponent.onNodeAction(contextMenuItem.command, targetObject, targetObject, _this.tag, _this.node); }), _this.contextMenuType.ACTION, null, contextMenuItem.isLicensed));
                            });
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    }, function () {
                        _this.loadExtraContextMenuItem(event, APIContextMenuItems, targetObject);
                        if (APIContextMenuItems.length > 0) {
                            if (_this.node != null && _this.node.isFilterTarget) {
                                APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem(models_1.EditorCommandsDTO.MENUCMDNONE.toString(), null, _this.contextMenuType.SEPARATOR));
                                APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_ADD_NODE_TO_FILTER_LOG", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigDevicesComponent.addNodeToLogFilter(_this.node.nodeFullName); }), _this.contextMenuType.ACTION));
                            }
                            if (_this.node != null && _this.node.nodeName == "Gateway" && _this.node.nodeClassName.toString() == "GatewayRootNode") {
                                APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem(models_1.EditorCommandsDTO.MENUCMDNONE.toString(), null, _this.contextMenuType.SEPARATOR));
                                APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_IMPORT_EXPORT_POINTS", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigDevicesComponent.importExportCSV(""); }), _this.contextMenuType.ACTION));
                            }
                            else if (_this.node != null && _this.node.nodeCollectionKind == models_2.TreeNodeDTO.NodeCollectionKindEnum.MDO) {
                                APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem(models_1.EditorCommandsDTO.MENUCMDNONE.toString(), null, _this.contextMenuType.SEPARATOR));
                                APIContextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_EXPORT_POINTS", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigDevicesComponent.importExportCSV(_this.node.nodeFullName); }), _this.contextMenuType.ACTION));
                            }
                            if (_this.node != null && _this.node.nodeName == "Gateway" && _this.node.nodeClassName.toString() == "GatewayRootNode") {
                                APIContextMenuItems.unshift(new context_menu_item_1.ContextMenuItem(models_1.EditorCommandsDTO.MENUCMDNONE.toString(), null, _this.contextMenuType.SEPARATOR));
                                APIContextMenuItems.unshift(new context_menu_item_1.ContextMenuItem("TR_DISPLAY_WARNING", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigDevicesComponent.onWarningOnRootActiveChange(_this.node); }), _this.contextMenuType.CHECKBOX, _this.node.isWarningActive));
                                APIContextMenuItems.unshift(new context_menu_item_1.ContextMenuItem("TR_SAVE_WORKSPACE", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigDevicesComponent.saveConfig(); }), _this.contextMenuType.ACTION_BOLD, null, true, "save.svg"));
                            }
                            if (contextMenuItems.length > 0) {
                                contextMenuItems.unshift(new context_menu_item_1.ContextMenuItem(models_1.EditorCommandsDTO.MENUCMDNONE.toString(), null, _this.contextMenuType.SEPARATOR));
                                _this.contextMenuService.show.next({ event: event, obj: APIContextMenuItems.concat(contextMenuItems) });
                            }
                            else {
                                _this.contextMenuService.show.next({ event: event, obj: APIContextMenuItems });
                            }
                        }
                        else if (contextMenuItems.length > 0) {
                            _this.contextMenuService.show.next({ event: event, obj: APIContextMenuItems.concat(contextMenuItems) });
                        }
                    });
                };
                DashboardConfigContextMenuDirective.prototype.loadContextMenuItem = function (event, targetObject) {
                    var _this = this;
                    var contextMenuItems = [];
                    if (this.instanceOfVirtualNode(targetObject) && targetObject.name != "warning")
                        return;
                    if (this.instanceOfVirtualNode(targetObject) && targetObject.name === "warning" && this.checkRole("OPERATOR_ROLE")) {
                        contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_RESET_COUNTER", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigComponent.resetWarningTags(); })));
                        this.contextMenuService.show.next({ event: event, obj: contextMenuItems });
                        return;
                    }
                    if (this.checkRole("OPERATOR_ROLE")) {
                        this.loadAPIContextMenuItems(event, contextMenuItems, targetObject);
                    }
                    else if (contextMenuItems.length > 0) {
                        this.contextMenuService.show.next({ event: event, obj: contextMenuItems });
                    }
                };
                DashboardConfigContextMenuDirective.prototype.loadExtraContextMenuItem = function (event, contextMenuItems, targetObject) {
                    var _this = this;
                    if (this.node != null && this.node.hasChildren && this.node.nodeName != "Gateway") {
                        var dashboardConfigDevicesComponent_1 = this.getDashboardConfigDevicesComponentInstance(this.vcRef['_view']);
                        if (dashboardConfigDevicesComponent_1 != null) {
                            contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_EXPAND_CHILDREN", new rxjs_1.Subject().subscribe(function (val) { return dashboardConfigDevicesComponent_1.expandChild(_this.node, _this.rootNode); })));
                            contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_COLLAPSE_CHILDREN", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigDevicesComponent.collapseChild(_this.node); })));
                        }
                    }
                    if (this.checkRole("CONFIGURATOR_ROLE")) {
                        if (this.selectedRows && this.selectedRows.length > 1) {
                            var canTagBeDeleted_1;
                            this.selectedRows.forEach(function (tag) {
                                if (models_2.TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, models_2.TagObjectDTO.TagPropertyMaskEnum.CAN_DELETE)) {
                                    canTagBeDeleted_1 = true;
                                }
                            });
                            if (canTagBeDeleted_1) {
                                var contextMenuItemIndex = contextMenuItems.findIndex(function (x) { return x.title == "TR_MENU_CMD_DELETE"; });
                                if (contextMenuItemIndex != -1)
                                    contextMenuItems.splice(contextMenuItemIndex, 1);
                                contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_DELETE_SELECTED", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigComponent.deleteAllTags(_this.selectedRows); })));
                            }
                            var canTagValueBeChanged_1;
                            this.selectedRows.forEach(function (tag) {
                                if (models_2.TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, models_2.TagObjectDTO.TagPropertyMaskEnum.CAN_CHANGE_VALUE)) {
                                    canTagValueBeChanged_1 = true;
                                }
                            });
                            if (canTagValueBeChanged_1) {
                                var contextMenuItemIndex = contextMenuItems.findIndex(function (x) { return x.title == "TR_MENU_CMD_CHANGE_VALUE"; });
                                if (contextMenuItemIndex != -1)
                                    contextMenuItems.splice(contextMenuItemIndex, 1);
                                contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_CHANGE_VALUE_OF_SELECTED", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigComponent.changeAllValueTag(_this.selectedRows); })));
                            }
                            contextMenuItems.push(new context_menu_item_1.ContextMenuItem(models_1.EditorCommandsDTO.MENUCMDNONE.toString(), null, this.contextMenuType.SEPARATOR));
                            contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_UNSELECT_ALL", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigGridComponent.unSelectedAllRows(_this.selectedRows); })));
                        }
                        if (this.tag != null) {
                            contextMenuItems.push(new context_menu_item_1.ContextMenuItem("TR_SELECT_ALL", new rxjs_1.Subject().subscribe(function (val) { return _this.dashboardConfigGridComponent.selectedAllRows(_this.selectedRows, _this.rows); })));
                        }
                    }
                };
                DashboardConfigContextMenuDirective.prototype.checkRole = function (minRole) {
                    return this.checkRolePipe.transform(this.authenticationService.role, minRole);
                };
                DashboardConfigContextMenuDirective.prototype.getDashboardConfigDevicesComponentInstance = function (view) {
                    if (view.component.constructor != null &&
                        view.component.constructor.__annotations__ != null &&
                        view.component.constructor.__annotations__.length > 0 &&
                        view.component.constructor.__annotations__[0].selector == "dashboardConfigDevicesComponent")
                        return view.parent.component;
                    else
                        return this.getParentInstance(view.parent);
                };
                DashboardConfigContextMenuDirective.prototype.getParentInstance = function (viewParent) {
                    if (viewParent.component.constructor != null &&
                        viewParent.component.constructor.__annotations__ != null &&
                        viewParent.component.constructor.__annotations__.length > 0 &&
                        viewParent.component.constructor.__annotations__[0].selector == "dashboardConfigDevicesComponent")
                        return viewParent.component;
                    else if (viewParent.parent != null)
                        return this.getParentInstance(viewParent.parent);
                    else
                        return null;
                };
                __decorate([
                    core_1.Input("dashboardConfigContextMenuDirective"),
                    __metadata("design:type", Object)
                ], DashboardConfigContextMenuDirective.prototype, "targetObject", void 0);
                __decorate([
                    core_1.Input("rootNode"),
                    __metadata("design:type", Object)
                ], DashboardConfigContextMenuDirective.prototype, "rootNode", void 0);
                __decorate([
                    core_1.Input("node"),
                    __metadata("design:type", Object)
                ], DashboardConfigContextMenuDirective.prototype, "node", void 0);
                __decorate([
                    core_1.Input("tag"),
                    __metadata("design:type", Object)
                ], DashboardConfigContextMenuDirective.prototype, "tag", void 0);
                __decorate([
                    core_1.Input("selectedRows"),
                    __metadata("design:type", Array)
                ], DashboardConfigContextMenuDirective.prototype, "selectedRows", void 0);
                __decorate([
                    core_1.Input("rows"),
                    __metadata("design:type", Array)
                ], DashboardConfigContextMenuDirective.prototype, "rows", void 0);
                DashboardConfigContextMenuDirective = __decorate([
                    core_1.Directive({
                        selector: "[dashboardConfigContextMenuDirective]",
                        host: { "(contextmenu)": "rightClicked($event)" },
                        providers: [dashboard_config_devices_component_1.DashboardConfigDevicesComponent, dashboard_config_grid_component_1.DashboardConfigGridComponent]
                    }),
                    __metadata("design:paramtypes", [context_menu_service_1.ContextMenuService, dashboard_config_component_1.DashboardConfigComponent, dashboard_config_devices_component_1.DashboardConfigDevicesComponent,
                        dashboard_config_grid_component_1.DashboardConfigGridComponent, alert_service_1.AlertService, authentication_service_1.AuthenticationService,
                        api_1.EditorContextMenuService, check_role_pipe_1.CheckRolePipe, core_1.ViewContainerRef, core_1.ComponentFactoryResolver])
                ], DashboardConfigContextMenuDirective);
                return DashboardConfigContextMenuDirective;
            }());
            exports_1("DashboardConfigContextMenuDirective", DashboardConfigContextMenuDirective);
        }
    };
});
//# sourceMappingURL=dashboard.config.context-menu.directive.js.map