System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, DashboardConfigDeviceVirtualNodeComponent, VirtualNode;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            DashboardConfigDeviceVirtualNodeComponent = (function () {
                function DashboardConfigDeviceVirtualNodeComponent() {
                    this.virtualNodeList = [];
                    this.onSelectedChanged = new core_1.EventEmitter();
                }
                DashboardConfigDeviceVirtualNodeComponent.prototype.ngOnInit = function () {
                };
                DashboardConfigDeviceVirtualNodeComponent.prototype.onSelectVirtualNode = function (virtualNode) {
                    this.selectedVirtualNode = virtualNode;
                    this.onSelectedChanged.emit(virtualNode);
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigDeviceVirtualNodeComponent.prototype, "isDevicesTreeViewSelected", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Array)
                ], DashboardConfigDeviceVirtualNodeComponent.prototype, "virtualNodeList", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDeviceVirtualNodeComponent.prototype, "onSelectedChanged", void 0);
                DashboardConfigDeviceVirtualNodeComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigDeviceVirtualNodeComponent",
                        styles: ["\n\t\t  .icon-button {\n        font-size: 8px;\n\t\t\t  display: inline-block; \n\t\t\t  margin-right: 5px;\n\t\t\t  cursor: pointer;\n\t\t\t  color:#fffaaa;\n        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n\t\t  }\n\t\t  ul {\n\t\t\t  padding-left: 6px;\n\t\t\t  list-style-type: none;\n\t\t  }\n\t\t  li{\n\t\t\t  margin-top: 4px;\n\t\t  }\n\t\t  ol, ul {\n\t\t\t  margin-top: 0;\n\t\t  }\n      .leaf{\n        white-space: nowrap;\n      }\n      .leaf:hover{\n        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n      }\n      .glyphicon-none {\n        padding-right: 8px;  \n        color: transparent !important;\n      }\n      .nodeImage{\n        width: 20px;\n        height: 20px;\n        filter: drop-shadow( 2px 2px 1px rgba(0, 0, 0, .7));\n      }\n      .node-text{\n        cursor: pointer;\n        color:black;\n        text-decoration:none;\n        vertical-align: bottom;\n        padding-left: 4px;\n      }\n      .hide {\n        display: none;\n      }\n      "
                        ],
                        template: "\n          <ul *ngFor=\"let virtualNode of virtualNodeList\">\n\t\t\t\t\t  <li>\n              <div class=\"leaf\" [class.is-selected]=\"selectedVirtualNode === virtualNode && !isDevicesTreeViewSelected\">\n                <img [src]=\"'../../images/' + virtualNode.icon\" class=\"nodeImage\" [ngClass]=\"{'blinking-red' : !virtualNode.isHealthy}\"/> \n\t\t\t\t\t\t\t  <a (click)=\"onSelectVirtualNode(virtualNode)\" class=\"node-text\" [tooltip]=\"[virtualNode.description]\" [dashboardConfigContextMenuDirective]=\"virtualNode\">{{virtualNode.label | translate}}</a>\n              </div>\n\t\t\t\t\t  </li>\n\t\t\t\t  </ul>"
                    }),
                    __metadata("design:paramtypes", [])
                ], DashboardConfigDeviceVirtualNodeComponent);
                return DashboardConfigDeviceVirtualNodeComponent;
            }());
            exports_1("DashboardConfigDeviceVirtualNodeComponent", DashboardConfigDeviceVirtualNodeComponent);
            VirtualNode = (function () {
                function VirtualNode(name, label, description, isHealthy, icon) {
                    this.name = name;
                    this.label = label;
                    this.description = description;
                    this.isHealthy = isHealthy;
                    this.icon = icon;
                }
                return VirtualNode;
            }());
            exports_1("VirtualNode", VirtualNode);
        }
    };
});
//# sourceMappingURL=dashboard.config.device.virtual-node.component.js.map