System.register(["@angular/common/http"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var http_1, CustomHttpUrlEncodingCodec;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (http_1_1) {
                http_1 = http_1_1;
            }
        ],
        execute: function () {
            CustomHttpUrlEncodingCodec = (function (_super) {
                __extends(CustomHttpUrlEncodingCodec, _super);
                function CustomHttpUrlEncodingCodec() {
                    return _super !== null && _super.apply(this, arguments) || this;
                }
                CustomHttpUrlEncodingCodec.prototype.encodeKey = function (k) {
                    k = _super.prototype.encodeKey.call(this, k);
                    return k.replace(/\+/gi, '%2B');
                };
                CustomHttpUrlEncodingCodec.prototype.encodeValue = function (v) {
                    v = _super.prototype.encodeValue.call(this, v);
                    return v.replace(/\+/gi, '%2B');
                };
                return CustomHttpUrlEncodingCodec;
            }(http_1.HttpUrlEncodingCodec));
            exports_1("CustomHttpUrlEncodingCodec", CustomHttpUrlEncodingCodec);
        }
    };
});
//# sourceMappingURL=encoder.js.map