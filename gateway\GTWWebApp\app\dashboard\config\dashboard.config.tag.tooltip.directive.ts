﻿import { Directive, Input } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { TagObjectDTO } from "../../data/model/models";
import { TagsService } from "../../data/api/api";
import { TooltipItem } from "../../modules/angular-tooltip/tooltip-item";
import { TooltipService } from "../../modules/angular-tooltip/tooltip.service";
import { Subject } from "rxjs";

@Directive({
  selector: "[dashboardConfigTagTooltipDirective]",
  host: {
    "(mouseenter)": "onMouseEnter($event)",
    "(mouseleave)": "onMouseLeave($event)"
  }
})

export class DashboardConfigTagTooltipDirective {
  @Input("dashboardConfigTagTooltipDirective") tagFullName;
  private tooltipId: any;

  constructor(private translateService: TranslateService, private tooltipService: TooltipService, private alertService: AlertService, private authenticationService: AuthenticationService, private tagsService: TagsService) { }

  private onMouseEnter(event: MouseEvent): void {
    this.tooltipId = setTimeout(() => {
      this.loadTooltip(event);
    }, 2000)
    event.preventDefault();
  }

  private onMouseLeave(event: MouseEvent): void {
    clearTimeout(this.tooltipId);
    this.tooltipService.hide.next({ event: event, obj: null });
  }

  private loadTooltip(event: MouseEvent): void {
    let tooltipItems: Array<TooltipItem> = [];
    this.tagsService.getTag(this.tagFullName).subscribe(
      data => {
        if (data != null) {
          this.translateService.get("TR_NAME").subscribe(res => {
            tooltipItems.push(new TooltipItem(res + ": " + data.tagName));
          });
          if (data.tagUserName) {
            this.translateService.get("TR_USER_TAG_NAME").subscribe(res => {
              tooltipItems.push(new TooltipItem(res + ": " + data.tagUserName));
            });
          }
          this.translateService.get("TR_VALUE").subscribe(res => {
            tooltipItems.push(new TooltipItem(res + ": " + data.tagValue));
          });
          this.translateService.get("TR_QUALITY").subscribe(res => {
            tooltipItems.push(new TooltipItem(res + ": " + data.tagQuality));
          });
          this.translateService.get("TR_TIME").subscribe(res => {
            tooltipItems.push(new TooltipItem(res + ": " + data.tagTime));
          });
          this.translateService.get("TR_OPTIONS").subscribe(res => {
            tooltipItems.push(new TooltipItem(res + ": " + data.tagOptions));
          });
          this.translateService.get("TR_TYPE").subscribe(res => {
            let valueType: string = TagObjectDTO.getTagPropertyMaskStringValue(data.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.VALUE_TYPE);
            tooltipItems.push(new TooltipItem(res + ": " + valueType));
          });
        }
        if (tooltipItems.length > 0)
        {
          this.tooltipService.show.next({ event: event, obj: tooltipItems });
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      }
    );
  }
}