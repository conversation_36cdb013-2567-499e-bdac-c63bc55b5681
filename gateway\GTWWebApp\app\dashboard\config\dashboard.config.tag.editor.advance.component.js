System.register(["@angular/core", "../../modules/alert/alert.service", "@ngx-translate/core", "../../data/model/models", "@angular/forms", "../../global/keys.pipe"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, alert_service_1, core_2, models_1, forms_1, keys_pipe_1, DashboardConfigTagEditorAdvanceComponent, AdvanceParameter;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (forms_1_1) {
                forms_1 = forms_1_1;
            },
            function (keys_pipe_1_1) {
                keys_pipe_1 = keys_pipe_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagEditorAdvanceComponent = (function () {
                function DashboardConfigTagEditorAdvanceComponent(alertService, translate, keysPipe) {
                    this.alertService = alertService;
                    this.translate = translate;
                    this.keysPipe = keysPipe;
                    this.onChange = new core_1.EventEmitter();
                    this.parameterList = [];
                    this.isDataLoaded = false;
                    this.controlTypeEnum = models_1.EditorFieldObjectDTO.ControlTypeEnum;
                }
                DashboardConfigTagEditorAdvanceComponent.prototype.ngOnInit = function () {
                    var _a;
                    if ((_a = this.editorField) === null || _a === void 0 ? void 0 : _a.controlSource) {
                        this.parameterList = AdvanceParameter.initParameterValueList(this.editorField.controlSource, this.keysPipe);
                        this.isDataLoaded = true;
                    }
                };
                DashboardConfigTagEditorAdvanceComponent.prototype.parameterOnChange = function () {
                    var parameterItemList = [];
                    try {
                        this.parameterList.forEach(function (parameter) {
                            if (parameter.value != null) {
                                parameterItemList.push(["{\"name\":\"" + parameter.name + "\",\"value\":\"" + parameter.value + "\"}"]);
                            }
                        });
                    }
                    catch (e) { }
                    this.onChange.emit();
                    this.editorFieldcontrol.setValue("[" + parameterItemList + "]");
                };
                DashboardConfigTagEditorAdvanceComponent.prototype.onlyNumericCharInt = function (event, value, rangeMin, rangeMax) {
                    var input = String.fromCharCode(event.keyCode);
                    if ((/[^0-9]/.test(input))) {
                        event.preventDefault();
                        return false;
                    }
                    if (rangeMin != null && rangeMax != null && value != null) {
                        if (value > rangeMax || value < rangeMin) {
                            event.preventDefault();
                            return false;
                        }
                    }
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigTagEditorAdvanceComponent.prototype, "editorField", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", forms_1.FormControl)
                ], DashboardConfigTagEditorAdvanceComponent.prototype, "editorFieldcontrol", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigTagEditorAdvanceComponent.prototype, "onChange", void 0);
                DashboardConfigTagEditorAdvanceComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagEditorAdvanceComponent",
                        styles: ["\n    .table{\n      margin-bottom: 0px;\n      width: 90%;\n      max-width: 90%;\n      margin-left: 10px;\n    }\n    .table>tr>td,\n    .table>tr>th{\n\t    padding: 2px !important;\n      line-height: 4px;\n      border-bottom: gray solid 1px;\n      vertical-align: baseline !important;\n    }\n    .input-text-xs {\n      height: 20px;\n      font-size: 11px;\n      line-height: 14px;\n    }\n    .input-number-xs{\n      height: 20px;\n      width: 100px;\n      font-size: 11px;\n      line-height: 14px;\n    }\n    .select-xs{\n      height: 20px;\n      font-size: 11px;\n      line-height: 14px;\n      padding: 0;\n    }\n    .inline {\n      display: inline-block;\n    }\n    fieldset {\n      border-radius: 6px;\n      border: 1px LightGray solid;\n      padding: 0px 6px 0px 6px;\n      margin-bottom: 10px;\n    }\n    legend{\n      width: inherit;\n      font-size: 11px !important;\n      padding: 0 10px;\n      border-bottom: none;\n      margin-bottom: 4px;\n      font-weight: bold;\n      font-size: 15px;\n    }\n  "],
                        template: "\n\t<div *ngIf=\"isDataLoaded\" class=\"container-fluid\" style=\"margin-left: -12px\">\n    <table class=\"table\">\n      <tr *ngFor=\"let parameter of parameterList\" title=\"{{parameter.helpTR | translateKey:parameter.helpText:translate}}\">\n        <td>\n          <label>{{parameter.name}}</label>\n        </td>\n        <td *ngIf=\"(parameter.controlType===controlTypeEnum.Text)\">\n          <input type=\"text\" class=\"form-control input-text-xs\" [(ngModel)]=\"parameter.value\" (change)=\"parameterOnChange()\"/>\n        </td>\n        <td *ngIf=\"(parameter.controlType===controlTypeEnum.Number)\">\n          <input type=\"number\" style=\"display: inline;\" class=\"form-control input-number-xs\" min=\"{{parameter.range.min}}\" max=\"{{parameter.range.max}}\" [(ngModel)]=\"parameter.value\" (change)=\"parameterOnChange()\" (keypress)=\"onlyNumericCharInt($event, parameter.value, parameter.range.min, parameter.range.max)\"/>\n          <div style=\"display: inline; padding-left: 4px;\" *ngIf=\"(parameter.range!=='')\">{{'TR_MINIMUM_MAXIMUM_RANGE' | translate}} {{parameter.range.min}} - {{parameter.range.max}}</div>\n        </td>\n        <td *ngIf=\"(parameter.controlType===controlTypeEnum.Checkbox)\">\n          <input type=\"checkbox\" class=\"form-check\" [(ngModel)]=\"parameter.value\" (change)=\"parameterOnChange()\"/>\n        </td>\n        <td *ngIf=\"(parameter.controlType===controlTypeEnum.Combobox) || (parameter.controlType===controlTypeEnum.FileManagement)\">\n\t\t\t    <select class=\"form-control select-xs\" [(ngModel)]=\"parameter.value\" (change)=\"parameterOnChange()\">\n            <option *ngFor=\"let item of parameter.choices\" [ngValue]=\"item.key\">{{item.value}}</option>\n\t\t\t    </select>\n        </td>\n\t\t    <td *ngIf=\"(parameter.controlType===controlTypeEnum.MaskEditor)\">\n          <bitmaskAdvanceEditorComponent (bitmaskOnChange)=\"parameterOnChange()\" [(parameter)]=\"parameter\"></bitmaskAdvanceEditorComponent>\n        </td>\n        <td *ngIf=\"(parameter.isGlobalParam===true)\">\n          <div style=\"color: #ff4242;\" class=\"glyphicon glyphicon-exclamation-sign warning-editable\"></div>\n        </td>\n      </tr>\n    </table>\n    <fieldset style=\"background-color: rgba(256, 256,256, 0.3) !important; margin: 8px;\">\n      <div style=\"display: inline;font-weight: bold; margin: 12px\"><div style=\"color: #ff4242;\" class=\"glyphicon glyphicon-exclamation-sign warning-editable\"></div> {{'TR_GLOBAL_PARAMATER_APPLY_TO_THE_WORKSPACE' | translate}}</div>\n      <br>\n      <div style=\"display: inline;font-weight: bold; margin: 12px\"><div style=\"color: #ff4242;\" class=\"glyphicon glyphicon-warning-sign warning-editable\"></div> {{'TR_CHANGING_ADVANCE_PARAMATERS_REQUIRE_A_RESTART' | translate}}</div>\n    </fieldset>\n    <alertStatusBarComponent></alertStatusBarComponent>\n  </div>"
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, core_2.TranslateService, keys_pipe_1.KeysPipe])
                ], DashboardConfigTagEditorAdvanceComponent);
                return DashboardConfigTagEditorAdvanceComponent;
            }());
            exports_1("DashboardConfigTagEditorAdvanceComponent", DashboardConfigTagEditorAdvanceComponent);
            AdvanceParameter = (function () {
                function AdvanceParameter(name, helpTR, helpText, controlType, isGlobalParam, choices, range, value) {
                    if (choices === void 0) { choices = null; }
                    if (value === void 0) { value = null; }
                    this.name = name;
                    this.helpTR = helpTR;
                    this.helpText = helpText;
                    this.controlType = controlType;
                    this.isGlobalParam = isGlobalParam;
                    this.choices = choices;
                    this.range = range;
                    this.value = value;
                }
                AdvanceParameter.initParameterValueList = function (parameterListSource, keysPipe) {
                    var parameterValueList = [];
                    var parameterList = JSON.parse(parameterListSource);
                    try {
                        if (parameterList) {
                            parameterList.forEach(function (parameter) {
                                var helpText = unescape(parameter.helpText);
                                if (parameter.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Combobox.toString()) {
                                    var jsonSource = JSON.parse(parameter.choices);
                                    parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, keysPipe.transformJson(jsonSource), "", parameter.value));
                                }
                                else if (parameter.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Checkbox.toString()) {
                                    var valueCheckbox = false;
                                    if (parameter.value === "1")
                                        valueCheckbox = true;
                                    parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, "", "", valueCheckbox));
                                }
                                else if (parameter.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Number.toString() && parameter.range != "") {
                                    var range = JSON.parse(parameter.range);
                                    parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, "", range, parameter.value));
                                }
                                else if (parameter.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.MaskEditor.toString()) {
                                    parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, parameter.choices, "", parameter.value));
                                }
                                else {
                                    parameterValueList.push(new AdvanceParameter(parameter.name, parameter.helpTR, helpText, parameter.controlType, parameter.isGlobalParam, "", "", parameter.value));
                                }
                            });
                        }
                    }
                    catch (e) { }
                    return parameterValueList;
                };
                return AdvanceParameter;
            }());
            exports_1("AdvanceParameter", AdvanceParameter);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.editor.advance.component.js.map