/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { TreeNodeCollectionObjectDTO } from '../model/treeNodeCollectionObjectDTO';
import { TreeNodeDTO } from '../model/treeNodeDTO';
import { TreeNodePageInfoDTO } from '../model/treeNodePageInfoDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class NodesService {

    public basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Removes nodes from the database.
     * Removes nodes from the database.
     * @param body nodes data
     * @param parentObjectName parent object path
     * @param objectCollectionKind whether this node&#39;s collection has MDOs or SDOs in it.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteNodes(body: TreeNodeCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'body', reportProgress?: boolean): Observable<any>;
    public deleteNodes(body: TreeNodeCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public deleteNodes(body: TreeNodeCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public deleteNodes(body: TreeNodeCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling deleteNodes.');
        }



        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (parentObjectName !== undefined && parentObjectName !== null) {
            queryParameters = queryParameters.set('parentObjectName', <any>parentObjectName);
        }
        if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
            queryParameters = queryParameters.set('objectCollectionKind', <any>objectCollectionKind);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.delete<any>(`${this.basePath}/nodes`,
              {
                  params: queryParameters,
                  withCredentials: this.configuration.withCredentials,
                  headers: headers,
                  observe: observe,
                  reportProgress: reportProgress,
                  body: body
              }
          );
    }

    /**
     * Get node page info.
     * Provides number of tags and mappings for items in the SDG tree
     * @param rootNodePath root node full path (we start at this node)
     * @param sortColumn filed name of column to sort.
     * @param sortColumnDirection Direction of column to sort.
     * @param valueTypeFilter mdo value type filter
     * @param nameFilter returns items that contain this string (use empty string for no filter).
     * @param recursive get recursivly
     * @param tagPurposeFilter tag purpose mask (i.e. a binary or of health(1), performance(2), data(4), unhealthy(8) etc. see GTWTYPES_TAG_PURPOSE_MASK), a value of zero(0) or undefined(empty string)  will select all tags
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */            
  public getNodePageInfo(rootNodePath?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', nameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, recursive?: boolean, tagPurposeFilter?: number, observe?: 'body', reportProgress?: boolean): Observable<TreeNodePageInfoDTO>;
  public getNodePageInfo(rootNodePath?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', nameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, recursive?: boolean, tagPurposeFilter?: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<TreeNodePageInfoDTO>>;
  public getNodePageInfo(rootNodePath?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', nameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, recursive?: boolean, tagPurposeFilter?: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<TreeNodePageInfoDTO>>;
  public getNodePageInfo(rootNodePath?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', nameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, recursive?: boolean, tagPurposeFilter?: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (rootNodePath !== undefined && rootNodePath !== null) {
            queryParameters = queryParameters.set('rootNodePath', <any>rootNodePath);
        }
        if (sortColumn !== undefined && sortColumn !== null) {
          queryParameters = queryParameters.set('sortColumn', <any>sortColumn);
        }
        if (sortColumnDirection !== undefined && sortColumnDirection !== null) {
          queryParameters = queryParameters.set('sortColumnDirection', <any>sortColumnDirection);
        }
        if (valueTypeFilter !== undefined && valueTypeFilter !== null) {
            queryParameters = queryParameters.set('valueTypeFilter', <any>valueTypeFilter);
        }
        if (nameFilter !== undefined && nameFilter !== null) {
            queryParameters = queryParameters.set('nameFilter', <any>nameFilter);
        }
        if (tagAliasFilter !== undefined && tagAliasFilter !== null) {
          queryParameters = queryParameters.set('tagAliasFilter', <any>tagAliasFilter);
        }
        if (tagDescriptionFilter !== undefined && tagDescriptionFilter !== null) {
          queryParameters = queryParameters.set('tagDescriptionFilter', <any>tagDescriptionFilter);
        }
        if (recursive !== undefined && recursive !== null) {
            queryParameters = queryParameters.set('recursive', <any>recursive);
        }
        if (tagPurposeFilter !== undefined && tagPurposeFilter !== null) {
            queryParameters = queryParameters.set('tagPurposeFilter', <any>tagPurposeFilter);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<TreeNodePageInfoDTO>(`${this.basePath}/node_page_info`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get nodes.
     * Provides items from the SDG tree as TreeNodeDTO objects
     * @param rootNodePath root node full path (we start at this node)
     * @param nodeFullNameFilter node full name filter
     * @param includeLeaves include leaf nodes in result
     * @param onlyFirstChildrenLevel include only the first children level in result
     * @param nodeCollectionKindFilter node collection kind filter (i.e. MDO,SDO,ALL)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getNodes(rootNodePath?: string, nodeFullNameFilter?: string, includeLeaves?: boolean, onlyFirstChildrenLevel?: boolean, nodeCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', observe?: 'body', reportProgress?: boolean): Observable<TreeNodeDTO>;
    public getNodes(rootNodePath?: string, nodeFullNameFilter?: string, includeLeaves?: boolean, onlyFirstChildrenLevel?: boolean, nodeCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<TreeNodeDTO>>;
    public getNodes(rootNodePath?: string, nodeFullNameFilter?: string, includeLeaves?: boolean, onlyFirstChildrenLevel?: boolean, nodeCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<TreeNodeDTO>>;
    public getNodes(rootNodePath?: string, nodeFullNameFilter?: string, includeLeaves?: boolean, onlyFirstChildrenLevel?: boolean, nodeCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', observe: any = 'body', reportProgress: boolean = false ): Observable<any> {






        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (rootNodePath !== undefined && rootNodePath !== null) {
            queryParameters = queryParameters.set('rootNodePath', <any>rootNodePath);
        }
        if (nodeFullNameFilter !== undefined && nodeFullNameFilter !== null) {
            queryParameters = queryParameters.set('nodeFullNameFilter', <any>nodeFullNameFilter);
        }
        if (includeLeaves !== undefined && includeLeaves !== null) {
            queryParameters = queryParameters.set('includeLeaves', <any>includeLeaves);
        }
        if (onlyFirstChildrenLevel !== undefined && onlyFirstChildrenLevel !== null) {
            queryParameters = queryParameters.set('onlyFirstChildrenLevel', <any>onlyFirstChildrenLevel);
        }
        if (nodeCollectionKindFilter !== undefined && nodeCollectionKindFilter !== null) {
            queryParameters = queryParameters.set('nodeCollectionKindFilter', <any>nodeCollectionKindFilter);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<TreeNodeDTO>(`${this.basePath}/nodes`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
