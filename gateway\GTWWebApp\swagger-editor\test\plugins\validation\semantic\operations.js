// import expect from "expect"
// import { validate } from "plugins/validation/semantic-validators/validators/operations"

describe.skip("validation plugin - semantic - operations", () => {

  describe("Operations cannot have both a 'body' parameter and a 'formData' parameter", () => {

  })

  describe("Operations must have only one body parameter", () => {

  })

  describe("Operations must have only one body parameter", () => {

  })

  describe("Operations must have unique (name + in combination) parameters", () => {

  })

  describe("Integrations", () => {

  })

})
