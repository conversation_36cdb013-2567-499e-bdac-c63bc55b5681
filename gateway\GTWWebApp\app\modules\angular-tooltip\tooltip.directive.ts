﻿import { Directive, Input } from "@angular/core";
import { TooltipItem } from "../../modules/angular-tooltip/tooltip-item";
import { TooltipService } from "../../modules/angular-tooltip/tooltip.service";

@Directive({
  selector: "[tooltip]",
  host: {
    "(mouseenter)": "onMouseEnter($event)",
    "(mouseleave)": "onMouseLeave($event)"
  }
})

export class TooltipDirective {
  @Input("tooltip") tooltipContents: Array<string>;
  private tooltipId: any;
  constructor(private tooltipService: TooltipService,) { }

  private onMouseEnter(event: MouseEvent): void {
    this.tooltipId = setTimeout(() => {
      this.loadTooltip(event);
    }, 1000)
    event.preventDefault();
  }

  private onMouseLeave(event: MouseEvent): void {
    clearTimeout(this.tooltipId);
    this.tooltipService.hide.next({ event: event, obj: null });
    event.preventDefault();
  }

  private loadTooltip(event: MouseEvent): void {
    let tooltipItems: Array<TooltipItem> = [];
    this.tooltipContents.forEach((tooltipContent) => {
      tooltipItems.push(new TooltipItem(tooltipContent));
    });
    if (tooltipItems.length > 0)
      this.tooltipService.show.next({ event: event, obj: tooltipItems });
  }
}