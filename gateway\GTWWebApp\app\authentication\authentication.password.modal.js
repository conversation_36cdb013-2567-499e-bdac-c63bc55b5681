System.register(["@angular/core", "@angular/router", "@angular/forms", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "../modules/alert/alert.service", "../data/api/api", "@ngx-translate/core"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, router_1, forms_1, ngx_modialog_7_1, bootstrap_1, alert_service_1, api_1, core_2, AuthenticationPasswordModal, PasswordModalContext;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (forms_1_1) {
                forms_1 = forms_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            }
        ],
        execute: function () {
            AuthenticationPasswordModal = (function () {
                function AuthenticationPasswordModal(dialog, router, alertService, authService, translate) {
                    this.dialog = dialog;
                    this.router = router;
                    this.alertService = alertService;
                    this.authService = authService;
                    this.translate = translate;
                    this.authenticationService = null;
                    this.username = "";
                    this.isPasswordReset = false;
                    this.context = dialog.context;
                    this.context.dialogClass = "modal-dialog modal-sm";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                }
                AuthenticationPasswordModal.prototype.ngOnInit = function () {
                    this.authenticationService = this.dialog.context.authenticationService;
                    this.username = this.dialog.context.username;
                    this.authService.configuration = this.authenticationService.userApiConfig;
                    this.isPasswordReset = this.dialog.context.isPasswordReset;
                    this.editPasswordForm = new forms_1.FormGroup({
                        currentPassword: new forms_1.FormControl("", [forms_1.Validators.required]),
                        newPassword: new forms_1.FormControl("", [forms_1.Validators.required, forms_1.Validators.minLength(6)]),
                        newPasswordConfirm: new forms_1.FormControl("", [forms_1.Validators.required, forms_1.Validators.minLength(6)]),
                    }, this.passwordMatchValidator);
                };
                AuthenticationPasswordModal.prototype.passwordMatchValidator = function (formGroup) {
                    return formGroup.get("newPassword").value === formGroup.get("newPasswordConfirm").value ? null : { "mismatch": true };
                };
                AuthenticationPasswordModal.prototype.beforeDismiss = function () {
                    return true;
                };
                AuthenticationPasswordModal.prototype.cancel = function () {
                    this.alertService.clearMessage();
                    this.dialog.close();
                };
                AuthenticationPasswordModal.prototype.save = function (passwordForm, isValid) {
                    var _this = this;
                    this.submitted = true;
                    if (isValid) {
                        this.authService.changeUserPassword(this.username, this.editPasswordForm.get("currentPassword").value, this.editPasswordForm.get("newPassword").value).subscribe(function (data) {
                            _this.alertService.success("TR_DATA_SAVED");
                            _this.authService.logoffUser()
                                .subscribe(function (data) {
                                _this.authenticationService.logoff();
                            }, function (error) {
                                _this.alertService.debug(error.message.toString());
                            }, function () { return location.reload(); });
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                                _this.dialog.close();
                            }
                            else if (error.status == 403) {
                                _this.translate.get("TR_CURRENT_PASSWORD_DOESNT_MATCH").subscribe(function (res) {
                                    _this.alertService.error(res);
                                });
                            }
                            else {
                                _this.translate.get("TR_ERROR", { error: error.error }).subscribe(function (res) {
                                    _this.alertService.error(res);
                                });
                            }
                        });
                    }
                };
                AuthenticationPasswordModal = __decorate([
                    core_1.Component({
                        selector: "authenticationPasswordModal",
                        providers: [api_1.AuthService],
                        styles: ["\n      .password {\n        display:inline-block;\n        width:90%;\n        margin-bottom: 0px;\n        margin-top: 8px;\n      }\n      .status-bar{\n        margin: 2px;\n      }\n    "],
                        template: "\n\t  <div class=\"container-fluid\">\n\t\t  <div class=\"modal-heading\">{{'TR_CHANGE_PASSWORD' | translate}}</div>\n\t\t  <div class=\"modal-body\">\n\t\t    <form [formGroup]=\"editPasswordForm\" novalidate>\n\t\t      <div class=\"form-group password\" [ngClass]=\"{'has-error': (!editPasswordForm.controls.currentPassword.valid)}\">\n\t\t\t      <label>{{'TR_CURRENT_PASSWORD' | translate}}</label>\n\t\t\t      <input type=\"password\" class=\"form-control\" formControlName=\"currentPassword\" show-password>\n\t\t      </div>\n\t\t\t    <small class=\"text-danger\" [hidden]=\"editPasswordForm.controls.currentPassword.valid || (editPasswordForm.controls.currentPassword.pristine && !submitted)\">\n            {{'TR_THE_CURRENT_PASSWORD_IS_REQUIRED' | translate}}\n\t\t\t    </small>\n\t\t      <div class=\"form-group password\" [ngClass]=\"{'has-error': (!editPasswordForm.controls.newPassword.valid)}\">\n\t\t\t      <label>{{'TR_NEW_PASSWORD' | translate}}</label>\n            <img class=\"help-icon\" src=\"../../images/help.svg\" title=\"{{'TR_PASSWORD_COMPLEXITY_REQUIREMENTS' | translate}}\">\n\t\t\t      <input type=\"password\" class=\"form-control\" formControlName=\"newPassword\" pattern=\"^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@$#!\\-+*_%.])\\S{8,24}$\" show-password>\n\t\t      </div>\n\t\t\t    <small class=\"text-danger\" [hidden]=\"editPasswordForm.controls.newPassword.valid || (editPasswordForm.controls.newPassword.pristine && !submitted)\" [innerHtml]=\"'TR_YOUR_PASSWORD_DOESNT_MEET_COMPLEXITY_REQUIREMENTS' | translate\"></small>\n\t\t      <div class=\"form-group password\" [ngClass]=\"{'has-error': (!editPasswordForm.controls.newPasswordConfirm.valid)}\">\n\t\t\t      <label>{{'TR_CONFIRM_NEW_PASSWORD' | translate}}</label>\n\t\t\t      <input type=\"password\" class=\"form-control\" formControlName=\"newPasswordConfirm\" show-password>\n\t\t      </div>\n\t\t\t    <small class=\"text-danger\" [hidden]=\"!editPasswordForm.errors?.mismatch || (editPasswordForm.controls.newPasswordConfirm.pristine && !submitted)\">\n            {{'TR_THE_TWO_PASSWORD_FIELDS_DIDN_T_MATCH' | translate}}\n\t\t\t    </small>\n\t\t      <div style=\"display: inline-block;width:99%; text-align: center; margin-top: 20px;\">\n\t\t\t      <div style=\"display: table-cell;\" *ngIf=\"!isPasswordReset\">\n              <button class=\"btn btn-default\" (click)=\"cancel()\">{{'TR_CANCEL' | translate}}</button>\n\t\t\t      </div>\n\t\t\t      <div style=\"display: table-cell;width:99%\"></div>\n\t\t\t      <div style=\"display: table-cell;\">\n\t\t\t        <button (click)=\"save(editPasswordForm.value, editPasswordForm.valid)\" class=\"btn btn-default\">{{'TR_SAVE' | translate}}</button>\n\t\t\t      </div>\n\t\t      </div>\n          <br>\n\t\t      <alertStatusBarComponent class=\"status-bar\"></alertStatusBarComponent>\n\t\t    </form>\n      </div>\n\t</div>"
                    }),
                    __metadata("design:paramtypes", [ngx_modialog_7_1.DialogRef, router_1.Router, alert_service_1.AlertService, api_1.AuthService, core_2.TranslateService])
                ], AuthenticationPasswordModal);
                return AuthenticationPasswordModal;
            }());
            exports_1("AuthenticationPasswordModal", AuthenticationPasswordModal);
            PasswordModalContext = (function (_super) {
                __extends(PasswordModalContext, _super);
                function PasswordModalContext() {
                    var _this = _super !== null && _super.apply(this, arguments) || this;
                    _this.authenticationService = null;
                    _this.username = "";
                    _this.isPasswordReset = false;
                    return _this;
                }
                return PasswordModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("PasswordModalContext", PasswordModalContext);
        }
    };
});
//# sourceMappingURL=authentication.password.modal.js.map