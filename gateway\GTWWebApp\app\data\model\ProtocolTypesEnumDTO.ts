/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum ProtocolTypesEnumDTO {
    S101 = <any> 'GTWTYPES_PROTOCOL_S101',
    S102 = <any> 'GTWTYPES_PROTOCOL_S102',
    S103 = <any> 'GTWTYPES_PROTOCOL_S103',
    S104 = <any> 'GTWTYPES_PROTOCOL_S104',
    SDNP = <any> 'GTWTYPES_PROTOCOL_SDNP',
    M101 = <any> 'GTWTYPES_PROTOCOL_M101',
    M102 = <any> 'GTWTYPES_PROTOCOL_M102',
    M103 = <any> 'GTWTYPES_PROTOCOL_M103',
    M104 = <any> 'GTWTYPES_PROTOCOL_M104',
    MDNP = <any> 'GTWTYPES_PROTOCOL_MDNP',
    MMB = <any> 'GTWTYPES_PROTOCOL_MMB',
    SMB = <any> 'GTWTYPES_PROTOCOL_SMB',
}
