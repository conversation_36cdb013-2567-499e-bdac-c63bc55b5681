System.register(["@angular/core", "@angular/common/http", "../encoder", "../variables", "../configuration"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, http_1, encoder_1, variables_1, configuration_1, NodesService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (encoder_1_1) {
                encoder_1 = encoder_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            }
        ],
        execute: function () {
            NodesService = (function () {
                function NodesService(httpClient, basePath, configuration) {
                    this.httpClient = httpClient;
                    this.basePath = 'http://localhost/rest';
                    this.defaultHeaders = new http_1.HttpHeaders();
                    this.configuration = new configuration_1.Configuration();
                    if (basePath) {
                        this.basePath = basePath;
                    }
                    if (configuration) {
                        this.configuration = configuration;
                        this.basePath = basePath || configuration.basePath || this.basePath;
                    }
                }
                NodesService.prototype.canConsumeForm = function (consumes) {
                    var form = 'multipart/form-data';
                    for (var _i = 0, consumes_1 = consumes; _i < consumes_1.length; _i++) {
                        var consume = consumes_1[_i];
                        if (form === consume) {
                            return true;
                        }
                    }
                    return false;
                };
                NodesService.prototype.deleteNodes = function (body, parentObjectName, objectCollectionKind, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (body === null || body === undefined) {
                        throw new Error('Required parameter body was null or undefined when calling deleteNodes.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (parentObjectName !== undefined && parentObjectName !== null) {
                        queryParameters = queryParameters.set('parentObjectName', parentObjectName);
                    }
                    if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
                        queryParameters = queryParameters.set('objectCollectionKind', objectCollectionKind);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    var httpContentTypeSelected = this.configuration.selectHeaderContentType(consumes);
                    if (httpContentTypeSelected != undefined) {
                        headers = headers.set('Content-Type', httpContentTypeSelected);
                    }
                    return this.httpClient.delete(this.basePath + "/nodes", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress,
                        body: body
                    });
                };
                NodesService.prototype.getNodePageInfo = function (rootNodePath, sortColumn, sortColumnDirection, valueTypeFilter, nameFilter, tagDescriptionFilter, tagAliasFilter, recursive, tagPurposeFilter, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (rootNodePath !== undefined && rootNodePath !== null) {
                        queryParameters = queryParameters.set('rootNodePath', rootNodePath);
                    }
                    if (sortColumn !== undefined && sortColumn !== null) {
                        queryParameters = queryParameters.set('sortColumn', sortColumn);
                    }
                    if (sortColumnDirection !== undefined && sortColumnDirection !== null) {
                        queryParameters = queryParameters.set('sortColumnDirection', sortColumnDirection);
                    }
                    if (valueTypeFilter !== undefined && valueTypeFilter !== null) {
                        queryParameters = queryParameters.set('valueTypeFilter', valueTypeFilter);
                    }
                    if (nameFilter !== undefined && nameFilter !== null) {
                        queryParameters = queryParameters.set('nameFilter', nameFilter);
                    }
                    if (tagAliasFilter !== undefined && tagAliasFilter !== null) {
                        queryParameters = queryParameters.set('tagAliasFilter', tagAliasFilter);
                    }
                    if (tagDescriptionFilter !== undefined && tagDescriptionFilter !== null) {
                        queryParameters = queryParameters.set('tagDescriptionFilter', tagDescriptionFilter);
                    }
                    if (recursive !== undefined && recursive !== null) {
                        queryParameters = queryParameters.set('recursive', recursive);
                    }
                    if (tagPurposeFilter !== undefined && tagPurposeFilter !== null) {
                        queryParameters = queryParameters.set('tagPurposeFilter', tagPurposeFilter);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/node_page_info", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                NodesService.prototype.getNodes = function (rootNodePath, nodeFullNameFilter, includeLeaves, onlyFirstChildrenLevel, nodeCollectionKindFilter, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (rootNodePath !== undefined && rootNodePath !== null) {
                        queryParameters = queryParameters.set('rootNodePath', rootNodePath);
                    }
                    if (nodeFullNameFilter !== undefined && nodeFullNameFilter !== null) {
                        queryParameters = queryParameters.set('nodeFullNameFilter', nodeFullNameFilter);
                    }
                    if (includeLeaves !== undefined && includeLeaves !== null) {
                        queryParameters = queryParameters.set('includeLeaves', includeLeaves);
                    }
                    if (onlyFirstChildrenLevel !== undefined && onlyFirstChildrenLevel !== null) {
                        queryParameters = queryParameters.set('onlyFirstChildrenLevel', onlyFirstChildrenLevel);
                    }
                    if (nodeCollectionKindFilter !== undefined && nodeCollectionKindFilter !== null) {
                        queryParameters = queryParameters.set('nodeCollectionKindFilter', nodeCollectionKindFilter);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/nodes", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                NodesService = __decorate([
                    core_1.Injectable(),
                    __param(1, core_1.Optional()),
                    __param(1, core_1.Inject(variables_1.BASE_PATH)),
                    __param(2, core_1.Optional()),
                    __metadata("design:paramtypes", [http_1.HttpClient, String, configuration_1.Configuration])
                ], NodesService);
                return NodesService;
            }());
            exports_1("NodesService", NodesService);
        }
    };
});
//# sourceMappingURL=nodes.service.js.map