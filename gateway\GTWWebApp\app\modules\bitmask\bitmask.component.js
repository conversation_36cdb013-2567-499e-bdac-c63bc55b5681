System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, BitmaskComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            BitmaskComponent = (function () {
                function BitmaskComponent() {
                    this.bitmaskString = "0";
                    this.bitmaskLength = 4;
                    this.bitmaskList = [];
                    this.bitmaskOnChange = new core_1.EventEmitter();
                    this.bitmaskOnInit = new core_1.EventEmitter();
                    this.bitmask = 0x0;
                }
                BitmaskComponent.prototype.ngOnInit = function () {
                    this.bitmaskOnInit.emit(this.bitmaskList);
                    if (this.bitmaskString != null)
                        this.bitmask = parseInt(this.bitmaskString, 16);
                    else
                        this.bitmask = 0;
                    this.bitmaskString = this.decimalToHexConverter(this.bitmask);
                    if (this.bitmask === 0 && this.bitmaskList[0].value === 0x0)
                        this.bitmaskList[0].isChecked = true;
                    else
                        for (var i in this.bitmaskList)
                            if (this.bitmaskList[i].value & this.bitmask)
                                this.bitmaskList[i].isChecked = true;
                };
                BitmaskComponent.prototype.selectOpenToggle = function (isOpen) {
                    this.isSelectOpen = isOpen;
                };
                BitmaskComponent.prototype.bitmaskStringOnChange = function (event) {
                    try {
                        if (this.bitmaskString != null && this.isHex(this.bitmaskString.toUpperCase())) {
                            this.bitmask = parseInt(this.bitmaskString, 16);
                        }
                        else {
                            this.bitmask = 0;
                        }
                    }
                    catch (e) {
                        this.bitmaskString = "0000";
                    }
                    if (this.bitmask === 0 && this.bitmaskList[0].value === 0x0) {
                        this.bitmaskList[0].isChecked = true;
                        for (var i in this.bitmaskList)
                            if (i > "0")
                                this.bitmaskList[i].isChecked = false;
                    }
                    else {
                        for (var i in this.bitmaskList)
                            if (this.bitmaskList[i].value & this.bitmask)
                                this.bitmaskList[i].isChecked = true;
                        this.bitmaskList[0].isChecked = false;
                    }
                    this.bitmaskOnChange.emit(this.bitmaskString);
                };
                BitmaskComponent.prototype.bitmaskListOnChange = function (bitmask) {
                    bitmask.isChecked = !bitmask.isChecked;
                    if (bitmask.isChecked && bitmask.value === 0x0) {
                        for (var i in this.bitmaskList)
                            if (i > "0")
                                this.bitmaskList[i].isChecked = false;
                        this.bitmask = 0x0;
                    }
                    else if (this.bitmaskList[0].value === 0x0) {
                        this.bitmaskList[0].isChecked = false;
                    }
                    var bitmaskTemp = this.bitmask;
                    if (bitmask.isChecked)
                        bitmaskTemp = bitmaskTemp | bitmask.value;
                    else
                        bitmaskTemp = bitmaskTemp & (~(bitmask.value));
                    this.bitmask = bitmaskTemp;
                    this.bitmaskString = this.decimalToHexConverter(this.bitmask);
                    this.bitmaskOnChange.emit(this.bitmaskString);
                };
                BitmaskComponent.prototype.decimalToHexConverter = function (inputNumber) {
                    var str = inputNumber.toString(16);
                    return ("0".repeat(this.bitmaskLength - str.length) + str).toUpperCase();
                };
                BitmaskComponent.prototype.isHex = function (hexString) {
                    var a = parseInt(hexString, 16);
                    var b = this.decimalToHexConverter(a);
                    return (b === hexString);
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", String)
                ], BitmaskComponent.prototype, "bitmaskString", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Number)
                ], BitmaskComponent.prototype, "bitmaskLength", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Array)
                ], BitmaskComponent.prototype, "bitmaskList", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], BitmaskComponent.prototype, "bitmaskOnChange", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], BitmaskComponent.prototype, "bitmaskOnInit", void 0);
                BitmaskComponent = __decorate([
                    core_1.Component({
                        selector: "bitmaskComponent",
                        styles: ["\n        .panel {\n          margin-bottom: 2px; !important;\n        }\n \t\t\t  .chevron {\n\t\t\t\t  font-size: 16px;\n\t\t\t\t  margin-left: 6px;\n\t\t\t\t  margin-right: 4px;\n\t\t\t  }\n        .input-bitmask {\n          display: block;\n          width: 104px;\n          padding: 2px 12px;\n          font-size: inherit;\n          color: #555;\n          background-color: #fff;\n          background-image: none;\n          border: 1px solid #ccc;\n          border-radius: 4px;\n        }\n        .list-bitmask{\n          padding:8px; \n          position:absolute; \n          background-color:#f8f8f8;\n          border: 1px solid #ccc;\n          z-index:99;\n        }\n"],
                        template: "\n\t\t\t  <div class=\"panel\" style=\"width: 134px;background-color: #f8f8f8;border: 1px solid #ccc;\" (mouseenter)=\"selectOpenToggle(true)\"  (mouseleave)=\"selectOpenToggle(false)\">\n\t\t\t\t  <div>\n\t\t\t\t\t  <div style=\"display: inline-block; width:100px;\">\n\t\t\t\t\t\t  <input type=\"text\" uppercase class=\"input-bitmask\" [(ngModel)]=\"bitmaskString\" (change)=\"bitmaskStringOnChange($event)\" />\n\t\t\t\t\t  </div>\n\t\t\t\t\t  <div style=\"display: inline-block; vertical-align:middle;\">\n\t\t\t\t\t\t  <div *ngIf=\"isSelectOpen\" class=\"glyphicon glyphicon-chevron-down chevron\"></div>\n\t\t\t\t\t\t  <div *ngIf=\"!isSelectOpen\" class=\"glyphicon glyphicon-chevron-up chevron\"></div>\n\t\t\t\t\t  </div>\n\t\t\t\t  </div>\n\t\t\t\t  <div *ngIf=\"isSelectOpen\" class=\"panel list-bitmask\" style=\"\">\n\t\t\t\t\t  <div *ngFor=\"let bitmask of this.bitmaskList\">\n\t\t\t\t\t\t  <label title=\"{{bitmask.description | translate}}\">\n\t\t\t\t\t\t\t\t  <input type=\"checkbox\"\n\t\t\t\t\t\t\t\t\t\t  class=\"form-check\"\n\t\t\t\t\t\t\t\t\t\t  [checked]=bitmask.isChecked\n\t\t\t\t\t\t\t\t\t\t  (change)=\"bitmaskListOnChange(bitmask)\"/> {{bitmask.name | translate}}\n\t\t\t\t\t\t  </label>\n\t\t\t\t\t  </div>\n\t\t\t\t  </div>\n\t\t\t  </div>\n\t\t    "
                    }),
                    __metadata("design:paramtypes", [])
                ], BitmaskComponent);
                return BitmaskComponent;
            }());
            exports_1("BitmaskComponent", BitmaskComponent);
        }
    };
});
//# sourceMappingURL=bitmask.component.js.map