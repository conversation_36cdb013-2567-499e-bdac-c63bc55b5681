System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, CollapsiblePanel;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            CollapsiblePanel = (function () {
                function CollapsiblePanel() {
                    this.islocalStorageEnabled = true;
                }
                CollapsiblePanel.prototype.ngOnInit = function () {
                    if (this.lsName != null)
                        this.isOpen = (eval(localStorage.getItem(this.lsName)) === true);
                };
                CollapsiblePanel.prototype.toggle = function () {
                    this.isOpen = !this.isOpen;
                    if (this.lsName != null && this.islocalStorageEnabled === true)
                        localStorage.setItem(this.lsName, String(this.isOpen));
                };
                __decorate([
                    core_1.Input("lsName"),
                    __metadata("design:type", String)
                ], CollapsiblePanel.prototype, "lsName", void 0);
                __decorate([
                    core_1.Input("title"),
                    __metadata("design:type", String)
                ], CollapsiblePanel.prototype, "title", void 0);
                __decorate([
                    core_1.Input("isOpen"),
                    __metadata("design:type", Boolean)
                ], CollapsiblePanel.prototype, "isOpen", void 0);
                __decorate([
                    core_1.Input("islocalStorageEnabled"),
                    __metadata("design:type", Boolean)
                ], CollapsiblePanel.prototype, "islocalStorageEnabled", void 0);
                CollapsiblePanel = __decorate([
                    core_1.Component({
                        selector: "collapsiblePanel",
                        styles: ["\n  .hide {\n    display: none;\n  }\n  .panel {\n    background-color: rgba(150, 0, 0, 0.02) !important;\n  }\n  .panel-default>.panel-heading {\n    background-color: transparent !important;\n    font-size: 16px !important;\n  }"
                        ],
                        template: "\n    <div class=\"panel panel-default\" *ngIf=\"title\">\n      <div class=\"panel-heading\" (click)=\"toggle()\">\n        <div *ngIf=\"isOpen\" class=\"glyphicon glyphicon-chevron-down glyphicon-size cell\" title=\"{{'TR_COLLAPSE' | translate}}\"></div>\n        <div *ngIf=\"!isOpen\" class=\"glyphicon glyphicon-chevron-right glyphicon-size cell\" title=\"{{'TR_EXPAND' | translate}}\"></div>\n        {{title | translate}}\n      </div>\n      <div class=\"panel-body\" [ngClass]=\"{hide: !isOpen}\"><ng-content></ng-content></div>\n    </div>",
                    })
                ], CollapsiblePanel);
                return CollapsiblePanel;
            }());
            exports_1("CollapsiblePanel", CollapsiblePanel);
        }
    };
});
//# sourceMappingURL=collapsible-panel.js.map