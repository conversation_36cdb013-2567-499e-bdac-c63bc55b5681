/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

/**
 * Describes an editable object
 */
export interface EditorSpecificationObjectDTO {
    objectDataJson?: string;

    editorType?: string;

    children?: Array<models.EditorFieldObjectDTO>;

    editorKind?: EditorSpecificationObjectDTO.EditorKind;
}
export namespace EditorSpecificationObjectDTO {
  export enum EditorKind {
    CancelOk = <any>'CancelOk',
    AddItem = <any>'AddItem',
    CloseSave = <any>'CloseSave',
    Close = <any>'Close'
  }
}