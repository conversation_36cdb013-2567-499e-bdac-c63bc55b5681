﻿import { Component, Input, OnInit } from "@angular/core";

@Component({
  selector: "collapsiblePanel",
  styles: [`
  .hide {
    display: none;
  }
  .panel {
    background-color: rgba(150, 0, 0, 0.02) !important;
  }
  .panel-default>.panel-heading {
    background-color: transparent !important;
    font-size: 16px !important;
  }`
  ],
  template: `
    <div class="panel panel-default" *ngIf="title">
      <div class="panel-heading" (click)="toggle()">
        <div *ngIf="isOpen" class="glyphicon glyphicon-chevron-down glyphicon-size cell" title="{{'TR_COLLAPSE' | translate}}"></div>
        <div *ngIf="!isOpen" class="glyphicon glyphicon-chevron-right glyphicon-size cell" title="{{'TR_EXPAND' | translate}}"></div>
        {{title | translate}}
      </div>
      <div class="panel-body" [ngClass]="{hide: !isOpen}"><ng-content></ng-content></div>
    </div>`,
})

export class CollapsiblePanel {
  @Input("lsName") lsName: string;
  @Input("title") title: string;
  @Input("isOpen") isOpen: boolean;
  @Input("islocalStorageEnabled") islocalStorageEnabled: boolean = true;

  public ngOnInit(): void {
    if (this.lsName != null)
      this.isOpen = (eval(localStorage.getItem(this.lsName)) === true);
  }

  private toggle(): void {
    this.isOpen = !this.isOpen;
    if (this.lsName != null && this.islocalStorageEnabled === true)
      localStorage.setItem(this.lsName, String(this.isOpen));
  }
}
