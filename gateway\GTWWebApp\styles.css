﻿html {
  font-size: 12px;
}

body {
  overflow-y: auto;
  font-size: 12px;
}

body.modal-open {
  overflow-y: auto;
}

/* Scrollbar */
:root {
  scrollbar-color: #909090 #eee;
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #eee;
  box-shadow: inset 0 0 7px rgba(0,0,0,0.3);
}

::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  background-color: rgba(0, 0, 0, 0.5);
}
/* Scrollbar */

/* Menu bar*/
.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
  max-height: 490px;
}
/* Menu bar*/

/* Loader */
.center-loader {
  position: absolute;
  z-index: 9999;
  top: 50%;
  left: 50%;
  margin-top: -50px;
  margin-left: -50px;
  width: 100px;
  height: 100px;
}

.loader {
  border: 12px solid #34434e;
  z-index: 9999;
  border-top: 12px solid #c12f5a;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  animation: spin 1.5s linear infinite;
}

.logo-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -51px;
  margin-left: -51px;
  width: 122px;
  height: 122px;
}

.block-click {
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(180,180,180,0.4);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
/* Loader */

/* Button */
.btn {
  font-weight: bold;
  box-shadow: 2px 3px 1px 0px #676767;
  border-radius: 6px;
  position: relative;
  padding: 4px 8px;
}

.btn-default {
  background-image: radial-gradient(circle, rgba(256, 256, 256, 0.90), rgba(0, 0, 0, 0.05));
  color: #676400;
}

.btn-default:hover {
  color: #8a8600 !important;
}

.btn:focus {
  outline: none !important;
}

.btn:active, .btn-default.active {
  background: #bfbfbf !important;
}

.round-button {
  display: inline-block;
  min-height: 24px;
  max-height: 24px;
  min-width: 24px;
  max-width: 24px;
  padding-bottom: 1px;
  text-align: center;
  line-height: 20px;
  border: 1px solid gray;
  border-radius: 50%;
  background-color: #ebebeb;
  filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
  cursor: pointer;
}

.round-button:hover {
  background-color: #cdcdcd;
}
/* Button */
.scroll-panel {
  max-height:500px;
  overflow: auto;
}

.inline {
  display: inline-block;
}

.glyphicon-size {
  font-size: 14px;
  cursor: pointer;
  margin-top: -2px;
}

.help-icon {
  width: 20px;
  height: 20px;
  filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
  padding-right: 6px;
  vertical-align: top;
}

.module-icon {
  width: 26px;
  height: 26px;
  filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
  padding-right: 6px;
  vertical-align: top;
}

option[value=""][disabled] {
  display: none;
}

option {
  color: black;
}

.placeholder {
  color: gray !important;
  font-weight: bold;
}

.is-selected {
  background-image: linear-gradient(to right, rgba(220, 255, 220, 0.90), rgba(150, 210, 190, 0.30));
  font-weight: bold !important;
}

.active {
  color: rgba(192, 255, 192, 1) !important;
  font-weight: bold !important;
}
/* Active Menu/Treeview */

.container-fluid {
  padding-left: 0px;
  padding-right: 0px;
}

.panel-heading {
  font-weight: bold;
  font-size: 20px;
  padding: 6px;
  word-break: break-word;
}

.panel-heading-config-bg {
  background-image: linear-gradient(to right, rgba(176, 188, 192, 1), rgba(230,230,230, 0.80));
  padding: 2px;
  margin-bottom: -1px;
}

.panel-heading-config-dirty-bg {
  background-image: linear-gradient(to right, rgba(192, 176, 176, 1), rgba(230,230,230, 0.80));
  padding: 2px;
  margin-bottom: -1px;
}

.dashboard-panel .panel-heading {
  padding: 0px;
}

.navbar-nav > li > a {
  padding-top: 20px;
  padding-bottom: 10px;
}

.navbar-inverse .navbar-brand {
  color: #fff;
}

.table {
  margin-bottom: 0px;
}

.modal-message {
  padding: 6px;
}

.modalPosition1 {
  position: relative;
  top: 15px;
  left: 15px;
}

.modalPosition2 {
  position: relative;
  top: 30px;
  left: 30px;
}

.modalPosition3 {
  position: relative;
  top: 45px;
  left: 45px;
}

.modalPosition4 {
  position: relative;
  top: 60px;
  left: 60px;
}

.modalPosition5 {
  position: relative;
  top: 80px;
  left: 80px;
}

.shadow-bg {
  background-color: rgba(0, 0, 0, 0.04) !important
}

.success-msg-popup {
  background-color: green;
  color: lightgreen;
  display: inline-block;
}

.modal-content::before {
  content: "";
  opacity: 0.1;
  background-image: url(images/background_7.svg), linear-gradient(#000000, #c5c5c5);
  background-size: 30%;
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: -999;
}

.modal-content {
  background-image: linear-gradient(to bottom, rgba(218, 218, 218, 0.06), rgba(0, 0, 0, 0.13));
  z-index: -998;
  /*position: absolute;*/
  border: none;
  top: 140px;
}

.modal-heading {
  background-color: #d8d8d8;
  padding: 8px 10px 6px 10px;
  font-size: 22px;
  font-weight: bold;
  border-bottom: 1px solid #a31c3f;
  overflow-wrap: break-word;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.modal-body {
  padding: 12px 12px 2px 12px;
}

.modal-header {
  border-bottom: 1px solid #a31c3f !important;
  padding-top: 10px;
  padding-bottom: 0px;
}
}

.modal-footer {
  border-top: none !important;
  padding-top: 0px;
}

.modal.fade.show {
  opacity: 100 !important;
  transition: visibility 0s linear 0s, opacity 300ms
}

.modal-backdrop.fade {
  opacity: 0.35 !important;
}

.content-background {
  height: 100vh;
  width: 100vw;
  position: fixed;
  margin-top: -10px;
  left: 0px;
  z-index: -999;
  background-image: url(images/background_7.svg), linear-gradient(#c5c5c5, #000000);
  background-size: 100%;
  opacity: 0.1
}

.content-background-color {
  height: 100vh;
  width: 100vw;
  position: fixed;
  margin-top: -10px;
  z-index: -998;
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(170, 170, 170, 0));
}

.panel-pattern-bg {
  content: "";
  opacity: 0.1;
  background-image: url(images/background_7.svg), linear-gradient(#000000, #c5c5c5);
  background-size: 20%;
  position: absolute;
  height: 100%;
  width: 100%;
}

.panel-default > .panel-heading {
  color: #333;
  background-color: #d8d8d8;
  border-bottom: #a31c3f 2px solid;
}

.panel-body {
  background-image: linear-gradient(to bottom, rgba(165, 165, 165, 0.06), rgba(0, 0, 0, 0.13));
  padding: 15px;
  height: 100%;
  width: 100%;
  border-radius: 4px;
}

.panel-main {
  box-shadow: 0 5px 15px rgba(0,0,0,.5);
}

.timestamp {
  font-size: 8px;
  position: absolute;
  bottom: 22px;
  right: 22px;
}

.image {
  width: 20px;
  height: 20px;
  filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
  padding-right: 2px;
}

.image-button {
  width: 16px;
  height: 16px;
  filter: drop-shadow(1px 2px 1px #404040);
  margin-top: -2px;
}

.blinking-red {
  animation: blinking-red-background 3s infinite;
}

.grid-no-results {
  text-align: center;
  margin: 10px;
  font-size: 18px;
  font-weight: bold;
}

@keyframes blinking-red-background {
  0% {
    background-color: #ff3300;
  }

  25% {
    background-color: transparent;
  }

  75% {
    background-color: transparent;
  }

  100% {
    background-color: #ff3300;
  }
}
