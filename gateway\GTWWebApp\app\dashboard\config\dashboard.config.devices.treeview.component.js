System.register(["@angular/core", "../../data/model/models", "../../data/api/api", "../../modules/alert/alert.service", "../../authentication/authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, models_1, api_1, alert_service_1, authentication_service_1, DashboardConfigDevicesTreeViewComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            DashboardConfigDevicesTreeViewComponent = (function () {
                function DashboardConfigDevicesTreeViewComponent(alertService, nodesService, authenticationService) {
                    this.alertService = alertService;
                    this.nodesService = nodesService;
                    this.authenticationService = authenticationService;
                    this.onSelectedChanged = new core_1.EventEmitter();
                    this.onDropNode = new core_1.EventEmitter();
                    this.onToggleNode = new core_1.EventEmitter();
                }
                DashboardConfigDevicesTreeViewComponent.prototype.onSelectNode = function (node) {
                    this.onSelectedChanged.emit(node);
                };
                DashboardConfigDevicesTreeViewComponent.prototype.toggleNode = function (node) {
                    if (!node.isExpanded && this.deviceNode.hasChildren && this.deviceNode.nodeClassName != "GatewayRootNode") {
                        this.loadTagsTreeview();
                    }
                    node.isExpanded = !node.isExpanded;
                };
                DashboardConfigDevicesTreeViewComponent.prototype.onToggleNodeRecursive = function (node) {
                    this.onToggleNode.emit(node);
                };
                DashboardConfigDevicesTreeViewComponent.prototype.onDrop = function (dragData) {
                    if (this.deviceNode.isMappingTarget) {
                        var dropData = [dragData, this.deviceNode];
                        this.onDropNode.emit(dropData);
                    }
                };
                DashboardConfigDevicesTreeViewComponent.prototype.onDragleave = function (event) {
                    if (event && event.currentTarget)
                        event.currentTarget.style.background = "";
                };
                DashboardConfigDevicesTreeViewComponent.prototype.onDragover = function (event) {
                    if (this.deviceNode.isMappingTarget) {
                        if (event && event.currentTarget)
                            event.currentTarget.style.background = "rgba(251, 253, 180, 0.95)";
                        event.preventDefault();
                    }
                };
                DashboardConfigDevicesTreeViewComponent.prototype.onDropNodeRecursive = function (dropData) {
                    this.onDropNode.emit(dropData);
                };
                DashboardConfigDevicesTreeViewComponent.prototype.loadTagsTreeview = function () {
                    var _this = this;
                    try {
                        this.nodesService.getNodes(this.deviceNode.nodeFullName, null, false, true, this.deviceNode.nodeCollectionKind.toString()).subscribe(function (data) {
                            _this.deviceNode.children = data.children;
                            _this.onToggleNodeRecursive(_this.deviceNode);
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                            }
                        });
                    }
                    catch (err) {
                        return null;
                    }
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "deviceNode", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "deviceRootNode", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "selectedNode", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "isDevicesTreeViewSelected", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "isNodeTreeFromSearch", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "onSelectedChanged", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "onDropNode", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDevicesTreeViewComponent.prototype, "onToggleNode", void 0);
                DashboardConfigDevicesTreeViewComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigDevicesTreeViewComponent",
                        styles: ["\n\t\t  .icon-button {\n        font-size: 8px;\n\t\t\t  display: inline-block; \n\t\t\t  margin-right: 5px;\n\t\t\t  cursor: pointer;\n\t\t\t  color:#fffaaa;\n        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n\t\t  }\n\t\t  ul {\n\t\t\t  padding-left: 6px;\n\t\t\t  list-style-type: none;\n\t\t  }\n\t\t  li{\n\t\t\t  margin-top: 4px;\n\t\t  }\n\t\t  ol, ul {\n\t\t\t  margin-top: 0;\n\t\t  }\n      .leaf{\n        white-space: nowrap;\n      }\n      .leaf:hover{\n        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n      }\n      .leaf-text{\n        cursor: pointer;\n        display: inline-block;\n        width: 90%;\n      }\n      .glyphicon-none {\n        padding-right: 8px;  \n        color: transparent !important;\n      }\n      .node-text{\n        cursor: pointer;\n        color:black;\n        text-decoration:none;\n        vertical-align: bottom;\n      }\n      .search-text {\n        cursor: pointer;\n        color: #337ab7;\n        font-style: italic;\n        text-decoration:none;\n        vertical-align: bottom;\n      }"
                        ],
                        template: "<ul>\n\t\t\t\t\t\t\t\t<li>\n                  <div class=\"leaf\" [class.is-selected]=\"selectedNode === deviceNode && isDevicesTreeViewSelected\" [dashboardConfigContextMenuDirective]=\"deviceNode?.nodeFullName\" [node]=deviceNode [rootNode]=deviceRootNode>\n\t\t\t\t\t\t\t\t\t\t  <div *ngIf=\"deviceNode?.hasChildren == false\" style=\"cursor:not-allowed;\" class=\"glyphicon-none icon-button\"></div>\n\t\t\t\t\t\t\t\t\t\t  <div *ngIf=\"deviceNode?.hasChildren == true && deviceNode?.isExpanded\" class=\"glyphicon glyphicon-triangle-bottom icon-button\" (click)=\"toggleNode(deviceNode)\" title=\"{{TR_COLLAPSE_CHILDREN | translate}}\"></div>\n\t\t\t\t\t\t\t\t\t\t  <div *ngIf=\"deviceNode?.hasChildren == true && !deviceNode?.isExpanded\" class=\"glyphicon glyphicon-triangle-right icon-button\" (click)=\"toggleNode(deviceNode)\" title=\"{{TR_EXPAND_CHILDREN | translate}}\"></div>\n                      <div (click)=\"onSelectNode(deviceNode)\" class=\"leaf-text\" myDropTarget (myDrop)=\"onDrop($event)\" (dragover)=\"onDragover($event)\"(dragleave)=\"onDragleave($event)\" >\n                        <img [src]=\"'../../images/gtw_objects/' + (deviceNode?.nodeIcon | gtwImage)\" [ngClass]=\"{'blinking-red' : (!deviceNode?.isHealthy && deviceRootNode?.isWarningActive)}\" class=\"image\"/>\n\t\t\t\t\t\t\t\t\t      <a  [tooltip]=\"[deviceNode?.nodeDescription]\" [className]=\"isNodeTreeFromSearch ? 'search-text' : 'node-text'\">{{deviceNode?.nodeLabel}}</a>\n                      </div>\n                  </div>\n\t\t\t\t\t\t\t\t\t<ng-container *ngFor=\"let childrenDeviceNode of deviceNode?.children\">\n\t\t\t\t\t\t\t\t\t\t<dashboardConfigDevicesTreeViewComponent *ngIf=\"deviceNode?.isExpanded\" [(selectedNode)]=\"selectedNode\" (onSelectedChanged)=\"onSelectNode($event)\" [isDevicesTreeViewSelected]=\"isDevicesTreeViewSelected\" [deviceNode]=\"childrenDeviceNode\" [deviceRootNode]=\"deviceRootNode\" [isNodeTreeFromSearch]=\"isNodeTreeFromSearch\" (onToggleNode)=\"onToggleNodeRecursive($event)\" (onDropNode)=\"onDropNodeRecursive($event)\"></dashboardConfigDevicesTreeViewComponent>\n\t\t\t\t\t\t\t\t\t</ng-container>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t</ul>"
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, api_1.NodesService, authentication_service_1.AuthenticationService])
                ], DashboardConfigDevicesTreeViewComponent);
                return DashboardConfigDevicesTreeViewComponent;
            }());
            exports_1("DashboardConfigDevicesTreeViewComponent", DashboardConfigDevicesTreeViewComponent);
        }
    };
});
//# sourceMappingURL=dashboard.config.devices.treeview.component.js.map