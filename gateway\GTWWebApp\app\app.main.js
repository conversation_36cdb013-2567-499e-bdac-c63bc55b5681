System.register(["@angular/core", "@angular/platform-browser-dynamic", "./app.module"], function (exports_1, context_1) {
    "use strict";
    var core_1, platform_browser_dynamic_1, app_module_1, platform;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (platform_browser_dynamic_1_1) {
                platform_browser_dynamic_1 = platform_browser_dynamic_1_1;
            },
            function (app_module_1_1) {
                app_module_1 = app_module_1_1;
            }
        ],
        execute: function () {
            core_1.enableProdMode();
            platform = platform_browser_dynamic_1.platformBrowserDynamic();
            platform.bootstrapModule(app_module_1.AppModule);
        }
    };
});
//# sourceMappingURL=app.main.js.map