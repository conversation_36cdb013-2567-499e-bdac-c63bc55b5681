{"version": 3, "file": "splitArea.directive.js", "sourceRoot": "", "sources": ["splitArea.directive.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;gBAyDI,4BAAoB,UAAsB,EAC9B,QAAmB,EACnB,KAAqB;oBAFb,eAAU,GAAV,UAAU,CAAY;oBAC9B,aAAQ,GAAR,QAAQ,CAAW;oBACnB,UAAK,GAAL,KAAK,CAAgB;oBAzCzB,WAAM,GAAkB,IAAI,CAAC;oBAM7B,UAAK,GAAkB,IAAI,CAAC;oBAM5B,kBAAa,GAAW,CAAC,CAAC;oBAM1B,aAAQ,GAAY,IAAI,CAAC;oBAiBjC,eAAU,GAAW,OAAO,CAAC;oBAE7B,kBAAa,GAAoB,EAAE,CAAC;gBAIA,CAAC;gBAxC5B,sBAAI,qCAAK;yBAAT,UAAU,CAAS;wBACxB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBACnC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7E,CAAC;;;mBAAA;gBAGQ,sBAAI,oCAAI;yBAAR,UAAS,CAAM;wBACpB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAClC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7E,CAAC;;;mBAAA;gBAGQ,sBAAI,4CAAY;yBAAhB,UAAiB,CAAS;wBAC/B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7E,CAAC;;;mBAAA;gBAGQ,sBAAI,uCAAO;yBAYpB;wBACI,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACzB,CAAC;yBAdQ,UAAY,CAAU;wBAC3B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;wBACvC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;wBAElB,IAAG,IAAI,CAAC,OAAO,EAAE;4BACb,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;yBAC7B;6BACI;4BACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;yBAC7B;oBACL,CAAC;;;mBAAA;gBAcM,qCAAQ,GAAf;oBACI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC1E,CAAC;gBAEM,uCAAU,GAAjB;oBACI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,EAAE,UAAA,CAAC,IAAI,OAAA,KAAK,EAAL,CAAK,CAAC,CAAE,CAAC;oBAC1G,IAAI,CAAC,aAAa,CAAC,IAAI,CAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,EAAE,UAAA,CAAC,IAAI,OAAA,KAAK,EAAL,CAAK,CAAC,CAAE,CAAC;gBAC5G,CAAC;gBAEM,yCAAY,GAAnB;oBACI,OAAM,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;wBACjC,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;wBACrC,IAAG,GAAG,EAAE;4BACJ,GAAG,EAAE,CAAC;yBACT;qBACJ;gBACL,CAAC;gBAEM,qCAAQ,GAAf,UAAgB,GAAW,EAAE,KAAU;oBACnC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtE,CAAC;gBAEM,wCAAW,GAAlB;oBACI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAChC,CAAC;gBAEM,4CAAe,GAAtB,UAAuB,GAAoB;oBAEvC,IAAG,GAAG,CAAC,YAAY,KAAK,YAAY;wBAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;gBAClD,CAAC;gBAxEQ;oBAAR,YAAK,EAAE;;;+DAGP;gBAGQ;oBAAR,YAAK,EAAE;;;8DAGP;gBAGQ;oBAAR,YAAK,EAAE;;;sEAGP;gBAGQ;oBAAR,YAAK,EAAE;;;iEAUP;gBA/BQ,kBAAkB;oBAZ9B,gBAAS,CAAC;wBACP,QAAQ,EAAE,YAAY;wBACtB,IAAI,EAAE;4BACF,mBAAmB,EAAE,KAAK;4BAC1B,qBAAqB,EAAE,KAAK;4BAC5B,oBAAoB,EAAE,QAAQ;4BAC9B,oBAAoB,EAAE,QAAQ;4BAC9B,gBAAgB,EAAE,QAAQ;4BAC1B,eAAe,EAAE,UAAU;4BAC3B,iBAAiB,EAAE,yBAAyB;yBAC/C;qBACJ,CAAC;qDA0CkC,iBAAU;wBACpB,gBAAS;wBACZ,gCAAc;mBA3CxB,kBAAkB,CA4E9B;gBAAD,yBAAC;aAAA,AA5ED;;QA6EA,CAAC"}