System.register([], function (exports_1, context_1) {
    "use strict";
    var GlobalObjectToCSVUtil;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            GlobalObjectToCSVUtil = (function () {
                function GlobalObjectToCSVUtil() {
                }
                GlobalObjectToCSVUtil.copyToCSV = function (filename, data) {
                    var csvData = this.arrayObjToCSV(data);
                    var blob = new Blob([csvData], { type: 'text/csv' });
                    var url = window.URL.createObjectURL(blob);
                    if (navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, filename);
                    }
                    else {
                        var a = document.createElement("a");
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    }
                    window.URL.revokeObjectURL(url);
                };
                GlobalObjectToCSVUtil.isNotEmpty = function (obj) {
                    return obj != null && Object.keys(obj).length != 0;
                };
                GlobalObjectToCSVUtil.arrayObjToCSV = function (data) {
                    var columnDelimiter = ",";
                    var lineDelimiter = "\n";
                    var result;
                    var ctr;
                    var keys = [];
                    if (data === null || !data.length)
                        return "";
                    keys = Object.keys(data[0]);
                    for (var i = 0; i < keys.length; i++) {
                        keys[i] = keys[i].replace(/,/g, ' ');
                    }
                    result = "";
                    result += keys.join(columnDelimiter);
                    result += lineDelimiter;
                    data.forEach(function (item) {
                        ctr = 0;
                        keys.forEach(function (key) {
                            if (ctr > 0)
                                result += columnDelimiter;
                            if (typeof item[key] === "string" && item[key].includes(lineDelimiter))
                                item[key] = item[key].replace(/[\n]/g, ' ');
                            result += (typeof item[key] === "string" && item[key].includes(columnDelimiter)) ? item[key].replace(/,/g, ' ') : item[key];
                            ctr++;
                        });
                        result += lineDelimiter;
                    });
                    return result;
                };
                return GlobalObjectToCSVUtil;
            }());
            exports_1("GlobalObjectToCSVUtil", GlobalObjectToCSVUtil);
        }
    };
});
//# sourceMappingURL=global.objectToCSV.util.js.map