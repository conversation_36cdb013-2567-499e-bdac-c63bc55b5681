System.register(["@angular/core", "@angular/common/http", "../encoder", "../variables", "../configuration"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, http_1, encoder_1, variables_1, configuration_1, ConfigService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (encoder_1_1) {
                encoder_1 = encoder_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            }
        ],
        execute: function () {
            ConfigService = (function () {
                function ConfigService(httpClient, basePath, configuration) {
                    this.httpClient = httpClient;
                    this.basePath = 'http://localhost/rest';
                    this.defaultHeaders = new http_1.HttpHeaders();
                    this.configuration = new configuration_1.Configuration();
                    if (basePath) {
                        this.basePath = basePath;
                    }
                    if (configuration) {
                        this.configuration = configuration;
                        this.basePath = basePath || configuration.basePath || this.basePath;
                    }
                }
                ConfigService.prototype.canConsumeForm = function (consumes) {
                    var form = 'multipart/form-data';
                    for (var _i = 0, consumes_1 = consumes; _i < consumes_1.length; _i++) {
                        var consume = consumes_1[_i];
                        if (form === consume) {
                            return true;
                        }
                    }
                    return false;
                };
                ConfigService.prototype.getConfig = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/get_config", {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.getLicense = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    return this.httpClient.get(this.basePath + "/get_license", {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.getSdgStatus = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/get_sdg_status", {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.newWorkSpace = function (workSpaceName, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (workSpaceName !== undefined) {
                        formParams = formParams.append('workSpaceName', workSpaceName) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/newWorkSpace", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.processBroadcastEvent = function (body, productKey, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (body === null || body === undefined) {
                        throw new Error('Required parameter body was null or undefined when calling processBroadcastEvent.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (productKey !== undefined && productKey !== null) {
                        queryParameters = queryParameters.set('product_key', productKey);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    var httpContentTypeSelected = this.configuration.selectHeaderContentType(consumes);
                    if (httpContentTypeSelected != undefined) {
                        headers = headers.set('Content-Type', httpContentTypeSelected);
                    }
                    return this.httpClient.post(this.basePath + "/process_broadcast_event", body, {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.saveLicense = function (actionType, isNewLicense, productKey, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (actionType === null || actionType === undefined) {
                        throw new Error('Required parameter actionType was null or undefined when calling saveLicense.');
                    }
                    if (isNewLicense === null || isNewLicense === undefined) {
                        throw new Error('Required parameter isNewLicense was null or undefined when calling saveLicense.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (actionType !== undefined && actionType !== null) {
                        queryParameters = queryParameters.set('action_type', actionType);
                    }
                    if (productKey !== undefined && productKey !== null) {
                        queryParameters = queryParameters.set('product_key', productKey);
                    }
                    if (isNewLicense !== undefined && isNewLicense !== null) {
                        queryParameters = queryParameters.set('is_new_license', isNewLicense);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    return this.httpClient.get(this.basePath + "/save_license", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.sendV2CLicense = function (file, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (file === null || file === undefined) {
                        throw new Error('Required parameter file was null or undefined when calling sendV2CLicense.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'multipart/form-data'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    useForm = canConsumeForm;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (file !== undefined) {
                        formParams = formParams.append('file', file) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/sendV2CLicense", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.selectWorkSpace = function (workSpaceName, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (workSpaceName !== undefined) {
                        formParams = formParams.append('workSpaceName', workSpaceName) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/selectWorkSpace", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.setConfigJson = function (body, restartFlag, validateFlag, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (body === null || body === undefined) {
                        throw new Error('Required parameter body was null or undefined when calling setConfigJson.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (restartFlag !== undefined && restartFlag !== null) {
                        queryParameters = queryParameters.set('restartFlag', restartFlag);
                    }
                    if (validateFlag !== undefined && validateFlag !== null) {
                        queryParameters = queryParameters.set('validateFlag', validateFlag);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    var httpContentTypeSelected = this.configuration.selectHeaderContentType(consumes);
                    if (httpContentTypeSelected != undefined) {
                        headers = headers.set('Content-Type', httpContentTypeSelected);
                    }
                    return this.httpClient.post(this.basePath + "/set_config_json", body, {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.startEngine = function (arg, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (arg !== undefined) {
                        formParams = formParams.append('arg', arg) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/startEngine", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.stopEngine = function (observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    return this.httpClient.post(this.basePath + "/stopEngine", null, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService.prototype.stopMon = function (bothFlag, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (bothFlag !== undefined && bothFlag !== null) {
                        queryParameters = queryParameters.set('bothFlag', bothFlag);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    return this.httpClient.post(this.basePath + "/stopMon", null, {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                ConfigService = __decorate([
                    core_1.Injectable(),
                    __param(1, core_1.Optional()),
                    __param(1, core_1.Inject(variables_1.BASE_PATH)),
                    __param(2, core_1.Optional()),
                    __metadata("design:paramtypes", [http_1.HttpClient, String, configuration_1.Configuration])
                ], ConfigService);
                return ConfigService;
            }());
            exports_1("ConfigService", ConfigService);
        }
    };
});
//# sourceMappingURL=config.service.js.map