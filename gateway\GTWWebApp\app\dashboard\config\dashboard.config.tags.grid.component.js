System.register(["@angular/core", "@angular/router", "../../data/api/api", "../../data/wsApi/wsApi", "../../data/model/models", "./dashboard.config.component", "../../modules/alert/alert.service", "../../authentication/authentication.service", "../../modules/grid/grid.pagination.component", "./dashboard.config.grid.component", "ngx-modialog-7/plugins/bootstrap", "@ngx-translate/core", "../../global/global.data.service", "./dashboard.config.tags.grid.search.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, router_1, api_1, wsApi_1, models_1, dashboard_config_component_1, alert_service_1, authentication_service_1, grid_pagination_component_1, dashboard_config_grid_component_1, bootstrap_1, core_2, global_data_service_1, dashboard_config_tags_grid_search_component_1, DashboardConfigTagsGridComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (dashboard_config_component_1_1) {
                dashboard_config_component_1 = dashboard_config_component_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (grid_pagination_component_1_1) {
                grid_pagination_component_1 = grid_pagination_component_1_1;
            },
            function (dashboard_config_grid_component_1_1) {
                dashboard_config_grid_component_1 = dashboard_config_grid_component_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            },
            function (dashboard_config_tags_grid_search_component_1_1) {
                dashboard_config_tags_grid_search_component_1 = dashboard_config_tags_grid_search_component_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagsGridComponent = (function () {
                function DashboardConfigTagsGridComponent(modal, tagsService, editorsService, nodesApi, mappingsApi, tagsWSApi, alertService, authenticationService, broadcastEventWSApi, translate, globalDataService, dashboardConfigComponent, router) {
                    this.modal = modal;
                    this.tagsService = tagsService;
                    this.editorsService = editorsService;
                    this.nodesApi = nodesApi;
                    this.mappingsApi = mappingsApi;
                    this.tagsWSApi = tagsWSApi;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.broadcastEventWSApi = broadcastEventWSApi;
                    this.translate = translate;
                    this.globalDataService = globalDataService;
                    this.dashboardConfigComponent = dashboardConfigComponent;
                    this.router = router;
                    this.tagCount = 0;
                    this.currentPage = null;
                    this.itemPerPage = 0;
                    this.pageEdgePairs = [];
                    this.tagsWSRconnectAttempts = 0;
                    this.broadcastWSReconnectAttempts = 0;
                    this.columns = this.getColumns();
                }
                DashboardConfigTagsGridComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    this.itemPerPage = this.globalDataService.SDGConfig.gtwHttpPageBlockSize;
                    this.currentPage = new grid_pagination_component_1.Page(1, this.itemPerPage, "", "");
                    this.tagsWSApi.openWebsocket().subscribe(function (data) {
                        _this.tagsWebsocket = data;
                        _this.wsGetTagsData();
                    }, function (error) { _this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getTags", reason: "Open fail" }); });
                    this.openGlobalBroadcastServiceSubscription();
                    var searchFields = [];
                    searchFields.push(new dashboard_config_tags_grid_search_component_1.SearchField("tagName", "TR_NAME", "", models_1.EditorFieldObjectDTO.ControlTypeEnum.Text, null, "TR_TAG_NAME_SEARCH_HELP"));
                    searchFields.push(new dashboard_config_tags_grid_search_component_1.SearchField("tagUserName", "TR_TAG_USER_NAME", "", models_1.EditorFieldObjectDTO.ControlTypeEnum.Text, null, "TR_TAG_NAME_SEARCH_HELP"));
                    searchFields.push(new dashboard_config_tags_grid_search_component_1.SearchField("tagValueType", "TR_TYPE", "", models_1.EditorFieldObjectDTO.ControlTypeEnum.Combobox, [
                        { "value": "", "text": "" },
                        { "value": "Bool", "text": "Bool" },
                        { "value": "Char", "text": "Char" },
                        { "value": "Double", "text": "Double" },
                        { "value": "Int 64", "text": "Int 64" },
                        { "value": "Long", "text": "Long" },
                        { "value": "Float", "text": "Float" },
                        { "value": "Short", "text": "Short" },
                        { "value": "String", "text": "String" },
                        { "value": "Time", "text": "Time" },
                        { "value": "Unsigned Char", "text": "Unsigned Char" },
                        { "value": "Unsigned Int 64", "text": "Unsigned Int 64" },
                        { "value": "Unsigned Int", "text": "Unsigned Int" },
                        { "value": "Unsigned Short", "text": "Unsigned Short" }
                    ]));
                    searchFields.push(new dashboard_config_tags_grid_search_component_1.SearchField("tagDescription", "TR_TAG_DESCRIPTION", "", models_1.EditorFieldObjectDTO.ControlTypeEnum.Text, null, "TR_TAG_NAME_SEARCH_HELP"));
                    this.searchCriteria = new dashboard_config_tags_grid_search_component_1.SearchCriteria(searchFields, dashboard_config_tags_grid_search_component_1.SearchTypeEnum.Clear);
                };
                DashboardConfigTagsGridComponent.prototype.ngOnDestroy = function () {
                    if (this.tagsWebsocket != null && this.tagsWebsocket.readyState === WebSocket.OPEN)
                        this.tagsWebsocket.close(3000, "panelClose");
                    if (this.tagServiceSubscription != null)
                        this.tagServiceSubscription.unsubscribe();
                    if (this.globalBroadcastServiceSubscription != null)
                        this.globalBroadcastServiceSubscription.unsubscribe();
                };
                DashboardConfigTagsGridComponent.prototype.ngOnChanges = function (changes) {
                    if (this.selectedNode) {
                        this.itemPerPage = this.globalDataService.SDGConfig.gtwHttpPageBlockSize;
                        this.currentPage = new grid_pagination_component_1.Page(1, this.itemPerPage, "", "");
                        this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
                    }
                };
                DashboardConfigTagsGridComponent.prototype.openGlobalBroadcastServiceSubscription = function () {
                    var _this = this;
                    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(function (event) {
                        if (event.type === 'message') {
                            var broadcastEventData = JSON.parse(event.data);
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.RefreshUI.toString() && broadcastEventData.parameters != null && broadcastEventData.parameters.refreshWebBrowser == null) {
                                var objectName = null;
                                var objectCollectionKind = null;
                                if (broadcastEventData.parameters != null && broadcastEventData.parameters.objectName != null && broadcastEventData.parameters.objectCollectionKind != null) {
                                    _this.loadTagsGrid(broadcastEventData.parameters.objectName, broadcastEventData.parameters.objectCollectionKind);
                                    return;
                                }
                                if (_this.selectedNode) {
                                    _this.loadTagsGrid(_this.selectedNode.nodeFullName, _this.selectedNode.nodeCollectionKind.toString());
                                }
                            }
                        }
                    });
                };
                DashboardConfigTagsGridComponent.prototype.loadTagsGrid = function (nodeFullName, nodeCollectionKind, tagNameFilter, tagDescriptionFilter, tagUserNameFilter, tagTypeFilter) {
                    var _this = this;
                    if (tagNameFilter === void 0) { tagNameFilter = null; }
                    if (tagDescriptionFilter === void 0) { tagDescriptionFilter = null; }
                    if (tagUserNameFilter === void 0) { tagUserNameFilter = null; }
                    if (tagTypeFilter === void 0) { tagTypeFilter = null; }
                    tagNameFilter = this.searchIsActive ? tagNameFilter : null;
                    tagDescriptionFilter = this.searchIsActive ? tagDescriptionFilter : null;
                    tagUserNameFilter = this.searchIsActive ? tagUserNameFilter : null;
                    tagTypeFilter = this.searchIsActive ? tagTypeFilter : null;
                    if (this.tagPurposeFilter == models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY) {
                        this.tagsService.getTags("", this.sortColumn, this.sortColumnDirection, tagTypeFilter, tagNameFilter, tagDescriptionFilter, tagUserNameFilter, nodeCollectionKind, this.tagPurposeFilter, true, 0, 0).subscribe(function (data) { return _this.tags = _this.combineMapping(data); }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                            }
                        }, function () {
                            _this.onDoSubscribeComplete();
                        });
                        this.tagCount = 0;
                    }
                    else {
                        this.nodesApi.getNodePageInfo(nodeFullName, this.sortColumn, this.sortColumnDirection, tagTypeFilter, tagNameFilter, tagDescriptionFilter, tagUserNameFilter, (this.searchIsActive && this.searchCriteria.searchType == dashboard_config_tags_grid_search_component_1.SearchTypeEnum.DeepSearch ? true : this.isRecursive), this.tagPurposeFilter).subscribe(function (dataPage) {
                            _this.pageEdgePairs = dataPage.edgePairs;
                            _this.tagCount = dataPage.numTags;
                            _this.tagsService.getTags(nodeFullName, _this.sortColumn, _this.sortColumnDirection, tagTypeFilter, tagNameFilter, tagDescriptionFilter, tagUserNameFilter, nodeCollectionKind, _this.tagPurposeFilter, (_this.searchIsActive && _this.searchCriteria.searchType == dashboard_config_tags_grid_search_component_1.SearchTypeEnum.DeepSearch ? true : _this.isRecursive), _this.currentPage.start, _this.currentPage.end).subscribe(function (data) { return _this.tags = _this.combineMapping(data); }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                                }
                            }, function () {
                                _this.onDoSubscribeComplete();
                            });
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                            }
                        });
                    }
                };
                DashboardConfigTagsGridComponent.prototype.combineMapping = function (dataTags) {
                    var _this = this;
                    var tagMappings = [];
                    if (dataTags && dataTags.length > 0) {
                        dataTags.forEach(function (dataTag) {
                            var tagMapping = new Object();
                            tagMapping.tagClassName = dataTag.tagClassName;
                            tagMapping.tagObjectIcon = dataTag.tagObjectIcon;
                            tagMapping.tagName = dataTag.tagName;
                            tagMapping.tagUserName = dataTag.tagUserName;
                            tagMapping.tagValue = dataTag.tagValue;
                            tagMapping.tagQuality = dataTag.tagQuality;
                            tagMapping.tagTime = dataTag.tagTime;
                            tagMapping.tagOptions = dataTag.tagOptions;
                            if (dataTag.tagClassName == "GTWTASE2DataAttributeMDO" || dataTag.tagClassName == "GTWTase2SlaveDataObject")
                                tagMapping.tagValueType = dataTag.tagValueType;
                            else
                                tagMapping.tagValueType = dataTag.tagValueType + " (" + models_1.TagObjectDTO.getTagPropertyMaskStringValue(dataTag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.VALUE_TYPE) + ")";
                            tagMapping.tagDescription = dataTag.tagDescription;
                            tagMapping.tagPropertyMask = dataTag.tagPropertyMask;
                            tagMapping.tagCanChangeValue = models_1.TagObjectDTO.getTagPropertyMaskValue(dataTag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.CAN_CHANGE_VALUE);
                            tagMapping.tagCanEdit = models_1.TagObjectDTO.getTagPropertyMaskValue(dataTag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.CAN_EDIT);
                            tagMapping.tagCanDelete = models_1.TagObjectDTO.getTagPropertyMaskValue(dataTag.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.CAN_DELETE);
                            tagMapping.isHealthy = dataTag.isHealthy;
                            tagMapping.tag = dataTag;
                            tagMapping.tagMappingList = _this.addMappingToTag(dataTag);
                            tagMappings.push(tagMapping);
                        });
                    }
                    return tagMappings;
                };
                DashboardConfigTagsGridComponent.prototype.addMappingToTag = function (dataTag) {
                    var tagMappingList = [];
                    if (dataTag.tagBindingList) {
                        dataTag.tagBindingList.forEach(function (tagBinding) {
                            if (tagBinding.direction === models_1.BoundObjectDTO.DirectionEnum.RIGHT)
                                tagMappingList.push(new dashboard_config_grid_component_1.Cell("arrowRight", { leftTag: dataTag.tagName, rightTag: tagBinding.fullName }, tagBinding.canDelete ? "action$deleteMapping;action$lookupMapping" : "action$lookupMapping"));
                            else if (tagBinding.direction === models_1.BoundObjectDTO.DirectionEnum.LEFT)
                                tagMappingList.push(new dashboard_config_grid_component_1.Cell("arrowLeft", { leftTag: dataTag.tagName, rightTag: tagBinding.fullName }, tagBinding.canDelete ? "action$deleteMapping;action$lookupMapping" : "action$lookupMapping"));
                            else
                                tagMappingList.push(new dashboard_config_grid_component_1.Cell("arrowLeftRight", { leftTag: dataTag.tagName, rightTag: tagBinding.fullName }, tagBinding.canDelete ? "action$deleteMapping;action$lookupMapping" : "action$lookupMapping"));
                        });
                    }
                    return tagMappingList;
                };
                DashboardConfigTagsGridComponent.prototype.onDoSubscribeComplete = function () {
                    if (this.tagsWebsocket != null && this.tagsWebsocket.readyState === WebSocket.OPEN) {
                        var wsTagsName = [];
                        if (this.tagPurposeFilter == models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY) {
                            wsTagsName.push({ "nodeFullName": "menu.warning" });
                            var jsonWSTagsNameList = JSON.stringify(wsTagsName);
                            this.tagsWebsocket.send(jsonWSTagsNameList);
                            this.wsGetTagsDataWarning();
                        }
                        else if (this.tags == null) {
                            return;
                        }
                        else {
                            if (this.selectedNode != null && this.selectedNode.nodeFullName != "")
                                wsTagsName.push({ "nodeFullName": this.selectedNode.nodeFullName });
                            for (var x in this.tags)
                                wsTagsName.push({ "tagName": this.tags[x].tagName });
                            var jsonWSTagsNameList = JSON.stringify(wsTagsName);
                            this.tagsWebsocket.send(jsonWSTagsNameList);
                            this.wsGetTagsData();
                        }
                    }
                };
                DashboardConfigTagsGridComponent.prototype.wsGetTagsDataWarning = function () {
                    var _this = this;
                    this.tagServiceSubscription = this.tagsWSApi.getTagsData(this.tagsWebsocket).subscribe(function (event) {
                        if (event.type === 'message') {
                            _this.tagsWSRconnectAttempts = 0;
                            var wsTags = JSON.parse(event.data);
                            for (var x in wsTags) {
                                var isTagNotFound = true;
                                for (var i in _this.tags) {
                                    if (_this.tags[i].tagName == wsTags[x].tagName && wsTags[x].isHealthy) {
                                        var index = _this.tags.indexOf(_this.tags[i], 0);
                                        if (index > -1) {
                                            _this.tags.splice(index, 1);
                                            isTagNotFound = false;
                                            break;
                                        }
                                    }
                                    else if (_this.tags[i].tagName == wsTags[x].tagName && !wsTags[x].isHealthy) {
                                        _this.tags[i].tagValue = wsTags[x].tagValue;
                                        _this.tags[i].tagQuality = wsTags[x].tagQuality;
                                        _this.tags[i].tagTime = wsTags[x].tagTime;
                                        _this.tags[i].tagOptions = wsTags[x].tagOptions;
                                        _this.tags[i].tagPropertyMask = wsTags[x].tagPropertyMask;
                                        _this.tags[i].tagDescription = wsTags[x].tagDescription;
                                        _this.tags[i].isHealthy = wsTags[x].isHealthy;
                                        _this.tags[i].tagMappingList = _this.addMappingToTag(wsTags[x]);
                                        isTagNotFound = false;
                                        break;
                                    }
                                }
                                if (isTagNotFound && !wsTags[x].isHealthy) {
                                    _this.tags.push(wsTags[x]);
                                }
                            }
                        }
                        else if (event.type === 'close') {
                            _this.reconnectTagsWebsocket();
                        }
                    });
                };
                DashboardConfigTagsGridComponent.prototype.wsGetTagsData = function () {
                    var _this = this;
                    this.tagServiceSubscription = this.tagsWSApi.getTagsData(this.tagsWebsocket).subscribe(function (event) {
                        if (event.type === 'message') {
                            _this.tagsWSRconnectAttempts = 0;
                            var wsTags = JSON.parse(event.data);
                            for (var x in wsTags) {
                                if (_this.tags == null)
                                    return;
                                for (var i in _this.tags) {
                                    if (_this.tags[i].tagName == wsTags[x].tagName) {
                                        _this.tags[i].tagValue = wsTags[x].tagValue;
                                        _this.tags[i].tagQuality = wsTags[x].tagQuality;
                                        _this.tags[i].tagTime = wsTags[x].tagTime;
                                        _this.tags[i].tagOptions = wsTags[x].tagOptions;
                                        _this.tags[i].tagPropertyMask = wsTags[x].tagPropertyMask;
                                        _this.tags[i].tagDescription = wsTags[x].tagDescription;
                                        _this.tags[i].isHealthy = wsTags[x].isHealthy;
                                        _this.tags[i].tagMappingList = _this.addMappingToTag(wsTags[x]);
                                        break;
                                    }
                                }
                            }
                        }
                        else if (event.type === 'close') {
                            _this.reconnectTagsWebsocket();
                        }
                    });
                };
                DashboardConfigTagsGridComponent.prototype.reconnectTagsWebsocket = function () {
                    var _this = this;
                    if (this.tagsWSRconnectAttempts < 3) {
                        setTimeout(function () {
                            var currentPath = _this.router.url;
                            var pageName = currentPath.split('/').pop();
                            if (currentPath !== "/dashboard" && currentPath !== "/config" && currentPath !== "/")
                                return;
                            _this.tagsWSRconnectAttempts++;
                            _this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getTags", reconnectAttempt: _this.tagsWSRconnectAttempts }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                            _this.tagsWSApi.openWebsocket().subscribe(function (data) {
                                _this.tagsWebsocket = data;
                                _this.onDoSubscribeComplete();
                                _this.wsGetTagsData();
                            }, function (error) { _this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getTags", reason: "Reopen fail" }); });
                        }, 5000);
                    }
                };
                DashboardConfigTagsGridComponent.prototype.onSearchGrid = function () {
                    var tagNameFilter = null;
                    var tagTypeFilter = null;
                    var tagUserNameFilter = null;
                    var tagDescriptionFilter = null;
                    if (this.searchCriteria.searchFields != null) {
                        this.searchCriteria.searchFields.forEach(function (searchField) {
                            if (searchField.id == "tagName") {
                                if (searchField.value && searchField.value != "") {
                                    tagNameFilter = searchField.value;
                                }
                            }
                            if (searchField.id == "tagUserName") {
                                if (searchField.value && searchField.value != "") {
                                    tagUserNameFilter = searchField.value;
                                }
                            }
                            if (searchField.id == "tagValueType") {
                                if (searchField.value && searchField.value != "") {
                                    tagTypeFilter = searchField.value;
                                }
                            }
                            if (searchField.id == "tagDescription") {
                                if (searchField.value && searchField.value != "") {
                                    tagDescriptionFilter = searchField.value;
                                }
                            }
                        });
                    }
                    if ((!tagNameFilter && !tagTypeFilter && !tagUserNameFilter && !tagDescriptionFilter) || (tagNameFilter == "" && tagTypeFilter == "" && tagUserNameFilter == "" && tagDescriptionFilter == "")) {
                        this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
                    }
                    else {
                        this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString(), tagNameFilter, tagDescriptionFilter, tagUserNameFilter, tagTypeFilter);
                    }
                };
                DashboardConfigTagsGridComponent.prototype.onPageSelect = function (currentPage) {
                    this.currentPage = currentPage;
                    if (this.searchIsActive)
                        this.onSearchGrid();
                    else
                        this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
                };
                DashboardConfigTagsGridComponent.prototype.onSortChange = function (sortedColumn) {
                    this.sortColumn = sortedColumn.field;
                    this.sortColumnDirection = sortedColumn.isSortAscending ? "Ascending" : "Descending";
                    if (this.searchIsActive)
                        this.onSearchGrid();
                    else
                        this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
                };
                DashboardConfigTagsGridComponent.prototype.onSearchIsActive = function (searchIsActive) {
                    this.searchIsActive = searchIsActive;
                };
                DashboardConfigTagsGridComponent.prototype.clickActionGrid = function (actionObject) {
                    var _this = this;
                    var parentObjectNameParameter = "";
                    if (this.selectedNode != null)
                        parentObjectNameParameter = this.selectedNode.nodeFullName;
                    if (actionObject.action == "action$changeValue") {
                        this.dashboardConfigComponent.onNodeAction(models_1.EditorCommandsDTO.MENUCMDCHANGEVALUE.toString(), actionObject.tag.tagName, actionObject.tag.tagName, actionObject.tag, null);
                    }
                    else if (actionObject.action == "action$edit") {
                        this.dashboardConfigComponent.onNodeAction(models_1.EditorCommandsDTO.MENUCMDEDIT.toString(), actionObject.tag.tagName, actionObject.tag.tagName, actionObject.tag, null);
                    }
                    else if (actionObject.action == "action$delete") {
                        this.dashboardConfigComponent.onNodeAction(models_1.EditorCommandsDTO.MENUCMDDELETE.toString(), actionObject.tag.tagName, actionObject.tag.tagName, actionObject.tag, null);
                    }
                    else if (actionObject.action.includes("action$deleteMapping")) {
                        var dashboardUserModalDeleteRef_1;
                        this.translate.get("TR_ARE_YOU_SURE_TO_REMOVE_MAPPING", { mapping: actionObject.tag.leftTag + "/" + actionObject.tag.rightTag }).subscribe(function (res) {
                            dashboardUserModalDeleteRef_1 = _this.modal.confirm()
                                .size('lg')
                                .showClose(true)
                                .title(_this.translate.instant('TR_WARNING'))
                                .okBtn(_this.translate.instant('TR_DELETE'))
                                .okBtnClass('btn btn-default')
                                .body("\n\t\t\t\t\t<div class=\"panel panel-warning\">\n\t\t\t\t\t\t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t</div>\n        ")
                                .open();
                        });
                        dashboardUserModalDeleteRef_1.result.then(function (result) {
                            if (result) {
                                _this.mappingsApi.removeMapping(actionObject.tag.leftTag, actionObject.tag.rightTag).subscribe(function (data) {
                                    _this.translate.get("TR_OBJECT_TAGNAME_DELETED", { tagName: actionObject.tag.leftTag + "/" + actionObject.tag.rightTag }).subscribe(function (res) {
                                        _this.alertService.success(res);
                                    });
                                }, function (error) {
                                    if (error.status == 401) {
                                        _this.authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        _this.alertService.error("TR_ERROR_OBJECT_NOT_DELETED");
                                    }
                                });
                            }
                        }, function () { });
                    }
                };
                DashboardConfigTagsGridComponent.prototype.getColumns = function () {
                    return [
                        new dashboard_config_grid_component_1.Column("tagObjectIcon", "", "action$image", "", "isHealthy"),
                        new dashboard_config_grid_component_1.Column("tagName", "TR_NAME", "action$context-menu-device", "tagDescription", "tag", true, true),
                        new dashboard_config_grid_component_1.Column("tagUserName", "TR_TAG_USER_NAME", "action$tooltip", "tagDescription"),
                        new dashboard_config_grid_component_1.Column("tagMappingList", "TR_MAPPING", "action$list", "", "tagMappingList"),
                        new dashboard_config_grid_component_1.Column("tagValue", "TR_VALUE", "action$tooltip", "tagValue"),
                        new dashboard_config_grid_component_1.Column("tagQuality", "TR_QUALITY", "", ""),
                        new dashboard_config_grid_component_1.Column("tagTime", "TR_TIME", "", "TR_TIME_FLAG_TOOLTIP_HELP"),
                        new dashboard_config_grid_component_1.Column("tagOptions", "TR_OPTIONS", "", ""),
                        new dashboard_config_grid_component_1.Column("tagValueType", "TR_TYPE", "", "", "", true, null),
                        new dashboard_config_grid_component_1.Column("", "", "action$changeValue", "TR_CHANGE_VALUE"),
                        new dashboard_config_grid_component_1.Column("", "", "action$edit", "TR_EDIT"),
                        new dashboard_config_grid_component_1.Column("", "", "action$delete", "TR_DELETE")
                    ];
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigTagsGridComponent.prototype, "selectedNode", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigTagsGridComponent.prototype, "isRecursive", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Number)
                ], DashboardConfigTagsGridComponent.prototype, "tagPurposeFilter", void 0);
                DashboardConfigTagsGridComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagsGridComponent",
                        styles: ["\n    ::ng-deep [hidden].page+split-gutter { \n      flex-basis: 0px !important;\n      height: 0px !important;\n    }\n    .search-bar{\n      position: relative;\n      display: block;\n    }\n  "],
                        template: "\n    <split direction=\"vertical\">\n      <split-area [size]=\"93\">\n\t\t    <dashboardConfigTagsGridSearchComponent class=\"search-bar\" (onSearch)=\"onSearchGrid()\" (onSearchIsActive)=\"onSearchIsActive($event)\" [(searchCriteria)]=\"searchCriteria\" [isRecursive]=\"isRecursive\"></dashboardConfigTagsGridSearchComponent>\n        <dashboardConfigGridComponent [rows]=\"tags\" [columns]=\"columns\" (onSortChange)=\"onSortChange($event)\" (onClickActionGrid)=\"clickActionGrid($event)\"></dashboardConfigGridComponent>\n      </split-area>\n      <split-area [size]=\"7\" *ngIf=\"this.tagCount > this.itemPerPage && this.itemPerPage > 0\">\n\t\t    <gridPaginationComponent (onPageSelect)=\"onPageSelect($event)\" [pageEdgePairs]=\"pageEdgePairs\"></gridPaginationComponent>\n      </split-area>\n    </split>\n\t"
                    }),
                    __metadata("design:paramtypes", [bootstrap_1.Modal, api_1.TagsService, api_1.EditorsService, api_1.NodesService, api_1.MappingsService, wsApi_1.TagsWSApi,
                        alert_service_1.AlertService, authentication_service_1.AuthenticationService, wsApi_1.BroadcastEventWSApi, core_2.TranslateService,
                        global_data_service_1.GlobalDataService, dashboard_config_component_1.DashboardConfigComponent, router_1.Router])
                ], DashboardConfigTagsGridComponent);
                return DashboardConfigTagsGridComponent;
            }());
            exports_1("DashboardConfigTagsGridComponent", DashboardConfigTagsGridComponent);
        }
    };
});
//# sourceMappingURL=dashboard.config.tags.grid.component.js.map