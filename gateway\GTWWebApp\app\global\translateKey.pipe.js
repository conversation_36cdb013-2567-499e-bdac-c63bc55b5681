System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, TranslateKeyPipe;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            TranslateKeyPipe = (function () {
                function TranslateKeyPipe() {
                }
                TranslateKeyPipe.prototype.transform = function (key, defaultText, translate) {
                    if (key == "")
                        return "";
                    var translated = translate.instant(key);
                    if (translated == key)
                        return defaultText;
                    return translated;
                };
                TranslateKeyPipe = __decorate([
                    core_1.Pipe({ name: 'translateKey' })
                ], TranslateKeyPipe);
                return TranslateKeyPipe;
            }());
            exports_1("TranslateKeyPipe", TranslateKeyPipe);
        }
    };
});
//# sourceMappingURL=translateKey.pipe.js.map