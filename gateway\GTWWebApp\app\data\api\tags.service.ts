/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { TagCollectionObjectDTO } from '../model/tagCollectionObjectDTO';
import { TagObjectDTO } from '../model/tagObjectDTO';
import { TagValuesDTO } from '../model/tagValuesDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class TagsService {

    public basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Removes tags from the database.
     * Removes tags from the database.
     * @param body tags data
     * @param parentObjectName parent object path
     * @param objectCollectionKind whether this node&#39;s collection has MDOs or SDOs in it.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteTags(body: TagCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'body', reportProgress?: boolean): Observable<any>;
    public deleteTags(body: TagCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public deleteTags(body: TagCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public deleteTags(body: TagCollectionObjectDTO, parentObjectName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling deleteTags.');
        }



        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (parentObjectName !== undefined && parentObjectName !== null) {
            queryParameters = queryParameters.set('parentObjectName', <any>parentObjectName);
        }
        if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
            queryParameters = queryParameters.set('objectCollectionKind', <any>objectCollectionKind);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.delete<any>(`${this.basePath}/tags`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
                body: body
            }
        );
    }

    /**
     * Gets a single tags value as a json object.
     * Gets a single tags value as a json object.
     * @param tagName tag path
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getTag(tagName: string, observe?: 'body', reportProgress?: boolean): Observable<TagObjectDTO>;
    public getTag(tagName: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<TagObjectDTO>>;
    public getTag(tagName: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<TagObjectDTO>>;
    public getTag(tagName: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (tagName === null || tagName === undefined) {
            throw new Error('Required parameter tagName was null or undefined when calling getTag.');
        }

        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (tagName !== undefined && tagName !== null) {
            queryParameters = queryParameters.set('tagName', <any>tagName);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<TagObjectDTO>(`${this.basePath}/gettag`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get tags based on filter (i.e. MDOS, SDOS, etc) as TagObjectDTO objects.
     * 
     * @param nodeFullName node name/path (i.e. mmb.L1).  Tags will be obtained starting at this node. Empty string starts at the root node.
     * @param sortColumn filed name of column to sort.
     * @param sortColumnDirection Direction of column to sort.
     * @param valueTypeFilter tag value type filter
     * @param tagNameFilter tag name filter
     * @param tagCollectionKindFilter tag collection kind filter (i.e. MDO,SDO,ALL)
     * @param tagPurposeFilter tag purpose mask (i.e. a binary or of health(0x01), performance(0x02), data(0x04), unhealthy(0x08), get_mappings_csv(0x10), get_points_csv(0x20) etc. see GTWTYPES_TAG_PURPOSE_MASK), a value of zero(0) or undefined(empty string)  will select all tags
     * @param recursive get member tags recursively
     * @param startIndex 1 based start index number of tags to get (0 for not specified)
     * @param endIndex 1 based last index number of tags to get (0 for not specified)
     * @param fieldSelectionMask field selection mask (i.e. a binary or of name(0x1), value(0x2), quality(0x4), time(0x8), description(0x10), options(0x20), class name(0x40), object icon(0x80), health(0x100), property mask(0x200), binding list(0x400) etc. see GTWTYPES_TAG_FIELD_MASK), a value of zero(0) or undefined(empty string)  will select all fields
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getTags(nodeFullName?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', tagPurposeFilter?: number, recursive?: boolean, startIndex?: number, endIndex?: number, fieldSelectionMask?: number, observe?: 'body', reportProgress?: boolean): Observable<Array<TagObjectDTO>>;
    public getTags(nodeFullName?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', tagPurposeFilter?: number, recursive?: boolean, startIndex?: number, endIndex?: number, fieldSelectionMask?: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<TagObjectDTO>>>;
    public getTags(nodeFullName?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', tagPurposeFilter?: number, recursive?: boolean, startIndex?: number, endIndex?: number, fieldSelectionMask?: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<TagObjectDTO>>>;
    public getTags(nodeFullName?: string, sortColumn?: 'tagName' | 'tagValueType', sortColumnDirection?: 'Ascending' | 'Descending', valueTypeFilter?: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagCollectionKindFilter?: 'MDO' | 'SDO' | 'ALL', tagPurposeFilter?: number, recursive?: boolean, startIndex?: number, endIndex?: number, fieldSelectionMask?: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (nodeFullName !== undefined && nodeFullName !== null) {
            queryParameters = queryParameters.set('nodeFullName', <any>nodeFullName);
        }
        if (sortColumn !== undefined && sortColumn !== null) {
            queryParameters = queryParameters.set('sortColumn', <any>sortColumn);
        }
        if (sortColumnDirection !== undefined && sortColumnDirection !== null) {
            queryParameters = queryParameters.set('sortColumnDirection', <any>sortColumnDirection);
        }
        if (valueTypeFilter !== undefined && valueTypeFilter !== null) {
            queryParameters = queryParameters.set('valueTypeFilter', <any>valueTypeFilter);
        }
        if (tagNameFilter !== undefined && tagNameFilter !== null) {
            queryParameters = queryParameters.set('tagNameFilter', <any>tagNameFilter);
        }
        if (tagAliasFilter !== undefined && tagAliasFilter !== null) {
          queryParameters = queryParameters.set('tagAliasFilter', <any>tagAliasFilter);
        }
        if (tagDescriptionFilter !== undefined && tagDescriptionFilter !== null) {
          queryParameters = queryParameters.set('tagDescriptionFilter', <any>tagDescriptionFilter);
        }
        if (tagPurposeFilter !== undefined && tagPurposeFilter !== null) {
          queryParameters = queryParameters.set('tagPurposeFilter', <any>tagPurposeFilter);
        }
        if (tagCollectionKindFilter !== undefined && tagCollectionKindFilter !== null) {
            queryParameters = queryParameters.set('tagCollectionKindFilter', <any>tagCollectionKindFilter);
        }
        if (tagPurposeFilter !== undefined && tagPurposeFilter !== null) {
            queryParameters = queryParameters.set('tagPurposeFilter', <any>tagPurposeFilter);
        }
        if (recursive !== undefined && recursive !== null) {
            queryParameters = queryParameters.set('recursive', <any>recursive);
        }
        if (startIndex !== undefined && startIndex !== null) {
            queryParameters = queryParameters.set('startIndex', <any>startIndex);
        }
        if (endIndex !== undefined && endIndex !== null) {
            queryParameters = queryParameters.set('endIndex', <any>endIndex);
        }
        if (fieldSelectionMask !== undefined && fieldSelectionMask !== null) {
            queryParameters = queryParameters.set('fieldSelectionMask', <any>fieldSelectionMask);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<Array<TagObjectDTO>>(`${this.basePath}/tags`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * upload a point list csv file
     * 
     * @param file file
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public postPoints(file: Blob, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public postPoints(file: Blob, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public postPoints(file: Blob, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public postPoints(file: Blob, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (file === null || file === undefined) {
            throw new Error('Required parameter file was null or undefined when calling postPoints.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (file !== undefined) {
            formParams = formParams.append('file', <any>file) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/tags`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Sets a single tags value.
     * 
     * @param tagName tag path
     * @param tagValue tag value
     * @param tagQuality tag quality (only for internal MDOs)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public setTagForm(tagName: string, tagValue: string, tagQuality?: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public setTagForm(tagName: string, tagValue: string, tagQuality?: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public setTagForm(tagName: string, tagValue: string, tagQuality?: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public setTagForm(tagName: string, tagValue: string, tagQuality?: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (tagName === null || tagName === undefined) {
            throw new Error('Required parameter tagName was null or undefined when calling setTagForm.');
        }

        if (tagValue === null || tagValue === undefined) {
            throw new Error('Required parameter tagValue was null or undefined when calling setTagForm.');
        }


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (tagName !== undefined) {
            formParams = formParams.append('tagName', <any>tagName) || formParams;
        }
        if (tagValue !== undefined) {
            formParams = formParams.append('tagValue', <any>tagValue) || formParams;
        }
        if (tagQuality !== undefined) {
            formParams = formParams.append('tagQuality', <any>tagQuality) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/settag`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Sets a collection of tags values.
     * 
     * @param body tags data
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public setTags(body: TagValuesDTO, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public setTags(body: TagValuesDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public setTags(body: TagValuesDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public setTags(body: TagValuesDTO, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling setTags.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.post<any>(`${this.basePath}/settags`,
            body,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Set tags based on filter.
     * 
     * @param valueTypeFilter tag value type filter
     * @param tagValue tag value
     * @param nodeFullName node name/path (i.e. mmb.L1).  Tags will be set starting at this node. Empty string starts at the root node.
     * @param tagNameFilter tag name filter
     * @param tagPurposeFilter tag purpose mask (i.e. a binary or of health(0x01), performance(0x02), data(0x04), unhealthy(0x08), get_mappings_csv(0x10), get_points_csv(0x20) etc. see GTWTYPES_TAG_PURPOSE_MASK), a value of zero(0) or undefined(empty string)  will select all tags
     * @param recursive set member tags recursivly
     * @param tagQuality tag quality (only for internal MDOs)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public setTagsFiltered(valueTypeFilter: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagValue: string, nodeFullName?: string, tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagPurposeFilter?: number, recursive?: boolean, tagQuality?: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public setTagsFiltered(valueTypeFilter: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagValue: string, nodeFullName?: string, tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagPurposeFilter?: number, recursive?: boolean, tagQuality?: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public setTagsFiltered(valueTypeFilter: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagValue: string, nodeFullName?: string, tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagPurposeFilter?: number, recursive?: boolean, tagQuality?: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public setTagsFiltered(valueTypeFilter: 'Unknown' | 'Bool' | 'Spare' | 'Char' | 'Unsigned_Char' | 'Short' | 'Unsigned_Short' | 'Long' | 'Unsigned_Int' | 'Float' | 'Double' | 'String' | 'Time' | 'Int_64' | 'Unsined_Int_64', tagValue: string, nodeFullName?: string, tagNameFilter?: string, tagDescriptionFilter?: string, tagAliasFilter?: string, tagPurposeFilter?: number, recursive?: boolean, tagQuality?: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (valueTypeFilter === null || valueTypeFilter === undefined) {
            throw new Error('Required parameter valueTypeFilter was null or undefined when calling setTagsFiltered.');
        }

        if (tagValue === null || tagValue === undefined) {
            throw new Error('Required parameter tagValue was null or undefined when calling setTagsFiltered.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (nodeFullName !== undefined) {
            formParams = formParams.append('nodeFullName', <any>nodeFullName) || formParams;
        }
        if (valueTypeFilter !== undefined) {
            formParams = formParams.append('valueTypeFilter', <any>valueTypeFilter) || formParams;
        }
        if (tagNameFilter !== undefined) {
          formParams = formParams.append('tagNameFilter', <any>tagNameFilter) || formParams;
        }
        if (tagAliasFilter !== undefined) {
          formParams = formParams.append('tagAliasFilter', <any>tagAliasFilter) || formParams;
        }
        if (tagNameFilter !== undefined) {
          formParams = formParams.append('tagPurposeFilter', <any>tagPurposeFilter) || formParams;
        }
        if (tagDescriptionFilter !== undefined) {
          formParams = formParams.append('tagDescriptionFilter', <any>tagDescriptionFilter) || formParams;
        }
        if (recursive !== undefined) {
            formParams = formParams.append('recursive', <any>recursive) || formParams;
        }
        if (tagValue !== undefined) {
            formParams = formParams.append('tagValue', <any>tagValue) || formParams;
        }
        if (tagQuality !== undefined) {
            formParams = formParams.append('tagQuality', <any>tagQuality) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/set_tags_filtered`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
