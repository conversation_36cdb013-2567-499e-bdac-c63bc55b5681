System.register(["@angular/core", "../data/api/api", "../authentication/authentication.service", "../modules/panel/panel.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, api_1, authentication_service_1, panel_service_1, DashboardComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (panel_service_1_1) {
                panel_service_1 = panel_service_1_1;
            }
        ],
        execute: function () {
            DashboardComponent = (function () {
                function DashboardComponent(panelService, authenticationService, configService) {
                    this.panelService = panelService;
                    this.authenticationService = authenticationService;
                    this.configService = configService;
                }
                DashboardComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    this.panelService.panelInit();
                    this.configService.getConfig().subscribe(function (data) {
                        var currentWorkSpaceName = data.currentWorkSpaceName;
                        _this.panelService.panelList.forEach(function (panel, index) {
                            panel.titleToolTip = currentWorkSpaceName;
                        });
                        if (_this.panelService.panelLayoutDirection != 0)
                            _this.panelService.toggleLayoutDasboard(_this.panelService.panelLayoutDirection);
                    });
                };
                DashboardComponent.prototype.beforeunloadHandler = function (event) {
                    localStorage.setItem("SDGDashboardLayout", JSON.stringify(this.panelService.panelList));
                    localStorage.setItem("SDGDashboardLayoutDirection", String(this.panelService.panelLayoutDirection));
                };
                DashboardComponent.prototype.onResize = function (event) {
                    if (this.panelService.panelLayoutDirection != 0)
                        this.panelService.toggleLayoutDasboard(this.panelService.panelLayoutDirection);
                };
                __decorate([
                    core_1.HostListener('window:beforeunload', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [Object]),
                    __metadata("design:returntype", void 0)
                ], DashboardComponent.prototype, "beforeunloadHandler", null);
                __decorate([
                    core_1.HostListener('window:resize', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [Object]),
                    __metadata("design:returntype", void 0)
                ], DashboardComponent.prototype, "onResize", null);
                DashboardComponent = __decorate([
                    core_1.Component({
                        selector: 'dashboardComponent',
                        host: { "style": "height: 100%" },
                        styles: ["\n      .unselectable {\n\t\t\t  -moz-user-select: -moz-none;\n\t\t\t  -khtml-user-select: none;\n\t\t\t  -webkit-user-select: none;\n\t\t\t  -ms-user-select: none;\n\t\t\t  user-select: none;\n\t\t\t}\n      "
                        ],
                        template: "\n\t\t<div *ngFor=\"let panel of this.panelService.panelList\" class=\"unselectable\">\n\t\t\t<panelComponent class=\"dashboard-panel\" [panel]=\"panel\" *ngIf=\"(this.authenticationService.role | checkRole:'VIEWER_ROLE') && (panel.visible === true)\" (onPanelCloseParent)=this.panelService.toggleLayoutDasboard(this.panelLayoutDirection)></panelComponent>\n\t\t</div>\n\t\t"
                    }),
                    __metadata("design:paramtypes", [panel_service_1.PanelService, authentication_service_1.AuthenticationService, api_1.ConfigService])
                ], DashboardComponent);
                return DashboardComponent;
            }());
            exports_1("DashboardComponent", DashboardComponent);
        }
    };
});
//# sourceMappingURL=dashboard.component.js.map