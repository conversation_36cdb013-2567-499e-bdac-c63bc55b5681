<!DOCTYPE html>
<html>
  <head>
    <title>TMW SCADA Data Gateway Web UI</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="./node_modules/bootstrap/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="./styles.css">
    <style>
      .navbar-header {
        margin-right: 0px;
        margin-left: 0px;
      }

      .status-bar {
        overflow: hidden;
        background-color: #f8f8f8;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        border-top: 1px solid #a31c3f;
      }

      nav {
        text-align: center;
      }

      nav > a {
        font-size: 24px;
        padding: 16px;
      }

      .navbar-inverse .navbar-nav > li > a {
        color: white;
      }

      a {
        color: white;
        text-decoration: none;
        cursor: pointer;
      }

      .dropdown-menu {
        margin: 0px;
        padding: 0px;
        border-radius: 0px;
        background-color: black;
        font-size: 12px;
      }

      .dropdown-item {
        background-color: black;
        padding: 10px;
        border: 1px solid white;
        white-space: nowrap;
      }

      .container-fluid {
        padding-left: 0px;
        padding-right: 0px;
      }

      .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
        background-image: url(./images/background_7.svg), linear-gradient(to left, #4a4a4a, #000000);
        background-size: 100%;
        opacity: 0.9;
      }

      .navbar-brand {
        z-index: 999;
        position: relative;
      }

      .menu-icon {
        width: 20px;
        height: 20px;
        padding-right: 4px;
        padding-bottom: 2px;
      }

      .logo {
        width: 60px;
        height: 60px;
        margin-top: -12px;
        margin-right: 10px;
        margin-left: 6px;
      }

      .logo-text-container {
        display: inline-block;
        margin-left: -28px;
        transform: scale(.8, 1);
      }

      .logo-text {
        border: 0;
        vertical-align: 6px;
        font-size: 30px;
        font-weight: bold;
        color: #f1e186;
      }

      .logo-tmw {
        position: fixed;
        top: 10px;
        right: 35px;
        width: 90px;
      }

      .navbar-red {
        width: 100%;
        height: 4px;
        background-color: #a31c3f;
        position: fixed;
        z-index: -999;
      }

      @media only screen and (max-width: 770px) {
        .dropdown-menu {
          padding: 0px !important;
        }

        .dropdown-item {
          padding: 5px 5px 5px 20px;
          border: none;
        }

        .logo-tmw {
          display: none;
        }
      }

      .center {
        border: 3px solid #a31c3f;
        padding: 40px;
        position: absolute;
        top: 20%;
        right: 46%;
        background-color: gray;
        opacity: 0.8;
        text-align: center;
      }
    </style>
  </head>
  <body style="background-color: #e6e6e6; min-width: 550px;" class="">
    <div class="container-fluid">
      <div class="content-background-color"></div>
      <div class="content-background">
      </div>
      <nav class="navbar navbar-inverse navbar-fixed-top" style="min-width: 600px">
        <div class="container-fluid">
          <div class="navbar-header">
            <div class="navbar-brand">
              <div style="display:inline-block"><img class="logo" src="./images/GTWLogo.png"></div>
              <div class="logo-text-container"><span class="logo-text">SCADA Data Gateway</span></div>
            </div>
          </div>
          <div class="collapse navbar-collapse">
          </div>
          <div><img class="logo-tmw" src="./images/TMWLogo.png"></div>
        </div>
        <div class="navbar-red"></div>
      </nav>
      <div class="center">
        <div style="font-weight:bold; font-size:14px">Exception saved. Please use this button to go back:</div>
        <br>
        <button class="btn btn-default" onclick="javascript:window.history.back()">Back to SCADA Data Gateway</button>
      </div>
      <div style="margin-bottom:10px; min-width : 350px">
        <div class="status-bar">
        </div>
      </div>
    </div>
  </body>

</html>