System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, HelpSupportComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            HelpSupportComponent = (function () {
                function HelpSupportComponent() {
                }
                HelpSupportComponent = __decorate([
                    core_1.Component({
                        selector: "helpSupportComponent",
                        styles: ["\n   .center{\n      text-align: center\n    }\n\t"],
                        template: "\n\t\t<div>\n\t\t  <div class=\"panel panel-default panel-main\" style=\"width:99%; margin-left:6px; margin-top:60px\">\n        <div class=\"panel-heading\"><img src=\"../../images/support.svg\" class=\"module-icon\" />{{'TR_SUBMIT_SUPPORT_REQUEST' | translate}}</div>\n\t      <div class=\"panel-body center\">\n          <a href=\"mailto:<EMAIL>\"><EMAIL></a> <br>\n          919.781.1931<br>\n          <br>\n          Triangle MicroWorks, Inc.<br>\n          2840 Plaza Place, Suite 205<br>\n          Raleigh, North Carolina, 27612-6343, USA\n        </div>\n      </div>\n\t\t</div>"
                    })
                ], HelpSupportComponent);
                return HelpSupportComponent;
            }());
            exports_1("HelpSupportComponent", HelpSupportComponent);
        }
    };
});
//# sourceMappingURL=help.support.component.js.map