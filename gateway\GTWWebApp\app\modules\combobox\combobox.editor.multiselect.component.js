System.register(["@angular/core", "../../global/keys.pipe"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, keys_pipe_1, ComboboxEditorMultiselectComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (keys_pipe_1_1) {
                keys_pipe_1 = keys_pipe_1_1;
            }
        ],
        execute: function () {
            ComboboxEditorMultiselectComponent = (function () {
                function ComboboxEditorMultiselectComponent(keysPipe) {
                    this.keysPipe = keysPipe;
                    this.OnComboboxChange = new core_1.EventEmitter();
                    this.comboboxItemList = [];
                }
                ComboboxEditorMultiselectComponent.prototype.selectOpenToggle = function (event, isOpen) {
                    var eventRelatedTarget = event.relatedTarget;
                    if (eventRelatedTarget != null && eventRelatedTarget.className != null && eventRelatedTarget.className.includes("tooltipComponent"))
                        return;
                    this.isSelectOpen = isOpen;
                    event.preventDefault();
                };
                ComboboxEditorMultiselectComponent.prototype.ngOnChanges = function (changes) {
                    var _this = this;
                    if (changes["componentData"]) {
                        this.comboboxItemList = [];
                        if (this.componentData) {
                            this.componentData.forEach(function (data) {
                                _this.comboboxItemList.push({ value: data.value, isChecked: false });
                            });
                        }
                    }
                    if (changes["editorFieldcontrolValue"]) {
                        if (this.componentData) {
                            for (var i = 0; i < this.comboboxItemList.length; i++) {
                                this.comboboxItemList[i].isChecked = false;
                            }
                            if (this.editorFieldcontrolValue && this.editorFieldcontrolValue != "") {
                                var itemsList = JSON.parse(this.editorFieldcontrolValue);
                                for (var i = 0; i < itemsList.length; i++) {
                                    for (var y = 0; y < this.comboboxItemList.length; y++) {
                                        if (this.comboboxItemList[y].value == itemsList[i]) {
                                            this.comboboxItemList[y].isChecked = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                };
                ComboboxEditorMultiselectComponent.prototype.itemListOnChange = function (item) {
                    var comboboxItemResult = [];
                    item.isChecked = !item.isChecked;
                    for (var i = 0; i < this.comboboxItemList.filter(function (p) { return p.isChecked; }).length; i++) {
                        comboboxItemResult.push(this.comboboxItemList.filter(function (p) { return p.isChecked; })[i].value);
                    }
                    this.editorFieldcontrolValue = JSON.stringify(comboboxItemResult);
                    this.OnComboboxChange.emit(this.editorFieldcontrolValue);
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", String)
                ], ComboboxEditorMultiselectComponent.prototype, "editorFieldcontrolValue", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], ComboboxEditorMultiselectComponent.prototype, "componentData", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], ComboboxEditorMultiselectComponent.prototype, "OnComboboxChange", void 0);
                ComboboxEditorMultiselectComponent = __decorate([
                    core_1.Component({
                        selector: "comboboxEditorMultiselectComponent",
                        styles: ["\n    .panel {\n      margin-bottom: 2px; !important;\n      background-color: rgba(255, 255, 255, 0.7);\n      border: 1px solid #ccc;\n    }\n \t\t.chevron {\n\t\t\tfont-size: 12px;\n\t\t\tmargin-left: 6px;\n\t\t\tmargin-right: 4px;\n\t\t}\n    .input {\n      padding: 1px 12px;\n      width: 330px;\n      height: 16px;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      }\n    .list{\n      padding:8px; \n      position:absolute; \n      background-color: rgba(255, 255, 255);\n      border: 1px solid #ccc;\n      z-index:99;\n    }\n    .truncate {\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }"
                        ],
                        providers: [keys_pipe_1.KeysPipe],
                        template: "\n\t\t<div class=\"panel\" (mouseenter)=\"selectOpenToggle($event, true)\"  (mouseleave)=\"selectOpenToggle($event, false)\">\n\t\t\t<div style=\"display: inline-block;\">\n\t\t\t\t<div class=\"input\" title=\"{{editorFieldcontrolValue.replace('[','').replace(']','')}}\">{{editorFieldcontrolValue.replace('[','').replace(']','')}}</div>\n\t\t\t</div>\n\t\t\t<div style=\"display: inline-block; vertical-align:middle;\">\n\t\t\t\t<div class=\"glyphicon glyphicon-chevron-down chevron\"></div>\n\t\t\t</div>\n\t\t\t<div *ngIf=\"isSelectOpen && this.comboboxItemList?.length > 0\" class=\"panel list\" style=\"\">\n\t\t\t\t<div *ngFor=\"let item of this.comboboxItemList\">\n\t\t\t\t\t<label class=\"truncate\">\n\t\t\t\t\t\t\t<input type=\"checkbox\"\n\t\t\t\t\t\t\t\tclass=\"form-check\"\n\t\t\t\t\t\t\t\t[checked]=item.isChecked\n\t\t\t\t\t\t\t\t(change)=\"itemListOnChange(item)\"/>\n\t\t\t\t\t\t\t\t<span [tooltip]=[item.value]>&nbsp;{{item.value}}</span>\n\t\t\t\t\t</label>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\t"
                    }),
                    __metadata("design:paramtypes", [keys_pipe_1.KeysPipe])
                ], ComboboxEditorMultiselectComponent);
                return ComboboxEditorMultiselectComponent;
            }());
            exports_1("ComboboxEditorMultiselectComponent", ComboboxEditorMultiselectComponent);
        }
    };
});
//# sourceMappingURL=combobox.editor.multiselect.component.js.map