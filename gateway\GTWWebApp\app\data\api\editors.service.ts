/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { EditorCommandDTO } from '../model/editorCommandDTO';
import { EditorSpecificationObjectDTO } from '../model/editorSpecificationObjectDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class EditorsService {

    public basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Adds a new object to the database or updates the existing object.
     * For a new object send the MENU_CMD_ADD... command, for an existing object send the MENU_CMD_EDIT command. The definition returned by editors/get must be filled out and used in the objectDataJson member 
     * @param command add/update command (see: EditorCommandsDTO for enum)
     * @param body command data
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
      public createOrUpdateEditorObject(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_61850_DATASET' | 'MENU_CMD_CHANGE_61850_DATASET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT' | 'MENU_CMD_ADD_TASE2_DOMAIN' | 'MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER', body: EditorCommandDTO, observe?: 'body', reportProgress?: boolean): Observable<any>;
      public createOrUpdateEditorObject(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_61850_DATASET' | 'MENU_CMD_CHANGE_61850_DATASET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT' | 'MENU_CMD_ADD_TASE2_DOMAIN' | 'MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER', body: EditorCommandDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
      public createOrUpdateEditorObject(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_61850_DATASET' | 'MENU_CMD_CHANGE_61850_DATASET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT' | 'MENU_CMD_ADD_TASE2_DOMAIN' | 'MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER', body: EditorCommandDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
      public createOrUpdateEditorObject(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_61850_DATASET' | 'MENU_CMD_CHANGE_61850_DATASET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET' | 'MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT' | 'MENU_CMD_ADD_TASE2_DOMAIN' | 'MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER', body: EditorCommandDTO, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (command === null || command === undefined) {
            throw new Error('Required parameter command was null or undefined when calling createOrUpdateEditorObject.');
        }

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling createOrUpdateEditorObject.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.put<any>(`${this.basePath}/editors/${encodeURIComponent(String(command))}`,
            body,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * editor action
     * 
     * @param objectName object Name triggering the action
     * @param action action type triggering the action
     * @param objectCollectionKind whether this node&#39;s collection has MDOs or SDOs in it.
     * @param parameter1 parameter 1
     * @param parameter2 parameter 2
     * @param parameter3 parameter 3
     * @param parameter4 parameter 4
     * @param parameter5 parameter 5
     * @param parameter6 parameter 6
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public editorAction(objectName: string, action: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', parameter1?: string, parameter2?: string, parameter3?: string, parameter4?: string, parameter5?: string, parameter6?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public editorAction(objectName: string, action: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', parameter1?: string, parameter2?: string, parameter3?: string, parameter4?: string, parameter5?: string, parameter6?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public editorAction(objectName: string, action: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', parameter1?: string, parameter2?: string, parameter3?: string, parameter4?: string, parameter5?: string, parameter6?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public editorAction(objectName: string, action: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', parameter1?: string, parameter2?: string, parameter3?: string, parameter4?: string, parameter5?: string, parameter6?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (objectName === null || objectName === undefined) {
            throw new Error('Required parameter objectName was null or undefined when calling editorAction.');
        }

        if (action === null || action === undefined) {
            throw new Error('Required parameter action was null or undefined when calling editorAction.');
        }








        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (objectName !== undefined && objectName !== null) {
            queryParameters = queryParameters.set('objectName', <any>objectName);
        }
        if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
            queryParameters = queryParameters.set('objectCollectionKind', <any>objectCollectionKind);
        }
        if (action !== undefined && action !== null) {
            queryParameters = queryParameters.set('action', <any>action);
        }
        if (parameter1 !== undefined && parameter1 !== null) {
            queryParameters = queryParameters.set('parameter_1', <any>parameter1);
        }
        if (parameter2 !== undefined && parameter2 !== null) {
            queryParameters = queryParameters.set('parameter_2', <any>parameter2);
        }
        if (parameter3 !== undefined && parameter3 !== null) {
            queryParameters = queryParameters.set('parameter_3', <any>parameter3);
        }
        if (parameter4 !== undefined && parameter4 !== null) {
            queryParameters = queryParameters.set('parameter_4', <any>parameter4);
        }
        if (parameter5 !== undefined && parameter5 !== null) {
            queryParameters = queryParameters.set('parameter_5', <any>parameter5);
        }
        if (parameter6 !== undefined && parameter6 !== null) {
            queryParameters = queryParameters.set('parameter_6', <any>parameter6);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<any>(`${this.basePath}/editor/action`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Gets an editable object editor.
     * For a new object send the MENU_CMD_ADD... command, for an existing object send the MENU_CMD_EDIT command
     * @param command command name (see: EditorCommandsDTO for enum)
     * @param objectName object path
     * @param parentObjectName parent object path
     * @param editAtRuntime edit at runtime (true to edit objects that require the SDG Engine to be restarted)
     * @param objectClassName object class i.e. the type of the object
     * @param objectCollectionKind whether this node&#39;s collection has MDOs or SDOs in it.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getEditorData(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_SUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_CREATE_THXML_POINT_FILE' | 'MENU_CMD_CREATE_DTM_CSV_POINT_FILE' | 'MENU_CMD_CONNECT_OPC_SERVER' | 'MENU_CMD_DISCONNECT_OPC_SERVER' | 'MENU_CMD_CONNECT_OPC_AE_SERVER' | 'MENU_CMD_DISCONNECT_OPC_AE_SERVER' | 'MENU_CMD_CONNECT_OPC_UA_SERVER' | 'MENU_CMD_DISCONNECT_OPC_UA_SERVER' | 'MENU_CMD_OPC_UA_GET_SERVER_STATUS' | 'MENU_CMD_ENABLE_DSTS' | 'MENU_CMD_DISABLE_DSTS' | 'MENU_CMD_SHOW_CONFIG_TASE2_SERVER' | 'MENU_CMD_SHOW_CONFIG_TASE2_CLIENT' | 'MENU_CMD_CREATE_SERVER' | 'MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER' | 'MENU_CMD_SWITCH_TO_RCHANNEL', objectName: string, parentObjectName: string, editAtRuntime: boolean, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'body', reportProgress?: boolean): Observable<EditorSpecificationObjectDTO>;
    public getEditorData(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_SUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_CREATE_THXML_POINT_FILE' | 'MENU_CMD_CREATE_DTM_CSV_POINT_FILE' | 'MENU_CMD_CONNECT_OPC_SERVER' | 'MENU_CMD_DISCONNECT_OPC_SERVER' | 'MENU_CMD_CONNECT_OPC_AE_SERVER' | 'MENU_CMD_DISCONNECT_OPC_AE_SERVER' | 'MENU_CMD_CONNECT_OPC_UA_SERVER' | 'MENU_CMD_DISCONNECT_OPC_UA_SERVER' | 'MENU_CMD_OPC_UA_GET_SERVER_STATUS' | 'MENU_CMD_ENABLE_DSTS' | 'MENU_CMD_DISABLE_DSTS' | 'MENU_CMD_SHOW_CONFIG_TASE2_SERVER' | 'MENU_CMD_SHOW_CONFIG_TASE2_CLIENT' | 'MENU_CMD_CREATE_SERVER' | 'MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER' | 'MENU_CMD_SWITCH_TO_RCHANNEL', objectName: string, parentObjectName: string, editAtRuntime: boolean, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<EditorSpecificationObjectDTO>>;
    public getEditorData(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_SUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_CREATE_THXML_POINT_FILE' | 'MENU_CMD_CREATE_DTM_CSV_POINT_FILE' | 'MENU_CMD_CONNECT_OPC_SERVER' | 'MENU_CMD_DISCONNECT_OPC_SERVER' | 'MENU_CMD_CONNECT_OPC_AE_SERVER' | 'MENU_CMD_DISCONNECT_OPC_AE_SERVER' | 'MENU_CMD_CONNECT_OPC_UA_SERVER' | 'MENU_CMD_DISCONNECT_OPC_UA_SERVER' | 'MENU_CMD_OPC_UA_GET_SERVER_STATUS' | 'MENU_CMD_ENABLE_DSTS' | 'MENU_CMD_DISABLE_DSTS' | 'MENU_CMD_SHOW_CONFIG_TASE2_SERVER' | 'MENU_CMD_SHOW_CONFIG_TASE2_CLIENT' | 'MENU_CMD_CREATE_SERVER' | 'MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER' | 'MENU_CMD_SWITCH_TO_RCHANNEL', objectName: string, parentObjectName: string, editAtRuntime: boolean, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<EditorSpecificationObjectDTO>>;
    public getEditorData(command: 'MENU_CMD_EDIT' | 'MENU_CMD_ADD_MBP_CHANNEL' | 'MENU_CMD_ADD_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL' | 'MENU_CMD_ADD_DNP3_XML_DEVICE' | 'MENU_CMD_ADD_SERIAL_CHANNEL' | 'MENU_CMD_ADD_MODEM_POOL_CHANNEL' | 'MENU_CMD_ADD_MODEM' | 'MENU_CMD_ADD_MODEM_POOL' | 'MENU_CMD_ADD_OPC_CLIENT' | 'MENU_CMD_ADD_OPC_AE_CLIENT' | 'MENU_CMD_ADD_61850_CLIENT' | 'MENU_CMD_ADD_TASE2_CLIENT' | 'MENU_CMD_ADD_TASE2_SERVER' | 'MENU_CMD_ADD_TASE2_CLIENT_SERVER' | 'MENU_CMD_EDIT_TASE2_EDIT_MODEL' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING' | 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM' | 'MENU_CMD_ADD_61850_SERVER' | 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE' | 'MENU_CMD_ADD_ODBC_CLIENT' | 'MENU_CMD_ADD_EQ_MDO' | 'MENU_CMD_ADD_INTERNAL_MDO' | 'MENU_CMD_ADD_61850_REPORT' | 'MENU_CMD_ADD_61850_GOOSE' | 'MENU_CMD_ADD_61850_POLLED_DATA_SET' | 'MENU_CMD_ADD_61850_POLLED_POINT_SET' | 'MENU_CMD_ADD_61850_COMMAND_POINT' | 'MENU_CMD_ADD_61850_COMMAND_POINT_SET' | 'MENU_CMD_ADD_61850_WRITABLE_POINT' | 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET' | 'MENU_CMD_ADD_GOOSE_MONITOR' | 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL' | 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL' | 'MENU_CMD_DELETE_REDUNDANT_CHANNEL' | 'MENU_CMD_ADD_61400_ALARMS_NODE' | 'MENU_CMD_ADD_61400_ALARM_MDO' | 'MENU_CMD_ADD_61850_ITEM' | 'MENU_CMD_ADD_TASE2_DSTS' | 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET' | 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT' | 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET' | 'MENU_CMD_ADD_TASE2_ITEM' | 'MENU_CMD_ADD_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM' | 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM' | 'MENU_CMD_ADD_ODBC_ITEM' | 'MENU_CMD_ADD_OPC_AE_ITEM' | 'MENU_CMD_ADD_SESSION' | 'MENU_CMD_ADD_MDO' | 'MENU_CMD_ADD_SECTOR' | 'MENU_CMD_ADD_DATA_TYPE' | 'MENU_CMD_ADD_DNP_PROTO' | 'MENU_CMD_ADD_DNP_DESCP' | 'MENU_CMD_ADD_DATASET_ELEMENT' | 'MENU_CMD_ADD_OPC_AE_ATTR' | 'MENU_CMD_ADD_WRITE_ACTION' | 'MENU_CMD_ADD_MULTI_POINT' | 'MENU_CMD_SUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM' | 'MENU_CMD_CREATE_THXML_POINT_FILE' | 'MENU_CMD_CREATE_DTM_CSV_POINT_FILE' | 'MENU_CMD_CONNECT_OPC_SERVER' | 'MENU_CMD_DISCONNECT_OPC_SERVER' | 'MENU_CMD_CONNECT_OPC_AE_SERVER' | 'MENU_CMD_DISCONNECT_OPC_AE_SERVER' | 'MENU_CMD_CONNECT_OPC_UA_SERVER' | 'MENU_CMD_DISCONNECT_OPC_UA_SERVER' | 'MENU_CMD_OPC_UA_GET_SERVER_STATUS' | 'MENU_CMD_ENABLE_DSTS' | 'MENU_CMD_DISABLE_DSTS' | 'MENU_CMD_SHOW_CONFIG_TASE2_SERVER' | 'MENU_CMD_SHOW_CONFIG_TASE2_CLIENT' | 'MENU_CMD_CREATE_SERVER' | 'MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE' | 'MENU_CMD_ADD_USER_DEFINED_FOLDER' | 'MENU_CMD_SWITCH_TO_RCHANNEL', objectName: string, parentObjectName: string, editAtRuntime: boolean, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (command === null || command === undefined) {
            throw new Error('Required parameter command was null or undefined when calling getEditorData.');
        }

        if (objectName === null || objectName === undefined) {
            throw new Error('Required parameter objectName was null or undefined when calling getEditorData.');
        }

        if (parentObjectName === null || parentObjectName === undefined) {
            throw new Error('Required parameter parentObjectName was null or undefined when calling getEditorData.');
        }

        if (editAtRuntime === null || editAtRuntime === undefined) {
            throw new Error('Required parameter editAtRuntime was null or undefined when calling getEditorData.');
        }



        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (objectName !== undefined && objectName !== null) {
            queryParameters = queryParameters.set('objectName', <any>objectName);
        }
        if (objectClassName !== undefined && objectClassName !== null) {
            queryParameters = queryParameters.set('objectClassName', <any>objectClassName);
        }
        if (parentObjectName !== undefined && parentObjectName !== null) {
            queryParameters = queryParameters.set('parentObjectName', <any>parentObjectName);
        }
        if (editAtRuntime !== undefined && editAtRuntime !== null) {
            queryParameters = queryParameters.set('editAtRuntime', <any>editAtRuntime);
        }
        if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
            queryParameters = queryParameters.set('objectCollectionKind', <any>objectCollectionKind);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<EditorSpecificationObjectDTO>(`${this.basePath}/editors/${encodeURIComponent(String(command))}`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
