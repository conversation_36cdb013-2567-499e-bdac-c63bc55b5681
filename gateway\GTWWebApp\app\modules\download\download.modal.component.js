System.register(["@angular/core", "../alert/alert.service", "../../authentication/authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, alert_service_1, authentication_service_1, DownloadModalComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            DownloadModalComponent = (function () {
                function DownloadModalComponent(alertService, authenticationService) {
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                }
                DownloadModalComponent.prototype.downloadClick = function (fileName, fileType, fileService) {
                    var _this = this;
                    if (fileService === void 0) { fileService = null; }
                    fileService.fileGet(fileName, fileType).subscribe(function (data) {
                        _this.saveData(data, fileName);
                        _this.alertService.success("TR_FILE_DOWNLOADED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED");
                        }
                    });
                };
                DownloadModalComponent.prototype.saveData = function (blob, fileName) {
                    var a = document.createElement("a");
                    document.body.appendChild(a);
                    var url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = fileName;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    a.remove();
                };
                DownloadModalComponent = __decorate([
                    core_1.Component({
                        selector: "downloadModalComponent",
                        template: ""
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, authentication_service_1.AuthenticationService])
                ], DownloadModalComponent);
                return DownloadModalComponent;
            }());
            exports_1("DownloadModalComponent", DownloadModalComponent);
        }
    };
});
//# sourceMappingURL=download.modal.component.js.map