﻿import { Component, OnInit } from "@angular/core";
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { AlertService } from "../modules/alert/alert.service";
import { UserObjectDTO } from "../data/model/models";
import { TranslateService } from "@ngx-translate/core";
import { UserRoleEnum, AuthenticationService } from '../authentication/authentication.service';

@Component({
  selector: "userModal",
  styles: [`
      .user-roles-checkbox {
	      display: inline-block;
	      width: 140px;
	      height: 10px;
	      margin: 1px 0 0 8px;
      }      
      .password {
        display: inline-block;
        width: 94%;
        margin-bottom: 0px;
      }
    `],
  template: `
	  <div class="container-fluid">
		  <div class="modal-heading" *ngIf="isUserNew">{{'TR_ADD_NEW_USER' | translate}}</div>
		  <div class="modal-heading" *ngIf="!isUserNew">{{'TR_EDIT_USER' | translate}}</div>
		  <div class="modal-body">
        <form name="form" (ngSubmit)="f.form.valid && save()" #f="ngForm" novalidate>
	        <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !username.valid }">
		        <label for="username">{{'TR_USERNAME' | translate}}</label>
		        <input type="text" class="form-control" name="username" [(ngModel)]="currentUser.username" #username="ngModel" required [readonly]="!isUserNew" minlength="4"/>
		        <small class="text-danger" *ngIf="f.submitted && !username.valid">{{'TR_USERNAME_IS_REQUIRED_CHARACTERS_MINIMUM' | translate}}</small>
	        </div>
          <div *ngIf="isUserNew">
	          <div class="form-group password" [ngClass]="{ 'has-error': f.submitted && !password.valid && isUserNew }">
		          <label for="password">{{'TR_TEMPORARY_PASSWORD' | translate}}</label>
              <img class="help-icon" src="../../images/help.svg" title="{{'TR_PASSWORD_COMPLEXITY_REQUIREMENTS' | translate}}">
		          <input type="password" class="form-control" name="password" [(ngModel)]="currentUser.password" #password="ngModel" [required]="true" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@$#!\\-+*_%.])\\S{8,24}$" show-password>
	          </div>
		        <small class="text-danger" *ngIf="f.submitted && !password.valid" [innerHtml]="'TR_YOUR_PASSWORD_DOESNT_MEET_COMPLEXITY_REQUIREMENTS' | translate"></small>
          </div>
          <br>
	        <div class="form-group">
		        <label for="email">{{'TR_EMAIL' | translate}}</label>
		        <input type="text" class="form-control" name="email" [(ngModel)]="currentUser.email" #email="ngModel" />
	        </div>
	        <div class="form-group">
            <label for="roles">{{'TR_ROLE' | translate}}</label><br>
            <select class="input-sm" [(ngModel)]="currentUser.role" name="role" [disabled]="(currentUser.username == 'admin')">
              <option *ngFor="let userRole of userRoles" [ngValue]="userRole">{{('TR_' + userRoleEnum[userRole]) | translate}}</option>
            </select>
	        </div>
	        <div class="form-group">
            <input type="checkbox" [(ngModel)]="currentUser.isactive" name="isactive" #isactive="ngModel" [disabled]="(currentUser.username == 'admin' || currentUser.username == this.authenticationService.userApiConfig?.username)" />&nbsp;{{'TR_IS_ACTIVE' | translate}}
	        </div>
	        <div class="form-group" style="display: inline-block;width:99%; text-align: center;">
		        <div style="display: table-cell;">
		        <button class="btn btn-default" (click)="cancel()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CANCEL' | translate}}</button>
		        </div>
		          <div style="display: table-cell;width:99%"></div>
		        <div style="display: table-cell;">
		        <button type="submit" class="btn btn-default"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_SAVE' | translate}}</button>
		        </div>
	        </div>
	        <alertStatusBarComponent></alertStatusBarComponent>
        </form>
      </div>
	  </div>`
})
export class UserModal implements CloseGuard, ModalComponent<UserModalContext> {
  private context: UserModalContext;
  private currentUser: UserObjectDTO;
  private userRoleEnum = UserRoleEnum;
  private userRoles: UserRoleEnum[] = [];
  private users: UserObjectDTO[] = [];
  private isUserNew: boolean = false;

  constructor(public dialog: DialogRef<UserModalContext>, private alertService: AlertService, private translate: TranslateService, private authenticationService: AuthenticationService) {
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-md";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }

  public ngOnInit(): void {
    this.currentUser = Object.assign({}, this.dialog.context.currentUser);
    this.users = Object.assign({}, this.dialog.context.users);
    this.isUserNew = Object.keys(this.currentUser).length === 0;
    if (this.isUserNew) {
      this.currentUser.isactive = true;
      this.currentUser.role = UserRoleEnum.VIEWER_ROLE;
    }

    this.userRoles.push(UserRoleEnum.SU_ROLE); 
    this.userRoles.push(UserRoleEnum.CONFIGURATOR_ROLE);
    this.userRoles.push(UserRoleEnum.OPERATOR_ROLE);
    this.userRoles.push(UserRoleEnum.VIEWER_ROLE);
  }

  beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private cancel() {
    this.dialog.close(this.dialog.context.currentUser);
  }

  private save() {
    for (var i in this.users["users"]) {
      if (this.users["users"][i].username == this.currentUser.username && this.dialog.context.currentUser == null) {
        this.translate.get("TR_THE_USERNAME_X_IS_ALREADY_USED", { username: this.currentUser.username }).subscribe(res => {
          this.alertService.error(res);
        }); 
        return;
      }
    }

    if (this.currentUser.isactive == null)
      this.currentUser.isactive = false;

    this.dialog.context.currentUser = this.currentUser;

    if (this.dialog.context.currentUser.role == 0) {
      this.alertService.error("TR_PLEASE_SELECT_AT_LEAST_ONE_ROLE");
      return;
    }

    this.dialog.close(this.dialog.context.currentUser);
  }
}

export class UserModalContext extends BSModalContext {
  public currentUser: UserObjectDTO = {};
  public users: UserObjectDTO[] = [];
}