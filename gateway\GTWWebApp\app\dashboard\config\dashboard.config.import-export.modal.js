System.register(["@angular/core", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "../../data/model/models", "../../modules/alert/alert.service", "../../authentication/authentication.service", "@ngx-translate/core", "../../modules/download/download.modal.component", "../../modules/grid/column", "../../global/global.objectToCSV.util"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ngx_modialog_7_1, bootstrap_1, models_1, alert_service_1, authentication_service_1, core_2, download_modal_component_1, column_1, global_objectToCSV_util_1, core_3, DashboardConfigImportExportModal, ImportExportModalContext, ImportExportCSV;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
                core_3 = core_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (download_modal_component_1_1) {
                download_modal_component_1 = download_modal_component_1_1;
            },
            function (column_1_1) {
                column_1 = column_1_1;
            },
            function (global_objectToCSV_util_1_1) {
                global_objectToCSV_util_1 = global_objectToCSV_util_1_1;
            }
        ],
        execute: function () {
            DashboardConfigImportExportModal = (function () {
                function DashboardConfigImportExportModal(dialog, alertService, authenticationService, translate, componentFactoryResolver, injector) {
                    this.dialog = dialog;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.translate = translate;
                    this.componentFactoryResolver = componentFactoryResolver;
                    this.injector = injector;
                    this.importExportResultsSummary = {};
                    this.importExportCSV = ImportExportCSV.EXPORT_POINTS;
                    this.context = dialog.context;
                    this.context.dialogClass = "modal-dialog modal-lg";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                    this.columns = this.getColumns();
                }
                DashboardConfigImportExportModal.prototype.beforeDismiss = function () {
                    return true;
                };
                DashboardConfigImportExportModal.prototype.close = function () {
                    this.dialog.close({ result: true });
                };
                DashboardConfigImportExportModal.prototype.save = function () {
                    var _this = this;
                    if (this.importExportCSV == ImportExportCSV.EXPORT_POINTS || this.importExportCSV == ImportExportCSV.EXPORT_MAPPINGS) {
                        var tagPurposeFilterEnum = void 0;
                        var fileName_1;
                        if (this.importExportCSV == ImportExportCSV.EXPORT_POINTS) {
                            tagPurposeFilterEnum = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV | models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
                            fileName_1 = (this.context.nodeFullName == "" ? "root" : this.context.nodeFullName) + "_points.csv";
                        }
                        else {
                            tagPurposeFilterEnum = models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV | models_1.TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
                            fileName_1 = (this.context.nodeFullName == "" ? "root" : this.context.nodeFullName) + "_mappings.csv";
                        }
                        this.context.tagsService.getTags(this.context.nodeFullName, null, null, null, null, null, null, "MDO", tagPurposeFilterEnum, this.isRecursive, 0, 0, null).subscribe(function (data) {
                            var dynamicComponentFactory = _this.componentFactoryResolver.resolveComponentFactory(download_modal_component_1.DownloadModalComponent);
                            var componentRef = dynamicComponentFactory.create(_this.injector);
                            componentRef.instance.downloadClick(fileName_1, "CurrentWorkspace", _this.context.fileService);
                            _this.alertService.success("TR_FILE_DOWNLOADED");
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR");
                            }
                        });
                    }
                    else if (this.importExportCSV == ImportExportCSV.IMPORT_POINTS) {
                        var element = document.getElementsByClassName("importPointsFile")[0];
                        element.click();
                    }
                    else if (this.importExportCSV == ImportExportCSV.IMPORT_MAPPINGS) {
                        var element = document.getElementsByClassName("importMappingsFile")[0];
                        element.click();
                    }
                };
                DashboardConfigImportExportModal.prototype.copyToCSV = function () {
                    global_objectToCSV_util_1.GlobalObjectToCSVUtil.copyToCSV("ImportResults.csv", this.importExportResults);
                };
                DashboardConfigImportExportModal.prototype.onImportExportFileUploadChange = function (event, importExportCSV) {
                    var _this = this;
                    if (importExportCSV == "IMPORT_POINTS") {
                        var fileList = event.target.files;
                        if (fileList.length > 0) {
                            var file = fileList[0];
                            event.target.value = "";
                            this.context.tagsService.postPoints(file).subscribe(function (data) {
                                if (data != null && data != "") {
                                    var result = data.result;
                                    if (result)
                                        _this.alertService.success("TR_SUCCESS");
                                    else
                                        _this.alertService.error("TR_ERROR");
                                    _this.importExportResults = data.messages;
                                    _this.importExportResultsSummary.mdo_created = data.mdo_created;
                                    _this.importExportResultsSummary.mdo_failed = data.mdo_failed;
                                    _this.importExportResultsSummary.mdo_duplicate = data.mdo_duplicate;
                                    _this.importExportResultsSummary.sdo_created = data.sdo_created;
                                    _this.importExportResultsSummary.sdo_failed = data.sdo_failed;
                                    _this.importExportResultsSummary.sdo_duplicate = data.sdo_duplicate;
                                }
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR");
                                }
                            });
                        }
                    }
                    else if (importExportCSV == "IMPORT_MAPPINGS") {
                        var fileList = event.target.files;
                        if (fileList.length > 0) {
                            var file = fileList[0];
                            this.context.mappingsService.postMappings(file).subscribe(function (data) {
                                if (data != null && data != "") {
                                    var result = data.result;
                                    if (result)
                                        _this.alertService.success("TR_SUCCESS");
                                    else
                                        _this.alertService.error("TR_ERROR");
                                    _this.importExportResults = data.messages;
                                    _this.importExportResultsSummary.mdo_created = data.mdo_created;
                                    _this.importExportResultsSummary.mdo_failed = data.mdo_failed;
                                    _this.importExportResultsSummary.mdo_duplicate = data.mdo_duplicate;
                                    _this.importExportResultsSummary.sdo_created = data.sdo_created;
                                    _this.importExportResultsSummary.sdo_failed = data.sdo_failed;
                                    _this.importExportResultsSummary.sdo_duplicate = data.sdo_duplicate;
                                }
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR");
                                }
                            });
                        }
                    }
                };
                DashboardConfigImportExportModal.prototype.getColumns = function () {
                    return [
                        new column_1.Column("lineNumber", "TR_LINE", "", ""),
                        new column_1.Column("isSuccess", "TR_SUCCESS", "", ""),
                        new column_1.Column("path", "TR_POINT_PATH", "", ""),
                        new column_1.Column("message", "TR_MESSAGE", "action$innerHTML", ""),
                        new column_1.Column("messageDetail", "TR_MESSAGE_DETAIL", "", "")
                    ];
                };
                DashboardConfigImportExportModal.prototype.removeBRFromMessages = function (importExportResults) {
                    importExportResults.forEach(function (result) {
                        result.message.replace("<br/>", "");
                    });
                    return importExportResults;
                };
                __decorate([
                    core_3.ViewChild("uploadFile", { static: false }),
                    __metadata("design:type", core_1.ElementRef)
                ], DashboardConfigImportExportModal.prototype, "uploadFile", void 0);
                DashboardConfigImportExportModal = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigImportExportModal",
                        styles: ["\n      .tag-modal-content {\n\t\t    padding: 10px;\n      }\n      label {\n        margin: 0px 60px 0px 6px;\n      }\n      fieldset {\n        border-radius: 6px;\n        border: 1px Gray solid;\n        margin-bottom: 20px;\n        padding: 0px 8px 8px 8px; \n      }\n      legend {\n        width: auto;\n        margin-bottom: 4px;\n      }\n      .summary {\n        padding: 4px;\n        background-color: whitesmoke;\n        width: 180px;\n        border-radius: 5px;\n        border: 1px gray solid;\n        box-shadow: 1px 1px grey;\n      }\n      .tag-modal-heading {\n        background-color: #d8d8d8;\n        padding: 8px 10px 6px 10px;\n        font-size: 22px;\n        font-weight: bold;\n        border-bottom: 1px solid #a31c3f;\n        overflow-wrap: break-word;\n        border-top-left-radius: 6px;\n        border-top-right-radius: 6px;\n      }\n    "],
                        template: "\n    <div class=\"container-fluid\">\n      <div class=\"tag-modal-heading\">\n       {{'TR_IMPORT_EXPORT_POINTS' | translate}}\n\t    </div>\n      <div class=\"tag-modal-content\">\n        <fieldset><legend>{{'TR_EXPORT' | translate}}</legend>\n          <input name=\"options\" ng-control=\"options\" type=\"radio\" [value]=\"3\"  [(ngModel)]=\"importExportCSV\"><label>{{'TR_EXPORT_POINTS' | translate}}</label>\n          <input name=\"options\" ng-control=\"options\" type=\"radio\" [value]=\"4\" [(ngModel)]=\"importExportCSV\"><label>{{'TR_EXPORT_MAPPINGS' | translate}}</label>\n          <br/>\n          <input ng-control=\"checkbox\" type=\"checkbox\" [(ngModel)]=\"isRecursive\" ><label>{{'TR_EXPORT_RECURSIVELY' | translate}}</label>\n        </fieldset>\n        <fieldset *ngIf=\"(this.context.nodeFullName === '')\"><legend>{{'TR_IMPORT' | translate}}</legend>\n          <input ng-control=\"options\" type=\"radio\" [value]=\"1\"  [(ngModel)]=\"importExportCSV\" ><label>{{'TR_IMPORT_POINTS' | translate}}</label>\n          <input ng-control=\"options\" type=\"radio\" [value]=\"2\" [(ngModel)]=\"importExportCSV\" ><label>{{'TR_IMPORT_MAPPINGS' | translate}}</label>\n\n          <div *ngIf=\"(importExportResults?.length > 0)\">\n            <b>{{'TR_RESULTS' | translate}}</b>\n            <div style=\"font-size: 10px; display: flex; font-weight: bold; border-radius: 25px;\">\n              <div class=\"summary\" style=\"margin-right: 20px\">\n                <span style=\"color: green\">{{'TR_MDO_CREATED' | translate}}: {{importExportResultsSummary.mdo_created}}</span><br>\n                <span style=\"color: orange\">{{'TR_MDO_DUPLICATED' | translate}}: {{importExportResultsSummary.mdo_duplicate}}</span><br>\n                <span style=\"color: red\">{{'TR_MDO_FAILED' | translate}}: {{importExportResultsSummary.mdo_failed}}<br></span>\n              </div>\n              <div class=\"summary\">\n                <span style=\"color: green\">{{'TR_SDO_CREATED' | translate}}: {{importExportResultsSummary.sdo_created}}</span><br>\n                <span style=\"color: orange\">{{'TR_SDO_DUPLICATED' | translate}}: {{importExportResultsSummary.sdo_duplicate}}</span><br>\n                <span style=\"color: red\">{{'TR_SDO_FAILED' | translate}}: {{importExportResultsSummary.sdo_failed}}<br></span>\n              </div>\n            </div>\n            <div style=\"max-height: 500px; overflow-y: scroll;\">\n\t\t          <gridComponent [rows]=\"importExportResults\" [columns]=\"columns\"></gridComponent>\n            </div>\n            <button class=\"btn btn-default btn-sm copy-button\" (click)=\"copyToCSV()\"><img src=\"../../images/download.svg\" class=\"image-button\"/>&nbsp;{{'TR_DOWNLOAD_RESULTS' | translate}}</button>\n          </div>\n        </fieldset>\n      </div>\n      <div style=\"display: inline-block; width:96%; text-align: center; margin: 10px;\">\n      <div class=\"form-group\" style=\"display:inline-block; width:92%; text-align:center;\">\n\t      <div style=\"display: table-cell;\">\n\t\t      <button class=\"btn btn-default\" (click)=\"close()\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CLOSE' | translate}}</button>\n\t      </div>\n\t      <div style=\"display: table-cell;width:90%\"></div>\n\t\t    <div style=\"display: table-cell;\" *ngIf=\"(importExportCSV == 1 || importExportCSV == 2)\">\n\t\t\t    <button class=\"btn btn-default\" (click)=\"save()\"><img src=\"../../images/ok.svg\" class=\"image-button\"/>&nbsp;{{'TR_SELECT_FILE' | translate}}</button>\n\t\t    </div>\n\t\t    <div style=\"display: table-cell;\" *ngIf=\"(importExportCSV == 3 || importExportCSV == 4)\">\n\t\t\t    <button class=\"btn btn-default\" (click)=\"save()\"><img src=\"../../images/ok.svg\" class=\"image-button\"/>&nbsp;{{'TR_EXPORT' | translate}}</button>\n\t\t    </div>\n\t    </div>\n\t    <alertStatusBarComponent></alertStatusBarComponent>\n      <input class=\"importPointsFile\" #uploadFile style=\"display: none;\" type=\"file\" accept=\".csv\" (change)=\"onImportExportFileUploadChange($event, 'IMPORT_POINTS')\">\n      <input class=\"importMappingsFile\" #uploadFile style=\"display: none;\" type=\"file\" accept=\".csv\" (change)=\"onImportExportFileUploadChange($event, 'IMPORT_MAPPINGS')\">\n    </div>"
                    }),
                    __metadata("design:paramtypes", [ngx_modialog_7_1.DialogRef, alert_service_1.AlertService,
                        authentication_service_1.AuthenticationService, core_2.TranslateService, core_1.ComponentFactoryResolver, core_1.Injector])
                ], DashboardConfigImportExportModal);
                return DashboardConfigImportExportModal;
            }());
            exports_1("DashboardConfigImportExportModal", DashboardConfigImportExportModal);
            ImportExportModalContext = (function (_super) {
                __extends(ImportExportModalContext, _super);
                function ImportExportModalContext() {
                    var _this = _super !== null && _super.apply(this, arguments) || this;
                    _this.nodeFullName = "";
                    _this.tagsService = null;
                    _this.mappingsService = null;
                    _this.fileService = null;
                    return _this;
                }
                return ImportExportModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("ImportExportModalContext", ImportExportModalContext);
            (function (ImportExportCSV) {
                ImportExportCSV[ImportExportCSV["NONE"] = 0] = "NONE";
                ImportExportCSV[ImportExportCSV["IMPORT_POINTS"] = 1] = "IMPORT_POINTS";
                ImportExportCSV[ImportExportCSV["IMPORT_MAPPINGS"] = 2] = "IMPORT_MAPPINGS";
                ImportExportCSV[ImportExportCSV["EXPORT_POINTS"] = 3] = "EXPORT_POINTS";
                ImportExportCSV[ImportExportCSV["EXPORT_MAPPINGS"] = 4] = "EXPORT_MAPPINGS";
            })(ImportExportCSV || (ImportExportCSV = {}));
            exports_1("ImportExportCSV", ImportExportCSV);
        }
    };
});
//# sourceMappingURL=dashboard.config.import-export.modal.js.map