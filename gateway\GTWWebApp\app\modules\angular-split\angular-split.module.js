System.register(["@angular/core", "@angular/common", "./split.component", "./splitArea.directive", "./splitGutter.directive"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, common_1, split_component_1, splitArea_directive_1, splitGutter_directive_1, AngularSplitModule;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (common_1_1) {
                common_1 = common_1_1;
            },
            function (split_component_1_1) {
                split_component_1 = split_component_1_1;
            },
            function (splitArea_directive_1_1) {
                splitArea_directive_1 = splitArea_directive_1_1;
            },
            function (splitGutter_directive_1_1) {
                splitGutter_directive_1 = splitGutter_directive_1_1;
            }
        ],
        execute: function () {
            AngularSplitModule = (function () {
                function AngularSplitModule() {
                }
                AngularSplitModule = __decorate([
                    core_1.NgModule({
                        imports: [
                            common_1.CommonModule
                        ],
                        declarations: [
                            split_component_1.SplitComponent,
                            splitArea_directive_1.SplitAreaDirective,
                            splitGutter_directive_1.SplitGutterDirective
                        ],
                        exports: [
                            split_component_1.SplitComponent,
                            splitArea_directive_1.SplitAreaDirective,
                            splitGutter_directive_1.SplitGutterDirective
                        ]
                    })
                ], AngularSplitModule);
                return AngularSplitModule;
            }());
            exports_1("AngularSplitModule", AngularSplitModule);
        }
    };
});
//# sourceMappingURL=angular-split.module.js.map