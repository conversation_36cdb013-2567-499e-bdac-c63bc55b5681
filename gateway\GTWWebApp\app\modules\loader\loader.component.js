System.register(["@angular/core", "./loader.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, loader_service_1, LoaderComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (loader_service_1_1) {
                loader_service_1 = loader_service_1_1;
            }
        ],
        execute: function () {
            LoaderComponent = (function () {
                function LoaderComponent(loaderService) {
                    var _this = this;
                    this.loaderService = loaderService;
                    this.isLoading = false;
                    this.isLoadingChangeSubscriptionShow = this.loaderService.isLoadingChange.subscribe(function (value) { _this.isLoading = value; });
                }
                LoaderComponent.prototype.ngOnDestroy = function () {
                    this.isLoadingChangeSubscriptionShow.unsubscribe();
                };
                LoaderComponent = __decorate([
                    core_1.Component({
                        selector: "loaderComponent",
                        template: "\n    <div *ngIf=\"this.isLoading\" class=\"block-click\">\n      <div class=\"center-loader\">\n        <img src=\"./images/GTWLogo.png\" class=\"logo-loader\">\n        <div class=\"loader\"></div>\n      </div>\n    </div>\n  "
                    }),
                    __metadata("design:paramtypes", [loader_service_1.LoaderService])
                ], LoaderComponent);
                return LoaderComponent;
            }());
            exports_1("LoaderComponent", LoaderComponent);
        }
    };
});
//# sourceMappingURL=loader.component.js.map