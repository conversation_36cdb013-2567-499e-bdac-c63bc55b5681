﻿import { Component, OnInit, isDevMode } from "@angular/core";
import { GlobalDataService } from "../global/global.data.service";

@Component({
		selector: "helpComponent",
		templateUrl: "app/help/help.component.template.html",
})

export class HelpComponent implements OnInit {
  private isInDevMode: boolean = false;

  constructor(private globalDataService: GlobalDataService) {}

  public ngOnInit(): void {
    this.isInDevMode = isDevMode();
  }

  private quickStartShowHideOnChange(): void {
    this.globalDataService.isQuickStartVisible = !this.globalDataService.isQuickStartVisible;
  }
}