System.register(["@angular/core", "@angular/router"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, router_1, AppBlankComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            }
        ],
        execute: function () {
            AppBlankComponent = (function () {
                function AppBlankComponent(router) {
                    this.router = router;
                }
                AppBlankComponent.prototype.goToDashboard = function () {
                    this.router.navigate(["/dashboard"]);
                };
                AppBlankComponent = __decorate([
                    core_1.Component({
                        selector: 'appBlankComponent',
                        template: "\n\t\t\t<div class=\"center\">\n\t\t\t\t<button class=\"btn btn-default\" (click)=\"goToDashboard()\">{{'TR_GO_TO_DASHBOARD' | translate}}</button>\n\t\t\t</div>\n\t\t",
                        styles: ["\n\t\t.center {\n\t\t\tborder: 3px solid #a31c3f;\n\t\t\tpadding: 40px;\n\t\t\tposition: absolute;\n\t\t\ttop: 20%;\n\t\t\tright: 46%;\n\t\t\tbackground-color: gray;\n\t\t\topacity: 0.8;\n\t\t}"]
                    }),
                    __metadata("design:paramtypes", [router_1.Router])
                ], AppBlankComponent);
                return AppBlankComponent;
            }());
            exports_1("AppBlankComponent", AppBlankComponent);
        }
    };
});
//# sourceMappingURL=app.blank.component.js.map