System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, GTWImagePipe;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            GTWImagePipe = (function () {
                function GTWImagePipe() {
                }
                GTWImagePipe.prototype.transform = function (nodeIcon) {
                    var Icon;
                    (function (Icon) {
                        Icon[Icon["IDI_MDO_DATAOBJECT"] = 509] = "IDI_MDO_DATAOBJECT";
                        Icon[Icon["IDI_INTERNAL_PORT"] = 511] = "IDI_INTERNAL_PORT";
                        Icon[Icon["IDI_EQUATION"] = 516] = "IDI_EQUATION";
                        Icon[Icon["IDI_PORT"] = 524] = "IDI_PORT";
                        Icon[Icon["IDI_SESSION"] = 525] = "IDI_SESSION";
                        Icon[Icon["IDI_GATEWAY"] = 526] = "IDI_GATEWAY";
                        Icon[Icon["IDI_UNDEFINED"] = 527] = "IDI_UNDEFINED";
                        Icon[Icon["IDI_OPC_CLIENT"] = 528] = "IDI_OPC_CLIENT";
                        Icon[Icon["IDI_SECTOR"] = 529] = "IDI_SECTOR";
                        Icon[Icon["IDI_DATAOBJECT"] = 531] = "IDI_DATAOBJECT";
                        Icon[Icon["IDI_DATATYPE"] = 532] = "IDI_DATATYPE";
                        Icon[Icon["IDI_INTERNAL_DATA_OBJECT"] = 533] = "IDI_INTERNAL_DATA_OBJECT";
                        Icon[Icon["IDI_SDO_DATAOBJECT"] = 534] = "IDI_SDO_DATAOBJECT";
                        Icon[Icon["IDI_INTERNAL_BDO"] = 539] = "IDI_INTERNAL_BDO";
                        Icon[Icon["IDI_OPC_SERVER"] = 565] = "IDI_OPC_SERVER";
                        Icon[Icon["IDI_MODEM_POOLS"] = 570] = "IDI_MODEM_POOLS";
                        Icon[Icon["IDI_MODEM_POOL"] = 571] = "IDI_MODEM_POOL";
                        Icon[Icon["IDI_MODEM"] = 572] = "IDI_MODEM";
                        Icon[Icon["IDI_INTERNAL_USER_DATA_OBJECT"] = 573] = "IDI_INTERNAL_USER_DATA_OBJECT";
                        Icon[Icon["IDI_DNP_PROTOTYPE"] = 1002] = "IDI_DNP_PROTOTYPE";
                        Icon[Icon["IDI_61850_MDO"] = 1003] = "IDI_61850_MDO";
                        Icon[Icon["IDI_61850_CLIENT"] = 1006] = "IDI_61850_CLIENT";
                        Icon[Icon["IDI_61850_REPORT"] = 1007] = "IDI_61850_REPORT";
                        Icon[Icon["IDI_61850_POLLED_DATA_SET"] = 1008] = "IDI_61850_POLLED_DATA_SET";
                        Icon[Icon["IDI_OPC_AE_MDO"] = 1043] = "IDI_OPC_AE_MDO";
                        Icon[Icon["IDI_DNP_DATA_ELEMENT"] = 1045] = "IDI_DNP_DATA_ELEMENT";
                        Icon[Icon["IDI_ALARM"] = 1052] = "IDI_ALARM";
                        Icon[Icon["IDI_OPC_AE_CLIENT"] = 1053] = "IDI_OPC_AE_CLIENT";
                        Icon[Icon["IDI_TASE2_CLIENT"] = 1058] = "IDI_TASE2_CLIENT";
                        Icon[Icon["IDI_DNP_DESCRIPTOR"] = 1060] = "IDI_DNP_DESCRIPTOR";
                        Icon[Icon["IDI_61850_POLLED_POINT_SET"] = 1061] = "IDI_61850_POLLED_POINT_SET";
                        Icon[Icon["IDI_61850_SERVER"] = 1064] = "IDI_61850_SERVER";
                        Icon[Icon["IDI_LOGICAL_DEVICE"] = 1075] = "IDI_LOGICAL_DEVICE";
                        Icon[Icon["IDI_OPC_MDO"] = 1082] = "IDI_OPC_MDO";
                        Icon[Icon["IDI_TASE2_SERVER"] = 1084] = "IDI_TASE2_SERVER";
                        Icon[Icon["IDI_61850_REPORT1"] = 1090] = "IDI_61850_REPORT1";
                        Icon[Icon["IDI_ICON1"] = 1091] = "IDI_ICON1";
                        Icon[Icon["IDI_ICON2"] = 1092] = "IDI_ICON2";
                        Icon[Icon["IDI_61850_GOOSE"] = 1095] = "IDI_61850_GOOSE";
                        Icon[Icon["IDI_ICON3"] = 1100] = "IDI_ICON3";
                        Icon[Icon["IDI_ALARMS"] = 1102] = "IDI_ALARMS";
                        Icon[Icon["IDI_ODBC_CLIENT"] = 1104] = "IDI_ODBC_CLIENT";
                        Icon[Icon["IDI_ODBC_QUERY"] = 1106] = "IDI_ODBC_QUERY";
                        Icon[Icon["IDI_ODBC"] = 1932] = "IDI_ODBC";
                        Icon[Icon["IDI_ODBC_CLIENT1"] = 1933] = "IDI_ODBC_CLIENT1";
                        Icon[Icon["IDI_TASE2_MDO"] = 2219] = "IDI_TASE2_MDO";
                        Icon[Icon["IDI_TASE2_REPORT"] = 2220] = "IDI_TASE2_REPORT";
                        Icon[Icon["IDI_TASE2_POLLED_POINT_SET"] = 2221] = "IDI_TASE2_POLLED_POINT_SET";
                        Icon[Icon["IDI_TASE2_POLLED_DATA_SET"] = 2222] = "IDI_TASE2_POLLED_DATA_SET";
                        Icon[Icon["IDI_61850_CLIENT_NODE"] = 2223] = "IDI_61850_CLIENT_NODE";
                        Icon[Icon["IDI_TASE2_CLIENT_NODE"] = 2224] = "IDI_TASE2_CLIENT_NODE";
                        Icon[Icon["IDI_USER_FOLDER"] = 999] = "IDI_USER_FOLDER";
                    })(Icon || (Icon = {}));
                    if (nodeIcon == Icon.IDI_INTERNAL_USER_DATA_OBJECT)
                        return "internalUserDataObject.svg";
                    if (nodeIcon == Icon.IDI_INTERNAL_BDO)
                        return "internal.svg";
                    if (nodeIcon == Icon.IDI_INTERNAL_DATA_OBJECT)
                        return "internal.svg";
                    if (nodeIcon == Icon.IDI_EQUATION)
                        return "equation.svg";
                    if (nodeIcon == Icon.IDI_DATATYPE)
                        return "dataType.svg";
                    if (nodeIcon == Icon.IDI_SDO_DATAOBJECT)
                        return "sdoData.svg";
                    if (nodeIcon == Icon.IDI_MDO_DATAOBJECT)
                        return "mdoData.svg";
                    if (nodeIcon == Icon.IDI_DATAOBJECT)
                        return "internal.svg";
                    if (nodeIcon == Icon.IDI_MODEM)
                        return "modem.svg";
                    if (nodeIcon == Icon.IDI_MODEM_POOL)
                        return "modem_pool.svg";
                    if (nodeIcon == Icon.IDI_MODEM_POOLS)
                        return "modem_pools.svg";
                    if (nodeIcon == Icon.IDI_OPC_SERVER)
                        return "opcServer.svg";
                    if (nodeIcon == Icon.IDI_OPC_CLIENT)
                        return "opcClient.svg";
                    if (nodeIcon == Icon.IDI_OPC_MDO)
                        return "opcMDO.svg";
                    if (nodeIcon == Icon.IDI_OPC_AE_CLIENT)
                        return "opcAEClient.svg";
                    if (nodeIcon == Icon.IDI_OPC_AE_MDO)
                        return "opcAEMDO.svg";
                    if (nodeIcon == Icon.IDI_SECTOR)
                        return "sector.svg";
                    if (nodeIcon == Icon.IDI_SESSION)
                        return "session.svg";
                    if (nodeIcon == Icon.IDI_INTERNAL_PORT)
                        return "internalPort.svg";
                    if (nodeIcon == Icon.IDI_PORT)
                        return "port.svg";
                    if (nodeIcon == Icon.IDI_GATEWAY)
                        return "gateway.png";
                    if (nodeIcon == Icon.IDI_UNDEFINED)
                        return "undefine.svg";
                    if (nodeIcon == Icon.IDI_DNP_DATA_ELEMENT)
                        return "dnpDataElement.svg";
                    if (nodeIcon == Icon.IDI_DNP_PROTOTYPE)
                        return "dnpPrototype.svg";
                    if (nodeIcon == Icon.IDI_DNP_DESCRIPTOR)
                        return "dnpDescriptor.svg";
                    if (nodeIcon == Icon.IDI_61850_SERVER)
                        return "61850Server.svg";
                    if (nodeIcon == Icon.IDI_61850_CLIENT)
                        return "61850Client.svg";
                    if (nodeIcon == Icon.IDI_61850_REPORT)
                        return "61850Report.svg";
                    if (nodeIcon == Icon.IDI_61850_MDO)
                        return "61850MDO.svg";
                    if (nodeIcon == Icon.IDI_61850_POLLED_DATA_SET)
                        return "61850PolledDataSet.svg";
                    if (nodeIcon == Icon.IDI_61850_POLLED_POINT_SET)
                        return "61850PolledPointSet.svg";
                    if (nodeIcon == Icon.IDI_61850_GOOSE)
                        return "61850Goose.svg";
                    if (nodeIcon == Icon.IDI_ODBC_CLIENT)
                        return "odbcClient.svg";
                    if (nodeIcon == Icon.IDI_ODBC_QUERY)
                        return "odbcQuery.svg";
                    if (nodeIcon == Icon.IDI_TASE2_SERVER)
                        return "tase2Server.svg";
                    if (nodeIcon == Icon.IDI_TASE2_CLIENT)
                        return "tase2Client.svg";
                    if (nodeIcon == Icon.IDI_TASE2_REPORT)
                        return "61850Report.svg";
                    if (nodeIcon == Icon.IDI_TASE2_MDO)
                        return "61850MDO.svg";
                    if (nodeIcon == Icon.IDI_TASE2_POLLED_DATA_SET)
                        return "61850PolledDataSet.svg";
                    if (nodeIcon == Icon.IDI_TASE2_POLLED_POINT_SET)
                        return "61850PolledPointSet.svg";
                    if (nodeIcon == Icon.IDI_LOGICAL_DEVICE)
                        return "tase2LogicalDevice.svg";
                    if (nodeIcon == Icon.IDI_ALARMS)
                        return "alarms2.svg";
                    if (nodeIcon == Icon.IDI_ALARM)
                        return "alarms.svg";
                    if (nodeIcon == Icon.IDI_61850_CLIENT_NODE)
                        return "61850ClientNode.svg";
                    if (nodeIcon == Icon.IDI_TASE2_CLIENT_NODE)
                        return "tase2ClientNode.svg";
                    if (nodeIcon == Icon.IDI_USER_FOLDER)
                        return "userFolder.svg";
                    return "undefine.svg";
                };
                GTWImagePipe = __decorate([
                    core_1.Pipe({ name: 'gtwImage' })
                ], GTWImagePipe);
                return GTWImagePipe;
            }());
            exports_1("GTWImagePipe", GTWImagePipe);
        }
    };
});
//# sourceMappingURL=gtwImage.pipe.js.map