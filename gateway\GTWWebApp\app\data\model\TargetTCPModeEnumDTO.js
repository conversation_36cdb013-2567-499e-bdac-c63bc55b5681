System.register([], function (exports_1, context_1) {
    "use strict";
    var TargetTCPModeEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (TargetTCPModeEnumDTO) {
                TargetTCPModeEnumDTO[TargetTCPModeEnumDTO["SERVER"] = 'TMWTARGTCP_MODE_SERVER'] = "SERVER";
                TargetTCPModeEnumDTO[TargetTCPModeEnumDTO["CLIENT"] = 'TMWTARGTCP_MODE_CLIENT'] = "CLIENT";
                TargetTCPModeEnumDTO[TargetTCPModeEnumDTO["UDP"] = 'TMWTARGTCP_MODE_UDP'] = "UDP";
                TargetTCPModeEnumDTO[TargetTCPModeEnumDTO["DUALENDPOINT"] = 'TMWTARGTCP_MODE_DUAL_ENDPOINT'] = "DUALENDPOINT";
            })(TargetTCPModeEnumDTO || (TargetTCPModeEnumDTO = {}));
            exports_1("TargetTCPModeEnumDTO", TargetTCPModeEnumDTO);
        }
    };
});
//# sourceMappingURL=TargetTCPModeEnumDTO.js.map