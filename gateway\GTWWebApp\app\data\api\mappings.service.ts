/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { MappingObjectDTO } from '../model/mappingObjectDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class MappingsService {

    public basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Get sdo to mdo mappings based on filter.
     * 
     * @param nodeFullName node name/path (i.e. mmb).  Mappings will be obtained starting at this node. Empty string starts at the root node.
     * @param startIndex 1 based start index number of mappings to get (0 for not specified)
     * @param endIndex 1 based last index number of mappings to get (0 for not specified)
     * @param nameFilter returns items that contain this string (use empty string for no filter).
     * @param recursive get mappings recursivly
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getMappings(nodeFullName?: string, startIndex?: number, endIndex?: number, nameFilter?: string, recursive?: boolean, observe?: 'body', reportProgress?: boolean): Observable<MappingObjectDTO>;
    public getMappings(nodeFullName?: string, startIndex?: number, endIndex?: number, nameFilter?: string, recursive?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<MappingObjectDTO>>;
    public getMappings(nodeFullName?: string, startIndex?: number, endIndex?: number, nameFilter?: string, recursive?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<MappingObjectDTO>>;
    public getMappings(nodeFullName?: string, startIndex?: number, endIndex?: number, nameFilter?: string, recursive?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {






        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (nodeFullName !== undefined && nodeFullName !== null) {
            queryParameters = queryParameters.set('nodeFullName', <any>nodeFullName);
        }
        if (startIndex !== undefined && startIndex !== null) {
            queryParameters = queryParameters.set('startIndex', <any>startIndex);
        }
        if (endIndex !== undefined && endIndex !== null) {
            queryParameters = queryParameters.set('endIndex', <any>endIndex);
        }
        if (nameFilter !== undefined && nameFilter !== null) {
            queryParameters = queryParameters.set('nameFilter', <any>nameFilter);
        }
        if (recursive !== undefined && recursive !== null) {
            queryParameters = queryParameters.set('recursive', <any>recursive);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<MappingObjectDTO>(`${this.basePath}/mappings`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * upload a mapping csv file
     * 
     * @param file file
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public postMappings(file: Blob, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public postMappings(file: Blob, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public postMappings(file: Blob, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public postMappings(file: Blob, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (file === null || file === undefined) {
            throw new Error('Required parameter file was null or undefined when calling postMappings.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (file !== undefined) {
            formParams = formParams.append('file', <any>file) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/mappings`,
            convertFormParamsToString ? formParams.toString() : formParams,
          {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Removes an existing mapping.
     * Removes an existing mapping.
     * @param mdoName master Name
     * @param sdoName slave Name
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public removeMapping(mdoName: string, sdoName: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public removeMapping(mdoName: string, sdoName: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public removeMapping(mdoName: string, sdoName: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public removeMapping(mdoName: string, sdoName: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (mdoName === null || mdoName === undefined) {
            throw new Error('Required parameter mdoName was null or undefined when calling removeMapping.');
        }

        if (sdoName === null || sdoName === undefined) {
            throw new Error('Required parameter sdoName was null or undefined when calling removeMapping.');
        }

        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (mdoName !== undefined && mdoName !== null) {
            queryParameters = queryParameters.set('mdoName', <any>mdoName);
        }
        if (sdoName !== undefined && sdoName !== null) {
            queryParameters = queryParameters.set('sdoName', <any>sdoName);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];

        return this.httpClient.delete<any>(`${this.basePath}/mappings`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
