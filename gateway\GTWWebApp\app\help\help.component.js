System.register(["@angular/core", "../global/global.data.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, global_data_service_1, HelpComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            }
        ],
        execute: function () {
            HelpComponent = (function () {
                function HelpComponent(globalDataService) {
                    this.globalDataService = globalDataService;
                    this.isInDevMode = false;
                }
                HelpComponent.prototype.ngOnInit = function () {
                    this.isInDevMode = core_1.isDevMode();
                };
                HelpComponent.prototype.quickStartShowHideOnChange = function () {
                    this.globalDataService.isQuickStartVisible = !this.globalDataService.isQuickStartVisible;
                };
                HelpComponent = __decorate([
                    core_1.Component({
                        selector: "helpComponent",
                        templateUrl: "app/help/help.component.template.html",
                    }),
                    __metadata("design:paramtypes", [global_data_service_1.GlobalDataService])
                ], HelpComponent);
                return HelpComponent;
            }());
            exports_1("HelpComponent", HelpComponent);
        }
    };
});
//# sourceMappingURL=help.component.js.map