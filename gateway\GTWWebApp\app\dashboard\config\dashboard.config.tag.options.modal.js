System.register(["@angular/core", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "../../modules/alert/alert.service", "@ngx-translate/core"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ngx_modialog_7_1, bootstrap_1, alert_service_1, core_2, DashboardConfigTagOptionsModal, TagOptionsModalContext, TagOption;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            }
        ],
        execute: function () {
            DashboardConfigTagOptionsModal = (function () {
                function DashboardConfigTagOptionsModal(dialog, alertService, translate) {
                    this.dialog = dialog;
                    this.alertService = alertService;
                    this.translate = translate;
                    this.tagOptionList = [];
                    this.isDataLoaded = false;
                    this.context = dialog.context;
                    this.context.dialogClass = "modal-dialog modal-md";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                }
                DashboardConfigTagOptionsModal.prototype.ngOnInit = function () {
                    this.tagOptionList = TagOption.initTagOptionList(this.dialog.context.controlSource, this.dialog.context.tagOptionsValue);
                    this.isDataLoaded = true;
                };
                DashboardConfigTagOptionsModal.prototype.optionMaskOnChange = function (value, tagOptionBitMask) {
                    tagOptionBitMask.value = value;
                };
                DashboardConfigTagOptionsModal.prototype.optionScaleOnChange = function (event, tagOption, pos) {
                    tagOption.value[pos] = event.target.value;
                };
                DashboardConfigTagOptionsModal.prototype.optionMaskOnInit = function (optionMaskList, choices) {
                    optionMaskList.push({ name: "GTWDEFS_UPDTRSN_NONE", value: 0x00000000, description: "TR_GTWDEFS_UPDTRSN_NONE_DESC", isChecked: false });
                    var jsonChoices = JSON.parse(choices);
                    jsonChoices.forEach(function (choice) {
                        optionMaskList.push({ name: choice.name, value: choice.value, description: choice.description, isChecked: false });
                    });
                };
                DashboardConfigTagOptionsModal.prototype.beforeDismiss = function () {
                    return true;
                };
                DashboardConfigTagOptionsModal.prototype.changeOptionValue = function (tagOptionName, tagOptionGroupName) {
                    this.tagOptionList.forEach(function (option) {
                        if (tagOptionName != null && tagOptionGroupName != null && option.optionType == "PARSED_OPTION_BOOL_CHOICE_VALUE") {
                            if (option.name == tagOptionName && option.choices == tagOptionGroupName)
                                option.value = option.valueToUse;
                            else if (option.name != tagOptionName && option.choices == tagOptionGroupName)
                                option.value = null;
                        }
                    });
                };
                DashboardConfigTagOptionsModal.prototype.cancel = function () {
                    this.dialog.close(null);
                };
                DashboardConfigTagOptionsModal.prototype.save = function () {
                    var _this = this;
                    var hasError = false;
                    var optionItemList = [];
                    this.tagOptionList.forEach(function (option) {
                        if (option.value != null) {
                            if (option.optionType == "PARSED_OPTION_BOOL_VALUE" && option.value != false) {
                                optionItemList.push([option.name, ""]);
                            }
                            else if (option.optionType == "PARSED_OPTION_BOOL_CHOICE_VALUE" && option.value != false) {
                                optionItemList.push([option.name, ""]);
                            }
                            else if (option.optionType == "PARSED_OPTION_SCALE_VALUE" && option.value != false && option.value.indexOf("") === -1 && option.value.length == 4) {
                                try {
                                    var rawMin = parseFloat(option.value[0]);
                                    var rawMax = parseFloat(option.value[1]);
                                    var engineeringMin = parseFloat(option.value[2]);
                                    var engineeringMax = parseFloat(option.value[3]);
                                    if (rawMin > rawMax || engineeringMin > engineeringMax) {
                                        _this.alertService.error("TR_ERROR_SCALE_OPTION_IS_INCORRECT");
                                        hasError = true;
                                    }
                                    else {
                                        optionItemList.push([option.name, option.value.join(" ")]);
                                    }
                                }
                                catch (e) {
                                    _this.alertService.error("TR_ERROR_SCALE_INCORRECT");
                                    hasError = true;
                                }
                            }
                            else if (option.optionType == "PARSED_OPTION_SCALE_VALUE" && option.value != false && option.value.length != 4) {
                                _this.alertService.error("TR_ERROR_SCALE_OPTION_IS_INCORRECT");
                                hasError = true;
                            }
                            else if (option.optionType != "PARSED_OPTION_SCALE_VALUE" && option.value != false) {
                                optionItemList.push([option.name, option.value]);
                            }
                            else if (option.optionType == "PARSED_OPTION_CHOICE_VALUE") {
                                optionItemList.push([option.name, option.value]);
                            }
                        }
                    });
                    if (!hasError) {
                        this.alertService.clearMessage();
                        this.dialog.close(optionItemList);
                    }
                };
                DashboardConfigTagOptionsModal.prototype.onlyNumericChar = function (event, editorField) {
                    var input = String.fromCharCode(event.keyCode);
                    if ((/[^-.0-9]/.test(input))) {
                        event.preventDefault();
                        return false;
                    }
                };
                DashboardConfigTagOptionsModal = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagOptionsModal",
                        styles: ["\n    .table{\n      margin-bottom: 0px;\n      width: 90%;\n      max-width: 90%;\n      margin-left: 10px;\n    }\n    .table>tr>td,\n    .table>tr>th{\n\t\t  padding: 2px !important;\n      vertical-align: middle !important;\n      line-height: 12px;\n    }\n    .tag-options-modal-container {\n      margin-top: 240px;\n    }\n    .input-text-xs {\n      height: 20px;\n      font-size: 11px;\n      line-height: 14px;\n    }\n    .input-number-xs{\n      height: 20px;\n      width: 100px;\n      font-size: 11px;\n      line-height: 14px;\n    }\n    .select-xs{\n      height: 26px;\n      font-size: 11px;\n      line-height: 14px;\n    }\n    .inline {\n      display: inline-block;\n    }\n    fieldset {\n      border-radius: 6px;\n      border: 1px LightGray solid;\n      padding: 0px 6px 0px 6px;\n      margin-bottom: 10px;\n    }\n    legend{\n      width: inherit;\n      font-size: 11px !important;\n      padding: 0 10px;\n      border-bottom: none;\n      margin-bottom: 4px;\n      font-weight: bold;\n      font-size: 15px;\n    }\n    .tag-modal-heading {\n      background-color: #d8d8d8;\n      padding: 8px 10px 6px 10px;\n      font-size: 22px;\n      font-weight: bold;\n      border-bottom: 1px solid #a31c3f;\n      overflow-wrap: break-word;\n\t\t\tcursor: move;\n      border-top-left-radius: 6px;\n      border-top-right-radius: 6px;\n    }\n  "],
                        template: "\n\t<div class=\"container-fluid tag-options-modal-container\" *ngIf=\"isDataLoaded\">\n    <div class=\"tag-modal-heading\">\n       {{'TR_OPTIONS_EDITOR' | translate}}\n\t    </div> \n      <table class=\"table\">\n          <tr *ngFor=\"let tagOption of tagOptionList\">\n            <td><label title=\"{{tagOption.helpTR | translateKey:tagOption.helpText:translate}}\">{{tagOption.name}}</label></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_MASK_VALUE')\"><div style=\"font-size: 11px;\"><bitmaskComponent [bitmaskString]=\"tagOption.value\" (bitmaskOnChange)=\"optionMaskOnChange($event, tagOption)\" (bitmaskOnInit)=\"optionMaskOnInit($event, tagOption.choices)\"></bitmaskComponent></div></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_BOOL_VALUE')\"><input type=\"checkbox\" class=\"form-check\" [(ngModel)]=\"tagOption.value\"/></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_MDO')\"><input type=\"text\" class=\"form-control input-text-xs\" [(ngModel)]=\"tagOption.value\"/></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_CHOICE_VALUE')\">\n\t\t\t        <select class=\"form-control select-xs\" [(ngModel)]=\"tagOption.value\">\n\t\t\t\t          <option *ngFor=\"let choice of tagOption.choices\" [ngValue]=\"choice.value.toString()\">{{choice.name}}</option>\n\t\t\t        </select>\n            </td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_BOOL_CHOICE_VALUE')\"><input [name]=\"tagOption.choices\" ng-control=\"options\" type=\"radio\" [value]=\"tagOption.name\" [checked]=\"tagOption.value == tagOption.valueToUse || !tagOption.valueToUse\" (click)=\"changeOptionValue(tagOption.name, tagOption.choices)\"></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_SCALE_VALUE')\">\n              <fieldset>\n                <legend>{{'TR_RAW_UNITS_RANGE' | translate}}</legend>\n                <div><div class=\"inline\">{{'TR_MIN' | translate}}</div>&nbsp;&nbsp;<div class=\"inline\"><input type=\"number\" class=\"form-control input-number-xs\" (change)=\"optionScaleOnChange($event, tagOption, 0)\" min=\"0\" [(ngModel)]=\"tagOption.value[0]\" (keypress)=\"onlyNumericChar($event, editorField)\"/></div></div>\n                <div><div class=\"inline\">{{'TR_MAX' | translate}}</div>&nbsp;&nbsp;<div class=\"inline\"><input type=\"number\" class=\"form-control input-number-xs\" (change)=\"optionScaleOnChange($event, tagOption, 1)\" min=\"0\" [(ngModel)]=\"tagOption.value[1]\" (keypress)=\"onlyNumericChar($event, editorField)\"/></div></div>\n              </fieldset>\n              <fieldset>\n                <legend>{{'TR_ENGINEERING_UNITS_RANGE' | translate}}</legend>\n                <div><div class=\"inline\">{{'TR_MIN' | translate}}</div>&nbsp;&nbsp;<div class=\"inline\"><input type=\"number\" class=\"form-control input-number-xs\" (change)=\"optionScaleOnChange($event, tagOption, 2)\" min=\"0\" [(ngModel)]=\"tagOption.value[2]\" (keypress)=\"onlyNumericChar($event, editorField)\"/></div></div>\n                <div><div class=\"inline\">{{'TR_MAX' | translate}}</div>&nbsp;&nbsp;<div class=\"inline\"><input type=\"number\" class=\"form-control input-number-xs\" (change)=\"optionScaleOnChange($event, tagOption, 3)\" min=\"0\" [(ngModel)]=\"tagOption.value[3]\" (keypress)=\"onlyNumericChar($event, editorField)\"/></div></div>\n              </fieldset>\n            </td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_FLOAT_DECIMAL_VALUE')\"><input type=\"number\" class=\"form-control input-number-xs\" [(ngModel)]=\"tagOption.value\" (keypress)=\"onlyNumericChar($event, editorField)\"/></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_LONG_DECIMAL_VALUE')\"><input type=\"number\" class=\"form-control input-number-xs\" [(ngModel)]=\"tagOption.value\" (keypress)=\"onlyNumericChar($event, editorField)\"/></td>\n            <td *ngIf=\"(tagOption.optionType=='PARSED_OPTION_STRING_VALUE')\"><input type=\"text\" class=\"form-control input-text-xs\" [(ngModel)]=\"tagOption.value\"/></td>\n          </tr>\n      </table>\n\t  <div style=\"display: inline-block; width:99%; text-align: center; margin: 10px;\">\n\t\t<div style=\"display: table-cell;\">\n\t\t  <button type=\"reset\" class=\"btn btn-default\" (click)=\"cancel()\"><img src=\"../../images/close.svg\" class=\"image-button\"/>&nbsp;{{'TR_CANCEL' | translate}}</button>\n\t\t</div>\n\t\t<div style=\"display: table-cell;width:75%\"></div>\n\t\t  <div style=\"display: table-cell;\">\n\t\t    <button class=\"btn btn-default\" (click)=\"save()\"><img src=\"../../images/ok.svg\" class=\"image-button\"/>&nbsp;{{'TR_OK' | translate}}</button>\n\t\t  </div>\n\t  </div>\n    <alertStatusBarComponent></alertStatusBarComponent>\n  </div>"
                    }),
                    __metadata("design:paramtypes", [ngx_modialog_7_1.DialogRef, alert_service_1.AlertService, core_2.TranslateService])
                ], DashboardConfigTagOptionsModal);
                return DashboardConfigTagOptionsModal;
            }());
            exports_1("DashboardConfigTagOptionsModal", DashboardConfigTagOptionsModal);
            TagOptionsModalContext = (function (_super) {
                __extends(TagOptionsModalContext, _super);
                function TagOptionsModalContext() {
                    return _super !== null && _super.apply(this, arguments) || this;
                }
                return TagOptionsModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("TagOptionsModalContext", TagOptionsModalContext);
            TagOption = (function () {
                function TagOption(name, helpTR, helpText, optionType, choices, value, valueToUse) {
                    if (choices === void 0) { choices = null; }
                    if (value === void 0) { value = null; }
                    if (valueToUse === void 0) { valueToUse = null; }
                    this.name = name;
                    this.helpTR = helpTR;
                    this.helpText = helpText;
                    this.optionType = optionType;
                    this.choices = choices;
                    this.value = value;
                    this.valueToUse = valueToUse;
                }
                TagOption.initTagOptionList = function (controlSource, tagOptionsValue) {
                    var tagOptionList = [];
                    var optionList = JSON.parse(controlSource);
                    if (optionList) {
                        optionList.forEach(function (option) {
                            var key = Object.keys(option)[0];
                            var helpText = unescape(option.helpText);
                            if (option[key] == "PARSED_OPTION_CHOICE_VALUE") {
                                var jsource = JSON.parse(option.choices);
                                tagOptionList.push(new TagOption(key, option.helpTR, helpText, option[key], jsource, null));
                            }
                            else if (option[key] == "PARSED_OPTION_SCALE_VALUE") {
                                tagOptionList.push(new TagOption(key, option.helpTR, helpText, option[key], option.choices, ["", "", "", ""]));
                            }
                            else {
                                tagOptionList.push(new TagOption(key, option.helpTR, helpText, option[key], option.choices, null, option.valueToUse));
                            }
                        });
                    }
                    try {
                        var tagOptionSplit_1 = this.splitMulti(tagOptionsValue, [" ", "\n"]);
                        for (var i = 0; i < tagOptionSplit_1.length; i++) {
                            if (tagOptionList.filter(function (p) { return p.name == tagOptionSplit_1[i]; }).length > 0) {
                                var isLatest = true;
                                for (var y = i + 1; y < tagOptionSplit_1.length; y++) {
                                    if (tagOptionList.filter(function (p) { return p.name == tagOptionSplit_1[y]; }).length > 0) {
                                        this.addTagOptionValue(tagOptionList.filter(function (p) { return p.name == tagOptionSplit_1[i]; })[0], tagOptionSplit_1.slice(i + 1, y));
                                        isLatest = false;
                                        break;
                                    }
                                }
                                if (isLatest) {
                                    this.addTagOptionValue(tagOptionList.filter(function (p) { return p.name == tagOptionSplit_1[i]; })[0], tagOptionSplit_1.slice(i + 1, y));
                                }
                            }
                        }
                    }
                    catch (e) {
                        console.debug(e);
                    }
                    return tagOptionList;
                };
                TagOption.addTagOptionValue = function (tagOption, tagOptionsValue) {
                    try {
                        if (tagOption.optionType == "PARSED_OPTION_SCALE_VALUE" && tagOptionsValue.length > 3)
                            tagOption.value = tagOptionsValue.slice(0, 4);
                        else if (tagOptionsValue.length == 0 && tagOption.optionType == "PARSED_OPTION_BOOL_VALUE")
                            tagOption.value = true;
                        else if (tagOptionsValue.length == 0 && tagOption.optionType == "PARSED_OPTION_BOOL_CHOICE_VALUE")
                            tagOption.value = tagOption.valueToUse;
                        else if (tagOption.optionType == "PARSED_OPTION_CHOICE_VALUE" && tagOptionsValue.length == 1)
                            tagOption.value = tagOptionsValue[0];
                        else
                            tagOption.value = tagOptionsValue.join(" ");
                    }
                    catch (e) {
                        tagOption.value = "";
                    }
                };
                TagOption.formatTagOptionValue = function (controlSource, tagOptionsValue) {
                    var tagOptionsValueformated = "";
                    var optionNameList = [];
                    var optionList = JSON.parse(controlSource);
                    if (optionList) {
                        optionList.forEach(function (option) {
                            optionNameList.push(Object.keys(option)[0]);
                        });
                    }
                    try {
                        var tagOptionSplit = this.splitMulti(tagOptionsValue, [" ", "\n"]);
                        for (var i = 0; i < tagOptionSplit.length; i++) {
                            if (optionNameList.includes(tagOptionSplit[i])) {
                                var isLatest = true;
                                for (var y = i + 1; y < tagOptionSplit.length; y++) {
                                    if (optionNameList.includes(tagOptionSplit[y])) {
                                        tagOptionsValueformated += tagOptionSplit.slice(i, y).join(" ") + "\n";
                                        isLatest = false;
                                        break;
                                    }
                                }
                                if (isLatest) {
                                    tagOptionsValueformated += tagOptionSplit.slice(i, y).join(" ");
                                }
                            }
                        }
                    }
                    catch (e) { }
                    return tagOptionsValueformated;
                };
                TagOption.splitMulti = function (str, tokens) {
                    var tempChar = tokens[0];
                    for (var i = 1; i < tokens.length; i++) {
                        str = str.split(tokens[i]).join(tempChar);
                    }
                    return str.split(tempChar);
                };
                return TagOption;
            }());
            exports_1("TagOption", TagOption);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.options.modal.js.map