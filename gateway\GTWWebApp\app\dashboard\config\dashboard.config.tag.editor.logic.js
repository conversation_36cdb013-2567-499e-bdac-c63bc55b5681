System.register(["../../data/model/models", "@angular/forms", "ngx-modialog-7/plugins/bootstrap", "ngx-modialog-7", "./dashboard.config.tag.editor.modal", "../../modules/treeview/node", "../../modules/treeview-select/node-select"], function (exports_1, context_1) {
    "use strict";
    var models_1, forms_1, bootstrap_1, ngx_modialog_7_1, dashboard_config_tag_editor_modal_1, node_1, node_select_1, DashboardConfigTagEditorLogic;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (forms_1_1) {
                forms_1 = forms_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (dashboard_config_tag_editor_modal_1_1) {
                dashboard_config_tag_editor_modal_1 = dashboard_config_tag_editor_modal_1_1;
            },
            function (node_1_1) {
                node_1 = node_1_1;
            },
            function (node_select_1_1) {
                node_select_1 = node_select_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagEditorLogic = (function () {
                function DashboardConfigTagEditorLogic() {
                }
                DashboardConfigTagEditorLogic.prototype.manageForm = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService) {
                    var customError = "";
                    var selectValue = tagForm.controls[editorField.schemaName].value;
                    try {
                        customError = this[editorType](isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue);
                        return customError;
                    }
                    catch (err) {
                        if (err.message != "this[editorType] is not a function")
                            console.log(err);
                        return "";
                    }
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCUAClientEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "OpcUaTrustRejectedCertificate") {
                            var rejectedCert = tagForm.controls["OpcUaCertificateRejectedList"].value;
                            editorsService.editorAction(editorType, "TrustOPCUaClientCertificate", objectCollectionKind, rejectedCert).subscribe(function (data) {
                                tagForm.controls["OpcUaCertificateRejectedList"].component.listComponentDataFile(data.data);
                                alertService.success("TR_DATA_SAVED");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_UA_TRUST_CERTIFICATE_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "refreshList") {
                            var serverName = tagForm.controls["serverName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCUAServerList", objectCollectionKind, serverName).subscribe(function (data) {
                                tagForm.controls["serverList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_UA_SERVER_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "selectServer") {
                            var gidItem = tagForm.controls["serverList"].value;
                            tagForm.controls["OpcUaClientServerUrl"].setValue(gidItem.item.name);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWModbusSessionActionEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "pointType") {
                            var pointType = tagForm.controls["pointType"].value;
                            if (pointType == "COIL") {
                                tagForm.controls["actionValuesExample"].setValue("[{\"start\":0,\"values\":[4,7,3]},{\"start\":3,\"values\":[5,2]}]");
                            }
                            else {
                                tagForm.controls["actionValuesExample"].setValue("[{\"start\":0,\"values\":[true,false,true]},{\"start\":3,\"values\":[false,true]}]");
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCUAServerEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "OpcUaTrustRejectedCertificate") {
                            var rejectedCert = tagForm.controls["OpcUaCertificateRejectedList"].value;
                            editorsService.editorAction(editorType, "TrustOPCUaCertificate", objectCollectionKind, rejectedCert).subscribe(function (data) {
                                tagForm.controls["OpcUaCertificateRejectedList"].component.listComponentDataFile(data.data);
                                alertService.success("TR_DATA_SAVED");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_UA_TRUST_CERTIFICATE_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "OpcUaServerEditUserSecurity") {
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDEDIT;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var node = { nodeClassName: "GTWOPCUAUserSecurityEditor", nodeCollectionKind: models_1.TreeNodeDTO.NodeCollectionKindEnum.ALL };
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "", parentObjectName: "", addTitle: addTitle, editorsService: editorsService, node: node }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error); });
                        }
                        if (editorField.schemaName == "OpcUaServerSecurityModeNone" || editorField.schemaName == "OpcUaServerSecurityModeSign" || editorField.schemaName == "OpcUaServerSecurityModeSignAndEncrypt") {
                            if (tagForm.controls["OpcUaServerSecurityModeNone"].value == false && tagForm.controls["OpcUaServerSecurityModeSign"].value == false && tagForm.controls["OpcUaServerSecurityModeSignAndEncrypt"].value == false) {
                                tagForm.controls["OpcUaServerSecurityModeNone"].setValue(true);
                            }
                        }
                        if (editorField.schemaName == "OpcUaServerSecurityTokenAllowAnonymousUsers" || editorField.schemaName == "OpcUaServerSecurityTokenAllowUserNames" || editorField.schemaName == "OpcUaServerSecurityTokenAllowUserCerts") {
                            if (tagForm.controls["OpcUaServerSecurityTokenAllowAnonymousUsers"].value == false && tagForm.controls["OpcUaServerSecurityTokenAllowUserNames"].value == false && tagForm.controls["OpcUaServerSecurityTokenAllowUserCerts"].value == false) {
                                tagForm.controls["OpcUaServerSecurityTokenAllowAnonymousUsers"].setValue(true);
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCUAUserSecurityEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "OpcUaClientAddUser") {
                            var newUsername_1 = tagForm.controls["OpcUaClientNewUsername"].value;
                            var password = tagForm.controls["OpcUaClientPassword"].value;
                            var userListControlSource = tagForm.controls["OpcUaClientUserList"].component.componentData;
                            var maxUserPerOPCUaServer = tagForm.controls["OPCUaMaxUserPerServer"].value;
                            if (newUsername_1 == "") {
                                alertService.error("TR_ERROR_USERNAME_IS_NOT_SPECIFIED");
                                return "";
                            }
                            else if (password == "") {
                                alertService.error("TR_ERROR_PASSWORD_IS_NOT_SPECIFIED");
                                return "";
                            }
                            else if (userListControlSource != null && userListControlSource.filter(function (p) { return p.username == newUsername_1; }).length > 0) {
                                alertService.error("TR_ERROR_USERNAME_ALREADY_PRESENT");
                                return "";
                            }
                            else if (userListControlSource != null && userListControlSource.length >= maxUserPerOPCUaServer) {
                                alertService.error("TR_ERROR_CANNOT_ADD_ANOTHER_USER_THE_MAX_NUMBER_OF_SUPPORTED_USERS_HAS_BEEN_REACHED");
                                return "";
                            }
                            else {
                                editorsService.editorAction(editorType, "AddDeleteOPCUaUser", objectCollectionKind, "", "ADD", newUsername_1, password).subscribe(function (data) {
                                    tagForm.controls["OpcUaClientUserList"].component.setGridData(data.data);
                                    tagForm.controls["OpcUaClientNewUsername"].setValue("");
                                    tagForm.controls["OpcUaClientPassword"].setValue("");
                                    tagForm.controls["OpcUaClientUserList"].component.clearSelected();
                                    alertService.success("TR_SUCCESS");
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_OPC_UA_ADD_USER_FAILED");
                                    }
                                });
                            }
                        }
                        if (editorField.schemaName == "OpcUaClientDeleteUser") {
                            var usernameGrid = tagForm.controls["OpcUaClientUserList"].value;
                            if (usernameGrid != null && usernameGrid.item != null && usernameGrid.item.username != "") {
                                var username_1 = usernameGrid.item.username;
                                var ModalDeleteRef_1;
                                translateService.get("TR_ARE_YOU_SURE_TO_DELETE_USER_", { username: username_1 }).subscribe(function (res) {
                                    ModalDeleteRef_1 = modal.confirm()
                                        .size('lg')
                                        .showClose(true)
                                        .title(translateService.instant('TR_WARNING'))
                                        .okBtn(translateService.instant('TR_DELETE'))
                                        .okBtnClass('btn btn-default')
                                        .body("\n\t\t\t\t      <div class=\"panel panel-warning\">\n\t\t\t\t\t      <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t      </div>\n            ").open();
                                });
                                ModalDeleteRef_1.result.then(function (result) {
                                    if (result) {
                                        editorsService.editorAction(editorType, "AddDeleteOPCUaUser", objectCollectionKind, "", "DELETE", username_1, "").subscribe(function (data) {
                                            tagForm.controls["OpcUaClientUserList"].component.setGridData(data.data);
                                            tagForm.controls["OpcUaClientUserList"].component.clearSelected();
                                            alertService.success("TR_SUCCESS");
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_OPC_UA_DELETE_USER_FAILED");
                                            }
                                        });
                                    }
                                }, function () { });
                            }
                        }
                        if (editorField.schemaName == "OpcUaClientModifyUser") {
                            var newUsername_2 = tagForm.controls["OpcUaClientNewUsername"].value;
                            var password = tagForm.controls["OpcUaClientPassword"].value;
                            var userListControlSource = tagForm.controls["OpcUaClientUserList"].component.componentData;
                            var maxUserPerOPCUaServer = tagForm.controls["OPCUaMaxUserPerServer"].value;
                            if (newUsername_2 == "") {
                                alertService.error("TR_ERROR_USERNAME_IS_NOT_SPECIFIED");
                                return "";
                            }
                            else if (password == "") {
                                alertService.error("TR_ERROR_PASSWORD_IS_NOT_SPECIFIED");
                                return "";
                            }
                            else if (userListControlSource != null && userListControlSource.filter(function (p) { return p.username == newUsername_2; }).length == 0) {
                                alertService.error("TR_ERROR_USERNAME_NOT_PRESENT");
                                return "";
                            }
                            else if (userListControlSource != null && userListControlSource.length >= maxUserPerOPCUaServer) {
                                alertService.error("TR_ERROR_CANNOT_ADD_ANOTHER_USER_THE_MAX_NUMBER_OF_SUPPORTED_USERS_HAS_BEEN_REACHED");
                                return "";
                            }
                            else {
                                editorsService.editorAction(editorType, "AddDeleteOPCUaUser", objectCollectionKind, "", "MODIFY", newUsername_2, password).subscribe(function (data) {
                                    tagForm.controls["OpcUaClientUserList"].component.setGridData(data.data);
                                    tagForm.controls["OpcUaClientNewUsername"].setValue("");
                                    tagForm.controls["OpcUaClientPassword"].setValue("");
                                    tagForm.controls["OpcUaClientUserList"].component.clearSelected();
                                    alertService.success("TR_SUCCESS");
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_OPC_UA_MODIFY_USER_FAILED");
                                    }
                                });
                            }
                        }
                        if (editorField.schemaName == "OpcUaClientUserList") {
                            if (true) {
                                var newUsername = tagForm.controls["OpcUaClientUserList"].value.item.username;
                                tagForm.controls["OpcUaClientNewUsername"].setValue(newUsername);
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCClientItemEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "itemName")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                        if (editorField.schemaName == "itemId")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                        if (editorField.schemaName == "itemDescription")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                        if (editorField.schemaName == "itemType")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                    }
                    else {
                        if (editorField.schemaName == "refreshOPCItemParent") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCItemParent", objectCollectionKind, objectName).subscribe(function (data) {
                                tagForm.controls["itemParentBrowser"].component.setTreeviewData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "") {
                            var currentNode_1 = tagForm.controls["itemParentBrowser"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (currentNode_1 instanceof node_1.Node) {
                                if (currentNode_1.children.length === 0) {
                                    editorsService.editorAction(editorType, "LoadChildrenOPCClientItem", objectCollectionKind, objectName, currentNode_1.nodeFullName).subscribe(function (data) {
                                        var jsonSource = data.data;
                                        currentNode_1.children = _this.setTreeviewNode(jsonSource, currentNode_1);
                                        tagForm.controls["itemParentBrowser"].setValue(null);
                                        if (currentNode_1.children.length > 0)
                                            currentNode_1.isExpanded = true;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                                else {
                                    currentNode_1.toggle();
                                }
                            }
                            else {
                                alertService.clearMessage();
                                tagForm.controls["itemName"].setValue("");
                                editorsService.editorAction(editorType, "ValidateOPCItem", objectCollectionKind, objectName, currentNode_1).subscribe(function (data) {
                                    if (data.result)
                                        tagForm.controls["itemName"].setValue(currentNode_1);
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED");
                                    }
                                });
                            }
                        }
                        if (editorField.schemaName == "itemList" && tagForm.controls["itemList"].value != "" && tagForm.controls["itemParentBrowser"].value != "") {
                            var opcItemName = tagForm.controls["itemList"].value;
                            var parenOPCItemName = tagForm.controls["itemParentBrowser"].value;
                            tagForm.controls["itemName"].setValue(parenOPCItemName + "." + opcItemName.item.i);
                        }
                        if (editorField.schemaName == "AddOPCItem" && tagForm.controls["itemName"].value != "" && tagForm.controls["itemParentBrowser"].value != "") {
                            var opcItemName = tagForm.controls["itemName"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            var opcItemValueType = tagForm.controls["itemValueType"].value;
                            if (opcItemName == "") {
                                alertService.error("TR_OPC_INVALID_ITEM_NAME");
                                return "";
                            }
                            editorsService.editorAction(editorType, "AddOPCItem", objectCollectionKind, objectName, opcItemName, opcItemValueType, "0").subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_OPC_ADD_ITEM_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "AddOPCProperty" && tagForm.controls["daItemPropertyList"].value != "") {
                            var daItemProperty = tagForm.controls["daItemPropertyList"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (daItemProperty.item.PD == "") {
                                alertService.error("TR_PLEASE_SELECT_A_PROPERTY");
                                return "";
                            }
                            editorsService.editorAction(editorType, "AddOPCItem", objectCollectionKind, objectName, objectName, daItemProperty.item.TYPE, daItemProperty.item.ID).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_OPC_ADD_ITEM_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "RefreshOPCDAProperties") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCDAProperties", objectCollectionKind, objectName).subscribe(function (data) {
                                tagForm.controls["daItemPropertyList"].component.setGridData(data.data);
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_REFRESH_OPC_PROPERTIES_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "ItemAttributeIsActive") {
                            var objectName = tagForm.controls["objectName"].value;
                            var itemAttributeIsActive = tagForm.controls["ItemAttributeIsActive"].value;
                            editorsService.editorAction(editorType, "ActivateOPCItem", objectCollectionKind, objectName, itemAttributeIsActive).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_ACTIVATE_OPC_ITEM_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "ReadOPCItem") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "ReadOPCItem", objectCollectionKind, objectName).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_READ_OPC_ITEM_FAILED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCClientMultipleItemsEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "refreshOPCItemParent") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCItemParent", objectCollectionKind, objectName).subscribe(function (data) {
                                tagForm.controls["itemParentBrowser"].component.setTreeviewData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "") {
                            var currentNode_2 = tagForm.controls["itemParentBrowser"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (currentNode_2 instanceof node_1.Node) {
                                if (currentNode_2.children.length === 0) {
                                    editorsService.editorAction(editorType, "LoadChildrenOPCClientItem", objectCollectionKind, objectName, currentNode_2.nodeFullName).subscribe(function (data) {
                                        var jsonSource = data.data;
                                        currentNode_2.children = _this.setTreeviewNode(jsonSource, currentNode_2);
                                        tagForm.controls["itemParentBrowser"].setValue(null);
                                        if (currentNode_2.children.length > 0) {
                                            currentNode_2.isExpanded = true;
                                            currentNode_2.checkRecursive(currentNode_2.checked);
                                        }
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                                else {
                                    currentNode_2.toggle();
                                }
                            }
                        }
                        if (editorField.schemaName == "AddOPCItem") {
                            var objectName = tagForm.controls["objectName"].value;
                            var checkedNodes = this.getCheckedItems(tagForm.controls["itemParentBrowser"].component.componentData, true);
                            editorsService.editorAction(editorType, "AddOPCMultipleItems", objectCollectionKind, objectName, JSON.stringify(checkedNodes)).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_OPC_ADD_MULTIPLE_ITEMS_FAILED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCUAClientMultipleItemsEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "refreshOPCUAItemParent") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCUAItemParent", objectCollectionKind, objectName).subscribe(function (data) {
                                tagForm.controls["itemParentBrowser"].component.setTreeviewData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "" && !isRunDuringInit) {
                            var currentNode_3 = tagForm.controls["itemParentBrowser"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (currentNode_3 instanceof node_select_1.NodeSelect) {
                                if (currentNode_3.children.length === 0) {
                                    editorsService.editorAction(editorType, "LoadChildrenOPCUAClientItem", objectCollectionKind, objectName, currentNode_3.nodeFullName).subscribe(function (data) {
                                        var jsonSource = data.data;
                                        currentNode_3.children = _this.setTreeviewNodeSelect(jsonSource, currentNode_3);
                                        tagForm.controls["itemParentBrowser"].setValue(null);
                                        if (currentNode_3.children.length > 0) {
                                            currentNode_3.isExpanded = true;
                                            currentNode_3.checkRecursive(currentNode_3.checked, currentNode_3);
                                        }
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                                else {
                                    currentNode_3.toggle();
                                }
                            }
                        }
                        if (editorField.schemaName == "AddOPCUAItem" && !isRunDuringInit) {
                            var objectName = tagForm.controls["objectName"].value;
                            var checkedNodes = this.getCheckedItems(tagForm.controls["itemParentBrowser"].component.componentData, true);
                            editorsService.editorAction(editorType, "AddOPCUAMultipleItems", objectCollectionKind, objectName, JSON.stringify(checkedNodes)).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_OPC_ADD_MULTIPLE_ITEMS_FAILED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCUAClientItemEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "itemName")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                        if (editorField.schemaName == "itemId")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                        if (editorField.schemaName == "itemType")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                    }
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "refreshOPCUAItemParent") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCUAItemParent", objectCollectionKind, objectName).subscribe(function (data) {
                                tagForm.controls["itemParentBrowser"].component.setTreeviewData(data.data);
                                tagForm.controls["itemName"].setValue("");
                                tagForm.controls["itemId"].setValue("");
                                tagForm.controls["itemDescription"].setValue("");
                                tagForm.controls["itemType"].setValue("");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "" && !isRunDuringInit) {
                            var currentNode_4 = tagForm.controls["itemParentBrowser"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (currentNode_4 instanceof node_1.Node) {
                                if (currentNode_4.children.length === 0) {
                                    editorsService.editorAction(editorType, "LoadChildrenOPCUAClientItem", objectCollectionKind, objectName, currentNode_4.nodeFullName).subscribe(function (data) {
                                        var jsonSource = data.data;
                                        currentNode_4.children = _this.setTreeviewNode(jsonSource, currentNode_4);
                                        tagForm.controls["itemParentBrowser"].setValue(null);
                                        if (currentNode_4.children.length > 0)
                                            currentNode_4.isExpanded = true;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                                else {
                                    currentNode_4.toggle();
                                }
                            }
                            else {
                                editorsService.editorAction(editorType, "ValidateOPCUAItem", objectCollectionKind, objectName, currentNode_4).subscribe(function (data) {
                                    tagForm.controls["itemName"].setValue(data.data.itemName);
                                    tagForm.controls["itemId"].setValue(data.data.itemId);
                                    tagForm.controls["itemDescription"].setValue(data.data.itemDescription);
                                    tagForm.controls["itemType"].setValue(data.data.itemType);
                                    alertService.clearMessage();
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED");
                                    }
                                });
                            }
                        }
                        if (editorField.schemaName == "AddOPCUAItem" && !isRunDuringInit) {
                            var itemName = tagForm.controls["itemName"].value;
                            var itemId = tagForm.controls["itemId"].value;
                            var itemType = tagForm.controls["itemType"].value;
                            var itemDescription = tagForm.controls["itemDescription"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (itemName == "") {
                                alertService.error("TR_OPC_INVALID_ITEM_NAME");
                                return "";
                            }
                            if (itemId == "") {
                                alertService.error("TR_OPC_INVALID_ITEM_ID");
                                return "";
                            }
                            if (itemType == "") {
                                alertService.error("TR_OPC_INVALID_ITEM_TYPE");
                                return "";
                            }
                            editorsService.editorAction(editorType, "AddOPCUAItem", objectCollectionKind, objectName, itemName, itemId, itemType, itemDescription).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_OPC_ADD_ITEM_FAILED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCClientEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "refreshList" || editorField.schemaName == "availableServers") {
                            var availableServers = tagForm.controls["availableServers"].value;
                            var nodeName = tagForm.controls["nodeName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCServerList", objectCollectionKind, availableServers, nodeName).subscribe(function (data) {
                                tagForm.controls["serverList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "selectServer") {
                            var gidItem = tagForm.controls["serverList"].value;
                            var nodeName = tagForm.controls["nodeName"].value;
                            tagForm.controls["OPCserverProgID"].setValue(gidItem.item.progID);
                            tagForm.controls["OPCserverNode"].setValue(nodeName);
                        }
                        if (editorField.schemaName == "OPCserverType") {
                            var OPCserverType = tagForm.controls["OPCserverType"].value;
                            if (OPCserverType == "XML DA 1.0") {
                                tagForm.controls["OPCClientDataRetrievalMode"].setValue("Opc_DataRetrievalMode_SYNCREAD");
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCAEClientEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "refreshList") {
                            var nodeName = tagForm.controls["nodeName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCAEServerList", objectCollectionKind, nodeName).subscribe(function (data) {
                                tagForm.controls["serverList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_AE_SERVER_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "selectServer") {
                            var gidItem = tagForm.controls["serverList"].value;
                            var nodeName = tagForm.controls["nodeName"].value;
                            tagForm.controls["OPCAEserverProgID"].setValue(gidItem.item.progID);
                            tagForm.controls["OPCAEserverNode"].setValue(nodeName);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCAEClientItemEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (isRunDuringInit) {
                    }
                    else {
                        if (editorField.schemaName == "refreshOPCAEAreaApace") {
                            var objectName = tagForm.controls["objectName"].value;
                            editorsService.editorAction(editorType, "RefreshOPCAEAreaApace", objectCollectionKind, objectName).subscribe(function (data) {
                                tagForm.controls["areaSpace"].component.setTreeviewData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_OPC_AE_ITEM_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "areaSpace") {
                            var currentNode_5 = tagForm.controls["areaSpace"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (currentNode_5 instanceof node_1.Node) {
                                if (currentNode_5.children.length === 0) {
                                    editorsService.editorAction(editorType, "LoadChildrenOPCAEAreaApace", objectCollectionKind, objectName, currentNode_5.nodeFullName).subscribe(function (data) {
                                        var jsonSource = data.data;
                                        currentNode_5.children = _this.setTreeviewNode(jsonSource, currentNode_5);
                                        tagForm.controls["areaSpace"].setValue(null);
                                        if (currentNode_5.children.length > 0)
                                            currentNode_5.isExpanded = true;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                                else {
                                    currentNode_5.toggle();
                                }
                            }
                            else {
                                editorsService.editorAction(editorType, "ValidateOPCItem", objectCollectionKind, objectName, currentNode_5).subscribe(function (data) {
                                    if (data.result) {
                                        tagForm.controls["itemName"].setValue(currentNode_5);
                                    }
                                    else {
                                        tagForm.controls["itemName"].setValue("");
                                    }
                                    alertService.clearMessage();
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED");
                                    }
                                });
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPCAEClientAttrEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "itemName")
                            editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                    }
                    else {
                        if (editorField.schemaName == "eventSpace") {
                            var attributepath = tagForm.controls["eventSpace"].value;
                            if (attributepath.indexOf("_TMWOPCAEAT_")) {
                                tagForm.controls["eventName"].setValue(attributepath.substring(0, attributepath.indexOf('_TMWOPCAEAT_')));
                                tagForm.controls["attributePath"].setValue(attributepath);
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2MappingDataAttributeEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "addNewDA")
                            tagForm.controls["addNewDA"].setValue(false);
                        if (editorField.schemaName == "destinationMemberList")
                            this.enableControl(tagForm.controls["destinationMemberList"], true);
                        if (editorField.schemaName == "domainName")
                            this.enableControl(tagForm.controls["domainName"], false);
                        if (editorField.schemaName == "destinationMemberList")
                            this.enableControl(tagForm.controls["controlBlockName"], false);
                        if (editorField.schemaName == "controlBlockName")
                            this.enableControl(tagForm.controls["dataAttributeName"], false);
                        if (editorField.schemaName == "dataType")
                            this.enableControl(tagForm.controls["dataType"], false);
                        if (editorField.schemaName == "SBO")
                            this.enableControl(tagForm.controls["SBO"], false);
                    }
                    else {
                        if (editorField.schemaName == "destinationMemberList") {
                            var destinationMember = tagForm.controls["destinationMemberList"].value;
                            tagForm.controls["destinationMemberName"].setValue(destinationMember.item.tagName);
                        }
                        if (editorField.schemaName == "dataType") {
                            var dataType = tagForm.controls["dataType"].value;
                            editorsService.editorAction(editorType, "ChangeDataTypeTase2", objectCollectionKind, dataType).subscribe(function (data) {
                                _this.enableControl(tagForm.controls["SBO"], data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_DATA_TYPE");
                                }
                            });
                        }
                        if (editorField.schemaName == "addNewDA") {
                            if (tagForm.controls["addNewDA"].value) {
                                this.enableControl(tagForm.controls["destinationMemberList"], false);
                                this.enableControl(tagForm.controls["domainName"], true);
                                this.enableControl(tagForm.controls["controlBlockName"], true);
                                this.enableControl(tagForm.controls["dataAttributeName"], true);
                                this.enableControl(tagForm.controls["dataType"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["destinationMemberList"], true);
                                this.enableControl(tagForm.controls["domainName"], false);
                                this.enableControl(tagForm.controls["controlBlockName"], false);
                                this.enableControl(tagForm.controls["dataAttributeName"], false);
                                this.enableControl(tagForm.controls["dataType"], false);
                                tagForm.controls["dataType"].setValue("");
                                this.enableControl(tagForm.controls["SBO"], false);
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWMdoMappingEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "destinationMemberList") {
                            var destinationMember = tagForm.controls["destinationMemberList"].value;
                            tagForm.controls["destinationMemberName"].setValue(destinationMember.item.tagName);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWMDOEquationEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "includeSDO") {
                            var includeSDO = tagForm.controls["includeSDO"].value;
                            editorsService.editorAction(editorType, "RefreshEquationArgument", objectCollectionKind, includeSDO).subscribe(function (data) {
                                tagForm.controls["mdoList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_MDO_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "validateEquation") {
                            var expression = tagForm.controls["expression"].value;
                            editorsService.editorAction(editorType, "ValidateEquation", objectCollectionKind, expression.replace(/\+/gi, '%2B')).subscribe(function (data) {
                                alertService.success("TR_VALID_EQUATION_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_INVALID_EQUATION");
                                }
                            });
                        }
                        if (editorField.schemaName == "expression") {
                            tagForm.controls["objectName"].component.inputNativeElement.focus();
                        }
                        if (editorField.schemaName == "mdoList") {
                            var expression = tagForm.controls["expression"].value;
                            if (tagForm.controls["mdoList"].value && tagForm.controls["mdoList"].value.item) {
                                var item = tagForm.controls["mdoList"].value.item.draggableField;
                                tagForm.controls["expression"].setValue(expression + item);
                            }
                        }
                        if (editorField.schemaName == "operationList") {
                            var expression = tagForm.controls["expression"].value;
                            if (tagForm.controls["operationList"].value && tagForm.controls["operationList"].value.item) {
                                var item = tagForm.controls["operationList"].value.item.draggableField;
                                tagForm.controls["expression"].setValue(expression + item);
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWChannelEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (isRunDuringInit) {
                        this.enableControl(tagForm.controls["IsRedundantChannel"], false);
                        this.enableControl(tagForm.controls["IsRedundancyGroup"], false);
                        if (editorField.schemaName == "IsRedundantChannel") {
                            if (tagForm.controls["IsRedundantChannel"].value == true)
                                this.enableControl(tagForm.controls["PhysComProtocol"], false);
                        }
                    }
                    if (editorField.schemaName == "ChanTLSEnable") {
                        var collapsiblePanelsfiltered = collapsiblePanels.filter(function (item) { return item.lsName == "TR_TLS_CONFIG"; });
                        if (collapsiblePanelsfiltered.length > 0)
                            collapsiblePanelsfiltered[0].isOpen = tagForm.controls["ChanTLSEnable"].value;
                    }
                    if (tagForm.controls["PhysComType"].value == models_1.TargetTypeEnumDTO.MBP.toString()) {
                        if (editorField.schemaName == "PhysComProtocol" && selectValue == models_1.ProtocolTypesEnumDTO.MMB) {
                            var channelMBPRouteAddress = tagForm.controls["physComMBPRoutePath"];
                            this.enableControl(channelMBPRouteAddress, false);
                            var channelMBPSlavePath = tagForm.controls["physComMBPSlavePath"];
                            this.enableControl(channelMBPSlavePath);
                        }
                        else if (editorField.schemaName == "PhysComProtocol" && selectValue == models_1.ProtocolTypesEnumDTO.SMB) {
                            var channelMBPRouteAddress = tagForm.controls["physComMBPRoutePath"];
                            this.enableControl(channelMBPRouteAddress);
                            var channelMBPSlavePath = tagForm.controls["physComMBPSlavePath"];
                            this.enableControl(channelMBPSlavePath, false);
                        }
                    }
                    else if (tagForm.controls["PhysComType"].value == models_1.TargetTypeEnumDTO.MODEMPOOLCHANNEL.toString()) {
                        var physComModbusRTU = tagForm.controls["physComModbusRTU"];
                        this.enableControl(physComModbusRTU, false);
                        if (tagForm.controls["PhysComProtocol"].value == models_1.ProtocolTypesEnumDTO.MMB || tagForm.controls["PhysComProtocol"].value == models_1.ProtocolTypesEnumDTO.SMB)
                            this.enableControl(physComModbusRTU, true);
                    }
                    else if (tagForm.controls["PhysComType"].value == models_1.TargetTypeEnumDTO._232.toString()) {
                        var offlinePollPeriod = tagForm.controls["PhysOfflinePollPeriod"];
                        this.enableControl(offlinePollPeriod, false);
                        if (tagForm.controls["PhysComProtocol"].value == models_1.ProtocolTypesEnumDTO.MMB || tagForm.controls["PhysComProtocol"].value == models_1.ProtocolTypesEnumDTO.MDNP)
                            this.enableControl(offlinePollPeriod, true);
                    }
                    else if (tagForm.controls["PhysComType"].value == models_1.TargetTypeEnumDTO.UDPTCP.toString()) {
                        if (editorField.schemaName == "PhysComProtocol" && selectValue == models_1.ProtocolTypesEnumDTO.MDNP) {
                            tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                        }
                        else if (editorField.schemaName == "PhysComProtocol" && selectValue == models_1.ProtocolTypesEnumDTO.SDNP) {
                            tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                        }
                    }
                    else if (tagForm.controls["PhysComType"].value == models_1.TargetTypeEnumDTO.TCP.toString()) {
                        if (editorField.schemaName == "ChanTLSEnable") {
                            this.enableControl(tagForm.controls["ChanTLSCertAuthChainingVerDepth"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSCertificateAuthorityFile"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSCertificateRevocationFile"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSCommonName"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSRenegotiationCount"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSRenegotiationSeconds"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSHandshakeTimeout"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSDhFile"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSRSAPrivateKeyFile"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSRSAPrivateKeyPassPhrase"], selectValue);
                            this.enableControl(tagForm.controls["ChanTLSRSACertificateFile"], selectValue);
                            var collapsiblePanelsfiltered = collapsiblePanels.filter(function (item) { return item.lsName == "TR_TLS_CONFIG"; });
                            if (collapsiblePanelsfiltered.length > 0)
                                collapsiblePanelsfiltered[0].isOpen = selectValue;
                        }
                        if (editorField.schemaName == "PhysComProtocol") {
                            this.enableControl(tagForm.controls["IsRedundantChannel"], false);
                            this.enableControl(tagForm.controls["IsRedundancyGroup"], false);
                            this.enableControl(tagForm.controls["IsRedundancyGroup"], false);
                            tagForm.controls["IsRedundancyGroup"].setValue(false);
                            tagForm.controls["IsRedundancyGroup"].setValue(false);
                            if (editorCommand != models_1.EditorCommandsDTO.MENUCMDEDIT.toString()) {
                                switch (selectValue) {
                                    case models_1.ProtocolTypesEnumDTO.M101:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.M102:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.M103:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.M104:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        this.enableControl(tagForm.controls["IsRedundancyGroup"], true);
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.MDNP:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                                        tagForm.controls["PhysComIpPort"].setValue("20000");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.MMB:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.CLIENT);
                                        tagForm.controls["PhysComIpPort"].setValue("502");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.S101:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.S102:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.S103:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.S104:
                                        tagForm.controls["PhysComIpPort"].setValue("2404");
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                                        this.enableControl(tagForm.controls["IsRedundancyGroup"], true);
                                        this.enableControl(tagForm.controls["PhysComIpMode"], false);
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.SDNP:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                                        tagForm.controls["PhysComIpPort"].setValue("20000");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.SMB:
                                        tagForm.controls["PhysComIpMode"].setValue(models_1.TargetTCPModeEnumDTO.SERVER);
                                        tagForm.controls["PhysComIpPort"].setValue("502");
                                        break;
                                }
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWSessionEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var customError;
                    if (!isRunDuringInit) {
                        if (tagForm.controls["linkMode"].value == "GTWTYPES_LINK_UNBALANCED" && tagForm.controls["linkSizeAddress"].value == "0") {
                            tagForm.controls["linkSizeAddress"].setErrors({ "incorrect": true });
                            customError = "TR_LINK_SIZE_ADDRESS_0_NOT_ALLOW_IN_UNBALANCED_LINK";
                        }
                        else {
                            tagForm.controls["linkSizeAddress"].setErrors(null);
                            customError = "";
                        }
                    }
                    else {
                        if (editorField.schemaName == "SessionLinkAddress") {
                            if (editorCommand != models_1.EditorCommandsDTO.MENUCMDEDIT.toString()) {
                                switch (tagForm.controls["protocol"].value) {
                                    case models_1.ProtocolTypesEnumDTO.M101:
                                        tagForm.controls["SessionLinkAddress"].setValue("3");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.M103:
                                        tagForm.controls["SessionLinkAddress"].setValue("3");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.M104:
                                        tagForm.controls["SessionLinkAddress"].setValue("4");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.MMB:
                                        tagForm.controls["SessionLinkAddress"].setValue("1");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.MDNP:
                                        tagForm.controls["SessionLinkAddress"].setValue("4");
                                        tagForm.controls["SessionLocalAddress"].setValue("3");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.S101:
                                        tagForm.controls["SessionLinkAddress"].setValue("3");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.S104:
                                        tagForm.controls["SessionLinkAddress"].setValue("4");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.SDNP:
                                        tagForm.controls["SessionLinkAddress"].setValue("3");
                                        tagForm.controls["SessionLocalAddress"].setValue("4");
                                        break;
                                    case models_1.ProtocolTypesEnumDTO.SMB:
                                        tagForm.controls["SessionLinkAddress"].setValue("1");
                                        break;
                                }
                            }
                        }
                    }
                    return customError;
                };
                DashboardConfigTagEditorLogic.prototype.GTWSectorEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "m14AuthEnable") {
                            this.enableControl(tagForm.controls["m14AuthHMACAlgorithm"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthReplyTimeout"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthKeyChangeInterval"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthMaxKeyChangeCount"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthRandomChallengeDataLength"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthSecurityStatsIOA"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthExtraDiags"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthUserKey"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthUserNumber"], selectValue);
                            this.enableControl(tagForm.controls["m14AuthUserName"], selectValue);
                            var collapsiblePanelsfiltered = collapsiblePanels.filter(function (item) { return item.lsName == "AUTH_CONFIG"; });
                            if (collapsiblePanelsfiltered.length > 0)
                                collapsiblePanelsfiltered[0].isOpen = selectValue;
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ServerNetworkParamter = function (data, tagForm) {
                    tagForm.controls["Isrv61850ServerTransportAddress"].setValue(data.data["Isrv61850ServerTransportAddress"]);
                    tagForm.controls["Isrv61850ServerSessionAddress"].setValue(data.data["Isrv61850ServerSessionAddress"]);
                    tagForm.controls["Isrv61850ServerPresentationAddress"].setValue(data.data["Isrv61850ServerPresentationAddress"]);
                    tagForm.controls["Isrv61850ServerAppID"].setValue(data.data["Isrv61850ServerAppID"]);
                    tagForm.controls["Isrv61850ServerAPInvokeID"].setValue(data.data["Isrv61850ServerAPInvokeID"]);
                    tagForm.controls["Isrv61850ServerAEQualifier"].setValue(data.data["Isrv61850ServerAEQualifier"]);
                    tagForm.controls["Isrv61850ServerAEInvokeID"].setValue(data.data["Isrv61850ServerAEInvokeID"]);
                    tagForm.controls["Isrv61850ServerIPPort"].setValue(data.data["Isrv61850ServerIPPort"]);
                    tagForm.controls["Isrv61850ServerIPAddress"].setValue(data.data["Isrv61850ServerIPAddress"]);
                    if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["Isrv61850ServerAuthMechanism"].value != "Certificate")
                        tagForm.controls["Isrv61850ServerIPPort"].setValue("102");
                    else if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate")
                        tagForm.controls["Isrv61850ServerIPPort"].setValue("3782");
                    if (data.data["Isrv61850ServerIPAddress"] == "")
                        tagForm.controls["Isrv61850ServerIPAddress"].setValue("127.0.0.1");
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ServerEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "Isrv61850ServerICDFile" && tagForm.controls["Isrv61850ServerICDFile"].value != "") {
                            editorsService.editorAction(editorType, "GetAvailableIEDFromFile", objectCollectionKind, tagForm.controls["Isrv61850ServerICDFile"].value, "true").subscribe(function (data) {
                                if (data.result) {
                                    tagForm.controls["I61850ServerSCLFileIEDName"].component.listComponentData(data.data);
                                    if (tagForm.controls["I61850ServerSCLFileIEDName"].component.componentData.length > 0) {
                                        tagForm.controls["I61850ServerSCLFileIEDName"].setValue(tagForm.controls["I61850ServerSCLFileIEDName"].component.componentData[0].value);
                                    }
                                    editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["Isrv61850ServerICDFile"].value, tagForm.controls["I61850ServerSCLFileIEDName"].value).subscribe(function (data) {
                                        _this.GTW61850ServerNetworkParamter(data, tagForm);
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "I61850ServerSCLFileIEDName" && tagForm.controls["I61850ServerSCLFileIEDName"].value != "") {
                            editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["Isrv61850ServerICDFile"].value, tagForm.controls["I61850ServerSCLFileIEDName"].value).subscribe(function (data) {
                                _this.GTW61850ServerNetworkParamter(data, tagForm);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                }
                            });
                        }
                    }
                    if (editorField.schemaName == "I61850ServerUseMMSOnly") {
                        if (tagForm.controls["I61850ServerUseMMSOnly"].value == true && tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate") {
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], true);
                        }
                        else {
                            if (tagForm.controls["I61850ServerUseTLSOnly"].value == false) {
                                tagForm.controls["I61850ServerUseMMSOnly"].setValue(true);
                                return;
                            }
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], false);
                        }
                    }
                    else if (editorField.schemaName == "I61850ServerUseTLSOnly") {
                        if (tagForm.controls["I61850ServerUseTLSOnly"].value == true && tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate") {
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], true);
                        }
                        else {
                            if (tagForm.controls["I61850ServerUseMMSOnly"].value == false) {
                                tagForm.controls["I61850ServerUseTLSOnly"].setValue(true);
                                return;
                            }
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], false);
                        }
                    }
                    else if (editorField.schemaName == "Isrv61850ServerAuthMechanism") {
                        if (tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Certificate") {
                            this.enableControl(tagForm.controls["Isrv61850ServerCertAuthChainingVerDepth"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerAuthPassword"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityFile"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityRevokeListFile"], true);
                            this.enableControl(tagForm.controls["Isrv61850ServerDirectoryToCertificateAuthority"], true);
                            this.enableControl(tagForm.controls["I61850ServerUseTLSOnly"], true);
                            this.enableControl(tagForm.controls["I61850ServerUseMMSOnly"], true);
                            if (tagForm.controls["Isrv61850ServerIPPort"].value == "102") {
                                translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 3782 }).subscribe(function (res) {
                                    if (confirm(res) == true) {
                                        tagForm.controls["Isrv61850ServerIPPort"].setValue("3782");
                                    }
                                });
                            }
                            if (tagForm.controls["I61850ServerUseMMSOnly"].value == true) {
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], false);
                            }
                            if (tagForm.controls["I61850ServerUseTLSOnly"].value == true) {
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], true);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], false);
                                this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], false);
                            }
                        }
                        else {
                            if (tagForm.controls["Isrv61850ServerAuthMechanism"].value == "Password") {
                                this.enableControl(tagForm.controls["Isrv61850ServerAuthPassword"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["Isrv61850ServerAuthPassword"], false);
                                if (tagForm.controls["Isrv61850ServerIPPort"].value == "3782") {
                                    translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 102 }).subscribe(function (res) {
                                        if (confirm(res) == true) {
                                            tagForm.controls["Isrv61850ServerIPPort"].setValue("102");
                                        }
                                    });
                                }
                            }
                            this.enableControl(tagForm.controls["Isrv61850ServerCertAuthChainingVerDepth"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerCertificateAuthorityRevokeListFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerDirectoryToCertificateAuthority"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSCommonName"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerMMSPublicCertificateFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSCommonName"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxPDUs"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSMaxRenegotiationWaitTime"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRenegotiation"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["Isrv61850ServerTLSRSAPublicCertFile"], false);
                            this.enableControl(tagForm.controls["I61850ServerUseTLSOnly"], false);
                            this.enableControl(tagForm.controls["I61850ServerUseMMSOnly"], false);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW62351I61850SecurityEditor = function (isRunDuringInit, tagForm, editorField) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "isCertificateAuthorityFileEnabled") {
                            tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(true);
                            tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(false);
                        }
                        else if (editorField.schemaName == "isCertificateAuthorityFolderEnabled") {
                            tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(false);
                            tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(true);
                        }
                    }
                    if (tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
                        if (tagForm.controls["isCertificateAuthorityFileEnabled"].value == true) {
                            this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], true);
                            this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], false);
                            tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValue("");
                            tagForm.controls["I61850ClientCertificateAuthorityFile"].setValidators([forms_1.Validators.required]);
                            tagForm.controls["I61850ClientCertificateAuthorityFile"].updateValueAndValidity();
                            tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValidators(null);
                            tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].updateValueAndValidity();
                        }
                        else if (tagForm.controls["isCertificateAuthorityFolderEnabled"].value == true) {
                            this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], true);
                            tagForm.controls["I61850ClientCertificateAuthorityFile"].setValue("");
                            tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValidators([forms_1.Validators.required]);
                            tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].updateValueAndValidity();
                            tagForm.controls["I61850ClientCertificateAuthorityFile"].setValidators(null);
                            tagForm.controls["I61850ClientCertificateAuthorityFile"].updateValueAndValidity();
                        }
                    }
                    else {
                        tagForm.controls["I61850ClientCertificateAuthorityFile"].setValidators(null);
                        tagForm.controls["I61850ClientCertificateAuthorityFile"].updateValueAndValidity();
                        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].setValidators(null);
                        tagForm.controls["I61850ClientDirectoryToCertificateAuthority"].updateValueAndValidity();
                    }
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ClientNetworkParamterServer = function (data, tagForm) {
                    tagForm.controls["I61850ServerTransportAddress"].setValue(data.data["Isrv61850ServerTransportAddress"]);
                    tagForm.controls["I61850ServerSessionAddress"].setValue(data.data["Isrv61850ServerSessionAddress"]);
                    tagForm.controls["I61850ServerPresentationAddress"].setValue(data.data["Isrv61850ServerPresentationAddress"]);
                    tagForm.controls["I61850ServerAppID"].setValue(data.data["Isrv61850ServerAppID"]);
                    tagForm.controls["I61850ServerAPInvokeID"].setValue(data.data["Isrv61850ServerAPInvokeID"]);
                    tagForm.controls["I61850ServerAEQualifier"].setValue(data.data["Isrv61850ServerAEQualifier"]);
                    tagForm.controls["I61850ServerAEInvokeID"].setValue(data.data["Isrv61850ServerAEInvokeID"]);
                    tagForm.controls["I61850ServerIPPort"].setValue(data.data["Isrv61850ServerIPPort"]);
                    tagForm.controls["I61850ServerIPAddress"].setValue(data.data["Isrv61850ServerIPAddress"]);
                    if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["I61850AuthMechanism"].value != "Certificate")
                        tagForm.controls["I61850ServerIPPort"].setValue("102");
                    else if (data.data["Isrv61850ServerIPPort"] == "" && tagForm.controls["I61850AuthMechanism"].value == "Certificate")
                        tagForm.controls["I61850ServerIPPort"].setValue("3782");
                    if (data.data["Isrv61850ServerIPAddress"] == "")
                        tagForm.controls["I61850ServerIPAddress"].setValue("127.0.0.1");
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ClientNetworkParamterClient = function (data, tagForm) {
                    tagForm.controls["I61850ClientIPAddress"].setValue(data.data["Isrv61850ServerIPAddress"]);
                    tagForm.controls["I61850ClientAEInvokeID"].setValue(data.data["Isrv61850ServerAEInvokeID"]);
                    tagForm.controls["I61850ClientAPInvokeID"].setValue(data.data["Isrv61850ServerAPInvokeID"]);
                    tagForm.controls["I61850ClientAEQualifier"].setValue(data.data["Isrv61850ServerAEQualifier"]);
                    tagForm.controls["I61850ClientAppID"].setValue(data.data["Isrv61850ServerAppID"]);
                    tagForm.controls["I61850ClientPresentationAddress"].setValue(data.data["Isrv61850ServerPresentationAddress"]);
                    tagForm.controls["I61850ClientSessionAddress"].setValue(data.data["Isrv61850ServerSessionAddress"]);
                    tagForm.controls["I61850ClientTransportAddress"].setValue(data.data["Isrv61850ServerTransportAddress"]);
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ClientEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "I61850SCLFileName" && tagForm.controls["I61850SCLFileName"].value != "") {
                            tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(true);
                            editorsService.editorAction(editorType, "GetAvailableIEDFromFile", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, "true").subscribe(function (data) {
                                tagForm.controls["I61850SCLFileIEDName"].component.listComponentData(data.data);
                                if (tagForm.controls["I61850SCLFileIEDName"].component.componentData.length > 0) {
                                    tagForm.controls["I61850SCLFileIEDName"].setValue(tagForm.controls["I61850SCLFileIEDName"].component.componentData[0].value);
                                    editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, tagForm.controls["I61850SCLFileIEDName"].value).subscribe(function (data) {
                                        _this.GTW61850ClientNetworkParamterServer(data, tagForm);
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                }
                            });
                            editorsService.editorAction(editorType, "GetAvailableIEDFromFile", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, "false").subscribe(function (data) {
                                tagForm.controls["I61850SCLClientIEDName"].component.listComponentData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "I61850SCLClientIEDName" && tagForm.controls["I61850SCLClientIEDName"].value != "") {
                            editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, tagForm.controls["I61850SCLClientIEDName"].value).subscribe(function (data) {
                                _this.GTW61850ClientNetworkParamterClient(data, tagForm);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "I61850SCLFileIEDName" && tagForm.controls["I61850SCLFileIEDName"].value != "") {
                            editorsService.editorAction(editorType, "LoadConfigFromServer", objectCollectionKind, tagForm.controls["I61850SCLFileName"].value, tagForm.controls["I61850SCLFileIEDName"].value).subscribe(function (data) {
                                _this.GTW61850ClientNetworkParamterServer(data, tagForm);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_IED_LIST_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "I61850SCLFileName" && tagForm.controls["I61850SCLFileName"].value == "") {
                            tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(false);
                            tagForm.controls["I61850SCLFileIEDName"].component.listComponentData(null);
                            tagForm.controls["I61850SCLFileIEDName"].component.clearSelected();
                            tagForm.controls["I61850SCLClientIEDName"].component.listComponentData(null);
                            tagForm.controls["I61850SCLClientIEDName"].component.clearSelected();
                        }
                    }
                    if (editorField.schemaName == "isCertificateAuthorityFileEnabled" || editorField.schemaName == "isCertificateAuthorityFolderEnabled")
                        this.GTW62351I61850SecurityEditor(isRunDuringInit, tagForm, editorField);
                    if (editorField.schemaName == "generateFileFromDiscovery") {
                        if (tagForm.controls["generateFileFromDiscovery"].value == false) {
                            if (tagForm.controls["I61850LoadModelFromFileEnabled"].value == false && tagForm.controls["I61850LoadModelFromFileEnabledNOT"].value == false)
                                tagForm.controls["generateFileFromDiscovery"].setValue(true);
                        }
                        else {
                            tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(false);
                            tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(false);
                            this.enableControl(tagForm.controls["I61850SCLFileIEDName"], false);
                            this.enableControl(tagForm.controls["I61850SCLFileName"], false);
                            this.enableControl(tagForm.controls["I61850SCLClientIEDName"], false);
                        }
                    }
                    if (editorField.schemaName == "I61850LoadModelFromFileEnabledNOT") {
                        if (tagForm.controls["I61850LoadModelFromFileEnabledNOT"].value == false) {
                            if (tagForm.controls["generateFileFromDiscovery"].value == false && tagForm.controls["I61850LoadModelFromFileEnabled"].value == false)
                                tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(true);
                            else {
                                this.enableControl(tagForm.controls["I61850SCLFileIEDName"], true);
                                this.enableControl(tagForm.controls["I61850SCLFileName"], true);
                                this.enableControl(tagForm.controls["I61850SCLClientIEDName"], true);
                            }
                        }
                        else {
                            tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(false);
                            tagForm.controls["generateFileFromDiscovery"].setValue(false);
                            this.enableControl(tagForm.controls["I61850SCLFileIEDName"], false);
                            this.enableControl(tagForm.controls["I61850SCLFileName"], false);
                            this.enableControl(tagForm.controls["I61850SCLClientIEDName"], false);
                        }
                    }
                    if (editorField.schemaName == "I61850LoadModelFromFileEnabled") {
                        if (tagForm.controls["I61850LoadModelFromFileEnabled"].value == false) {
                            if (tagForm.controls["generateFileFromDiscovery"].value == false && tagForm.controls["I61850LoadModelFromFileEnabledNOT"].value == false)
                                tagForm.controls["I61850LoadModelFromFileEnabled"].setValue(true);
                            else {
                                tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(true);
                                this.enableControl(tagForm.controls["I61850SCLFileIEDName"], false);
                                this.enableControl(tagForm.controls["I61850SCLFileName"], false);
                                this.enableControl(tagForm.controls["I61850SCLClientIEDName"], false);
                            }
                        }
                        else {
                            tagForm.controls["I61850LoadModelFromFileEnabledNOT"].setValue(false);
                            tagForm.controls["generateFileFromDiscovery"].setValue(false);
                            this.enableControl(tagForm.controls["I61850SCLFileIEDName"], true);
                            this.enableControl(tagForm.controls["I61850SCLFileName"], true);
                            this.enableControl(tagForm.controls["I61850SCLClientIEDName"], true);
                        }
                    }
                    if (editorField.schemaName == "I61850ClientUseMMSOnly") {
                        if (tagForm.controls["I61850ClientUseMMSOnly"].value == true && tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
                            this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], true);
                            this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], true);
                            this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], true);
                            this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], true);
                        }
                        else {
                            if (tagForm.controls["I61850ClientUseTLSOnly"].value == false) {
                                tagForm.controls["I61850ClientUseMMSOnly"].setValue(true);
                                return;
                            }
                            this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], false);
                        }
                    }
                    else if (editorField.schemaName == "I61850ClientUseTLSOnly") {
                        if (tagForm.controls["I61850ClientUseTLSOnly"].value == true && tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
                            this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], true);
                            this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], true);
                            this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], true);
                            this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], true);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], true);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], true);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], true);
                        }
                        else {
                            if (tagForm.controls["I61850ClientUseMMSOnly"].value == false) {
                                tagForm.controls["I61850ClientUseTLSOnly"].setValue(true);
                                return;
                            }
                            this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], false);
                        }
                    }
                    else if (editorField.schemaName == "I61850AuthMechanism") {
                        if (tagForm.controls["I61850AuthMechanism"].value == "Certificate") {
                            this.enableControl(tagForm.controls["I61850ClientCertAuthChainingVerDepth"], true);
                            this.enableControl(tagForm.controls["I61850AuthPassword"], false);
                            this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], true);
                            this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityRevokeListFile"], true);
                            this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], true);
                            this.enableControl(tagForm.controls["I61850ClientUseTLSOnly"], true);
                            this.enableControl(tagForm.controls["I61850ClientUseMMSOnly"], true);
                            if (tagForm.controls["I61850ServerIPPort"].value == "102") {
                                translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 3782 }).subscribe(function (res) {
                                    if (confirm(res) == true) {
                                        tagForm.controls["I61850ServerIPPort"].setValue("3782");
                                    }
                                });
                            }
                            if (tagForm.controls["I61850ClientUseMMSOnly"].value == true) {
                                this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], true);
                                this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], true);
                                this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], true);
                                this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], false);
                                this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], false);
                                this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], false);
                                this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], false);
                            }
                            if (tagForm.controls["I61850ClientUseTLSOnly"].value == true) {
                                this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], true);
                                this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], true);
                                this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], true);
                                this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], true);
                                this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], true);
                                this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], true);
                                this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], false);
                                this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], false);
                                this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], false);
                                this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], false);
                                this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], false);
                                this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], false);
                                this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], false);
                            }
                        }
                        else {
                            if (tagForm.controls["I61850AuthMechanism"].value == "Password") {
                                this.enableControl(tagForm.controls["I61850AuthPassword"], true);
                            }
                            else {
                                this.enableControl(tagForm.controls["I61850AuthPassword"], false);
                                if (tagForm.controls["I61850ServerIPPort"].value == "3782") {
                                    translateService.get("TR_61850_CHANGE_PORT_FOR_SECURITY", { port: 102 }).subscribe(function (res) {
                                        if (confirm(res) == true) {
                                            tagForm.controls["I61850ServerIPPort"].setValue("102");
                                        }
                                    });
                                }
                            }
                            this.enableControl(tagForm.controls["I61850ClientCertAuthChainingVerDepth"], false);
                            this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientCertificateAuthorityRevokeListFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientDirectoryToCertificateAuthority"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSCommonName"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["I61850ClientMMSPublicCertificateFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSCommonName"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSMaxPDUs"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSMaxRenegotiationWaitTime"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRenegotiation"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["I61850ClientTLSRSAPublicCertFile"], false);
                            this.enableControl(tagForm.controls["I61850ClientUseTLSOnly"], false);
                            this.enableControl(tagForm.controls["I61850ClientUseMMSOnly"], false);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ReportEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "RCBName")
                            this.enableControl(tagForm.controls["RCBName"], false);
                        if (editorField.schemaName == "currentEntryID")
                            this.enableControl(tagForm.controls["currentEntryID"], false);
                    }
                    else {
                        if (editorField.schemaName == "RCBList" && tagForm.controls["61850ClientName"].value != "") {
                            var RCBListValue = tagForm.controls["RCBList"].value;
                            tagForm.controls["RCBName"].setValue(RCBListValue.item.reportName);
                            tagForm.controls["DSName"].setValue(RCBListValue.item.dataSet);
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, RCBListValue.item.reportName).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "DSName" && tagForm.controls["61850ClientName"].value != "") {
                            var reportName_1;
                            var RCBListValue = tagForm.controls["RCBList"].value;
                            if (RCBListValue != null && RCBListValue != "")
                                reportName_1 = RCBListValue.item.reportName;
                            else
                                reportName_1 = tagForm.controls["RCBList"].component.componentData[0].reportName;
                            var clientName61850 = tagForm.controls["61850ClientName"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDCHANGE61850DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: reportName_1, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName_1, dataModal.DSName).subscribe(function (data) {
                                        tagForm.controls["RCBList"].component.setGridData(data.dataDS);
                                        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName_1).subscribe(function (data) {
                                            tagForm.controls["DSMemberList"].component.setGridData(data.data);
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                            }
                                        });
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error); });
                        }
                        if (editorField.schemaName == "createDS" && tagForm.controls["61850ClientName"].value != "") {
                            var reportName_2;
                            var RCBListValue = tagForm.controls["RCBList"].value;
                            if (RCBListValue != null && RCBListValue != "")
                                reportName_2 = RCBListValue.item.reportName;
                            else
                                reportName_2 = tagForm.controls["RCBList"].component.componentData[0].reportName;
                            var clientName61850 = tagForm.controls["61850ClientName"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADD61850DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: reportName_2, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName_2, dataModal.DSName).subscribe(function (data) {
                                        tagForm.controls["RCBList"].component.setGridData(data.dataDS);
                                        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, reportName_2).subscribe(function (data) {
                                            tagForm.controls["DSMemberList"].component.setGridData(data.data);
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                            }
                                        });
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                        if (editorField.schemaName == "readConfigFromServer") {
                            editorsService.editorAction(editorType, "ReadConfigFromServer", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["RCBName"].value).subscribe(function (data) {
                                tagForm.controls["integrityPeriod"].setValue(data.data.integrityPeriod);
                                tagForm.controls["bufferTime"].setValue(data.data.bufferTime);
                                tagForm.controls["purgeBefore1stEnable"].setValue(_this.convertBool(data.data.purgeBefore1stEnable));
                                tagForm.controls["purgeBeforeEnableOnReconnect"].setValue(_this.convertBool(data.data.purgeBeforeEnableOnReconnect));
                                tagForm.controls["integrityPeriodMonitored"].setValue(_this.convertBool(data.data.integrityPeriodMonitored));
                                tagForm.controls["dataUpdateChange"].setValue(_this.convertBool(data.data.dataUpdateChange));
                                tagForm.controls["qualityChange"].setValue(_this.convertBool(data.data.qualityChange));
                                tagForm.controls["dataChange"].setValue(_this.convertBool(data.data.dataChange));
                                tagForm.controls["generalInterrogation"].setValue(_this.convertBool(data.data.generalInterrogation));
                                tagForm.controls["bufferOverflow"].setValue(_this.convertBool(data.data.bufferOverflow));
                                tagForm.controls["configurationRevision"].setValue(_this.convertBool(data.data.configurationRevision));
                                tagForm.controls["dataReference"].setValue(_this.convertBool(data.data.dataReference));
                                tagForm.controls["dataSetName"].setValue(_this.convertBool(data.data.dataSetName));
                                tagForm.controls["entryID"].setValue(_this.convertBool(data.data.entryID));
                                tagForm.controls["reasonForInclusion"].setValue(_this.convertBool(data.data.reasonForInclusion));
                                tagForm.controls["sequenceNumber"].setValue(_this.convertBool(data.data.sequenceNumber));
                                tagForm.controls["timeStamp"].setValue(_this.convertBool(data.data.timeStamp));
                                tagForm.controls["retryEnableCount"].setValue(data.data.retryEnableCount);
                                tagForm.controls["retryEnablePeriod"].setValue(data.data.retryEnablePeriod);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_SERVER_READ");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850GOOSEEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "GCBName")
                            this.enableControl(tagForm.controls["GCBName"], false);
                    }
                    else {
                        if (editorField.schemaName == "GCBList" && tagForm.controls["61850ClientName"].value != "") {
                            var GCBListValue = tagForm.controls["GCBList"].value;
                            tagForm.controls["GCBName"].setValue(GCBListValue.item.GOOSEName);
                            tagForm.controls["DSName"].setValue(GCBListValue.item.dataSet);
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, GCBListValue.item.GOOSEName).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "DSName" && tagForm.controls["61850ClientName"].value != "") {
                            var GOOSEName_1;
                            var GCBListValue = tagForm.controls["GCBList"].value;
                            if (GCBListValue != null && GCBListValue != "")
                                GOOSEName_1 = GCBListValue.item.GOOSEName;
                            else
                                GOOSEName_1 = tagForm.controls["GCBList"].component.componentData[0].GOOSEName;
                            var clientName61850 = tagForm.controls["61850ClientName"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDCHANGE61850DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: GOOSEName_1, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName_1, dataModal.DSName).subscribe(function (data) {
                                        tagForm.controls["GCBList"].component.setGridData(data.dataDS);
                                        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName_1).subscribe(function (data) {
                                            tagForm.controls["DSMemberList"].component.setGridData(data.data);
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE");
                                            }
                                        });
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error); });
                        }
                        if (editorField.schemaName == "createDS" && tagForm.controls["61850ClientName"].value != "") {
                            var GOOSEName_2;
                            var GCBListValue = tagForm.controls["GCBList"].value;
                            if (GCBListValue != null && GCBListValue != "")
                                GOOSEName_2 = GCBListValue.item.GOOSEName;
                            else
                                GOOSEName_2 = tagForm.controls["GCBList"].component.componentData[0].GOOSEName;
                            var clientName61850 = tagForm.controls["61850ClientName"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADD61850DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: GOOSEName_2, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName_2, dataModal.DSName).subscribe(function (data) {
                                        tagForm.controls["GCBList"].component.setGridData(data.dataDS);
                                        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, GOOSEName_2).subscribe(function (data) {
                                            tagForm.controls["DSMemberList"].component.setGridData(data.data);
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE");
                                            }
                                        });
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                        if (editorField.schemaName == "readConfigFromServer") {
                            editorsService.editorAction(editorType, "ReadConfigFromServer", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["RCBName"].value).subscribe(function (data) {
                                tagForm.controls["integrityPeriod"].setValue(data.data.integrityPeriod);
                                tagForm.controls["bufferTime"].setValue(data.data.bufferTime);
                                tagForm.controls["purgeBefore1stEnable"].setValue(_this.convertBool(data.data.purgeBefore1stEnable));
                                tagForm.controls["purgeBeforeEnableOnReconnect"].setValue(_this.convertBool(data.data.purgeBeforeEnableOnReconnect));
                                tagForm.controls["integrityPeriodMonitored"].setValue(_this.convertBool(data.data.integrityPeriodMonitored));
                                tagForm.controls["dataUpdateChange"].setValue(_this.convertBool(data.data.dataUpdateChange));
                                tagForm.controls["qualityChange"].setValue(_this.convertBool(data.data.qualityChange));
                                tagForm.controls["dataChange"].setValue(_this.convertBool(data.data.dataChange));
                                tagForm.controls["generalInterrogation"].setValue(_this.convertBool(data.data.generalInterrogation));
                                tagForm.controls["bufferOverflow"].setValue(_this.convertBool(data.data.bufferOverflow));
                                tagForm.controls["configurationRevision"].setValue(_this.convertBool(data.data.configurationRevision));
                                tagForm.controls["dataReference"].setValue(_this.convertBool(data.data.dataReference));
                                tagForm.controls["dataSetName"].setValue(_this.convertBool(data.data.dataSetName));
                                tagForm.controls["entryID"].setValue(_this.convertBool(data.data.entryID));
                                tagForm.controls["reasonForInclusion"].setValue(_this.convertBool(data.data.reasonForInclusion));
                                tagForm.controls["sequenceNumber"].setValue(_this.convertBool(data.data.sequenceNumber));
                                tagForm.controls["timeStamp"].setValue(_this.convertBool(data.data.timeStamp));
                                tagForm.controls["retryEnableCount"].setValue(data.data.retryEnableCount);
                                tagForm.controls["retryEnablePeriod"].setValue(data.data.retryEnablePeriod);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_SERVER_READ");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850PolledDataSetEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "I61850PolledDataSetName")
                            this.enableControl(tagForm.controls["I61850PolledDataSetName"], false);
                        if (editorField.schemaName == "DSList" && tagForm.controls["61850ClientName"].value != "") {
                            var DSListValue = tagForm.controls["DSList"].value;
                            tagForm.controls["DSList"].setValue(DSListValue.item.dataSet);
                            tagForm.controls["I61850PolledDataSetName"].setValue(DSListValue.item.dataSet);
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, DSListValue.item.dataSet).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "createDS" && tagForm.controls["61850ClientName"].value != "") {
                            var clientName61850 = tagForm.controls["61850ClientName"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADD61850DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "NewDS", parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (data) {
                                if (data != null && data.result && data.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["61850ClientName"].value, data.DSName).subscribe(function (datac) {
                                        tagForm.controls["DSList"].component.setGridData(datac.dataDS);
                                        tagForm.controls["DSList"].component.selectLastGridDataMember();
                                        editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, data.DSName).subscribe(function (datam) {
                                            tagForm.controls["DSMemberList"].component.setGridData(datam.result);
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE");
                                            }
                                        });
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (data == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                        if (editorField.schemaName == "deleteDS" && tagForm.controls["61850ClientName"].value != "") {
                            var DSListValue_1 = tagForm.controls["DSList"].value;
                            var ModalDeleteRef_2;
                            translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue_1 }).subscribe(function (res) {
                                ModalDeleteRef_2 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_DELETE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t\t<div class=\"panel panel-warning\">\n\t\t\t\t\t\t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t</div>\n        ").open();
                            });
                            ModalDeleteRef_2.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["61850ClientName"].value, DSListValue_1).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectGridDataMember(0);
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850ChangeDataSetEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "DSName") {
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["DSName"].value).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850DatasetEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "changeFCDisplay") {
                            editorsService.editorAction(editorType, "ChangeFCDS", objectCollectionKind, tagForm.controls["61850ClientName"].value, tagForm.controls["changeFCDisplay"].value).subscribe(function (data) {
                                tagForm.controls["nodeList"].component.setTreeviewData(data.data);
                                if (tagForm.controls["changeFCDisplay"].value === "true") {
                                    tagForm.controls["changeFCDisplay"].setValue("false");
                                    tagForm.controls["DSMemberList"].component.componentData = null;
                                    editorField.name = 'TR_SHOW_ALL_FCS';
                                }
                                else {
                                    tagForm.controls["changeFCDisplay"].setValue("true");
                                    tagForm.controls["DSMemberList"].component.componentData = null;
                                    editorField.name = 'TR_SHOW_ONLY_ST_MX';
                                }
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_FC_DATASET_NOT_CHANGED");
                                }
                            });
                        }
                        if (editorField.schemaName == "nodeList") {
                            var checkedNodes = this.getCheckedItems(tagForm.controls["nodeList"].value);
                            var DSGridData = "{ \"columns\": [{ \"field\": \"item\", \"header\": \"ITEM\" }], \"data\":" + JSON.stringify(checkedNodes) + "}";
                            tagForm.controls["DSMemberList"].component.setGridData(DSGridData);
                            tagForm.controls["DSMemberList"].setValue(JSON.stringify(checkedNodes));
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850DataAttributeMDOEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "Add61850Item") {
                            var RCBListComponentData = tagForm.controls["DAPointList"].component.componentData;
                            var DAPointListChecked_1 = [];
                            RCBListComponentData.forEach(function (item) {
                                if (item.checkbox === true) {
                                    DAPointListChecked_1.push(item.itemName);
                                }
                                ;
                            });
                            editorsService.editorAction(editorType, "Add61850MultipleItems", objectCollectionKind, tagForm.controls["61850ControlBlock"].value, JSON.stringify(DAPointListChecked_1), tagForm.controls["autoMapQualityTime"].value).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_ADD_MULTIPLE_ITEMS_FAILED");
                                }
                            });
                        }
                        if ((editorField.schemaName == "autoMapQualityTime")) {
                            editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850ControlBlock"].value, tagForm.controls["autoMapQualityTime"].value, tagForm.controls["functionalConstraint"].value, tagForm.controls["DASearch"].value).subscribe(function (data) {
                                tagForm.controls["DAPointList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED");
                                }
                            });
                        }
                        if ((editorField.schemaName == "BTNSearch")) {
                            editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850ControlBlock"].value, tagForm.controls["autoMapQualityTime"].value, tagForm.controls["functionalConstraint"].value, tagForm.controls["DASearch"].value).subscribe(function (data) {
                                tagForm.controls["DAPointList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED");
                                }
                            });
                        }
                        if ((editorField.schemaName == "tagQuality" || editorField.schemaName == "tagTime")) {
                            var clientName61850 = tagForm.controls["controlBlock"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDSELECTDATAATTRIBUTE;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var selectDA = "SelectDAT";
                            if (editorField.schemaName == "tagQuality")
                                selectDA = "SelectDAQ";
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: selectDA, parentObjectName: clientName61850, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (data) {
                                if (data != null && data.result && data.DAName != "") {
                                    if (editorField.schemaName == "tagQuality")
                                        tagForm.controls["tagQuality"].setValue(data.DAName);
                                    else
                                        tagForm.controls["tagTime"].setValue(data.DAName);
                                }
                                else if (data == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850WriteablePointEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "DAPointList") {
                            var RCBListComponentData = tagForm.controls["DAPointList"].component.componentData;
                            var DAPointListChecked_2 = [];
                            RCBListComponentData.forEach(function (item) {
                                if (item.checkbox === true) {
                                    DAPointListChecked_2.push(item.itemName);
                                }
                                ;
                            });
                            tagForm.controls["DAPointListChecked"].setValue(JSON.stringify(DAPointListChecked_2));
                        }
                        if ((editorField.schemaName == "FC_SP" || editorField.schemaName == "FC_CF" || editorField.schemaName == "FC_SV" || editorField.schemaName == "DASearch")) {
                            var functionalConstraint = void 0;
                            if (tagForm.controls["FC_SP"].value === true)
                                functionalConstraint += "FC_SP;";
                            if (tagForm.controls["FC_CF"].value === true)
                                functionalConstraint += "FC_CF;";
                            if (tagForm.controls["FC_SV"].value === true)
                                functionalConstraint += "FC_SV;";
                            editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850Client"].value, functionalConstraint, tagForm.controls["DASearch"].value).subscribe(function (data) {
                                tagForm.controls["DAPointList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850SelectDataAttributeEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (isRunDuringInit) {
                        tagForm.controls["DAPointList"].component.selectGridDataMember(-1);
                    }
                    else {
                        if (editorField.schemaName == "DAPointList") {
                            var DAPointListValue = tagForm.controls["DAPointList"].value;
                            tagForm.controls["DAName"].setValue(DAPointListValue.item.itemName);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850CommandPointEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "objectName")
                            this.enableControl(tagForm.controls["objectName"], false);
                    }
                    else {
                        if (editorField.schemaName == "controlpointList") {
                            var controlpointListValue = tagForm.controls["controlpointList"].value;
                            tagForm.controls["objectName"].setValue(controlpointListValue.item.itemName);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW61850SdoEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "objectName")
                            this.enableControl(tagForm.controls["objectName"], false);
                        if (editorField.schemaName == "dropMemberName")
                            this.enableControl(tagForm.controls["dropMemberName"], false);
                    }
                    else {
                        if (editorField.schemaName == "DAPointList") {
                            var DAListValue = tagForm.controls["DAPointList"].value;
                            tagForm.controls["DAValue"].setValue(DAListValue.item.itemName);
                            tagForm.controls["DATime"].setValue(DAListValue.item.time);
                            tagForm.controls["DAQuality"].setValue(DAListValue.item.quality);
                            if (tagForm.controls["objectName"].component.editorField.value == "") {
                                var DAValueName = DAListValue.item.itemName.replace(/[.+\/]/g, "_");
                                tagForm.controls["objectName"].setValue(DAValueName);
                            }
                        }
                        if ((editorField.schemaName == "autoMapQualityTime" || editorField.schemaName == "functionalConstraint" || editorField.schemaName == "DASearch" || editorField.schemaName == "onlyExtRef" || editorField.schemaName == "DAPointsToSearch")) {
                            editorsService.editorAction(editorType, "ChangeDAPointList", objectCollectionKind, tagForm.controls["61850Server"].value, tagForm.controls["autoMapQualityTime"].value, tagForm.controls["functionalConstraint"].value, tagForm.controls["DASearch"].value, tagForm.controls["DAPointsToSearch"].value).subscribe(function (data) {
                                tagForm.controls["DAPointList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPC61850ServerControlEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if ((editorField.schemaName == "OPCSelectRequestPoint" ||
                            editorField.schemaName == "OPCSelectResponsePoint" ||
                            editorField.schemaName == "OPCOperateRequestPoint" ||
                            editorField.schemaName == "OPCOperateResponsePoint" ||
                            editorField.schemaName == "OPCCancelRequestPoint" ||
                            editorField.schemaName == "OPCCancelResponsePoint" ||
                            editorField.schemaName == "OPCaddCausePoint" ||
                            editorField.schemaName == "OPCCommandTermPoint")) {
                            var OPCClientName = tagForm.controls["OPCClientName"].value;
                            if (OPCClientName == "") {
                                alertService.error("TR_OPC_CLIENT_IS_EMPTY");
                                return "";
                            }
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADD61850CONTROLTOOPCMAPPINGITEM;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: OPCClientName, parentObjectName: OPCClientName, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result) {
                                    tagForm.controls[editorField.schemaName].setValue(dataModal.itemName);
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_NO_OPC_ITEM_SELECTED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWOPC61850ServerControlItemEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (editorField.schemaName == "refreshOPCItemParent") {
                        var objectName = tagForm.controls["objectName"].value;
                        editorsService.editorAction(editorType, "RefreshOPCItemParent", objectCollectionKind, objectName).subscribe(function (data) {
                            tagForm.controls["itemParentBrowser"].component.setTreeviewData(data.data);
                        }, function (error) {
                            if (error.status == 401) {
                                authenticationService.onLoginFailed("/");
                            }
                            else {
                                alertService.error("TR_OPC_ITEM_LIST_UNAVAILABLE");
                            }
                        });
                    }
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "itemParentBrowser" && tagForm.controls["itemParentBrowser"].value != "") {
                            var currentNode_6 = tagForm.controls["itemParentBrowser"].value;
                            var objectName = tagForm.controls["objectName"].value;
                            if (currentNode_6 instanceof node_1.Node) {
                                if (currentNode_6.children.length === 0) {
                                    editorsService.editorAction(editorType, "LoadChildrenOPCClientItem", objectCollectionKind, objectName, currentNode_6.nodeFullName).subscribe(function (data) {
                                        var jsonSource = data.data;
                                        currentNode_6.children = _this.setTreeviewNode(jsonSource, currentNode_6);
                                        tagForm.controls["itemParentBrowser"].setValue(null);
                                        if (currentNode_6.children.length > 0)
                                            currentNode_6.isExpanded = true;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_OPC_SERVER_LIST_UNAVAILABLE");
                                        }
                                    });
                                }
                                else {
                                    currentNode_6.toggle();
                                }
                            }
                            else {
                                alertService.clearMessage();
                                tagForm.controls["itemName"].setValue("");
                                editorsService.editorAction(editorType, "ValidateOPCItem", objectCollectionKind, objectName, currentNode_6).subscribe(function (data) {
                                    if (data.result)
                                        tagForm.controls["itemName"].setValue(currentNode_6);
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_OPC_LOAD_ITEM_FAILED");
                                    }
                                });
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTW62351TASE2SecurityEditor = function (isRunDuringInit, tagForm, editorField) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "isCertificateAuthorityFileEnabled") {
                            tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(true);
                            tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(false);
                        }
                        else if (editorField.schemaName == "isCertificateAuthorityFolderEnabled") {
                            tagForm.controls["isCertificateAuthorityFileEnabled"].setValue(false);
                            tagForm.controls["isCertificateAuthorityFolderEnabled"].setValue(true);
                        }
                    }
                    if (tagForm.controls["TASE2SecurityOn"].value == true) {
                        if (tagForm.controls["isCertificateAuthorityFileEnabled"].value == true) {
                            this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], true);
                            this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], false);
                            tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValue("");
                            tagForm.controls["TASE2CertificateAuthorityFile"].setValidators([forms_1.Validators.required]);
                            tagForm.controls["TASE2CertificateAuthorityFile"].updateValueAndValidity();
                            tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValidators(null);
                            tagForm.controls["TASE2DirectoryToCertificateAuthority"].updateValueAndValidity();
                        }
                        else if (tagForm.controls["isCertificateAuthorityFolderEnabled"].value == true) {
                            this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], false);
                            this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], true);
                            tagForm.controls["TASE2CertificateAuthorityFile"].setValue("");
                            tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValidators([forms_1.Validators.required]);
                            tagForm.controls["TASE2DirectoryToCertificateAuthority"].updateValueAndValidity();
                            tagForm.controls["TASE2CertificateAuthorityFile"].setValidators(null);
                            tagForm.controls["TASE2CertificateAuthorityFile"].updateValueAndValidity();
                        }
                    }
                    else {
                        tagForm.controls["TASE2CertificateAuthorityFile"].setValidators(null);
                        tagForm.controls["TASE2CertificateAuthorityFile"].updateValueAndValidity();
                        tagForm.controls["TASE2DirectoryToCertificateAuthority"].setValidators(null);
                        tagForm.controls["TASE2DirectoryToCertificateAuthority"].updateValueAndValidity();
                    }
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2ClientEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (editorField.schemaName == "TASE2SecurityOn") {
                        if (tagForm.controls["TASE2SecurityOn"].value == true) {
                            this.enableControl(tagForm.controls["TASE2CertAuthChainingVerDepth"], true);
                            this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], true);
                            this.enableControl(tagForm.controls["TASE2CertificateAuthorityRevokeListFile"], true);
                            this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], true);
                            this.enableControl(tagForm.controls["TASE2MMSCommonName"], true);
                            this.enableControl(tagForm.controls["TASE2MMSPrivateKeyFile"], true);
                            this.enableControl(tagForm.controls["TASE2MMSPrivateKeyPassPhrase"], true);
                            this.enableControl(tagForm.controls["TASE2MMSPublicCertificateFile"], true);
                            this.enableControl(tagForm.controls["TASE2TLSCommonName"], true);
                            this.enableControl(tagForm.controls["TASE2TLSMaxPDUs"], true);
                            this.enableControl(tagForm.controls["TASE2TLSMaxRenegotiationWaitTime"], true);
                            this.enableControl(tagForm.controls["TASE2TLSRenegotiation"], true);
                            this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyFile"], true);
                            this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyPassPhrase"], true);
                            this.enableControl(tagForm.controls["TASE2TLSRSAPublicCertFile"], true);
                            this.enableControl(tagForm.controls["TASE2UseSiscoCompatability"], true);
                            this.enableControl(tagForm.controls["isCertificateAuthorityFileEnabled"], true);
                            this.enableControl(tagForm.controls["isCertificateAuthorityFolderEnabled"], true);
                        }
                        else {
                            this.enableControl(tagForm.controls["TASE2CertAuthChainingVerDepth"], false);
                            this.enableControl(tagForm.controls["TASE2CertificateAuthorityFile"], false);
                            this.enableControl(tagForm.controls["TASE2CertificateAuthorityRevokeListFile"], false);
                            this.enableControl(tagForm.controls["TASE2DirectoryToCertificateAuthority"], false);
                            this.enableControl(tagForm.controls["TASE2MMSCommonName"], false);
                            this.enableControl(tagForm.controls["TASE2MMSPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["TASE2MMSPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["TASE2MMSPublicCertificateFile"], false);
                            this.enableControl(tagForm.controls["TASE2TLSCommonName"], false);
                            this.enableControl(tagForm.controls["TASE2TLSMaxPDUs"], false);
                            this.enableControl(tagForm.controls["TASE2TLSMaxRenegotiationWaitTime"], false);
                            this.enableControl(tagForm.controls["TASE2TLSRenegotiation"], false);
                            this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyFile"], false);
                            this.enableControl(tagForm.controls["TASE2TLSRSAPrivateKeyPassPhrase"], false);
                            this.enableControl(tagForm.controls["TASE2TLSRSAPublicCertFile"], false);
                            this.enableControl(tagForm.controls["TASE2UseSiscoCompatability"], false);
                            this.enableControl(tagForm.controls["isCertificateAuthorityFileEnabled"], false);
                            this.enableControl(tagForm.controls["isCertificateAuthorityFolderEnabled"], false);
                        }
                    }
                    if (editorField.schemaName == "isCertificateAuthorityFileEnabled" || editorField.schemaName == "isCertificateAuthorityFolderEnabled" || editorField.schemaName == "TASE2SecurityOn")
                        this.GTW62351TASE2SecurityEditor(isRunDuringInit, tagForm, editorField);
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2CommandPointEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (isRunDuringInit) {
                        if (editorField.schemaName == "tagName")
                            this.enableControl(tagForm.controls["tagName"], false);
                    }
                    else {
                        if (editorField.schemaName == "controlpointList") {
                            var controlpointListValue = tagForm.controls["controlpointList"].value;
                            tagForm.controls["tagName"].setValue(controlpointListValue.item.itemName);
                            tagForm.controls["tagTase2Type"].setValue(controlpointListValue.item.datatype);
                            tagForm.controls["tagOptions"].setValue(controlpointListValue.item.options);
                        }
                        if (editorField.schemaName == "addTASE2CommandPoint") {
                            var TASE2ControlBlockName = tagForm.controls["TASE2ControlBlockName"].value;
                            var tagName = tagForm.controls["tagName"].value;
                            var tagOptions = tagForm.controls["tagOptions"].value;
                            var tagDescription = tagForm.controls["tagDescription"].value;
                            var userTagName = tagForm.controls["userTagName"].value;
                            var tagTase2Type = tagForm.controls["tagTase2Type"].value;
                            editorsService.editorAction(editorType, "AddTASE2CommandPoint", objectCollectionKind, TASE2ControlBlockName, tagName, tagOptions, tagDescription, userTagName, tagTase2Type).subscribe(function (data) {
                                alertService.success("TR_DATA_SAVED");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2PolledDataSetEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "DSList" && tagForm.controls["TASE2ClientName"].value != "") {
                            var DSListValue = tagForm.controls["DSList"].value;
                            tagForm.controls["DSList"].setValue(DSListValue.item.dataSetName);
                            tagForm.controls["DSMemberList"].component.componentData = null;
                            tagForm.controls["polledDSDSID"].setValue(DSListValue.item.dataSetName);
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue.item.dataSetName).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "createDS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var clientNameTASE2 = tagForm.controls["TASE2ClientName"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADDTASE2DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "NewDS", parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (data) {
                                if (data != null && data.result && data.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectLastGridDataMember();
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (data == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                        if (editorField.schemaName == "manageDS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var clientNameTASE2 = tagForm.controls["TASE2ClientName"].value;
                            var DSListValue = tagForm.controls["DSList"].value;
                            if (DSListValue != "") {
                                var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDMANAGETASE2DATASET;
                                var addTitle = "TR_" + editorCommandsDTOString;
                                var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                                dashboardConfigTagEditorModalRef.result.then(function (data) {
                                    if (data != null && data.result && data.DSName != "") {
                                        alertService.success("TR_DATA_SAVED");
                                        editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(function (data) {
                                            tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                            tagForm.controls["DSList"].component.selectLastGridDataMember();
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                            }
                                        });
                                    }
                                    else if (data == "") {
                                        alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                    }
                                }, function (error) { alertService.debug(error.toString()); });
                            }
                            else {
                                alertService.error("TR_TASE2_DATASET_IS_NOT_SELECTED");
                                return "";
                            }
                        }
                        if (editorField.schemaName == "deleteDS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var DSListValue_2 = tagForm.controls["DSList"].value;
                            var ModalDeleteRef_3;
                            translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue_2 }).subscribe(function (res) {
                                ModalDeleteRef_3 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_DELETE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t\t\t<div class=\"panel panel-warning\">\n\t\t\t\t\t\t\t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t\t</div>\n          ").open();
                            });
                            ModalDeleteRef_3.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue_2).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectGridDataMember(0);
                                        tagForm.controls["DSMemberList"].component.componentData = null;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                        if (editorField.schemaName == "addTASE2DSPD" && tagForm.controls["TASE2ClientName"].value != "") {
                            var tase2ClientName = tagForm.controls["TASE2ClientName"].value;
                            var polledDSID = tagForm.controls["polledDSID"].value;
                            var tase2CltReportedDSIntegrityPeriod = tagForm.controls["TASE2CltReportedDSIntegrityPeriod"].value;
                            var dsName = tagForm.controls["DSList"].value;
                            if (tase2ClientName != "" && polledDSID != "" && tase2CltReportedDSIntegrityPeriod != "" && dsName != "") {
                                editorsService.editorAction(editorType, "AddTASE2DSPD", objectCollectionKind, tase2ClientName, polledDSID, tase2CltReportedDSIntegrityPeriod, dsName).subscribe(function (data) {
                                    alertService.success("TR_DATA_SAVED");
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_TASE2_CANNOT_ADD_DSTS");
                                    }
                                });
                            }
                            else {
                                alertService.error("TR_ERROR_THE_REQUEST_IS_MISSING_ONE_OR_MORE_REQUIRED_FIELDS");
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2CltReportedDataSetEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "DSList" && tagForm.controls["TASE2ClientName"].value != "") {
                            var DSListValue = tagForm.controls["DSList"].value;
                            tagForm.controls["DSList"].setValue(DSListValue.item.dataSetName);
                            tagForm.controls["RCBDataSetName"].setValue(DSListValue.item.dataSetName);
                            tagForm.controls["DSMemberList"].setValue(DSListValue.item.dataSetName);
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue.item.dataSetName).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "createDS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var clientNameTASE2 = tagForm.controls["TASE2ClientName"].value;
                            var DSListValue = tagForm.controls["DSList"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADDTASE2DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (data) {
                                if (data != null && data.result && data.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectLastGridDataMember();
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (data == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                        if (editorField.schemaName == "manageDS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var clientNameTASE2 = tagForm.controls["TASE2ClientName"].value;
                            var DSListValue = tagForm.controls["DSList"].value;
                            if (DSListValue != "") {
                                var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDMANAGETASE2DATASET;
                                var addTitle = "TR_" + editorCommandsDTOString;
                                var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: clientNameTASE2, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                                dashboardConfigTagEditorModalRef.result.then(function (data) {
                                    if (data != null && data.result && data.DSName != "") {
                                        alertService.success("TR_DATA_SAVED");
                                        editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, data.DSName).subscribe(function (data) {
                                            tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                            tagForm.controls["DSList"].component.selectLastGridDataMember();
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                            }
                                        });
                                    }
                                    else if (data == "") {
                                        alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                    }
                                }, function (error) { alertService.debug(error.toString()); });
                            }
                            else {
                                alertService.error("TR_TASE2_DATASET_IS_NOT_SELECTED");
                                return "";
                            }
                        }
                        if (editorField.schemaName == "deleteDS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var DSListValue_3 = tagForm.controls["DSList"].value;
                            var ModalDeleteRef_4;
                            translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue_3 }).subscribe(function (res) {
                                ModalDeleteRef_4 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_DELETE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t\t    <div class=\"panel panel-warning\">\n\t\t\t\t\t\t    <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t    </div>\n            ").open();
                            });
                            ModalDeleteRef_4.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["TASE2ClientName"].value, DSListValue_3).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectGridDataMember(0);
                                        tagForm.controls["DSMemberList"].component.componentData = null;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                        if (editorField.schemaName == "addTASE2DSTS" && tagForm.controls["TASE2ClientName"].value != "") {
                            var tase2ClientName = tagForm.controls["TASE2ClientName"].value;
                            var tase2CltReportedDSName = tagForm.controls["TASE2CltReportedDSName"].value;
                            var tase2CltReportedDSDomainName = tagForm.controls["TASE2CltReportedDSDomainName"].value;
                            var dsName = tagForm.controls["DSList"].value;
                            if (tase2ClientName != "" && tase2CltReportedDSName != "" && tase2CltReportedDSDomainName != "" && dsName != "") {
                                editorsService.editorAction(editorType, "AddTASE2DSTS", objectCollectionKind, tase2ClientName, tase2CltReportedDSName, tase2CltReportedDSDomainName, dsName).subscribe(function (data) {
                                    alertService.success("TR_DATA_SAVED");
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_TASE2_CANNOT_ADD_DSTS");
                                    }
                                });
                            }
                            else {
                                alertService.error("TR_ERROR_THE_REQUEST_IS_MISSING_ONE_OR_MORE_REQUIRED_FIELDS");
                            }
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2DatasetEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "nodeList") {
                            var checkedNodes = this.getCheckedItems(tagForm.controls["nodeList"].value);
                            var DSGridData = "{ \"columns\": [{ \"field\": \"item\", \"header\": \"ITEM\" }], \"data\":" + JSON.stringify(checkedNodes) + "}";
                            tagForm.controls["DSMemberList"].component.setGridData(DSGridData);
                            tagForm.controls["DSMemberList"].setValue(JSON.stringify(checkedNodes));
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTase2ConfigEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "domainsList") {
                            var domainListValue = tagForm.controls["domainsList"].value;
                            editorsService.editorAction(editorType, "ShowConfigInfoTase2", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue.item.itemName).subscribe(function (data) {
                                tagForm.controls["domainInfo"].setValue(data.data.join("\r\n"));
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_CONFIG_INFO_UNAVAILABLE");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2DataAttributeMDOEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "DAPointList") {
                            var RCBListComponentData = tagForm.controls["DAPointList"].component.componentData;
                            var DAPointListChecked_3 = [];
                            RCBListComponentData.forEach(function (item) {
                                if (item.checkbox === true) {
                                    DAPointListChecked_3.push(item.itemName);
                                }
                                ;
                            });
                            tagForm.controls["DAPointListChecked"].setValue(JSON.stringify(DAPointListChecked_3));
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2ClientModelEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "domainsList") {
                            var domainListValue = tagForm.controls["domainsList"].value;
                            editorsService.editorAction(editorType, "ChangeSelectedDomain", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue.item.itemName).subscribe(function (data) {
                                tagForm.controls["dataAttributesList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "DSList") {
                            var DSListValue = tagForm.controls["DSList"].value;
                            tagForm.controls["DSList"].setValue(DSListValue.item.dataSetName);
                            tagForm.controls["DSMemberList"].setValue(DSListValue.item.dataSetName);
                            editorsService.editorAction(editorType, "GetDatasetMember", objectCollectionKind, tagForm.controls["objectName"].value, DSListValue.item.dataSetName).subscribe(function (data) {
                                tagForm.controls["DSMemberList"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "addDomain") {
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADDTASE2DOMAIN;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var node = { nodeClassName: "GTWTASE2DomainEditor", nodeCollectionKind: models_1.TreeNodeDTO.NodeCollectionKindEnum.ALL };
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: "", parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService, node: node }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.data != "") {
                                    tagForm.controls["domainsList"].component.setGridData(dataModal.data);
                                    alertService.success("TR_DATA_SAVED");
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error); });
                        }
                        if (editorField.schemaName == "editDomain") {
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDEDIT;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var domainListValue = tagForm.controls["domainsList"].value;
                            if (domainListValue == null || domainListValue == "") {
                                alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
                                return "";
                            }
                            var node = { nodeClassName: "GTWTASE2DomainEditor", nodeCollectionKind: models_1.TreeNodeDTO.NodeCollectionKindEnum.ALL };
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: domainListValue.item.itemName, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService, node: node }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.data != "") {
                                    tagForm.controls["domainsList"].component.setGridData(dataModal.data);
                                    alertService.success("TR_DATA_SAVED");
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error); });
                        }
                        if (editorField.schemaName == "deleteDomain") {
                            var domainListValue_1 = tagForm.controls["domainsList"].value;
                            if (domainListValue_1 == null || domainListValue_1 == "") {
                                alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
                                return "";
                            }
                            if (domainListValue_1.item.itemName == "VCC") {
                                alertService.error("TR_TASE2_CANNOT_DELETE_THE_VCC_DOMAIN");
                                return "";
                            }
                            var ModalDeleteRef_5;
                            translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DOMAIN").subscribe(function (res) {
                                ModalDeleteRef_5 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_DELETE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t    <div class=\"panel panel-warning\">\n\t\t\t\t\t    <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t    </div>\n          ").open();
                            });
                            ModalDeleteRef_5.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "DeleteTase2Domain", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue_1.item.itemName).subscribe(function (dataModal) {
                                        if (dataModal != null && dataModal.result && dataModal.data != "") {
                                            tagForm.controls["domainsList"].component.setGridData(dataModal.data);
                                            tagForm.controls["domainsList"].component.selectGridDataMember(null);
                                            alertService.success("TR_DATA_DELETED");
                                        }
                                        else if (dataModal == "") {
                                            alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                        }
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_CANNOT_DELETE_DOMAIN");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                        if (editorField.schemaName == "addDataAttribute") {
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADDTASE2DATAATTRIBUTE;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var domainListValue = tagForm.controls["domainsList"].value;
                            if (domainListValue == null || domainListValue == "") {
                                alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
                                return "";
                            }
                            var node = { nodeClassName: "GTWTASE2DomainEditor", nodeCollectionKind: models_1.TreeNodeDTO.NodeCollectionKindEnum.ALL };
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: domainListValue.item.itemName, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService, node: node }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (dataModal) {
                                if (dataModal != null && dataModal.result && dataModal.data != "") {
                                    tagForm.controls["dataAttributesList"].component.setGridData(dataModal.data);
                                    alertService.success("TR_DATA_SAVED");
                                }
                                else if (dataModal == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error); });
                        }
                        if (editorField.schemaName == "deleteDataAttribute") {
                            var domainListValue_2 = tagForm.controls["domainsList"].value;
                            var dataAttributesListValue_1 = tagForm.controls["dataAttributesList"].value;
                            if (domainListValue_2 == null || domainListValue_2 == "") {
                                alertService.error("TR_TASE2_DOMAIN_IS_NOT_SELECTED");
                                return "";
                            }
                            if (domainListValue_2 == "VCC") {
                                alertService.error("TR_TASE2_CANNOT_DELETE_THE_VCC_DOMAIN");
                                return "";
                            }
                            if (dataAttributesListValue_1 == "") {
                                alertService.error("TR_TASE2_DATA_ATTRIBUTE_IS_NOT_SELECTED");
                                return "";
                            }
                            var ModalDeleteRef_6;
                            translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATA_ATTRIBUTE").subscribe(function (res) {
                                ModalDeleteRef_6 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_DELETE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t    <div class=\"panel panel-warning\">\n\t\t\t\t\t    <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t    </div>\n          ").open();
                            });
                            ModalDeleteRef_6.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "DeleteTase2DataAttribute", objectCollectionKind, tagForm.controls["objectName"].value, domainListValue_2.item.itemName, dataAttributesListValue_1.item.itemName).subscribe(function (dataModal) {
                                        if (dataModal != null && dataModal.result && dataModal.data != "") {
                                            tagForm.controls["dataAttributesList"].component.setGridData(dataModal.data);
                                            tagForm.controls["dataAttributesList"].component.selectGridDataMember(null);
                                            alertService.success("TR_DATA_DELETED");
                                        }
                                        else if (dataModal == "") {
                                            alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                        }
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_CANNOT_DELETE_DATA_ATTRIBUTE");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                        if (editorField.schemaName == "createDS") {
                            var DSListValue = tagForm.controls["DSList"].value;
                            var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDADDTASE2DATASET;
                            var addTitle = "TR_" + editorCommandsDTOString;
                            var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                            dashboardConfigTagEditorModalRef.result.then(function (data) {
                                if (data != null && data.result && data.DSName != "") {
                                    alertService.success("TR_DATA_SAVED");
                                    editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["objectName"].value, data.DSName).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectLastGridDataMember();
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                        }
                                    });
                                }
                                else if (data == "") {
                                    alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                }
                            }, function (error) { alertService.debug(error.toString()); });
                        }
                        if (editorField.schemaName == "manageDS") {
                            var DSListValue = tagForm.controls["DSList"].value;
                            if (DSListValue != "") {
                                var editorCommandsDTOString = models_1.EditorCommandsDTO.MENUCMDMANAGETASE2DATASETFULLEDIT;
                                var addTitle = "TR_" + editorCommandsDTOString;
                                var dashboardConfigTagEditorModalRef = modal.open(dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, ngx_modialog_7_1.overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: DSListValue, parentObjectName: tagForm.controls["objectName"].value, addTitle: addTitle, editorsService: editorsService }, bootstrap_1.BSModalContext));
                                dashboardConfigTagEditorModalRef.result.then(function (data) {
                                    if (data != null && data.result && data.DSName != "") {
                                        alertService.success("TR_DATA_SAVED");
                                        editorsService.editorAction(editorType, "ChangeDataSetName", objectCollectionKind, tagForm.controls["objectName"].value, data.DSName).subscribe(function (data) {
                                            tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                            tagForm.controls["DSList"].component.selectLastGridDataMember();
                                        }, function (error) {
                                            if (error.status == 401) {
                                                authenticationService.onLoginFailed("/");
                                            }
                                            else {
                                                alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                            }
                                        });
                                    }
                                    else if (data == "") {
                                        alertService.error("TR_ERROR_DATA_NOT_SAVED");
                                    }
                                }, function (error) { alertService.debug(error.toString()); });
                            }
                            else {
                                alertService.error("TR_TASE2_DATASET_IS_NOT_SELECTED");
                                return "";
                            }
                        }
                        if (editorField.schemaName == "deleteDS") {
                            var DSListValue_4 = tagForm.controls["DSList"].value;
                            var ModalDeleteRef_7;
                            translateService.get("TR_ARE_YOU_SURE_TO_DELETE_DATASET", { DSName: DSListValue_4 }).subscribe(function (res) {
                                ModalDeleteRef_7 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_DELETE'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t\t    <div class=\"panel panel-warning\">\n\t\t\t\t\t\t    <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t    </div>\n            ").open();
                            });
                            ModalDeleteRef_7.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "DeleteDS", objectCollectionKind, tagForm.controls["objectName"].value, DSListValue_4).subscribe(function (data) {
                                        tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                        tagForm.controls["DSList"].component.selectGridDataMember(0);
                                        tagForm.controls["DSMemberList"].component.componentData = null;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                        if (editorField.schemaName == "loadModel" && tagForm.controls["tase2CSVFileName"].value != "") {
                            editorsService.editorAction(editorType, "LoadModel", objectCollectionKind, tagForm.controls["objectName"].value, tagForm.controls["tase2CSVFileName"].value).subscribe(function (data) {
                                tagForm.controls["domainsList"].component.setGridData(data.data);
                                if (tagForm.controls["domainsList"].component.CheckGridDataMember) {
                                    tagForm.controls["domainsList"].component.selectGridDataMember(0);
                                }
                                editorsService.editorAction(editorType, "GetDataSet", objectCollectionKind, tagForm.controls["objectName"].value).subscribe(function (data) {
                                    tagForm.controls["DSList"].component.setGridData(data.dataDS);
                                    if (tagForm.controls["DSList"].component.CheckGridDataMember) {
                                        tagForm.controls["DSList"].component.selectGridDataMember(0);
                                    }
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                    else {
                                        alertService.error("TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED");
                                    }
                                });
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_LOAD_MODEL");
                                }
                            });
                        }
                        if (editorField.schemaName == "exportModel" && tagForm.controls["exportFileName"].value != "") {
                            editorsService.editorAction(editorType, "ExportModel", objectCollectionKind, tagForm.controls["objectName"].value, tagForm.controls["exportFileName"].value).subscribe(function (data) {
                                var csvFileName = tagForm.controls["tase2CSVFileName"].value;
                                editorsService.editorAction(editorType, "LoadEditorFiles", objectCollectionKind, tagForm.controls["objectName"].value).subscribe(function (data) {
                                    tagForm.controls["tase2CSVFileName"].component.listComponentData(data.data);
                                }, function (error) {
                                    if (error.status == 401) {
                                        authenticationService.onLoginFailed("/");
                                    }
                                });
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_EXPORTMODEL_FAILED");
                                }
                            });
                        }
                        if (editorField.schemaName == "clearModel") {
                            var ModalDeleteRef_8;
                            translateService.get("TR_ARE_YOU_SURE_TO_CLEAR_MODEL").subscribe(function (res) {
                                ModalDeleteRef_8 = modal.confirm()
                                    .size('lg')
                                    .showClose(true)
                                    .title(translateService.instant('TR_WARNING'))
                                    .okBtn(translateService.instant('TR_CLEAR_MODEL'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t  <div class=\"panel panel-warning\">\n\t\t\t\t  \t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t  </div>\n        ").open();
                            });
                            ModalDeleteRef_8.result.then(function (result) {
                                if (result) {
                                    editorsService.editorAction(editorType, "ClearModel", objectCollectionKind, tagForm.controls["objectName"].value).subscribe(function (data) {
                                        tagForm.controls["domainsList"].component.componentData = null;
                                        tagForm.controls["dataAttributesList"].component.componentData = null;
                                        tagForm.controls["DSList"].component.componentData = null;
                                        tagForm.controls["DSMemberList"].component.componentData = null;
                                    }, function (error) {
                                        if (error.status == 401) {
                                            authenticationService.onLoginFailed("/");
                                        }
                                        else {
                                            alertService.error("TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE");
                                        }
                                    });
                                }
                            }, function () { });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWTASE2DataAttributeEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    var _this = this;
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "dataType") {
                            var dataType = tagForm.controls["dataType"].value;
                            editorsService.editorAction(editorType, "ChangeDataTypeTase2", objectCollectionKind, dataType).subscribe(function (data) {
                                _this.enableControl(tagForm.controls["SBO"], data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_TASE2_DATA_TYPE");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWODBCClientEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "TestODBCConnectionString" && tagForm.controls["ODBCConnectionString"].value != "") {
                            editorsService.editorAction(editorType, "TestODBCConnectionString", objectCollectionKind, tagForm.controls["ODBCConnectionString"].value).subscribe(function (data) {
                                alertService.success("TR_SUCCESS");
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ODBC_OPENDB_FAILED");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWODBCQueryEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "tableList") {
                            var tableValue = tagForm.controls["tableList"].value;
                            editorsService.editorAction(editorType, "ChangeTable", objectCollectionKind, tagForm.controls["ODBCClient"].value, tableValue).subscribe(function (data) {
                                tagForm.controls["tableInfo"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "ODBCQuery") {
                            tagForm.controls["objectName"].component.inputNativeElement.focus();
                        }
                        if (editorField.schemaName == "executeSql") {
                            var queryValue = tagForm.controls["ODBCQuery"].value;
                            var queryAlias = tagForm.controls["ODBCQueryAliasName"].value;
                            editorsService.editorAction(editorType, "ExecuteSql", objectCollectionKind, tagForm.controls["ODBCClient"].value, queryValue.replace(/\?/gi, '%3F'), queryAlias).subscribe(function (data) {
                                tagForm.controls["queryResults"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE");
                                }
                            });
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.GTWGooseMonitorEditor = function (isRunDuringInit, modal, tagForm, editorField, editorType, objectCollectionKind, collapsiblePanels, editorsService, editorCommand, authenticationService, alertService, translateService, selectValue) {
                    if (!isRunDuringInit) {
                        if (editorField.schemaName == "GOOSEMonitorSCLFile") {
                            editorsService.editorAction(editorType, "GetGOOSEMonitorStreams", objectCollectionKind, tagForm.controls["GOOSEMonitorSCLFile"].value).subscribe(function (data) {
                                tagForm.controls["GOOSEMonitorStream"].component.setGridData(data.data);
                            }, function (error) {
                                if (error.status == 401) {
                                    authenticationService.onLoginFailed("/");
                                }
                                else {
                                    alertService.error("TR_ERROR_GOOSE_MONITOR_STREAMS_UNAVAILABLE");
                                }
                            });
                        }
                        if (editorField.schemaName == "GOOSEMonitorStream") {
                            var gooseMonitorStreamComponentData = tagForm.controls["GOOSEMonitorStream"].component.componentData;
                            var gooseMonitorStreamThreshold = tagForm.controls["GOOSEMonitorStreamThreshold"].value;
                            var currentRow = tagForm.controls["GOOSEMonitorStream"].value.index;
                            if (gooseMonitorStreamComponentData[currentRow].checkbox == true)
                                gooseMonitorStreamComponentData[currentRow].threshold = gooseMonitorStreamThreshold.toString();
                            else
                                gooseMonitorStreamComponentData[currentRow].threshold = "60";
                        }
                        if (editorField.schemaName == "objectName") {
                            var gooseMonitorStreamComponentData = tagForm.controls["GOOSEMonitorStream"].component.componentData;
                            var checkedItem_1 = [];
                            var GOOSEMonitorStreamValue = {};
                            gooseMonitorStreamComponentData.forEach(function (rowItem) {
                                if (rowItem.checkbox == true) {
                                    checkedItem_1.push(rowItem);
                                }
                                ;
                            });
                            GOOSEMonitorStreamValue.checkedItem = checkedItem_1;
                            tagForm.controls["GOOSEMonitorStream"].setValue(GOOSEMonitorStreamValue);
                        }
                    }
                    return "";
                };
                DashboardConfigTagEditorLogic.prototype.setTreeviewNode = function (parentChildrenSource, parent) {
                    var children = [];
                    if (parentChildrenSource != null && parentChildrenSource != "") {
                        parentChildrenSource.forEach(function (childJson) {
                            var child = new node_1.Node(childJson.nodeName, childJson.nodeFullName, parent, false, childJson.displayCheckbox);
                            child.hasChildren = childJson.hasChildren;
                            children.push(child);
                        });
                    }
                    return children;
                };
                DashboardConfigTagEditorLogic.prototype.setTreeviewNodeSelect = function (parentChildrenSource, parent) {
                    var children = [];
                    if (parentChildrenSource != null && parentChildrenSource != "") {
                        parentChildrenSource.forEach(function (childJson) {
                            var child = new node_select_1.NodeSelect(childJson.nodeName, childJson.nodeFullName, parent, false, childJson.displayCheckbox);
                            child.hasChildren = childJson.hasChildren;
                            children.push(child);
                        });
                    }
                    return children;
                };
                DashboardConfigTagEditorLogic.prototype.convertBool = function (value) {
                    if (value == "1" || value.toUpperCase() == "TRUE")
                        return true;
                    if (value == "0" || value.toUpperCase() == "FALSE")
                        return false;
                };
                DashboardConfigTagEditorLogic.prototype.getCheckedItems = function (source, checkIfNodeHasChildren) {
                    if (checkIfNodeHasChildren === void 0) { checkIfNodeHasChildren = false; }
                    var nodes = source.children;
                    var nodePaths = [];
                    this.getCheckedNodes(nodes, nodePaths, 0, checkIfNodeHasChildren);
                    return nodePaths;
                };
                DashboardConfigTagEditorLogic.prototype.getCheckedNodes = function (nodes, nodePaths, nodeDepth, checkIfNodeHasChildren) {
                    var node, childCheckedNodes;
                    var checkedNodes = [];
                    for (var i = 0; i < nodes.length; i++) {
                        node = nodes[i];
                        if (node.checked && (!node.hasChildren || checkIfNodeHasChildren)) {
                            checkedNodes.push(node);
                            nodePaths.push({ "item": node.nodeFullName });
                        }
                        if (node.children.length > 0) {
                            nodeDepth++;
                            childCheckedNodes = this.getCheckedNodes(node.children, nodePaths, nodeDepth, checkIfNodeHasChildren);
                            if (childCheckedNodes.length > 0)
                                checkedNodes = checkedNodes.concat(childCheckedNodes);
                        }
                    }
                    return checkedNodes;
                };
                DashboardConfigTagEditorLogic.prototype.enableControl = function (abstractControl, enable) {
                    if (enable === void 0) { enable = true; }
                    if (enable == true) {
                        if (abstractControl.editorField.isRequired) {
                            abstractControl.setValidators([forms_1.Validators.required]);
                            abstractControl.updateValueAndValidity();
                        }
                        abstractControl.editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.Yes;
                        abstractControl.enable();
                    }
                    else {
                        abstractControl.setValidators(null);
                        abstractControl.updateValueAndValidity();
                        abstractControl.editorField.isEditable = models_1.EditorFieldObjectDTO.IsEditableEnum.No;
                        abstractControl.disable();
                    }
                };
                return DashboardConfigTagEditorLogic;
            }());
            exports_1("DashboardConfigTagEditorLogic", DashboardConfigTagEditorLogic);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.editor.logic.js.map