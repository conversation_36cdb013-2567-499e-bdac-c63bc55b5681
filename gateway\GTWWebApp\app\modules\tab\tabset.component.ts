import { Component, Input, Output, ContentChildren, HostListener, EventEmitter } from "@angular/core";
import { TabComponent } from "./tab.component";

@Component({
  selector: "tabset",
  template: `
    <ul class="nav nav-tabs">
      <li *ngFor="let tab of tabs" [class.active]="tab.active">
        <a (click)="tabClicked(tab)" class="btn" [class.disabled]="tab.disabled">
          <span>{{tab.title}}</span>
        </a>
      </li>
    </ul>
    <ng-content></ng-content>
  `,
  styles: [`
    .nav {
      padding-left: 0;
      margin-bottom: 0;
      list-style: none;
    }
    .nav-tabs > li {
      float: left;
      position: relative;
      display: block;
    }
    .nav-tabs > li > a {
      position: relative;
      display: block;
      padding: 10px 15px;
    }
    .nav-tabs {
      border-bottom:none;
      margin-bottom: 10px;
    }
    .nav-tabs > li.disabled:hover {
      background: transparent;
    }
    .nav-tabs > li.disabled:hover > a {
      margin-top: 1px;
    }
    .nav-tabs > li > a {
      border: none;
      color: #000;
      background: transparent;
      font-weight: 600;
      opacity: .5;
      outline: none;
      box-shadow: none;
      cursor:pointer;
    }
    .nav-tabs > li > a:hover {
      border: none;
      opacity: 1;
    }
    .nav-tabs > li > a::after {
      content: "";
      height: 2px;
      position: absolute;
      width: 100%;
      left: 0px;
      bottom: -1px;
      transition: all 250ms ease 0s;
      transform: scale(0);
      background-image: linear-gradient(to right, rgba(200, 255, 200, 1), rgba(200, 255, 200, 0.60));
      color: #fff;
    }
    .nav-tabs > li.active > a, .nav-tabs > li.active > a:focus, .nav-tabs > li.active > a:hover {
      border-width: 0;
      opacity: 1;
    }
    .nav-tabs > li.active > a::after, .nav-tabs > li:hover > a::after {
      transform: scale(1);
    }
	`]
})
export class TabsetComponent {
  @Output() onSelect = new EventEmitter();
  @ContentChildren(TabComponent) tabs;

  ngAfterContentInit() {
    const tabs = this.tabs.toArray();
    const actives = this.tabs.filter(t => { return t.active });

    if (actives.length > 1) {
      console.error("Multiple active tabs set 'active'");
    }
    else if (!actives.length && tabs.length) {
      tabs[0].active = true;
    }
  }

  tabClicked(tab) {
    const tabs = this.tabs.toArray();
    tabs.forEach(tab => tab.active = false);
    tab.active = true;
    this.onSelect.emit(tab);
  }
}