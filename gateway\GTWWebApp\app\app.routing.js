System.register(["@angular/router", "./dashboard/dashboard.component", "./app.blank.component", "./settings/settings.component", "./user/user.grid.component", "./license/license.component", "./log/log.monitor.component", "./log/log.soe.component", "./log/log.audit.grid.component", "./help/help.about.component", "./help/help.update.component", "./help/help.support.component"], function (exports_1, context_1) {
    "use strict";
    var router_1, dashboard_component_1, app_blank_component_1, settings_component_1, user_grid_component_1, license_component_1, log_monitor_component_1, log_soe_component_1, log_audit_grid_component_1, help_about_component_1, help_update_component_1, help_support_component_1, appRoutes, Routing;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (dashboard_component_1_1) {
                dashboard_component_1 = dashboard_component_1_1;
            },
            function (app_blank_component_1_1) {
                app_blank_component_1 = app_blank_component_1_1;
            },
            function (settings_component_1_1) {
                settings_component_1 = settings_component_1_1;
            },
            function (user_grid_component_1_1) {
                user_grid_component_1 = user_grid_component_1_1;
            },
            function (license_component_1_1) {
                license_component_1 = license_component_1_1;
            },
            function (log_monitor_component_1_1) {
                log_monitor_component_1 = log_monitor_component_1_1;
            },
            function (log_soe_component_1_1) {
                log_soe_component_1 = log_soe_component_1_1;
            },
            function (log_audit_grid_component_1_1) {
                log_audit_grid_component_1 = log_audit_grid_component_1_1;
            },
            function (help_about_component_1_1) {
                help_about_component_1 = help_about_component_1_1;
            },
            function (help_update_component_1_1) {
                help_update_component_1 = help_update_component_1_1;
            },
            function (help_support_component_1_1) {
                help_support_component_1 = help_support_component_1_1;
            }
        ],
        execute: function () {
            appRoutes = [
                {
                    path: "",
                    component: dashboard_component_1.DashboardComponent
                },
                {
                    path: "dashboard",
                    component: dashboard_component_1.DashboardComponent
                },
                {
                    path: "settings",
                    component: settings_component_1.SettingsComponent
                },
                {
                    path: "license",
                    component: license_component_1.LicenseComponent
                },
                {
                    path: "users",
                    component: user_grid_component_1.UserGridComponent
                },
                {
                    path: "support",
                    component: help_support_component_1.HelpSupportComponent
                },
                {
                    path: "monitorLog",
                    component: log_monitor_component_1.LogMonitorComponent
                },
                {
                    path: "auditLog",
                    component: log_audit_grid_component_1.LogAuditGridComponent
                },
                {
                    path: "soeLog",
                    component: log_soe_component_1.LogSoeComponent
                },
                {
                    path: "about",
                    component: help_about_component_1.HelpAboutComponent
                },
                {
                    path: "update",
                    component: help_update_component_1.HelpUpdateComponent
                },
                {
                    path: "blank",
                    component: app_blank_component_1.AppBlankComponent
                },
                {
                    path: "**",
                    component: dashboard_component_1.DashboardComponent
                }
            ];
            exports_1("Routing", Routing = router_1.RouterModule.forRoot(appRoutes));
        }
    };
});
//# sourceMappingURL=app.routing.js.map