System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, DropTargetDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            DropTargetDirective = (function () {
                function DropTargetDirective() {
                    this.drop = new core_1.EventEmitter();
                }
                DropTargetDirective.prototype.onDrop = function (event) {
                    if (event.dataTransfer.getData('text') != "") {
                        event.preventDefault();
                        event.stopPropagation();
                        var data = JSON.parse(event.dataTransfer.getData('text'));
                        this.drop.emit(data);
                        if (event && event.currentTarget)
                            event.currentTarget.style.background = "";
                    }
                };
                __decorate([
                    core_1.Output('myDrop'),
                    __metadata("design:type", Object)
                ], DropTargetDirective.prototype, "drop", void 0);
                __decorate([
                    core_1.HostListener('drop', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [Object]),
                    __metadata("design:returntype", void 0)
                ], DropTargetDirective.prototype, "onDrop", null);
                DropTargetDirective = __decorate([
                    core_1.Directive({
                        selector: '[myDropTarget]'
                    }),
                    __metadata("design:paramtypes", [])
                ], DropTargetDirective);
                return DropTargetDirective;
            }());
            exports_1("DropTargetDirective", DropTargetDirective);
        }
    };
});
//# sourceMappingURL=drop-target.directive.js.map