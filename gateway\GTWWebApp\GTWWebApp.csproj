﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{1601A954-17BF-4CB0-A5A6-1A05BD8CB589}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>GTWWebApp</RootNamespace>
    <AssemblyName>GTWWebApp</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <ApplicationInsightsResourceId />
    <TypeScriptToolsVersion>4.3</TypeScriptToolsVersion>
    <TargetFrameworkProfile />
    <CodeAnalysisRuleSet>..\.sonarlint\sdgcsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\.sonarlint\sdgcsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\.sonarlint\sdgcsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\.sonarlint\sdg\CSharp\SonarLint.xml">
      <Link>SonarLint.xml</Link>
    </AdditionalFiles>
    <Content Include="app\app.component.template.html" />
    <Content Include="app\license\license.component.template.html" />
    <Content Include="app\help\help.component.template.html" />
    <TypeScriptCompile Include="app\authentication\authentication.password.modal.ts" />
    <TypeScriptCompile Include="app\authentication\authentication-interceptor.service.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.devices.search.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.import-export.modal.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.editor.advance.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tags.grid.search.component.ts" />
    <TypeScriptCompile Include="app\dashboard\dashboard.manager.component.ts" />
    <TypeScriptCompile Include="app\authentication\check.role.pipe.ts" />
    <TypeScriptCompile Include="app\data\api.module.ts" />
    <TypeScriptCompile Include="app\data\api\auth.service.ts" />
    <TypeScriptCompile Include="app\data\api\config.service.ts" />
    <TypeScriptCompile Include="app\data\api\editorContextMenu.service.ts" />
    <TypeScriptCompile Include="app\data\api\editors.service.ts" />
    <TypeScriptCompile Include="app\data\api\file.service.ts" />
    <TypeScriptCompile Include="app\data\api\help.service.ts" />
    <TypeScriptCompile Include="app\data\api\log.service.ts" />
    <TypeScriptCompile Include="app\data\api\manage.service.ts" />
    <TypeScriptCompile Include="app\data\api\mappings.service.ts" />
    <TypeScriptCompile Include="app\data\api\misc.service.ts" />
    <TypeScriptCompile Include="app\data\api\nodes.service.ts" />
    <TypeScriptCompile Include="app\data\api\tags.service.ts" />
    <TypeScriptCompile Include="app\data\api\workspace.service.ts" />
    <TypeScriptCompile Include="app\data\encoder.ts" />
    <TypeScriptCompile Include="app\data\model\EngineExitFailStateEnumDTO.ts" />
    <TypeScriptCompile Include="app\data\model\INIParamNodeDTO.ts" />
    <TypeScriptCompile Include="app\data\model\logDeviceDTO.ts" />
    <TypeScriptCompile Include="app\data\model\logDeviceGetDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TreeNodeCollectionObjectDTO.ts" />
    <TypeScriptCompile Include="app\global\gridContain.pipe.ts" />
    <TypeScriptCompile Include="app\global\safeHtml.pipe.ts" />
    <TypeScriptCompile Include="app\app.info.component.ts" />
    <TypeScriptCompile Include="app\help\help.update.component.ts" />
    <TypeScriptCompile Include="app\help\help.ini.component.ts" />
    <TypeScriptCompile Include="app\help\help.support.component.ts" />
    <TypeScriptCompile Include="app\help\help.quick-start.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.devices.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.device.virtual-node.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.tooltip.directive.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.grid.component.ts" />
    <TypeScriptCompile Include="app\data\api\audit.service.ts" />
    <TypeScriptCompile Include="app\data\model\AuditLogEntryDTO.ts" />
    <TypeScriptCompile Include="app\data\model\BoundObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\BroadcastEventDTO.ts" />
    <TypeScriptCompile Include="app\data\model\ClassNodeDTO.ts" />
    <TypeScriptCompile Include="app\data\model\EdgePairDTO.ts" />
    <TypeScriptCompile Include="app\data\model\EngineStateEnumDTO.ts" />
    <TypeScriptCompile Include="app\data\model\LogConfigMaskDTO.ts" />
    <TypeScriptCompile Include="app\data\model\LogEntryDTO.ts" />
    <TypeScriptCompile Include="app\data\model\SDGAboutDTO.ts" />
    <TypeScriptCompile Include="app\data\model\SDGHealthObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TagPurposeFilterEnumDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TagValueDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TagValuesDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TreeNodePageInfoDTO.ts" />
    <TypeScriptCompile Include="app\data\wsApi\NodesWSApi.ts" />
    <TypeScriptCompile Include="app\global\logEntryContain.pipe.ts" />
    <TypeScriptCompile Include="app\global\global.objectToCSV.util.ts" />
    <TypeScriptCompile Include="app\global\gtwImage.pipe.ts" />
    <TypeScriptCompile Include="app\global\global.json.util.ts" />
    <TypeScriptCompile Include="app\global\translateKey.pipe.ts" />
    <TypeScriptCompile Include="app\help\help.about.component.ts" />
    <TypeScriptCompile Include="app\log\log.soe.component.ts" />
    <TypeScriptCompile Include="app\log\log.monitor.grid.component.ts" />
    <TypeScriptCompile Include="app\log\log.monitor.component.ts" />
    <TypeScriptCompile Include="app\license\license.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.context-menu.directive.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.editor.component.ts" />
    <TypeScriptCompile Include="app\data\model\EditorContextMenuDTO.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.editor.logic.ts" />
    <TypeScriptCompile Include="app\data\model\ProtocolTypesEnumDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TargetTCPModeEnumDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TargetTypeEnumDTO.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.editor.group.pipe.ts" />
    <TypeScriptCompile Include="app\global\keys.pipe.ts" />
    <TypeScriptCompile Include="app\modules\alert\alert.log.modal.ts" />
    <TypeScriptCompile Include="app\modules\alert\alert.status-bar.component.ts" />
    <TypeScriptCompile Include="app\modules\alert\message.ts" />
    <TypeScriptCompile Include="app\modules\alert\alert.service.ts" />
    <TypeScriptCompile Include="app\app.component.ts" />
    <TypeScriptCompile Include="app\app.main.ts" />
    <TypeScriptCompile Include="app\app.module.ts" />
    <TypeScriptCompile Include="app\app.password.component.ts" />
    <TypeScriptCompile Include="app\app.routing.ts" />
    <TypeScriptCompile Include="app\authentication\authentication.login.modal.ts" />
    <TypeScriptCompile Include="app\authentication\authentication.service.ts" />
    <TypeScriptCompile Include="app\dashboard\log\dashboard.log.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.options.modal.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.value.quality.modal.ts" />
    <TypeScriptCompile Include="app\data\model\BroadcastEventTypeEnumDTO.ts" />
    <TypeScriptCompile Include="app\data\model\EditorCommandDTO.ts" />
    <TypeScriptCompile Include="app\data\model\EditorCommandsDTO.ts" />
    <TypeScriptCompile Include="app\data\model\EditorFieldObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\EditorSpecificationObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\HealthObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TreeNodeDTO.ts" />
    <TypeScriptCompile Include="app\data\wsApi\BroadcastEventWSApi.ts" />
    <TypeScriptCompile Include="app\global\global.data.service.ts" />
    <TypeScriptCompile Include="app\app.blank.component.ts" />
    <TypeScriptCompile Include="app\dashboard\dashboard.component.ts" />
    <TypeScriptCompile Include="app\app.health.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tags.grid.component.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.tag.editor.modal.ts" />
    <TypeScriptCompile Include="app\dashboard\config\dashboard.config.devices.treeview.component.ts" />
    <TypeScriptCompile Include="app\dashboard\log\dashboard.log.extra.component.ts" />
    <TypeScriptCompile Include="app\dashboard\log\dashboard.log.grid.component.ts" />
    <TypeScriptCompile Include="app\modules\angular-tooltip\tooltip.directive.ts" />
    <TypeScriptCompile Include="app\modules\bitmask\bitmask.advance-editor.component.ts" />
    <TypeScriptCompile Include="app\modules\bitmask\bitmask.editor.component.ts" />
    <TypeScriptCompile Include="app\modules\bitmask\bitmask.component.ts" />
    <TypeScriptCompile Include="app\modules\angular-tooltip\tooltip-item.ts" />
    <TypeScriptCompile Include="app\modules\angular-tooltip\tooltip.component.ts" />
    <TypeScriptCompile Include="app\modules\angular-tooltip\tooltip.service.ts" />
    <TypeScriptCompile Include="app\modules\checkbox-indeterminated\checkbox-indeterminated.directive.ts" />
    <TypeScriptCompile Include="app\modules\combobox\combobox.editor.multiselect.component.ts" />
    <TypeScriptCompile Include="app\modules\context-menu\context-menu.component.ts" />
    <TypeScriptCompile Include="app\modules\context-menu\context-menu.directive.ts" />
    <TypeScriptCompile Include="app\modules\context-menu\context-menu.service.ts" />
    <TypeScriptCompile Include="app\modules\context-menu\context-menu-item.ts" />
    <TypeScriptCompile Include="app\modules\download\download.modal.component.ts" />
    <TypeScriptCompile Include="app\modules\download\download.component.ts" />
    <TypeScriptCompile Include="app\modules\dragndrop\draggable.directive.ts" />
    <TypeScriptCompile Include="app\modules\dragndrop\drop-target.directive.ts" />
    <TypeScriptCompile Include="app\modules\dropdown\dropdown.directive.ts" />
    <TypeScriptCompile Include="app\modules\grid\grid.pagination.component.ts" />
    <TypeScriptCompile Include="app\modules\grid\grid.search.component.ts" />
    <TypeScriptCompile Include="app\modules\loader\loader-interceptor.service.ts" />
    <TypeScriptCompile Include="app\modules\loader\loader.service.ts" />
    <TypeScriptCompile Include="app\modules\loader\loader.component.ts" />
    <TypeScriptCompile Include="app\modules\resizable\resizable.component.ts" />
    <TypeScriptCompile Include="app\modules\resizable\resizable.directive.ts" />
    <TypeScriptCompile Include="app\modules\show-password\show-password.directive.ts" />
    <TypeScriptCompile Include="app\modules\tab\tab.component.ts" />
    <TypeScriptCompile Include="app\modules\tab\tabset.component.ts" />
    <TypeScriptCompile Include="app\modules\treeview-select\dynamic-treeview-select.component.ts" />
    <TypeScriptCompile Include="app\modules\treeview-select\node-select.ts" />
    <TypeScriptCompile Include="app\modules\treeview\dynamic-treeview.component.ts" />
    <TypeScriptCompile Include="app\modules\treeview\treeview.component.ts" />
    <TypeScriptCompile Include="app\log\log.audit.grid.component.ts" />
    <TypeScriptCompile Include="app\settings\settings.component.ts" />
    <TypeScriptCompile Include="app\settings\settings.form.component.ts" />
    <TypeScriptCompile Include="app\user\user.reset.password.modal.ts" />
    <TypeScriptCompile Include="app\user\user.modal.ts" />
    <TypeScriptCompile Include="app\user\user.grid.component.ts" />
    <TypeScriptCompile Include="app\data\api\api.ts" />
    <TypeScriptCompile Include="app\data\configuration.ts" />
    <TypeScriptCompile Include="app\data\index.ts" />
    <TypeScriptCompile Include="app\data\model\MappingObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\MdoObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\models.ts" />
    <TypeScriptCompile Include="app\data\model\SDGCheckAuthDTO.ts" />
    <TypeScriptCompile Include="app\data\model\SDGConfigDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TagCollectionObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\TagObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\model\UserObjectDTO.ts" />
    <TypeScriptCompile Include="app\data\variables.ts" />
    <TypeScriptCompile Include="app\data\wsApi\TagsWSApi.ts" />
    <TypeScriptCompile Include="app\data\wsApi\HealthWSApi.ts" />
    <TypeScriptCompile Include="app\data\wsApi\LogWSApi.ts" />
    <TypeScriptCompile Include="app\data\wsApi\wsApi.ts" />
    <TypeScriptCompile Include="app\help\help.component.ts" />
    <TypeScriptCompile Include="app\modules\angular-split\angular-split.module.ts" />
    <TypeScriptCompile Include="app\modules\angular-split\index.ts" />
    <TypeScriptCompile Include="app\modules\angular-split\split.component.ts" />
    <TypeScriptCompile Include="app\modules\angular-split\splitArea.directive.ts" />
    <TypeScriptCompile Include="app\modules\angular-split\splitGutter.directive.ts" />
    <TypeScriptCompile Include="app\modules\grid\column.ts" />
    <TypeScriptCompile Include="app\modules\grid\grid.component.ts" />
    <TypeScriptCompile Include="app\modules\panel\panel.ts" />
    <TypeScriptCompile Include="app\modules\panel\panel.service.ts" />
    <TypeScriptCompile Include="app\modules\panel\panel.component.ts" />
    <TypeScriptCompile Include="app\modules\collapsible-panel\collapsible-panel.ts" />
    <TypeScriptCompile Include="app\modules\treeview\node.ts" />
    <TypeScriptCompile Include="app\modules\uppercase\uppercase.directive.ts" />
    <Content Include="app\settings\settings.component.template.html" />
    <Content Include="app\settings\settings.form.component.template.html" />
    <Content Include="bootstrap.min.css" />
    <Content Include="browser-test-shim.js" />
    <Content Include="DevelopmentSetup.txt" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="gulpfile.js" />
    <Content Include="images\about.svg" />
    <Content Include="images\arrowLeft.svg" />
    <Content Include="images\arrowLeftDelete.svg" />
    <Content Include="images\arrowLeftRight.svg" />
    <Content Include="images\arrowLeftRightDelete.svg" />
    <Content Include="images\arrowRight.svg" />
    <Content Include="images\arrowRightDelete.svg" />
    <Content Include="images\arrowTopBottom.svg" />
    <Content Include="images\auditLog.svg" />
    <Content Include="images\background_1.svg" />
    <Content Include="images\background_2.svg" />
    <Content Include="images\background_3.svg" />
    <Content Include="images\background_4.svg" />
    <Content Include="images\background_5.svg" />
    <Content Include="images\background_6.svg" />
    <Content Include="images\background_7.svg" />
    <Content Include="images\background_8.svg" />
    <Content Include="images\change_value.svg" />
    <Content Include="images\check.svg" />
    <Content Include="images\close.svg" />
    <Content Include="images\config.svg" />
    <Content Include="images\cpu.svg" />
    <Content Include="images\customSplit.svg" />
    <Content Include="images\dashboard.svg" />
    <Content Include="images\dataLog.svg" />
    <Content Include="images\defaultView.svg" />
    <Content Include="images\delete.svg" />
    <Content Include="images\download.svg" />
    <Content Include="images\edit.svg" />
    <Content Include="images\GTWLogo.png" />
    <Content Include="images\gtw_objects\userFolder.svg" />
    <Content Include="images\healthView.svg" />
    <Content Include="images\gtw_objects\61850Client.svg" />
    <Content Include="images\gtw_objects\61850ClientNode.svg" />
    <Content Include="images\gtw_objects\61850Goose.svg" />
    <Content Include="images\gtw_objects\61850MDO.svg" />
    <Content Include="images\gtw_objects\61850PolledDataSet.svg" />
    <Content Include="images\gtw_objects\61850PolledPointSet.svg" />
    <Content Include="images\gtw_objects\61850Report.svg" />
    <Content Include="images\gtw_objects\61850Server.svg" />
    <Content Include="images\gtw_objects\alarms.svg" />
    <Content Include="images\gtw_objects\alarms2.svg" />
    <Content Include="images\gtw_objects\dataType.svg" />
    <Content Include="images\gtw_objects\dnpDataElement.svg" />
    <Content Include="images\gtw_objects\dnpDescriptor.svg" />
    <Content Include="images\gtw_objects\dnpPrototype.svg" />
    <Content Include="images\gtw_objects\equation.svg" />
    <Content Include="images\gtw_objects\gateway.png" />
    <Content Include="images\gtw_objects\internal.svg" />
    <Content Include="images\gtw_objects\internalPort.svg" />
    <Content Include="images\gtw_objects\internalUserDataObject.svg" />
    <Content Include="images\gtw_objects\mdoData.svg" />
    <Content Include="images\gtw_objects\modem.svg" />
    <Content Include="images\gtw_objects\modem_pool.svg" />
    <Content Include="images\gtw_objects\modem_pools.svg" />
    <Content Include="images\gtw_objects\odbcClient.svg" />
    <Content Include="images\gtw_objects\odbcQuery.svg" />
    <Content Include="images\gtw_objects\opcAEClient.svg" />
    <Content Include="images\gtw_objects\opcAEMDO.svg" />
    <Content Include="images\gtw_objects\opcAEServer.svg" />
    <Content Include="images\gtw_objects\opcClient.svg" />
    <Content Include="images\gtw_objects\opcMDO.svg" />
    <Content Include="images\gtw_objects\opcServer.svg" />
    <Content Include="images\gtw_objects\port.svg" />
    <Content Include="images\gtw_objects\sdoData.svg" />
    <Content Include="images\gtw_objects\sector.svg" />
    <Content Include="images\gtw_objects\session.svg" />
    <Content Include="images\gtw_objects\tase2Client.svg" />
    <Content Include="images\gtw_objects\tase2ClientNode.svg" />
    <Content Include="images\gtw_objects\tase2LogicalDevice.svg" />
    <Content Include="images\gtw_objects\tase2MDO.svg" />
    <Content Include="images\gtw_objects\tase2PolledDataSet.svg" />
    <Content Include="images\gtw_objects\tase2PolledPointSet.svg" />
    <Content Include="images\gtw_objects\tase2Report.svg" />
    <Content Include="images\gtw_objects\tase2Server.svg" />
    <Content Include="images\gtw_objects\undefine.svg" />
    <Content Include="images\help.svg" />
    <Content Include="images\help\contextMenuPaused.gif" />
    <Content Include="images\help\contextMenuPlaying.gif" />
    <Content Include="images\help\dashboardPaused.gif" />
    <Content Include="images\help\dashboardPlaying.gif" />
    <Content Include="images\help\mappingPaused.gif" />
    <Content Include="images\help\mappingPlaying.gif" />
    <Content Include="images\help\searchTagsPaused.gif" />
    <Content Include="images\help\searchTagsPlaying.gif" />
    <Content Include="images\horizontalSplit.svg" />
    <Content Include="images\iniFile.svg" />
    <Content Include="images\license.svg" />
    <Content Include="images\log.svg" />
    <Content Include="images\logoff.svg" />
    <Content Include="images\magnifier.svg" />
    <Content Include="images\magnifierMinus.svg" />
    <Content Include="images\magnifierPlus.svg" />
    <Content Include="images\memory.svg" />
    <Content Include="images\menu.svg" />
    <Content Include="images\ok.svg" />
    <Content Include="images\pause.svg" />
    <Content Include="images\performanceView.svg" />
    <Content Include="images\play.svg" />
    <Content Include="images\powerSwitch.svg" />
    <Content Include="images\properties.svg" />
    <Content Include="images\protocolAnalyser.svg" />
    <Content Include="images\configPanel.svg" />
    <Content Include="images\requestQuote.svg" />
    <Content Include="images\reset.svg" />
    <Content Include="images\resetPassword.svg" />
    <Content Include="images\save.svg" />
    <Content Include="images\scroll.svg" />
    <Content Include="images\scrollNo.svg" />
    <Content Include="images\soeLog.svg" />
    <Content Include="images\support.svg" />
    <Content Include="images\TMWLogo.png" />
    <Content Include="images\upload.svg" />
    <Content Include="images\users.svg" />
    <Content Include="images\validation.svg" />
    <Content Include="images\verticalSplit.svg" />
    <Content Include="images\warningView.svg" />
    <Content Include="index-dist.html" />
    <Content Include="index.html" />
    <Content Include="redirect-dist.html" />
    <Content Include="redirect.html" />
    <Content Include="rest\config_api.html" />
    <Content Include="rest\runtime_api.html" />
    <Content Include="styles.css" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="package.json" />
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="systemjs.config.js" />
    <Content Include="systemjs.dist.js" />
    <Content Include="tsconfig.json" />
    <Content Include="Web.config" />
    <Content Include="i18n\en.json" />
    <Content Include="i18n\fr.json" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="rest\config_swagger.yaml" />
    <Content Include="rest\runtime_swagger.yaml" />
    <Content Include="i18n\es.json" />
    <Content Include="i18n\pt.json" />
    <Content Include="bootstrap.min.css.map" />
    <Content Include="..\.sonarlint\sdgcsharp.ruleset">
      <Link>sdgcsharp.ruleset</Link>
    </Content>
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <SaveServerSettingsInUserFile>True</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>