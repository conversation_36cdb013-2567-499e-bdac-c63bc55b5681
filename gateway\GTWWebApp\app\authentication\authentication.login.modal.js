System.register(["@angular/core", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "../modules/alert/alert.service", "../data/api/api", "@ngx-translate/core"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            if (typeof b !== "function" && b !== null)
                throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ngx_modialog_7_1, bootstrap_1, alert_service_1, api_1, core_2, AuthenticationLoginModal, LoginModalContext;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            }
        ],
        execute: function () {
            AuthenticationLoginModal = (function () {
                function AuthenticationLoginModal(dialog, authService, alertService, translate, modal) {
                    this.dialog = dialog;
                    this.authService = authService;
                    this.alertService = alertService;
                    this.translate = translate;
                    this.modal = modal;
                    this.currentUser = {};
                    this.authentication = {};
                    this.context = dialog.context;
                    this.context.dialogClass = "modal-dialog modal-sm";
                    dialog.setCloseGuard(this);
                    dialog.inElement = true;
                }
                AuthenticationLoginModal.prototype.beforeDismiss = function () {
                    return true;
                };
                AuthenticationLoginModal.prototype.login = function (isForcingUsersLogoff) {
                    var _this = this;
                    if (isForcingUsersLogoff === void 0) { isForcingUsersLogoff = false; }
                    this.authService.configuration = this.dialog.context.userApiConfig;
                    this.authService.loginUser(this.currentUser.username, this.currentUser.password, isForcingUsersLogoff)
                        .subscribe(function (data) {
                        _this.authentication = data;
                        _this.alertService.clearMessage();
                        _this.dialog.close(_this.authentication);
                    }, function (error) {
                        if (error.status == 419) {
                            _this.alertService.clearMessage();
                            _this.dialog.close({ status: error.status, username: _this.currentUser.username });
                        }
                        else if (error.status == 409) {
                            var confirmSaveModalRef_1;
                            _this.translate.get("TR_LOG_IN_FAILED_OTHER_USERS_ARE_ALREADY_LOGGED_IN").subscribe(function (res) {
                                confirmSaveModalRef_1 = _this.modal.confirm()
                                    .size('lg')
                                    .showClose(false)
                                    .title(_this.translate.instant('TR_WARNING'))
                                    .okBtn(_this.translate.instant('TR_YES'))
                                    .cancelBtn(_this.translate.instant('TR_NO'))
                                    .okBtnClass('btn btn-default')
                                    .body("\n\t\t\t\t\t\t<div class=\"panel panel-warning\">\n\t\t\t\t\t\t\t<div class=\"panel-heading\"><div class=\"glyphicon glyphicon-exclamation-sign\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t")
                                    .open();
                            });
                            confirmSaveModalRef_1.result.then(function (result) { return _this.login(true); }, function () { return _this.currentUser.password = ""; });
                        }
                        else if (error.status == 406) {
                            _this.translate.get("TR_UNAUTHORIZED_SU_ALREADY_LOGIN").subscribe(function (res) {
                                _this.alertService.error(res);
                            });
                            _this.alertService.debug(error.message.toString());
                        }
                        else if (error.status == 412) {
                            _this.translate.get("TR_ADMIN_USER_IS_NOT_CONFIGURED").subscribe(function (res) {
                                _this.alertService.error(res);
                            });
                            _this.alertService.debug(error.message.toString());
                        }
                        else if (error.status == 420) {
                            _this.translate.get("TR_UNAUTHORIZED_MAX_FAILED_LOGIN_ATTEMPT_REACHED").subscribe(function (res) {
                                _this.alertService.error(res);
                            });
                            _this.alertService.debug(error.message.toString());
                        }
                        else {
                            _this.translate.get("TR_UNAUTHORIZED").subscribe(function (res) {
                                _this.alertService.error(res);
                            });
                            _this.alertService.debug(error.message.toString());
                        }
                    });
                };
                AuthenticationLoginModal = __decorate([
                    core_1.Component({
                        selector: "authenticationLoginModal",
                        providers: [api_1.AuthService],
                        styles: ["\n      .password {\n        display:inline-block;\n        width:90%;\n        margin-bottom: 0px;\n      }\n      .status-bar{\n        margin: 2px;\n      }\n    "],
                        template: "\n\t\t\t\t\t<div class=\"container-fluid\">\n\t\t\t\t\t\t<div class=\"modal-heading\">{{'TR_LOGIN' | translate}}</div>\n\t\t\t\t\t\t<div class=\"modal-body\">\n              <form name=\"form\" (ngSubmit)=\"f.form.valid && login()\" #f=\"ngForm\" novalidate>\n\t              <div class=\"form-group\" [ngClass]=\"{ 'has-error': f.submitted && !username.valid }\">\n\t\t\t            <label for=\"username\">{{'TR_USERNAME' | translate}}</label>\n\t\t\t            <input type=\"text\" class=\"form-control\" name=\"username\" [(ngModel)]=\"currentUser.username\" #username=\"ngModel\" required />\n\t\t\t            <small class=\"text-danger\" *ngIf=\"f.submitted && !username.valid\">{{'TR_USERNAME_IS_REQUIRED' | translate}}</small>\n\t              </div>\n\t              <div class=\"form-group password\" [ngClass]=\"{ 'has-error': f.submitted && !password.valid }\">\n\t\t\t            <label for=\"password\">{{'TR_PASSWORD' | translate}}</label>\n\t\t\t            <input type=\"password\" class=\"form-control\" name=\"password\" [(ngModel)]=\"currentUser.password\" #password=\"ngModel\" required show-password/> \n\t              </div>\n                <small class=\"text-danger\" *ngIf=\"f.submitted && !password.valid\">{{'TR_PASSWORD_IS_REQUIRED' | translate}}</small>\n\t              <div class=\"form-group\" style=\"display: inline-block;width:99%; text-align: left; margin-top: 8px;\">\n\t\t              <div style=\"display: table-cell; text-align: right;\">\n\t\t\t              <button class=\"btn btn-default\"><img src=\"../../images/ok.svg\" class=\"image-button\"/>&nbsp;{{'TR_LOGIN_BUTTON' | translate}}</button>\n\t\t              </div>\n\t              </div>\n                <br>\n\t\t            <alertStatusBarComponent class=\"status-bar\"></alertStatusBarComponent>\n              </form>\n\t\t\t\t\t  </div>\n\t\t\t\t\t</div>"
                    }),
                    __metadata("design:paramtypes", [ngx_modialog_7_1.DialogRef, api_1.AuthService, alert_service_1.AlertService, core_2.TranslateService, bootstrap_1.Modal])
                ], AuthenticationLoginModal);
                return AuthenticationLoginModal;
            }());
            exports_1("AuthenticationLoginModal", AuthenticationLoginModal);
            LoginModalContext = (function (_super) {
                __extends(LoginModalContext, _super);
                function LoginModalContext() {
                    var _this = _super !== null && _super.apply(this, arguments) || this;
                    _this.userApiConfig = null;
                    return _this;
                }
                return LoginModalContext;
            }(bootstrap_1.BSModalContext));
            exports_1("LoginModalContext", LoginModalContext);
        }
    };
});
//# sourceMappingURL=authentication.login.modal.js.map