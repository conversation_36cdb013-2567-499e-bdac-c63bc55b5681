import { Component, Input, OnInit, On<PERSON><PERSON>roy } from "@angular/core";
import { Subscription } from "rxjs";
import { Router } from '@angular/router';
import { LogEntryDTO } from "../../data/model/models";
import { LogWSApi } from "../../data/wsApi/wsApi";
import { LogService } from "../../data/api/api";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { GlobalObjectToCSVUtil } from "../../global/global.objectToCSV.util";
import { LogEntryContainPipe } from "../../global/logEntryContain.pipe";


@Component({
	selector: "dashboardLogGridComponent",
  template: `
      <table style="table-layout:fixed; width:100%;">
        <tr>
          <th style="width: 15%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 10%">&nbsp;</th>
          <th style="width: 45%">&nbsp;</th>
        </tr>
        <tr *ngFor="let logEntry of logEntries | logEntryContain:logEntryFilter;" [ngClass]="{'error': (logEntry.severity == 'Exception' || logEntry.severity == 'Error')}">
          <td>{{logEntry.timeStamp}}</td>
          <td>{{logEntry.source}}</td>
          <td>{{logEntry.name}}</td>
          <td>{{logEntry.category}}</td>
          <td>{{logEntry.severity}}</td>
          <td>{{logEntry.message}}</td>
        </tr>
			</table>
		  <div *ngIf="logEntries.length == 0" class="grid-no-results">{{'TR_NO_RESULTS' | translate}}</div>
  `,
	styles: [`
          td {
				    padding-right: 6px;
				    padding-left: 6px;
            padding-top: 8px;
            overflow-wrap: anywhere;
          }
          th {
            line-height:1px;
            height: 1px;
            overflow: hidden;
          }
          tr:nth-of-type(odd) {
            background-color: rgba(255,	255, 255, 0.35) !important;
          }
          .error {
            color: red;
          }
        `]
})

export class DashboardLogGridComponent implements OnInit, OnDestroy {
  private logServiceSubscription: Subscription;
  @Input() logEntryFilter: LogEntryDTO = {};
  @Input() isWSPaused: boolean = false;
  private logEntries: Array<LogEntryDTO> = [];
  private websocket: WebSocket;
  private reconnectAttempts: number = 0;

  constructor(private dashboardLogService: LogService, private dashboardLogWSApi: LogWSApi,
    private authenticationService: AuthenticationService, private alertService: AlertService,
    private logEntryContainPipe: LogEntryContainPipe, private translate: TranslateService,
    private router: Router) {
	}

  public ngOnInit(): void {
    this.loadAPI();
	}

	public ngOnDestroy(): void {
    if (this.websocket && this.websocket.readyState != null && this.websocket.readyState === WebSocket.OPEN)
      this.websocket.close(3000, "panelClose");
    if (this.logServiceSubscription != null)
      this.logServiceSubscription.unsubscribe();
  }
 
  public copyToCSV(): void {
    let logEntriesFiltered: Array < LogEntryDTO > =[]
    logEntriesFiltered = this.logEntryContainPipe.transform(this.logEntries, this.logEntryFilter);
    GlobalObjectToCSVUtil.copyToCSV("DashboardLog.csv", logEntriesFiltered);
  }

  public clearLog(): void {
    this.logEntries = [];
  }

  private loadAPI(): void {
    let gtwHost = this.authenticationService.globalDataService.SDGConfig.gtwHost;
    let gtwHttpPort = this.authenticationService.globalDataService.SDGConfig.gtwHttpPort;
    let gtwWSHostPort: string = "ws://" + gtwHost + ":" + gtwHttpPort.toString();
    let gtwHostPort: string = "http://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";

    if (location.protocol === "https:") {
      gtwWSHostPort = "wss://" + gtwHost + ":" + gtwHttpPort.toString();
      gtwHostPort = "https://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";
    }

    this.dashboardLogWSApi.basePath = gtwWSHostPort;
    this.dashboardLogService.basePath = gtwHostPort;

    this.dashboardLogWSApi.openWebsocket().subscribe(
      data => {
        this.websocket = data;
        this.wsGetLogData();
      },
      error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Open fail" }); }
    );

    this.dashboardLogService.getLastLogEntryID().subscribe(
      lastLogEntryID => {
        if (isNaN(lastLogEntryID))
          lastLogEntryID = 0;
        if (lastLogEntryID && lastLogEntryID > 500) {
          this.dashboardLogService.getLogEntriesRange(lastLogEntryID - 500).subscribe(
            data => {
              if (GlobalObjectToCSVUtil.isNotEmpty(data))
                this.logEntries = data;
            },
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
            }
          );
        }
        else {
          this.dashboardLogService.getLogEntriesRange(0, lastLogEntryID).subscribe(
            data => {
              if (GlobalObjectToCSVUtil.isNotEmpty(data))
                this.logEntries = data;
            },
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
            }
          );
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      }
    );
  }

  private pauseWS(isWSPaused: boolean): void {
    this.isWSPaused = isWSPaused;
  }

  private wsGetLogData(): void {
    this.logServiceSubscription = this.dashboardLogWSApi.getLogData(this.websocket).subscribe(
      (event) => {
        if (event.type === 'message') {
          this.reconnectAttempts = 0;
          if (!this.isWSPaused) {
            let wsLogEntries: LogEntryDTO = JSON.parse(event.data);
            Array.prototype.push.apply(this.logEntries, wsLogEntries);
            if (this.logEntries.length > 1000)
              this.logEntries = this.logEntries.slice(this.logEntries.length - 1000, 1000);
          }
        }
        else if (event.type === 'close') {
          if (this.reconnectAttempts < 3) {
            setTimeout(() => {
              const currentPath: string = this.router.url;
              const pageName: string = currentPath.split('/').pop();
              if (currentPath !== "/dashboard" && currentPath !== "/config" && currentPath !== "/")
                return;
              this.reconnectAttempts++;
              this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getLogEntries", reconnectAttempt: this.reconnectAttempts }).subscribe(res => {
                this.alertService.debug(res);
              });
              this.dashboardLogWSApi.openWebsocket().subscribe(
                data => {
                  this.websocket = data;
                  this.wsGetLogData();
                },
                error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Reopen fail" }); }
              );
            }, 5000);
          }
        }
      }
    );
  }
}
