System.register([], function (exports_1, context_1) {
    "use strict";
    var EngineStateEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (EngineStateEnumDTO) {
                EngineStateEnumDTO[EngineStateEnumDTO["NOTRUNNING"] = 'NOT_RUNNING'] = "NOTRUNNING";
                EngineStateEnumDTO[EngineStateEnumDTO["RUNNING"] = 'RUNNING'] = "RUNNING";
                EngineStateEnumDTO[EngineStateEnumDTO["STARTUPSTARTING"] = 'STARTUP_STARTING'] = "STARTUPSTARTING";
                EngineStateEnumDTO[EngineStateEnumDTO["STARTUPCSVLOADED"] = 'STARTUP_CSV_LOADED'] = "STARTUPCSVLOADED";
                EngineStateEnumDTO[EngineStateEnumDTO["STARTUPINILOADED"] = 'STARTUP_INI_LOADED'] = "STARTUPINILOADED";
                EngineStateEnumDTO[EngineStateEnumDTO["STARTUPDONE"] = 'STARTUP_DONE'] = "STARTUPDONE";
                EngineStateEnumDTO[EngineStateEnumDTO["SAVINGINICSV"] = 'SAVING_INI_CSV'] = "SAVINGINICSV";
                EngineStateEnumDTO[EngineStateEnumDTO["SHUTTINGDOWN"] = 'SHUTTING_DOWN'] = "SHUTTINGDOWN";
                EngineStateEnumDTO[EngineStateEnumDTO["ERRORININI"] = 'ERROR_IN_INI'] = "ERRORININI";
                EngineStateEnumDTO[EngineStateEnumDTO["STARTUPUNKNOWN"] = 'STARTUP_UNKNOWN'] = "STARTUPUNKNOWN";
                EngineStateEnumDTO[EngineStateEnumDTO["PROCESSLOADED"] = 'PROCESS_LOADED'] = "PROCESSLOADED";
                EngineStateEnumDTO[EngineStateEnumDTO["RUNNING_NO_LICENSE"] = 'RUNNING_NO_LICENSE'] = "RUNNING_NO_LICENSE";
                EngineStateEnumDTO[EngineStateEnumDTO["RUNNING_IN_GRACE_PERIOD"] = 'RUNNING_IN_GRACE_PERIOD'] = "RUNNING_IN_GRACE_PERIOD";
            })(EngineStateEnumDTO || (EngineStateEnumDTO = {}));
            exports_1("EngineStateEnumDTO", EngineStateEnumDTO);
        }
    };
});
//# sourceMappingURL=EngineStateEnumDTO.js.map