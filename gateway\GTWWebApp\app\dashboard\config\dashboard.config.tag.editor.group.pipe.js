System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, DashboardConfigTagEditorGroupPipe;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagEditorGroupPipe = (function () {
                function DashboardConfigTagEditorGroupPipe() {
                }
                DashboardConfigTagEditorGroupPipe.prototype.transform = function (items, groupPanelName, fieldsetName) {
                    var result = [];
                    try {
                        if (!items || !(groupPanelName || fieldsetName)) {
                            return items;
                        }
                        if (groupPanelName && !fieldsetName) {
                            result = items.filter(function (item) { return item.groupPanelName == groupPanelName; });
                        }
                        if (!groupPanelName && fieldsetName) {
                            result = items.filter(function (item) { return item.fieldsetName == fieldsetName; });
                        }
                        if (groupPanelName && fieldsetName) {
                            result = items.filter(function (item) { return item.fieldsetName == fieldsetName && item.groupPanelName == groupPanelName; });
                        }
                    }
                    catch (err) {
                    }
                    return result;
                };
                DashboardConfigTagEditorGroupPipe = __decorate([
                    core_1.Pipe({ name: 'editorGroupPipe' })
                ], DashboardConfigTagEditorGroupPipe);
                return DashboardConfigTagEditorGroupPipe;
            }());
            exports_1("DashboardConfigTagEditorGroupPipe", DashboardConfigTagEditorGroupPipe);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.editor.group.pipe.js.map