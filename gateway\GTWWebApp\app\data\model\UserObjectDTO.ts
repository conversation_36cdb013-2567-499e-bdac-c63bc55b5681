/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export interface UserObjectDTO {
    username?: string;

    password?: string;

    email?: string;

    authenticated?: boolean;

    isactive?: boolean;

    role?: number;

}
