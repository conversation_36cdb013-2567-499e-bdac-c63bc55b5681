﻿<!DOCTYPE html> 
<html>
<head>
  <title>TMW SCADA Data Gateway Web UI</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="./node_modules/bootstrap/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="./styles.css">
  <!-- 1. Load libraries -->
  <script>var exports = {};</script>
  <script src="./node_modules/zone.js/dist/zone.js"></script>
  <script src="./node_modules/reflect-metadata/Reflect.js"></script>
  <script src="./node_modules/systemjs/dist/system.src.js"></script>
  <!-- 2. Configure SystemJS -->
  <script src="./systemjs.config.js"></script>
  <script type="text/javascript">
    if (navigator.userAgent.indexOf("Chrome") == -1 && navigator.userAgent.indexOf("Firefox") == -1)  {
      alert("This Web Browser is incompatible.\nPlease use the most recent version of Goolgle Chrome, Mozilla Firefox or Microsoft Edge.");
    }
    System.import("app").catch(function (err) { console.error(err); });
  </script>
</head>
<!-- 3. Display the application -->
<body style="background-color: #e6e6e6; min-width: 550px;">
	<div class="container-fluid">
		<app>
          <div class="center-loader">
            <img src="./images/GTWLogo.png" class="logo-loader">
            <div class="loader"></div>
          </div>
		</app>
	</div>
</body>
</html>