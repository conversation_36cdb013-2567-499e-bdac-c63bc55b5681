/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

/**
 * Sent by /getTags web socket endpoint, also used by other apis
 */
export interface TagObjectDTO {
  tagName?: string;

  tagUserName?: string;

  tagValue?: string;

  tagQuality?: string;

  tagTime?: string;

  tagDescription?: string;

  tagOptions?: string;

  tagClassName?: string;

  tagObjectIcon?: number;

  isHealthy?: boolean;

  /**
   * See GTWDEFS_TAG_PROPERTY_MASK definitions
   */
  tagPropertyMask?: number;

  /**
   * A more user friendly description for the type of the tag
   */
  tagValueType?: string;

  /**
   * See GTWTYPES_TAG_PURPOSE_MASK definitions
   */
  tagPurposeMask?: number;

  /**
   * A collection of tags bound to this tag
   */
  tagBindingList?: Array<models.BoundObjectDTO>;

}

export namespace TagObjectDTO {
  export enum TagValueTypeEnum {
    Unknown = <any>'Unknown',
    Bool = <any>'Bool',
    Spare = <any>'Spare',
    Char = <any>'Char',
    UnsignedChar = <any>'Unsigned Char',
    Short = <any>'Short',
    UnsignedShort = <any>'Unsigned Short',
    Long = <any>'Long',
    UnsignedLong = <any>'Unsigned Int',
    Float = <any>'Float',
    Double = <any>'Double',
    String = <any>'String',
    Time = <any>'Time',
    Int64 = <any>'Int 64',
    UnsignedInt64 = <any>'Unsigned Int 64',
    Empty = <any>''
  }

  export enum TagValueTypeTextEnum {
    'Unknown' = <any>'Unknown',
    'Bool' = <any>'Bool',
    'Spare'= <any>'Spare',
    'Char' = <any>'Char',
    'Unsigned Char' = <any>'Unsigned Char',
    'Short' = <any>'Short',
    'Unsigned Short' = <any>'Unsigned Short',
    'Long' = <any>'Long',
    'Unsigned Int' = <any>'Unsigned Int',
    'Float' = <any>'Float',
    'Double' = <any>'Double',
    'String' = <any>'String',
    'Time' = <any>'Time',
    'Int 64' = <any>'Int 64',
    'Unsigned Int 64' = <any>'Unsigned Int 64',
    '' = <any>'Empty'
  }

  export enum TagCollectionKindEnum {
    MDO = <any>'MDO',
    SDO = <any>'SDO',
  }

  export enum TagReadWriteEnum {
    Unknown = <any>'Unknown',
    R = <any>'R',
    W = <any>'W',
    RW = <any>'RW',
  }

  export enum TagPropertyMaskEnum {
    CAN_EDIT = 0,
    CAN_DELETE = 1,
    CAN_CHANGE_VALUE = 2,
    READ_WRITE = 3,
    VALUE_TYPE = 4,
    COLLEC_KIND = 5,
    SPARE = 6
  }

  let tagPropertyMaskType = [
    0x00000001, //  1
    0x00000002, //  1
    0x00000004, //  1
    0x00000018, //  2 00-? 01-R 10-W 11-RW
    0x000003E0, //  5 see GTWDEFS_TYPE
    0x00000400, //  1 0 MDO 1 SDO
    0x00000800  //  1 
   ]

  let tagPropertyMaskPosition = [
    0,  //  1
    1,  //  1
    2,  //  1
    3,  //  2 00-? 01-R 10-W 11-RW
    5,  //  5 see GTWDEFS_TYPE
    10, //  1 0 MDO 1 SDO
    11  //  1 
  ]
 
  export function getTagPropertyMaskValue(tagPropertyMask: number, tagPropertyMaskEnum: TagPropertyMaskEnum): number  {
    let value: number;
    value = (tagPropertyMask & tagPropertyMaskType[tagPropertyMaskEnum]) >> tagPropertyMaskPosition[tagPropertyMaskEnum];
    return value;
  }

  export function getTagPropertyMaskStringValue(tagPropertyMask: number, tagPropertyMaskEnum: TagPropertyMaskEnum): string {
    let value: number = this.getTagPropertyMaskValue(tagPropertyMask, tagPropertyMaskEnum);
    let stringValue: string;
    if (tagPropertyMaskEnum == TagPropertyMaskEnum.VALUE_TYPE)
      stringValue = Object.keys(TagValueTypeTextEnum)[value];
    else if (tagPropertyMaskEnum == TagPropertyMaskEnum.COLLEC_KIND)
      stringValue = Object.keys(TagCollectionKindEnum)[value];
    else if (tagPropertyMaskEnum == TagPropertyMaskEnum.READ_WRITE)
      stringValue = Object.keys(TagReadWriteEnum)[value];
    return stringValue;
  }
}
