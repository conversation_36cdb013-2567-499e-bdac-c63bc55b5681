System.register(["@angular/core", "@angular/common", "@angular/router", "./data/api/api", "./data/wsApi/wsApi", "./data/model/models", "./modules/alert/alert.service", "./authentication/authentication.service", "./global/global.data.service", "@ngx-translate/core", "./help/help.quick-start.component", "./help/help.ini.component", "./app.info.component", "ngx-modialog-7/plugins/bootstrap"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, common_1, router_1, api_1, wsApi_1, models_1, alert_service_1, authentication_service_1, global_data_service_1, core_2, help_quick_start_component_1, help_ini_component_1, app_info_component_1, bootstrap_1, AppComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (common_1_1) {
                common_1 = common_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (help_quick_start_component_1_1) {
                help_quick_start_component_1 = help_quick_start_component_1_1;
            },
            function (help_ini_component_1_1) {
                help_ini_component_1 = help_ini_component_1_1;
            },
            function (app_info_component_1_1) {
                app_info_component_1 = app_info_component_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            }
        ],
        execute: function () {
            AppComponent = (function () {
                function AppComponent(authenticationService, alertService, modal, globalDataService, router, auditService, authService, configService, nodesService, editorsService, manageService, mappingsService, logService, tagsService, miscService, editorContextMenuService, fileService, helpService, workspaceService, logWSApi, tagsWSApi, nodesWSApi, healthWSApi, broadcastEventWSApi, translate, location) {
                    this.authenticationService = authenticationService;
                    this.alertService = alertService;
                    this.modal = modal;
                    this.globalDataService = globalDataService;
                    this.router = router;
                    this.auditService = auditService;
                    this.authService = authService;
                    this.configService = configService;
                    this.nodesService = nodesService;
                    this.editorsService = editorsService;
                    this.manageService = manageService;
                    this.mappingsService = mappingsService;
                    this.logService = logService;
                    this.tagsService = tagsService;
                    this.miscService = miscService;
                    this.editorContextMenuService = editorContextMenuService;
                    this.fileService = fileService;
                    this.helpService = helpService;
                    this.workspaceService = workspaceService;
                    this.logWSApi = logWSApi;
                    this.tagsWSApi = tagsWSApi;
                    this.nodesWSApi = nodesWSApi;
                    this.healthWSApi = healthWSApi;
                    this.broadcastEventWSApi = broadcastEventWSApi;
                    this.translate = translate;
                    this.location = location;
                    this.title = "SCADA Data Gateway";
                    this.isNavigationIn = false;
                    this.tryBroadcastEventWS = null;
                    this.infoTypeEnumEnum = app_info_component_1.InfoTypeEnum;
                    this.isAppLoading = true;
                    translate.addLangs(["en"]);
                    translate.setDefaultLang("en");
                }
                AppComponent.prototype.isValidBrowser = function () {
                    if (navigator.userAgent.indexOf("Chrome") != -1)
                        return true;
                    else if (navigator.userAgent.indexOf("Firefox") != -1)
                        return true;
                    else
                        return false;
                };
                AppComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    this.configService.getConfig().subscribe(function (data) {
                        _this.globalDataService.SDGConfig = data;
                        _this.changeConfigAPI(_this.globalDataService.SDGConfig.gtwHost, _this.globalDataService.SDGConfig.gtwHttpPort, _this.globalDataService.SDGConfig.monHost, _this.globalDataService.SDGConfig.monHttpPort);
                        if (location.hostname.toLowerCase() != _this.globalDataService.SDGConfig.monHost.toLowerCase() || location.port != _this.globalDataService.SDGConfig.monHttpPort.toString()) {
                            var monURL = location.protocol + "//" + _this.globalDataService.SDGConfig.monHost + ":" + _this.globalDataService.SDGConfig.monHttpPort;
                            location.href = monURL;
                        }
                        _this.authService.checkAuth(_this.authenticationService.userApiConfig.apiKeys["Authorization"]).subscribe(function (data) { _this.authenticationService.login(data); }, function (error) { if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/dashboard");
                        }
                        else {
                            _this.alertService.debug(error.message.toString());
                        } });
                        if (!_this.globalDataService.SDGConfig.gtwUseWebSSL && localStorage.getItem("SDGHideHTTPSWarningAtStartup") !== "true") {
                            _this.appInfoUseWebSSL.show();
                            _this.appInfoUseWebSSL.setInfoType = _this.infoTypeEnumEnum.HTTPS_WARNING;
                        }
                        if (_this.globalDataService.SDGConfig.gtwHttpsCertIsTmwSigned) {
                            _this.appInfoHttpsCertIsTmwSigned.show();
                            _this.appInfoHttpsCertIsTmwSigned.setInfoType = _this.infoTypeEnumEnum.HTTPS_TMW_CERT;
                        }
                        if (!_this.isValidBrowser()) {
                            _this.appInfoValidBrowser.show();
                            _this.appInfoValidBrowser.setInfoType = _this.infoTypeEnumEnum.BROWSER_WARNING;
                        }
                    }, function (error) {
                        _this.alertService.debug(error.toString());
                    }, function () {
                        setTimeout(function () { _this.isAppLoading = false; }, 10000);
                        _this.openGlobalBroadcastServiceSubscription();
                    });
                };
                AppComponent.prototype.ngOnDestroy = function () {
                    if (this.broadcastEventWSApi.websocket !== null && this.broadcastEventWSApi.websocket.readyState === WebSocket.OPEN)
                        this.broadcastEventWSApi.websocket.close();
                    if (this.globalBroadcastServiceSubscription != null)
                        this.globalBroadcastServiceSubscription.unsubscribe();
                };
                AppComponent.prototype.beforeUnloadHander = function (event) {
                    this.logoff();
                };
                AppComponent.prototype.openGlobalBroadcastServiceSubscription = function () {
                    var _this = this;
                    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(function (event) {
                        if (event.type === 'message') {
                            var broadcastEventData = JSON.parse(event.data);
                            if (broadcastEventData.messageType == models_1.BroadcastEventTypeEnumDTO.ForceLogOffUser.toString()) {
                                _this.router.navigate(["/blank"]);
                                var modalLogoffRef_1;
                                _this.translate.get(broadcastEventData.messageKey).subscribe(function (res) {
                                    modalLogoffRef_1 = _this.modal.alert()
                                        .size('lg')
                                        .showClose(true)
                                        .title(_this.translate.instant('TR_ERROR'))
                                        .okBtn(_this.translate.instant('TR_CLOSE'))
                                        .okBtnClass('btn btn-default')
                                        .isBlocking(true)
                                        .body("\n\t\t\t\t\t\t      <div class=\"panel panel-danger\">\n\t\t\t\t\t\t\t      <div class=\"panel-heading\"><div class=\"glyphicon glyphicon-alert\"></div>&nbsp;&nbsp;" + res + "</div>\n\t\t\t\t\t\t      </div>")
                                        .open();
                                });
                                modalLogoffRef_1.result.then(function (result) { return location.reload(); }, function () { return location.reload(); });
                            }
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.RefreshUI.toString()) {
                                if (_this.isAppLoading == true)
                                    return;
                                if (broadcastEventData.parameters != null && broadcastEventData.parameters.refreshWebBrowser != null) {
                                    var currentUrl_1 = _this.router.url;
                                    _this.router.navigateByUrl('/', { skipLocationChange: true }).then(function () {
                                        _this.router.navigate([currentUrl_1]);
                                    });
                                }
                            }
                            else {
                                _this.alertService.show(broadcastEventData);
                            }
                        }
                    }, function (error) {
                        if (error != "restart") {
                            _this.displayRefreshModal();
                        }
                    });
                };
                AppComponent.prototype.displayRefreshModal = function () {
                    var broadcastEvent = {
                        messageKey: "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER",
                        messageLogMask: 3,
                        messageText: "The connection with the Gateway Monitor was lost.<br /> Please refresh your browser when the monitor is running again.",
                        messageTime: new Date().toUTCString(),
                        messageType: models_1.BroadcastEventTypeEnumDTO.MessageError,
                        parameters: null
                    };
                    this.alertService.show(broadcastEvent, false, -1);
                };
                AppComponent.prototype.changeConfigAPI = function (gtwHost, gtwHttpPort, monHost, monHttpPort) {
                    this.configService.configuration = this.authenticationService.userApiConfig;
                    this.auditService.configuration = this.authenticationService.userApiConfig;
                    this.fileService.configuration = this.authenticationService.userApiConfig;
                    this.authService.configuration = this.authenticationService.userApiConfig;
                    this.nodesService.configuration = this.authenticationService.userApiConfig;
                    this.editorsService.configuration = this.authenticationService.userApiConfig;
                    this.manageService.configuration = this.authenticationService.userApiConfig;
                    this.mappingsService.configuration = this.authenticationService.userApiConfig;
                    this.logService.configuration = this.authenticationService.userApiConfig;
                    this.tagsService.configuration = this.authenticationService.userApiConfig;
                    this.miscService.configuration = this.authenticationService.userApiConfig;
                    this.helpService.configuration = this.authenticationService.userApiConfig;
                    this.logWSApi.configuration = this.authenticationService.userApiConfig;
                    this.tagsWSApi.configuration = this.authenticationService.userApiConfig;
                    this.nodesWSApi.configuration = this.authenticationService.userApiConfig;
                    this.editorContextMenuService.configuration = this.authenticationService.userApiConfig;
                    this.broadcastEventWSApi.configuration = this.authenticationService.userApiConfig;
                    this.healthWSApi.configuration = this.authenticationService.userApiConfig;
                    this.workspaceService.configuration = this.authenticationService.userApiConfig;
                    var gtwHttpHostPort = "http://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";
                    var gtwWSHostPort = "ws://" + gtwHost + ":" + gtwHttpPort.toString();
                    var monWSHostPort = "ws://" + monHost + ":" + monHttpPort.toString();
                    if (location.protocol === "https:") {
                        gtwHttpHostPort = "https://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";
                        gtwWSHostPort = "wss://" + gtwHost + ":" + gtwHttpPort.toString();
                        monWSHostPort = "wss://" + monHost + ":" + monHttpPort.toString();
                    }
                    this.nodesService.basePath = gtwHttpHostPort;
                    this.editorsService.basePath = gtwHttpHostPort;
                    this.manageService.basePath = gtwHttpHostPort;
                    this.mappingsService.basePath = gtwHttpHostPort;
                    this.tagsService.basePath = gtwHttpHostPort;
                    this.helpService.basePath = gtwHttpHostPort;
                    this.logService.basePath = gtwHttpHostPort;
                    this.editorContextMenuService.basePath = gtwHttpHostPort;
                    this.logWSApi.basePath = gtwWSHostPort;
                    this.tagsWSApi.basePath = gtwWSHostPort;
                    this.nodesWSApi.basePath = gtwWSHostPort;
                    this.broadcastEventWSApi.basePath = monWSHostPort;
                    this.getConfigEngine();
                };
                AppComponent.prototype.getConfigEngine = function () {
                    var _this = this;
                    this.logService.getNumLogEntries().subscribe(function (data) { }, function (error) {
                        if (navigator.userAgent.indexOf("Firefox") > 0) {
                            var parameters = { URL_ENGINE: location.protocol + "//" + _this.globalDataService.SDGConfig.gtwHost + ":" + _this.globalDataService.SDGConfig.gtwHttpPort };
                            var broadcastEvent = { messageKey: "TR_WEB_BROWSER_CANNOT_CONNECT_GATEWAY_ENGINE_ACCEPT_SELFSIGNED_CERTIFICATE_FIREFOX", messageLogMask: 2, messageType: models_1.BroadcastEventTypeEnumDTO.MessageError, parameters: parameters };
                            _this.alertService.show(broadcastEvent, false);
                        }
                        else {
                            var broadcastEvent = { messageKey: "TR_WEB_BROWSER_CANNOT_CONNECT_GATEWAY_ENGINE_CHECK_ENGINE_SERVER_FIREWALL", messageLogMask: 2, messageType: models_1.BroadcastEventTypeEnumDTO.MessageError };
                            _this.alertService.show(broadcastEvent, false);
                        }
                    });
                };
                AppComponent.prototype.toggleNavigationState = function () {
                    var bool = this.isNavigationIn;
                    this.isNavigationIn = bool === false ? true : false;
                };
                AppComponent.prototype.quickStartVisible = function () {
                    this.helpQuickStart.show();
                };
                AppComponent.prototype.iniHelpVisible = function () {
                    this.helpIni.show();
                };
                AppComponent.prototype.logoff = function () {
                    var _this = this;
                    this.authService.logoffUser()
                        .subscribe(function (data) {
                        _this.authenticationService.logoff();
                    }, function (error) {
                        _this.alertService.debug(error.message.toString());
                    }, function () { return location.reload(); });
                };
                __decorate([
                    core_1.ViewChild('helpQuickStart', { static: false }),
                    __metadata("design:type", help_quick_start_component_1.HelpQuickStartComponent)
                ], AppComponent.prototype, "helpQuickStart", void 0);
                __decorate([
                    core_1.ViewChild('helpIni', { static: false }),
                    __metadata("design:type", help_ini_component_1.HelpIniComponent)
                ], AppComponent.prototype, "helpIni", void 0);
                __decorate([
                    core_1.ViewChild('appInfoHttpsCertIsTmwSigned', { static: false }),
                    __metadata("design:type", app_info_component_1.AppInfoComponent)
                ], AppComponent.prototype, "appInfoHttpsCertIsTmwSigned", void 0);
                __decorate([
                    core_1.ViewChild('appInfoValidBrowser', { static: false }),
                    __metadata("design:type", app_info_component_1.AppInfoComponent)
                ], AppComponent.prototype, "appInfoValidBrowser", void 0);
                __decorate([
                    core_1.ViewChild('appInfoUseWebSSL', { static: false }),
                    __metadata("design:type", app_info_component_1.AppInfoComponent)
                ], AppComponent.prototype, "appInfoUseWebSSL", void 0);
                __decorate([
                    core_1.HostListener('window:beforeunload', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [Object]),
                    __metadata("design:returntype", void 0)
                ], AppComponent.prototype, "beforeUnloadHander", null);
                AppComponent = __decorate([
                    core_1.Component({
                        selector: "app",
                        providers: [api_1.AuditService, api_1.AuthService, api_1.ConfigService, api_1.NodesService, api_1.EditorsService, api_1.ManageService, api_1.MappingsService, api_1.LogService, api_1.TagsService, api_1.FileService, api_1.MiscService, api_1.HelpService, api_1.WorkspaceService,
                            wsApi_1.LogWSApi, wsApi_1.TagsWSApi, wsApi_1.NodesWSApi, wsApi_1.HealthWSApi, api_1.EditorContextMenuService, wsApi_1.BroadcastEventWSApi],
                        templateUrl: "app/app.component.template.html",
                        styles: ["\n      .navbar-header {\n        margin-right: 0px;\n        margin-left: 0px;\n      }\n\t\t\t.status-bar {\n\t\t\t\toverflow: hidden;\n\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\tposition: fixed;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft:0;\n\t\t\t\twidth: 100%;\n\t\t\t\tborder-top: 1px solid #a31c3f;\n        z-index: 999;\n\t\t\t}\n\t\t\tnav {\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t\tnav > a {\n\t\t\t\tfont-size:24px;\n\t\t\t\tpadding:16px;\n\t\t\t}\n\t\t\t.navbar-inverse .navbar-nav>li>a {\n        color: white;\n\t\t\t}\n      a{\n        color: white;\n        text-decoration: none;\n        cursor: pointer;\n      }\n      .dropdown-menu{\n        margin: 0px;\n        padding:0px;\n        border-radius: 0px;\n        background-color: black;\n        font-size: 12px;\n      }\n      .dropdown-item{\n        background-color: black;\n        padding: 10px;\n        border: 1px solid white;\n        white-space: nowrap;\n      }\n      .container-fluid {\n        padding-left: 0px;\n        padding-right: 0px;\n      }\n      .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {\n        background-image: url(../images/background_7.svg), linear-gradient(to left, #4a4a4a, #000000);\n        background-size: 100%;\n        opacity: 0.9;\n      }\n      .navbar-brand {\n        z-index: 999;\n        position: relative;\n      }\n      .menu-icon{\n        width: 20px;\n        height: 20px;\n        padding-right: 4px;\n        padding-bottom: 2px;\n      }\n      .logo {\n        width: 60px;\n        height: 60px;\n        margin-top: -12px;\n        margin-right: 10px;\n        margin-left: 6px;\n      }\n      .logo-text-container{\n        display: inline-block; \n        margin-left: -28px; \n        transform: scale(.8, 1);\n      }\n      .logo-text {\n        border:0; \n        vertical-align: 6px; \n        font-size: 30px; \n        font-weight:bold;\n        color:#f1e186;\n      }\n      .logo-tmw {\n        position: fixed; \n        top: 10px; \n        right: 35px; \n        width: 90px;\n      }\n      .navbar-red\n      {\n        width: 100%;\n        height: 4px;\n        background-color: #a31c3f;\n        position: fixed;\n        z-index: -999;\n      }\n      @media only screen and (max-width: 770px) {\n        .dropdown-menu{\n          padding: 0px !important;\n        }\n        .dropdown-item{\n          padding: 5px 5px 5px 20px;\n          border: none;\n        }\n        .logo-tmw {\n          display: none;\n        }\n      }"]
                    }),
                    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService, alert_service_1.AlertService, bootstrap_1.Modal, global_data_service_1.GlobalDataService, router_1.Router,
                        api_1.AuditService, api_1.AuthService,
                        api_1.ConfigService, api_1.NodesService,
                        api_1.EditorsService, api_1.ManageService,
                        api_1.MappingsService, api_1.LogService,
                        api_1.TagsService, api_1.MiscService,
                        api_1.EditorContextMenuService, api_1.FileService,
                        api_1.HelpService, api_1.WorkspaceService,
                        wsApi_1.LogWSApi, wsApi_1.TagsWSApi,
                        wsApi_1.NodesWSApi, wsApi_1.HealthWSApi,
                        wsApi_1.BroadcastEventWSApi,
                        core_2.TranslateService, common_1.Location])
                ], AppComponent);
                return AppComponent;
            }());
            exports_1("AppComponent", AppComponent);
        }
    };
});
//# sourceMappingURL=app.component.js.map