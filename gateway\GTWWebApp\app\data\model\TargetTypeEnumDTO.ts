/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum TargetTypeEnumDTO {
    _232 = <any> 'TMWTARGIO_TYPE_232',
    MODEMPOOLCHANNEL = <any> 'TMWTARGIO_TYPE_MODEM_POOL_CHANNEL',
    MODEMPOOL = <any> 'TMWTARGIO_TYPE_MODEM_POOL',
    MODEM = <any> 'TMWTARGIO_TYPE_MODEM',
    TCP = <any> 'TMWTARGIO_TYPE_TCP',
    UDPTCP = <any> 'TMWTARGIO_TYPE_UDP_TCP',
    MBP = <any> 'TMWTARGIO_TYPE_MBP',
    MON = <any> 'TMWTARGIO_TYPE_MON',
}
