WebApp:
  TypeScript 4.3.2 for VisualStudio 2019

  Node.js
    https://nodejs.org/en/download/
    Make some setting changes in VisualStudio: Tools > Options > Projects And Solutions > External Web tools
	  C:\Program Files\nodejs
	  .\node_modules\.bin
	  $(PATH)

   NPM Task Runner
    https://marketplace.visualstudio.com/items?itemName=MadsKristensen.NPMTaskRunner
    package.json -> right click -> Task Runner Explorer

   GULP (only to build WebApp is release)
    npm install -g gulp

linux setup
  sudo apt-get install libssl-dev
  sudo apt-get install uuid-dev
