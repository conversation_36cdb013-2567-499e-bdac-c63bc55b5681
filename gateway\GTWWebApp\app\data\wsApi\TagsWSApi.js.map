{"version": 3, "file": "TagsWSApi.js", "sourceRoot": "", "sources": ["TagsWSApi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAYE,mBAAoB,YAA0B,EAAU,SAA2B;oBAA/D,iBAAY,GAAZ,YAAY,CAAc;oBAAU,cAAS,GAAT,SAAS,CAAkB;oBAH5E,aAAQ,GAAW,qBAAqB,CAAC;oBAC1C,kBAAa,GAAkB,IAAI,6BAAa,EAAE,CAAC;gBAGzD,CAAC;gBAEM,iCAAa,GAApB;oBAAA,iBAWC;oBAVC,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;oBAC/G,OAAO,iBAAU,CAAC,MAAM,CAAC,UAAA,QAAQ;wBAC/B,SAAS,CAAC,MAAM,GAAG,UAAC,SAAS;4BAC3B,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCACjF,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;4BACH,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;4BACzB,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACtB,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAA;gBACJ,CAAC;gBAEK,+BAAW,GAAlB,UAAmB,SAAoB;oBAAvC,iBAqBC;oBApBA,OAAO,iBAAU,CAAC,MAAM,CAAC,UAAA,QAAQ;wBAChC,SAAS,CAAC,SAAS,GAAG,UAAC,GAAG;4BACzB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACpB,CAAC,CAAC;wBACC,SAAS,CAAC,OAAO,GAAG,UAAC,GAAG;4BACtB,IAAI,GAAG,CAAC,MAAM,KAAK,YAAY,EAAE;gCAC/B,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;oCACxG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAC/B,CAAC,CAAC,CAAC;6BACJ;iCACI;gCACH,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;oCACpG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAC/B,CAAC,CAAC,CAAC;gCACH,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;6BACpB;4BACD,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACtB,CAAC,CAAC;oBACN,CAAC,CAAC;yBACC,IAAI,CAAC,iBAAK,EAAE,CAAC,CAAC;gBAClB,CAAC;gBAzCW,SAAS;oBADrB,iBAAU,EAAE;qDAKuB,4BAAY,EAAqB,uBAAgB;mBAJxE,SAAS,CA0CrB;gBAAD,gBAAC;aAAA,AA1CD;;QA0CC,CAAC"}