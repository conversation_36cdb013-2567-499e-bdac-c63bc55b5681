import { Injectable } from "@angular/core";
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs"
import { AuthenticationService } from "./authentication.service";

@Injectable()
export class AuthenticationInterceptorService implements HttpInterceptor {
  constructor(private authenticationService: AuthenticationService) { }

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    request = request.clone({
      params: (request.params ? request.params : new HttpParams())
        .set('token', this.authenticationService.userApiConfig.apiKeys["Authorization"])
    });
    return next.handle(request);
  }
}