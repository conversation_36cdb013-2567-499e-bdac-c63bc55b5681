﻿<div>
  <div class=" panel-main panel panel-default" style="width:99%; margin-left:6px; margin-top:60px" *ngIf="this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'">
    <div class="panel-heading"><img src="../../images/license.svg" class="module-icon" />{{ 'TR_LICENSE_MANAGER' | translate }}</div>
    <div class="panel-body config-content">
      <collapsiblePanel [title]="'TR_LICENSE_INFORMATION'" [isOpen]="true">
        <table cellpadding="4">
          <tr>
            <td><b>{{ 'TR_LICENSE_TYPE' | translate }}:</b></td>
            <td>{{ licenseInfo?.licenseType }} - {{ licenseInfo?.numberAllowedPoint }} {{ 'TR_POINTS' | translate }}</td>
          </tr>
          <tr *ngIf="licenseInfo?.licenseType !='Permanent'">
            <td><b>{{ 'TR_EXPIRES' | translate }}:</b></td>
            <td>{{ licenseInfo?.expires }}</td>
          </tr>
          <tr *ngIf="licenseInfo?.licenseType !='Trial'">
            <td><b>{{ 'TR_MAINTENANCE_PLAN_EXPIRES' | translate }}:</b></td>
            <td>{{ licenseInfo?.mpExpires }}</td>
          </tr>
          <tr>
            <td><b>{{ 'TR_KEY_ID' | translate }}:</b></td>
            <td>{{ licenseInfo?.keyId }}</td>
          </tr>
          <tr *ngIf="licenseInfo?.licenseType !='Trial'">
            <td><b>{{ 'TR_INVOICE' | translate }}:</b></td>
            <td>{{ licenseInfo?.invoice }}</td>
          </tr>
        </table>
      </collapsiblePanel>
      <collapsiblePanel [title]="'TR_LICENSE_LOGS'" [isOpen]="true">
        <div>
          <button type="button" id="downloadFile" (click)="onlogFileDownloadClick()" class="btn btn-default btn-sm"><img src="../../images/download.svg" class="image-button" />&nbsp;{{ 'TR_DOWNLOAD_LICENSE_LOG_FILES' | translate }}</button>
        </div>
      </collapsiblePanel>
      <collapsiblePanel [title]="'TR_LICENSE_OPTIONS'" [isOpen]="true">
        <table *ngIf="rows?.length>0" class="table table-striped">
          <tr>
            <td *ngFor="let col of columns"><b>{{col.header | translate}}</b></td>
          </tr>
          <tr *ngFor="let row of rows">
            <td *ngFor="let col of columns">
              <ng-container *ngIf="col.field == 'feature'"><div style="display: inline-block;">{{row[col.field]}}</div></ng-container>
              <ng-container *ngIf="col.field == 'isLicensed'">
                <div *ngIf="!isChecked(row['isLicensed'])" style="display: inline-block; color: red;padding-left: 50px;" class="glyphicon glyphicon-remove"></div>
                <div *ngIf="isChecked(row['isLicensed'])" style="display: inline-block; color: green;padding-left: 50px;" class="glyphicon glyphicon-ok"></div>
              </ng-container>
            </td>
          </tr>
        </table>
        <div *ngIf="!rows">{{'TR_NO_RESULTS' | translate}}</div>
      </collapsiblePanel>
      <collapsiblePanel [title]="'TR_ACTIVATE_PRODUCT_KEY_ONLINE'" [isOpen]="true">
        <form (ngSubmit)="onlineActivation()">
          <div><b>{{'TR_ONLINE_ACTIVATION' | translate}}</b></div>
          <br />
          <div>{{'TR_ONLINE_ACTIVATION_TEXT' | translate}}</div>
          <br />
          <div>{{'TR_ENTER_PRODUCT_KEY' | translate}}:&nbsp;&nbsp;<input type="text" name="product_key" size="30" [(ngModel)]="licenceForm.product_key" required /></div>
          <div style="margin:10px 0px 6px 0px">
            <fieldset>
              <legend>{{'TR_SELECT_ONE' | translate}}</legend>
              <input type="radio" name="isNewLicense" [value]=true [(ngModel)]="licenceForm.isNewLicense" />
              <span>{{'TR_PRODUCT_KEY_FOR_NEW_LICENSE' | translate}}</span>
              <br />
              <input type="radio" name="isNewLicense" [value]=false [(ngModel)]="licenceForm.isNewLicense" />
              <span>{{'TR_PRODUCT_KEY_FOR_UPDATE_LICENSE' | translate}}</span>
            </fieldset>
          </div>
          <div style="margin:12px 0px 6px 0px"><button class="btn btn-default" [disabled]="licenceForm.product_key.length < 32 || !licenceForm.product_key" type="submit"><img src="../../images/check.svg" class="image-button" />&nbsp;{{'TR_ACTIVATE_ONLINE' | translate}}</button></div>
        </form>
      </collapsiblePanel>
      <collapsiblePanel [title]="'TR_ACTIVATE_PRODUCT_KEY_OFFLINE'" [isOpen]="true">
        <div style="margin-top:10px"><b>{{'TR_OFFLINE_ACTIVATION' | translate}}</b></div>
        <br />
        <div>{{'TR_OFFLINE_ACTIVATION_STEP1_TEXT' | translate}} <br /><b><a href="https://licensing.trianglemicroworks.com/ems/customerLogin.html" target="_blank">https://licensing.trianglemicroworks.com/ems/customerLogin.html</a></b></div>
        <br />
        <div>
          <fieldset>
            <legend>{{'TR_SELECT_ONE' | translate}}</legend>
            <input type="radio" name="isNewLicense" [value]=true [(ngModel)]="licenceForm.isNewLicense" />
            <span>{{'TR_PRODUCT_KEY_FOR_NEW_LICENSE' | translate}}</span>
            <br />
            <input type="radio" name="isNewLicense" [value]=false [(ngModel)]="licenceForm.isNewLicense" />
            <span>{{'TR_PRODUCT_KEY_FOR_UPDATE_LICENSE' | translate}}</span>
          </fieldset>
        </div>
        <div style="margin:12px 0px 6px 0px"><button class="btn btn-default" (click)="offlineActivationStep1()"><img src="../../images/check.svg" class="image-button" />&nbsp;{{'TR_CREATE_C2V' | translate}}</button></div>
        <br />
        <br />
        <div>{{'TR_OFFLINE_ACTIVATION_STEP2_TEXT'| translate}}</div>
        <div style="margin:10px 0px 6px 0px">
          <label class="btn btn-default">
            <img src="../../images/check.svg" class="image-button" />&nbsp;{{'TR_INSTALL_V2C' | translate}}
            <input style="display: none;" #uploadV2CFile type="file" (change)="offlineActivationStep2($event)" placeholder="Upload file" accept=".v2c">
          </label>
        </div>
      </collapsiblePanel>
    </div>
  </div>
</div>
<downloadComponent #download></downloadComponent>