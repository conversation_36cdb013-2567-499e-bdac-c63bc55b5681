import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, Input, HostListener, OnChanges } from "@angular/core";
import { Router } from '@angular/router';
import { Subscription } from "rxjs"
import { TagsService, EditorsService, NodesService, MappingsService  } from "../../data/api/api";
import { TagsWSApi, BroadcastEventWSApi } from "../../data/wsApi/wsApi";
import { TreeNodeDTO, TagObjectDTO, EditorCommandsDTO, BroadcastEventTypeEnumDTO, EditorFieldObjectDTO, EdgePairDTO, BoundObjectDTO, TagPurposeFilterEnumDTO } from "../../data/model/models";
import { DashboardConfigComponent } from "./dashboard.config.component";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { Page } from "../../modules/grid/grid.pagination.component";
import { <PERSON><PERSON>n, Cell } from "./dashboard.config.grid.component";
import { Modal } from "ngx-modialog-7/plugins/bootstrap";
import { TranslateService } from "@ngx-translate/core";
import { GlobalDataService } from "../../global/global.data.service";
import { SearchTypeEnum, SearchCriteria, SearchField } from "./dashboard.config.tags.grid.search.component";

@Component({
  selector: "dashboardConfigTagsGridComponent",
  styles: [`
    ::ng-deep [hidden].page+split-gutter { 
      flex-basis: 0px !important;
      height: 0px !important;
    }
    .search-bar{
      position: relative;
      display: block;
    }
  `],
  template: `
    <split direction="vertical">
      <split-area [size]="93">
		    <dashboardConfigTagsGridSearchComponent class="search-bar" (onSearch)="onSearchGrid()" (onSearchIsActive)="onSearchIsActive($event)" [(searchCriteria)]="searchCriteria" [isRecursive]="isRecursive"></dashboardConfigTagsGridSearchComponent>
        <dashboardConfigGridComponent [rows]="tags" [columns]="columns" (onSortChange)="onSortChange($event)" (onClickActionGrid)="clickActionGrid($event)"></dashboardConfigGridComponent>
      </split-area>
      <split-area [size]="7" *ngIf="this.tagCount > this.itemPerPage && this.itemPerPage > 0">
		    <gridPaginationComponent (onPageSelect)="onPageSelect($event)" [pageEdgePairs]="pageEdgePairs"></gridPaginationComponent>
      </split-area>
    </split>
	`
})

export class DashboardConfigTagsGridComponent implements OnInit, OnDestroy, OnChanges {
  @Input() selectedNode: TreeNodeDTO;
  @Input() isRecursive: boolean;
  @Input() tagPurposeFilter: number;

  private globalBroadcastServiceSubscription: Subscription;
  private tagServiceSubscription: Subscription;
  private tags: Array<any>;
  private columns: Array<Column>;
  private tagsWebsocket: WebSocket;
  private searchCriteria: SearchCriteria;
  private searchIsActive: boolean;
  private tagCount: number = 0;
  private currentPage: Page = null;
  private itemPerPage: number = 0;
  private pageEdgePairs: Array<EdgePairDTO> = [];
  private sortColumn: string
  private sortColumnDirection: string;
  private tagsWSRconnectAttempts: number = 0;
  private broadcastWSReconnectAttempts: number = 0;

  constructor(private modal: Modal, private tagsService: TagsService, private editorsService: EditorsService, private nodesApi: NodesService, private mappingsApi: MappingsService, private tagsWSApi: TagsWSApi,
    private alertService: AlertService, private authenticationService: AuthenticationService, private broadcastEventWSApi: BroadcastEventWSApi, private translate: TranslateService,
    private globalDataService: GlobalDataService, private dashboardConfigComponent: DashboardConfigComponent, private router: Router) {
    this.columns = this.getColumns();
  }

  public ngOnInit(): void {
    this.itemPerPage = this.globalDataService.SDGConfig.gtwHttpPageBlockSize;
    this.currentPage = new Page(1, this.itemPerPage, "", "");

    this.tagsWSApi.openWebsocket().subscribe(
      data => {
        this.tagsWebsocket = data;
        this.wsGetTagsData();
      },
      error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getTags", reason: "Open fail" }); }
    );

    this.openGlobalBroadcastServiceSubscription();

    let searchFields: Array<SearchField> = [];
    searchFields.push(new SearchField("tagName", "TR_NAME", "", EditorFieldObjectDTO.ControlTypeEnum.Text, null, "TR_TAG_NAME_SEARCH_HELP"));
    searchFields.push(new SearchField("tagUserName", "TR_TAG_USER_NAME", "", EditorFieldObjectDTO.ControlTypeEnum.Text, null, "TR_TAG_NAME_SEARCH_HELP"));
    searchFields.push(new SearchField("tagValueType", "TR_TYPE", "", EditorFieldObjectDTO.ControlTypeEnum.Combobox, [
      { "value": "", "text":"" },
      { "value": "Bool", "text": "Bool" },
      { "value": "Char", "text": "Char" },
      { "value": "Double", "text": "Double" },
      { "value": "Int 64", "text": "Int 64" },
      { "value": "Long", "text": "Long" },
      { "value": "Float", "text": "Float" },
      { "value": "Short", "text": "Short" },
      { "value": "String", "text": "String" },
      { "value": "Time", "text": "Time" },
      { "value": "Unsigned Char", "text": "Unsigned Char" },
      { "value": "Unsigned Int 64", "text": "Unsigned Int 64" },
      { "value": "Unsigned Int", "text": "Unsigned Int" },
      { "value": "Unsigned Short", "text": "Unsigned Short" }
    ]));
    searchFields.push(new SearchField("tagDescription", "TR_TAG_DESCRIPTION", "", EditorFieldObjectDTO.ControlTypeEnum.Text, null, "TR_TAG_NAME_SEARCH_HELP"));
    this.searchCriteria = new SearchCriteria(searchFields, SearchTypeEnum.Clear);
  }

  public ngOnDestroy(): void {
    if (this.tagsWebsocket != null && this.tagsWebsocket.readyState === WebSocket.OPEN)
      this.tagsWebsocket.close(3000, "panelClose");
    if (this.tagServiceSubscription != null)
      this.tagServiceSubscription.unsubscribe();
    if (this.globalBroadcastServiceSubscription != null)
      this.globalBroadcastServiceSubscription.unsubscribe();
  }

  public ngOnChanges(changes): void {
    if (this.selectedNode) {
      this.itemPerPage = this.globalDataService.SDGConfig.gtwHttpPageBlockSize;
      this.currentPage = new Page(1, this.itemPerPage, "", "");
      this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
      //this.searchIsActive = false;
    }
  }

  private openGlobalBroadcastServiceSubscription(): void {
    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(
      event => {
        if (event.type === 'message') {
          let broadcastEventData = JSON.parse(event.data);
          if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.RefreshUI.toString() && broadcastEventData.parameters != null && broadcastEventData.parameters.refreshWebBrowser == null) {
            let objectName = null;
            let objectCollectionKind = null;
            if (broadcastEventData.parameters != null && broadcastEventData.parameters.objectName != null && broadcastEventData.parameters.objectCollectionKind != null) {
              //if (this.selectedNode.nodeFullName == broadcastEventData.parameters.objectName) {
              this.loadTagsGrid(broadcastEventData.parameters.objectName, broadcastEventData.parameters.objectCollectionKind);
              return;
              //}
            }
            if (this.selectedNode) {
              this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
            }
          }
        }
      }
    );
  }

  private loadTagsGrid(nodeFullName: string, nodeCollectionKind: string, tagNameFilter: string = null, tagDescriptionFilter: string = null, tagUserNameFilter: string = null, tagTypeFilter: string = null): void {
    tagNameFilter = this.searchIsActive ? tagNameFilter : null;
    tagDescriptionFilter = this.searchIsActive ? tagDescriptionFilter : null;
    tagUserNameFilter = this.searchIsActive ? tagUserNameFilter : null;
    tagTypeFilter = this.searchIsActive ? tagTypeFilter : null;

    if (this.tagPurposeFilter == TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY) {
      this.tagsService.getTags(
        "", //nodeFullName - Root
        <any>this.sortColumn, //sortColumn
        <any>this.sortColumnDirection, //sortColumnDirection
        <any>tagTypeFilter,//valueTypeFilter
        tagNameFilter,//tagNameFilter
        tagDescriptionFilter,//tagDescription
        tagUserNameFilter,//tagUserNameFilter
        <any>nodeCollectionKind, //tagCollectionKindFilter
        this.tagPurposeFilter, //tagPuposeFilter
        true, //recursive
        0, //startIndex
        0//endIndex
      ).subscribe(
        data => this.tags = this.combineMapping(data),
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        },
        () => {
          this.onDoSubscribeComplete();
          //this.searchIsActive = false;
        }
      );
      this.tagCount = 0;
    }
    else {
      this.nodesApi.getNodePageInfo(nodeFullName, <any>this.sortColumn, <any>this.sortColumnDirection, <any>tagTypeFilter, tagNameFilter, tagDescriptionFilter, tagUserNameFilter, (this.searchIsActive && this.searchCriteria.searchType == SearchTypeEnum.DeepSearch ? true : this.isRecursive), <any>this.tagPurposeFilter).subscribe(
        dataPage => {
          this.pageEdgePairs = dataPage.edgePairs;
          this.tagCount = dataPage.numTags;
          this.tagsService.getTags(
            nodeFullName, //nodeFullName - Root
            <any>this.sortColumn, //sortColumn
            <any>this.sortColumnDirection, //sortColumnDirection
            <any>tagTypeFilter,//valueTypeFilter
            tagNameFilter,//tagNameFilter
            tagDescriptionFilter,//tagDescription
            tagUserNameFilter,//tagUserNameFilter
            <any>nodeCollectionKind, //tagCollectionKindFilter
            this.tagPurposeFilter, //tagPuposeFilter
            (this.searchIsActive && this.searchCriteria.searchType == SearchTypeEnum.DeepSearch ? true : this.isRecursive), //recursive
            this.currentPage.start, //startIndex
            this.currentPage.end//endIndex
          ).subscribe(
            data => this.tags = this.combineMapping(data),
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
            },
            () => {
              this.onDoSubscribeComplete();
              //this.searchIsActive = false;
            }
          );
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        }
      );
    }
  }

  private combineMapping(dataTags: Array<TagObjectDTO>): Array<TagObjectDTO> {
    let tagMappings: Array<any> = [];
    if (dataTags && dataTags.length > 0) {
      dataTags.forEach((dataTag) => {
        let tagMapping: any = new Object();
        tagMapping.tagClassName = dataTag.tagClassName;
        tagMapping.tagObjectIcon = dataTag.tagObjectIcon;
        tagMapping.tagName = dataTag.tagName;
        tagMapping.tagUserName = dataTag.tagUserName;
        tagMapping.tagValue = dataTag.tagValue;
        tagMapping.tagQuality = dataTag.tagQuality;
        tagMapping.tagTime = dataTag.tagTime;
        tagMapping.tagOptions = dataTag.tagOptions;
        if (dataTag.tagClassName == "GTWTASE2DataAttributeMDO" || dataTag.tagClassName == "GTWTase2SlaveDataObject")
          tagMapping.tagValueType = dataTag.tagValueType;
        else
          tagMapping.tagValueType = dataTag.tagValueType + " (" + TagObjectDTO.getTagPropertyMaskStringValue(dataTag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.VALUE_TYPE) + ")";
        tagMapping.tagDescription = dataTag.tagDescription;
        tagMapping.tagPropertyMask = dataTag.tagPropertyMask;
        tagMapping.tagCanChangeValue = TagObjectDTO.getTagPropertyMaskValue(dataTag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_CHANGE_VALUE);
        tagMapping.tagCanEdit = TagObjectDTO.getTagPropertyMaskValue(dataTag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_EDIT);
        tagMapping.tagCanDelete = TagObjectDTO.getTagPropertyMaskValue(dataTag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_DELETE);
        tagMapping.isHealthy = dataTag.isHealthy;
        tagMapping.tag = dataTag;
        tagMapping.tagMappingList = this.addMappingToTag(dataTag);
        tagMappings.push(tagMapping)
      });
    }
    return tagMappings
  }

  private addMappingToTag(dataTag: TagObjectDTO): Array<Cell> {
    let tagMappingList: Array<Cell> = [];
    if (dataTag.tagBindingList) {
      dataTag.tagBindingList.forEach((tagBinding) => {
        if (tagBinding.direction === BoundObjectDTO.DirectionEnum.RIGHT)
          tagMappingList.push(new Cell("arrowRight", { leftTag: dataTag.tagName, rightTag: tagBinding.fullName }, tagBinding.canDelete ? "action$deleteMapping;action$lookupMapping" : "action$lookupMapping"));
        else if (tagBinding.direction === BoundObjectDTO.DirectionEnum.LEFT)
          tagMappingList.push(new Cell("arrowLeft", { leftTag: dataTag.tagName, rightTag: tagBinding.fullName }, tagBinding.canDelete ? "action$deleteMapping;action$lookupMapping" : "action$lookupMapping"));
        else
          tagMappingList.push(new Cell("arrowLeftRight", { leftTag: dataTag.tagName, rightTag: tagBinding.fullName }, tagBinding.canDelete ? "action$deleteMapping;action$lookupMapping" : "action$lookupMapping"));
      });
    }
    return tagMappingList;
  }

  private onDoSubscribeComplete(): void {
    //BroadcastEventTypeEnumDTO.RefreshUI might call this method while the WebSocket is not open yet (in case of reconnect)
    if (this.tagsWebsocket != null && this.tagsWebsocket.readyState === WebSocket.OPEN) {
      let wsTagsName = [];
      if (this.tagPurposeFilter == TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY) {
        wsTagsName.push({ "nodeFullName": "menu.warning" });
        let jsonWSTagsNameList: string = JSON.stringify(wsTagsName);
        this.tagsWebsocket.send(jsonWSTagsNameList);
        this.wsGetTagsDataWarning();
      }
      else if (this.tags == null) {
        return;
      }
      else {
        if (this.selectedNode != null && this.selectedNode.nodeFullName != "")
          wsTagsName.push({ "nodeFullName": this.selectedNode.nodeFullName });
        for (var x in this.tags)
          wsTagsName.push({ "tagName": this.tags[x].tagName });

        let jsonWSTagsNameList: string = JSON.stringify(wsTagsName);
        this.tagsWebsocket.send(jsonWSTagsNameList);
        this.wsGetTagsData();
      }
    }
  }

  private wsGetTagsDataWarning(): void {
    this.tagServiceSubscription = this.tagsWSApi.getTagsData(this.tagsWebsocket).subscribe((event) => {
      if (event.type === 'message') {
        this.tagsWSRconnectAttempts = 0;
        let wsTags = JSON.parse(event.data);
        for (var x in wsTags) {
          let isTagNotFound: boolean = true;
          for (var i in this.tags) {
            //Remove present healthy
            if (this.tags[i].tagName == wsTags[x].tagName && wsTags[x].isHealthy) {
              const index = this.tags.indexOf(this.tags[i], 0);
              if (index > -1) {
                this.tags.splice(index, 1);
                isTagNotFound = false;
                break;
              }
            }
            else if (this.tags[i].tagName == wsTags[x].tagName && !wsTags[x].isHealthy) {
              this.tags[i].tagValue = wsTags[x].tagValue;
              this.tags[i].tagQuality = wsTags[x].tagQuality;
              this.tags[i].tagTime = wsTags[x].tagTime;
              this.tags[i].tagOptions = wsTags[x].tagOptions;
              this.tags[i].tagPropertyMask = wsTags[x].tagPropertyMask;
              this.tags[i].tagDescription = wsTags[x].tagDescription;
              this.tags[i].isHealthy = wsTags[x].isHealthy;
              this.tags[i].tagMappingList = this.addMappingToTag(wsTags[x]);
              isTagNotFound = false;
              break;
            }
          }
          //Add Unhealthy tags
          if (isTagNotFound && !wsTags[x].isHealthy) {
            this.tags.push(wsTags[x])
          }
        }
      }
      else if (event.type === 'close') {
        this.reconnectTagsWebsocket();
      }
    });
  }

  private wsGetTagsData(): void {
    this.tagServiceSubscription = this.tagsWSApi.getTagsData(this.tagsWebsocket).subscribe((event) => {
      if (event.type === 'message') {
        this.tagsWSRconnectAttempts = 0;
        let wsTags = JSON.parse(event.data);
        for (var x in wsTags) {
          if (this.tags == null)
            return;
          for (var i in this.tags) {
            if (this.tags[i].tagName == wsTags[x].tagName) {
              this.tags[i].tagValue = wsTags[x].tagValue;
              this.tags[i].tagQuality = wsTags[x].tagQuality;
              this.tags[i].tagTime = wsTags[x].tagTime;
              this.tags[i].tagOptions = wsTags[x].tagOptions;
              this.tags[i].tagPropertyMask = wsTags[x].tagPropertyMask;
              this.tags[i].tagDescription = wsTags[x].tagDescription;
              this.tags[i].isHealthy = wsTags[x].isHealthy;
              this.tags[i].tagMappingList = this.addMappingToTag(wsTags[x]);
              break;
            }
          }
        }
      }
      else if (event.type === 'close') {
        this.reconnectTagsWebsocket();
      }
    });
  }

  private reconnectTagsWebsocket(): void {
    if (this.tagsWSRconnectAttempts < 3) {
      setTimeout(() => {
        const currentPath: string = this.router.url;
        const pageName: string = currentPath.split('/').pop();
        if (currentPath !== "/dashboard" && currentPath !== "/config" && currentPath !== "/")
          return;
        this.tagsWSRconnectAttempts++;
        this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getTags", reconnectAttempt: this.tagsWSRconnectAttempts }).subscribe(res => {
          this.alertService.debug(res);
        });
        this.tagsWSApi.openWebsocket().subscribe(
          data => {
            this.tagsWebsocket = data;
            this.onDoSubscribeComplete();
            this.wsGetTagsData();
          },
          error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getTags", reason: "Reopen fail" }); }
        );

      }, 5000);
    }
  }

  private onSearchGrid(): void {
    let tagNameFilter: string = null;
    let tagTypeFilter: string = null;
    let tagUserNameFilter: string = null;
    let tagDescriptionFilter: string = null;

    if (this.searchCriteria.searchFields != null) {
      this.searchCriteria.searchFields.forEach((searchField) => {
        if (searchField.id == "tagName") {
          if (searchField.value && searchField.value != "") {
            tagNameFilter = searchField.value;
          }
        }
        if (searchField.id == "tagUserName") {
          if (searchField.value && searchField.value != "") {
            tagUserNameFilter = searchField.value;
          }
        }
        if (searchField.id == "tagValueType") {
          if (searchField.value && searchField.value != "") {
            tagTypeFilter = searchField.value;
          }
        }
        if (searchField.id == "tagDescription") {
          if (searchField.value && searchField.value != "") {
            tagDescriptionFilter = searchField.value;
          }
        }
      });
    }
    if ((!tagNameFilter && !tagTypeFilter && !tagUserNameFilter && !tagDescriptionFilter) || (tagNameFilter == "" && tagTypeFilter == "" && tagUserNameFilter == "" && tagDescriptionFilter == "")) {
      //this.searchIsActive = false;
      this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
    }
    else {
      //this.searchIsActive = true;
      this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString(), tagNameFilter, tagDescriptionFilter, tagUserNameFilter, tagTypeFilter);
    }
  }

  private onPageSelect(currentPage: Page): void {
    this.currentPage = currentPage;
    if (this.searchIsActive)
      this.onSearchGrid();
    else
      this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
  }

  private onSortChange(sortedColumn: Column): void {
    this.sortColumn = sortedColumn.field
    this.sortColumnDirection = sortedColumn.isSortAscending ? "Ascending" : "Descending";
    if (this.searchIsActive)
      this.onSearchGrid();
    else
      this.loadTagsGrid(this.selectedNode.nodeFullName, this.selectedNode.nodeCollectionKind.toString());
  }
  private onSearchIsActive(searchIsActive: boolean): void {
    this.searchIsActive = searchIsActive
  }

  private clickActionGrid(actionObject: any): void {
    let parentObjectNameParameter = "";
    if (this.selectedNode != null)
      parentObjectNameParameter = this.selectedNode.nodeFullName;

    if (actionObject.action == "action$changeValue") {
      this.dashboardConfigComponent.onNodeAction(EditorCommandsDTO.MENUCMDCHANGEVALUE.toString(), actionObject.tag.tagName, actionObject.tag.tagName, actionObject.tag, null);
    }
    else if (actionObject.action == "action$edit") {
      this.dashboardConfigComponent.onNodeAction(EditorCommandsDTO.MENUCMDEDIT.toString(), actionObject.tag.tagName, actionObject.tag.tagName, actionObject.tag, null);
    }
    else if (actionObject.action == "action$delete") {
      this.dashboardConfigComponent.onNodeAction(EditorCommandsDTO.MENUCMDDELETE.toString(), actionObject.tag.tagName, actionObject.tag.tagName, actionObject.tag, null);
    }
    else if (actionObject.action.includes("action$deleteMapping")) {
      let dashboardUserModalDeleteRef;
      this.translate.get("TR_ARE_YOU_SURE_TO_REMOVE_MAPPING", { mapping: actionObject.tag.leftTag + "/" + actionObject.tag.rightTag }).subscribe(res => {
        dashboardUserModalDeleteRef = this.modal.confirm()
          .size('lg')
          .showClose(true)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_DELETE'))
          .okBtnClass('btn btn-default')
          .body(`
					<div class="panel panel-warning">
						<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					</div>
        `)
          .open()
      });
      dashboardUserModalDeleteRef.result.then(
        (result) => {
          if (result) {
            this.mappingsApi.removeMapping(actionObject.tag.leftTag, actionObject.tag.rightTag).subscribe(
              data => {
                this.translate.get("TR_OBJECT_TAGNAME_DELETED", { tagName: actionObject.tag.leftTag + "/" + actionObject.tag.rightTag }).subscribe(res => {
                  this.alertService.success(res);
                });
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OBJECT_NOT_DELETED"); }
              }
            );
          }
        },
        () => { } //needed
      );
    }
  }
  
  private getColumns(): Array<Column> {
    return [
      new Column("tagObjectIcon", "", "action$image", "", "isHealthy"),
      new Column("tagName", "TR_NAME", "action$context-menu-device", "tagDescription", "tag", true, true),
      new Column("tagUserName", "TR_TAG_USER_NAME", "action$tooltip", "tagDescription"),
      new Column("tagMappingList", "TR_MAPPING", "action$list", "", "tagMappingList"),   
      new Column("tagValue", "TR_VALUE", "action$tooltip", "tagValue"),
      new Column("tagQuality", "TR_QUALITY", "", ""),
      new Column("tagTime", "TR_TIME", "", "TR_TIME_FLAG_TOOLTIP_HELP"),
      new Column("tagOptions", "TR_OPTIONS", "", ""),
      new Column("tagValueType", "TR_TYPE", "", "", "", true, null),
      new Column("", "", "action$changeValue", "TR_CHANGE_VALUE"),
      new Column("", "", "action$edit", "TR_EDIT"),
      new Column("", "", "action$delete", "TR_DELETE")
    ];
  }
}
