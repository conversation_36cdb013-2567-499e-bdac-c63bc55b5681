System.register(["@angular/core", "@angular/router", "../../data/wsApi/wsApi", "../../data/api/api", "../../modules/alert/alert.service", "../../authentication/authentication.service", "@ngx-translate/core", "../../global/global.objectToCSV.util", "../../global/logEntryContain.pipe"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, router_1, wsApi_1, api_1, alert_service_1, authentication_service_1, core_2, global_objectToCSV_util_1, logEntryContain_pipe_1, DashboardLogGridComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (global_objectToCSV_util_1_1) {
                global_objectToCSV_util_1 = global_objectToCSV_util_1_1;
            },
            function (logEntryContain_pipe_1_1) {
                logEntryContain_pipe_1 = logEntryContain_pipe_1_1;
            }
        ],
        execute: function () {
            DashboardLogGridComponent = (function () {
                function DashboardLogGridComponent(dashboardLogService, dashboardLogWSApi, authenticationService, alertService, logEntryContainPipe, translate, router) {
                    this.dashboardLogService = dashboardLogService;
                    this.dashboardLogWSApi = dashboardLogWSApi;
                    this.authenticationService = authenticationService;
                    this.alertService = alertService;
                    this.logEntryContainPipe = logEntryContainPipe;
                    this.translate = translate;
                    this.router = router;
                    this.logEntryFilter = {};
                    this.isWSPaused = false;
                    this.logEntries = [];
                    this.reconnectAttempts = 0;
                }
                DashboardLogGridComponent.prototype.ngOnInit = function () {
                    this.loadAPI();
                };
                DashboardLogGridComponent.prototype.ngOnDestroy = function () {
                    if (this.websocket && this.websocket.readyState != null && this.websocket.readyState === WebSocket.OPEN)
                        this.websocket.close(3000, "panelClose");
                    if (this.logServiceSubscription != null)
                        this.logServiceSubscription.unsubscribe();
                };
                DashboardLogGridComponent.prototype.copyToCSV = function () {
                    var logEntriesFiltered = [];
                    logEntriesFiltered = this.logEntryContainPipe.transform(this.logEntries, this.logEntryFilter);
                    global_objectToCSV_util_1.GlobalObjectToCSVUtil.copyToCSV("DashboardLog.csv", logEntriesFiltered);
                };
                DashboardLogGridComponent.prototype.clearLog = function () {
                    this.logEntries = [];
                };
                DashboardLogGridComponent.prototype.loadAPI = function () {
                    var _this = this;
                    var gtwHost = this.authenticationService.globalDataService.SDGConfig.gtwHost;
                    var gtwHttpPort = this.authenticationService.globalDataService.SDGConfig.gtwHttpPort;
                    var gtwWSHostPort = "ws://" + gtwHost + ":" + gtwHttpPort.toString();
                    var gtwHostPort = "http://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";
                    if (location.protocol === "https:") {
                        gtwWSHostPort = "wss://" + gtwHost + ":" + gtwHttpPort.toString();
                        gtwHostPort = "https://" + gtwHost + ":" + gtwHttpPort.toString() + "/rest";
                    }
                    this.dashboardLogWSApi.basePath = gtwWSHostPort;
                    this.dashboardLogService.basePath = gtwHostPort;
                    this.dashboardLogWSApi.openWebsocket().subscribe(function (data) {
                        _this.websocket = data;
                        _this.wsGetLogData();
                    }, function (error) { _this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Open fail" }); });
                    this.dashboardLogService.getLastLogEntryID().subscribe(function (lastLogEntryID) {
                        if (isNaN(lastLogEntryID))
                            lastLogEntryID = 0;
                        if (lastLogEntryID && lastLogEntryID > 500) {
                            _this.dashboardLogService.getLogEntriesRange(lastLogEntryID - 500).subscribe(function (data) {
                                if (global_objectToCSV_util_1.GlobalObjectToCSVUtil.isNotEmpty(data))
                                    _this.logEntries = data;
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                                }
                            });
                        }
                        else {
                            _this.dashboardLogService.getLogEntriesRange(0, lastLogEntryID).subscribe(function (data) {
                                if (global_objectToCSV_util_1.GlobalObjectToCSVUtil.isNotEmpty(data))
                                    _this.logEntries = data;
                            }, function (error) {
                                if (error.status == 401) {
                                    _this.authenticationService.onLoginFailed("/");
                                }
                                else {
                                    _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                                }
                            });
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    });
                };
                DashboardLogGridComponent.prototype.pauseWS = function (isWSPaused) {
                    this.isWSPaused = isWSPaused;
                };
                DashboardLogGridComponent.prototype.wsGetLogData = function () {
                    var _this = this;
                    this.logServiceSubscription = this.dashboardLogWSApi.getLogData(this.websocket).subscribe(function (event) {
                        if (event.type === 'message') {
                            _this.reconnectAttempts = 0;
                            if (!_this.isWSPaused) {
                                var wsLogEntries = JSON.parse(event.data);
                                Array.prototype.push.apply(_this.logEntries, wsLogEntries);
                                if (_this.logEntries.length > 1000)
                                    _this.logEntries = _this.logEntries.slice(_this.logEntries.length - 1000, 1000);
                            }
                        }
                        else if (event.type === 'close') {
                            if (_this.reconnectAttempts < 3) {
                                setTimeout(function () {
                                    var currentPath = _this.router.url;
                                    var pageName = currentPath.split('/').pop();
                                    if (currentPath !== "/dashboard" && currentPath !== "/config" && currentPath !== "/")
                                        return;
                                    _this.reconnectAttempts++;
                                    _this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getLogEntries", reconnectAttempt: _this.reconnectAttempts }).subscribe(function (res) {
                                        _this.alertService.debug(res);
                                    });
                                    _this.dashboardLogWSApi.openWebsocket().subscribe(function (data) {
                                        _this.websocket = data;
                                        _this.wsGetLogData();
                                    }, function (error) { _this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Reopen fail" }); });
                                }, 5000);
                            }
                        }
                    });
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardLogGridComponent.prototype, "logEntryFilter", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardLogGridComponent.prototype, "isWSPaused", void 0);
                DashboardLogGridComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardLogGridComponent",
                        template: "\n      <table style=\"table-layout:fixed; width:100%;\">\n        <tr>\n          <th style=\"width: 15%\">&nbsp;</th>\n          <th style=\"width: 10%\">&nbsp;</th>\n          <th style=\"width: 10%\">&nbsp;</th>\n          <th style=\"width: 10%\">&nbsp;</th>\n          <th style=\"width: 10%\">&nbsp;</th>\n          <th style=\"width: 45%\">&nbsp;</th>\n        </tr>\n        <tr *ngFor=\"let logEntry of logEntries | logEntryContain:logEntryFilter;\" [ngClass]=\"{'error': (logEntry.severity == 'Exception' || logEntry.severity == 'Error')}\">\n          <td>{{logEntry.timeStamp}}</td>\n          <td>{{logEntry.source}}</td>\n          <td>{{logEntry.name}}</td>\n          <td>{{logEntry.category}}</td>\n          <td>{{logEntry.severity}}</td>\n          <td>{{logEntry.message}}</td>\n        </tr>\n\t\t\t</table>\n\t\t  <div *ngIf=\"logEntries.length == 0\" class=\"grid-no-results\">{{'TR_NO_RESULTS' | translate}}</div>\n  ",
                        styles: ["\n          td {\n\t\t\t\t    padding-right: 6px;\n\t\t\t\t    padding-left: 6px;\n            padding-top: 8px;\n            overflow-wrap: anywhere;\n          }\n          th {\n            line-height:1px;\n            height: 1px;\n            overflow: hidden;\n          }\n          tr:nth-of-type(odd) {\n            background-color: rgba(255,\t255, 255, 0.35) !important;\n          }\n          .error {\n            color: red;\n          }\n        "]
                    }),
                    __metadata("design:paramtypes", [api_1.LogService, wsApi_1.LogWSApi,
                        authentication_service_1.AuthenticationService, alert_service_1.AlertService,
                        logEntryContain_pipe_1.LogEntryContainPipe, core_2.TranslateService,
                        router_1.Router])
                ], DashboardLogGridComponent);
                return DashboardLogGridComponent;
            }());
            exports_1("DashboardLogGridComponent", DashboardLogGridComponent);
        }
    };
});
//# sourceMappingURL=dashboard.log.grid.component.js.map