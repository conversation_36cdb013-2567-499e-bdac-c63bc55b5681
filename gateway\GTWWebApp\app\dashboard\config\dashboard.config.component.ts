﻿import { Component, Input, ViewChild} from "@angular/core";
import { TreeNodeDTO, EditorCommandsDTO, TagObjectDTO, TagPurposeFilterEnumDTO, TagCollectionObjectDTO, TreeNodeCollectionObjectDTO } from "../../data/model/models";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { DashboardConfigTagValueQualityModal } from "./dashboard.config.tag.value.quality.modal";
import { DashboardConfigTagEditorModal } from "./dashboard.config.tag.editor.modal";
import { EditorsService, NodesService, TagsService, FileService } from "../../data/api/api";
import { TranslateService } from "@ngx-translate/core";
import { Panel } from '../../modules/panel/panel';
import { AlertLogModal } from "../../modules/alert/alert.log.modal";
import { DownloadComponent } from "../../modules/download/download.component";

@Component({
  selector: "dashboardConfigComponent",
  styles: [`
			.panel-body{
				height: 100%;
        padding: 0 2px 0 0;
      }
      .config {
        height: 100%;
				overflow: auto;
        background-image: url(../../../images/background_7.svg), linear-gradient(#000000, #c5c5c5);
        background-size: 20%;
      }
      .device-tree {
				height: 100%;
        background-image: linear-gradient(to bottom, rgba(231, 240, 247, 0.85), rgba(231, 240, 247, 0.95));
      }
     .tag-grid {
				height: 100%;
        background-image: linear-gradient(to bottom, rgba(231, 240, 247, 0.85), rgba(231, 240, 247, 0.95));
      }
      `],
  template: `
    <div class="panel-body">
      <div class="config">
        <split direction="horizontal">
          <split-area [size]="15" [minSizePixel]="150" class="device-tree">
            <dashboardConfigDevicesComponent [panel]="panel" (onSelectedChanged)="onSelectNode($event)" (onDropNode)="onDropNode($event)"></dashboardConfigDevicesComponent>
          </split-area>
          <split-area [size]="85" class="tag-grid">
            <dashboardConfigTagsGridComponent [selectedNode]="selectedNode" [tagPurposeFilter]="tagPurposeFilter" [isRecursive]="isTagSelectionRecursive"></dashboardConfigTagsGridComponent>
          </split-area>
        </split>
      </div>
    </div>
    <downloadComponent #download></downloadComponent>
  `
})

export class DashboardConfigComponent {
  @Input("panel") panel: Panel;
  private selectedNode: TreeNodeDTO;
  private isTagSelectionRecursive: boolean = false;
  private tagPurposeFilter: number = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL

  @ViewChild("download", { static: false }) download: DownloadComponent;

  constructor(private modal: Modal, private alertService: AlertService, private authenticationService: AuthenticationService, private editorsService: EditorsService, private nodesService: NodesService, private tagsService: TagsService, private fileService: FileService, private translate: TranslateService) { }

  public onNodeAction(editorCommandsDTOString: string, parentObjectNameParameter: string = "", objectNameParameter: string = "", tag: TagObjectDTO = null, node: TreeNodeDTO = null): void {
    let editorCommandsDTO: EditorCommandsDTO = EditorCommandsDTO[editorCommandsDTOString];
    let objectClassName: string = "";
    let objectCollectionKind: any = "";
    if (node != null) {
      objectClassName = node.nodeClassName
      objectCollectionKind = node.nodeCollectionKind.toString();
    }
    else if (tag != null) {
      objectClassName = tag.tagClassName
      objectCollectionKind = TagObjectDTO.getTagPropertyMaskStringValue(tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.COLLEC_KIND);
    }
    switch (editorCommandsDTO.toString()) {
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDEDITWORKSPACEPARAMETERS]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDODBCCLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDODBCITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMODEMPOOL]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMODEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTCPCHANNELOUTSTATIONSLAVE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDREDUNDANTSLAVECHANNEL]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDREDUNDANTMASTERCHANNEL]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTCPCHANNELMASTER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMBPCHANNEL]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDSERIALCHANNELOUTSTATIONSLAVE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDSERIALCHANNELMASTER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMODEMPOOLCHANNEL]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDDNP3UDPTCPCHANNELOUTSTATIONSLAVE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDDNP3UDPTCPCHANNELMASTER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDSESSION]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDSECTOR]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDDATATYPE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMULTIPLEMDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMAPPINGSDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMAPPINGSDOS]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMAPPINGMDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMAPPINGMDOS]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCCLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCAECLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCAEITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMULTIPLEOPCITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMULTIPLEOPCUAITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCAEATTR]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCUACLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCUACERTIFICATE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDEDITOPCUASERVER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDOPCUAITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850CONTROLTOOPCMAPPING]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850SERVER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850CLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850REPORT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850GOOSE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850POLLEDDATASET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850POLLEDPOINTSET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850ITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850WRITABLEPOINT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850WRITABLEPOINTSET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850COMMANDPOINT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61850COMMANDPOINTSET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDGOOSEMONITOR]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61400ALARMSNODE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADD61400ALARMMDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2CLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2SERVER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2CLIENTSERVER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDEDITTASE2DATAPOINTS]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2DSTS]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDMANAGETASE2DATASET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENU_CMD_EDIT_TASE2_EDIT_MODEL]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2LOGICALDEVICE]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2POLLEDPOINTSET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2POLLEDDATASET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2COMMANDPOINT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2COMMANDPOINTSET]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDTASE2ITEM]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSHOWCONFIGTASE2SERVER]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSHOWCONFIGTASE2CLIENT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDEDIT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDWRITEACTION]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDMULTIPOINT]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDEQMDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDINTERNALMDO]:
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDADDUSERDEFINEDFOLDER]:
        break;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDPERFORMWRITEACTION]:
        this.performWriteAction(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSETVALUEANDQUALITY]:
        this.tagsService.getTag(objectNameParameter).subscribe(
          data => {
            //let tagList: Array<TagObjectDTO> = [ data ];
            this.changeValueTags([data]);
          },
          error => {
            if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
          });
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCHANGEVALUE]:
        this.tagsService.getTag(objectNameParameter).subscribe(
          data => {
            //let tagList: Array<TagObjectDTO> = [ data ];
            this.changeValueTags([data]);
          },
          error => {
            if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
          });
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDELETE]:
        if (node != null)
          this.deleteNode(node, node.parentName, objectCollectionKind);
        if (tag != null) 
          this.deleteTags([tag]);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCONNECTOPCSERVER]:
        this.disconnectConnectFromOPCServer(objectNameParameter, objectCollectionKind, "Connect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISCONNECTOPCSERVER]:
        this.disconnectConnectFromOPCServer(objectNameParameter, objectCollectionKind, "Disconnect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCONNECTOPCAESERVER]:
        this.disconnectConnectFromOPCAEServer(objectNameParameter, objectCollectionKind, "Connect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISCONNECTOPCAESERVER]:
        this.disconnectConnectFromOPCAEServer(objectNameParameter, objectCollectionKind, "Disconnect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCONNECTOPCUASERVER]:
        this.disconnectConnectFromOPCUAServer(objectNameParameter, objectCollectionKind, "Connect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISCONNECTOPCUASERVER]:
        this.disconnectConnectFromOPCUAServer(objectNameParameter, objectCollectionKind, "Disconnect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDOPCUAGETSERVERSTATUS]:
        this.getStatusFromOPCUAServer(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDREAD61850MDO]:
        this.read61850Tag(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDREADICCPMDO]:
        this.readICCPTag(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISCONNECTFROM61850SERVER]:
        this.disconnectConnectFrom61850Server(objectNameParameter, objectCollectionKind, "Disconnect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCONNECTTO61850SERVER]:
        this.disconnectConnectFrom61850Server(objectNameParameter, objectCollectionKind, "Connect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDRESETAVERAGEMDOUPDATERATE]:
        this.resetAverageMdoUpdateRate(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDRESET61850RETRYCONNECTCOUNT]:
        this.reset61850RetryConnectCount(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDRESTART61850SERVER]:
        this.restart61850Server(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSAVEMODELTOFILE]:
        this.save61850ModelToFile(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDENABLERCB]:
        this.enableDisable61850RCB(objectNameParameter, objectCollectionKind, "EnableReport");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISABLERCB]:
        this.enableDisable61850RCB(objectNameParameter, objectCollectionKind, "DisableReport");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDENABLEDSTS]:
        this.enableDisableDSTS(objectNameParameter, objectCollectionKind, "EnableDSTS");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISABLEDSTS]:
        this.enableDisableDSTS(objectNameParameter, objectCollectionKind, "DisableDSTS");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDVERIFYDATASET]:
        this.verifyDataset(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDREADDATASET]:
        this.readDataset(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDREADOPCUAMDO]:
        this.readOPCUAMDO(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISCONNECTFROMTASE2SERVER]:
        this.disconnectConnectFromTase2Server(objectNameParameter, objectCollectionKind, "Disconnect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCONNECTTOTASE2SERVER]:
        this.disconnectConnectFromTase2Server(objectNameParameter, objectCollectionKind, "Connect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCONNECTTODISCOVERTASE2SERVER]:
        this.disconnectConnectFromTase2Server(objectNameParameter, objectCollectionKind, "ConnectDiscover");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDISCONNECTTASE2SERVER]:
        this.disconnectRestartTase2Server(objectNameParameter, objectCollectionKind, "Disconnect");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDRESTARTTASE2SERVER]:
        this.disconnectRestartTase2Server(objectNameParameter, objectCollectionKind, "Restart");
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDOPERATETASE2CONTROL]:
        this.tagsService.getTag(objectNameParameter).subscribe(
          data => {
            this.operateTase2Control(objectNameParameter, objectCollectionKind, [data]);
          },
          error => {
            if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
          });
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSAVETASE2SERVER]:
        this.saveTase2ServerToFile(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDAUTOCREATETAGS]:
        this.autoCreateTags(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDDROPONFOLDER]:
        this.dropOnFolder(parentObjectNameParameter, objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSUBSCRIBEGOOSESTREAM]:
        this.subscribeUnsubscribeGooseStream(objectNameParameter, true);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDUNSUBSCRIBEGOOSESTREAM]:
        this.subscribeUnsubscribeGooseStream(objectNameParameter, false);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCREATETHXMLPOINTFILE]:
        this.createTHXMLPointFile(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDCREATEDTMCSVPOINTFILE]:
        this.createDTMCSVPointFile(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDSWITCHTORCHANNEL]:
        this.switchToRChannel(objectNameParameter, objectCollectionKind);
        return;
      case EditorCommandsDTO[EditorCommandsDTO.MENUCMDREADOPCDAMDO]:
        this.readOPCDAMDO(objectNameParameter, objectCollectionKind);
        return;

      default: {
        alert("Not implemented!");
        return;
      }
    }
    this.callEditorModal(editorCommandsDTOString, objectNameParameter, parentObjectNameParameter, tag, node);
  }

  public changeAllValueTag(tagList: Array<TagObjectDTO>): void {
    let tagListToBeChanged: Array<TagObjectDTO> = [];
    tagList.forEach((tag) => {
      if (TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_CHANGE_VALUE)) {
        tagListToBeChanged.push(tag);
      }
    });
    if (tagListToBeChanged.length > 0) 
      this.changeValueTags(tagListToBeChanged);
  }

  public deleteAllTags(tagList: Array<TagObjectDTO>): void {
    let tagListToBeDeleted: Array<TagObjectDTO> = [];
    tagList.forEach((tag) => {
      if (TagObjectDTO.getTagPropertyMaskValue(tag.tagPropertyMask, TagObjectDTO.TagPropertyMaskEnum.CAN_DELETE)) {
        tagListToBeDeleted.push(tag);
      }
    });
    if (tagListToBeDeleted.length > 0)
      this.deleteTags(tagListToBeDeleted);
  }

  public resetWarningTags(): void {
    let tagPurposeFilter = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY;
    let tagValueType = TagObjectDTO.TagValueTypeEnum.UnsignedLong;
    this.tagsService.setTagsFiltered(<any>tagValueType, "0", "", "", "", "", tagPurposeFilter, true).subscribe(
      data => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      }
    );
  }

  private onSelectNode(nodeObject: any): void {
    if (nodeObject.virtualNodeName == "warning") {
      this.tagPurposeFilter = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY;
      this.isTagSelectionRecursive = true;
    }
    else if (nodeObject.virtualNodeName == "health") {
      this.tagPurposeFilter = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_HEALTH;
      this.isTagSelectionRecursive = true;
    }
    else if (nodeObject.virtualNodeName == "performance") {
      this.tagPurposeFilter = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE;
      this.isTagSelectionRecursive = true;
    }
    else {
      this.tagPurposeFilter = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
      this.isTagSelectionRecursive = false;
    }
    this.selectedNode = nodeObject.selectedNode;
  }

  private callEditorModal(editorCommandsDTOString: string, objectNameParameter: string = "", parentObjectNameParameter: string = "", tag: TagObjectDTO = null, node: TreeNodeDTO = null): void {
    let addTitle: string = "TR_" + editorCommandsDTOString;
    const dashboardConfigTagEditorModalRef = this.modal.open(DashboardConfigTagEditorModal, overlayConfigFactory({ editorCommand: editorCommandsDTOString, objectName: objectNameParameter, parentObjectName: parentObjectNameParameter, tag: tag, node: node, addTitle: addTitle, editorsService: this.editorsService }, BSModalContext));
    dashboardConfigTagEditorModalRef.result.then(data => {
      if (data != null && data != "") {
        this.alertService.success("TR_DATA_SAVED");
        let result = data.result;
        let objectParentName = data.objectParentName
        let nextEditorCommand = data.nextEditorCommand;
        let node: TreeNodeDTO = { nodeClassName: data.objectClassName, nodeCollectionKind: data.objectCollectionKind };
        if (result == true && objectParentName != null && objectParentName != "" && nextEditorCommand != null && nextEditorCommand != "")
          this.onNodeAction(nextEditorCommand, objectParentName, "", null, node);
        if (data.messages && data.messages.length > 0) {
          this.modal.open(AlertLogModal, overlayConfigFactory({ messages: data.messages, title: addTitle}, BSModalContext));
          //alertLogModalModalRef.result.then();
        }
      }
      else if (data == "") {
        this.alertService.error("TR_ERROR_DATA_NOT_SAVED");
      }
    },
      (error) => { this.alertService.debug(error.toString()); }
    );
  }

  private changeValueTags(tagList: Array<TagObjectDTO>): void {
    if (tagList.length > 0) {
      const dashboardConfigTagValueQualityModalRef = this.modal.open(DashboardConfigTagValueQualityModal, overlayConfigFactory({ tagList: tagList, tagValueList: {}}, BSModalContext));
      dashboardConfigTagValueQualityModalRef.result.then(
        (result) => {
          if (result) {
            let tagValueList = dashboardConfigTagValueQualityModalRef.context.tagValueList;
            this.tagsService.setTags(tagValueList).subscribe(
              data => {
                this.alertService.success("TR_DATA_SAVED");
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_DATA_NOT_SAVED"); }
              }
            );
          }
        },
        (error) => { this.alertService.debug(error.toString()); }
      );
    }
  }

  private deleteTags(tagList: Array<TagObjectDTO>): void {
    if (tagList.length > 0) {
      let dashboardUserModalDeleteRef;
      let tagNameList: string = "";
      tagList.forEach((tag) => {
        let tagName: string = tag.tagName;
        if (tag.tagClassName == "GTWOpcUaMDOArrayIndex") {
          tagName = tag.tagName.split('_[')[0] + " array (and all indexes)";
          if (!tagNameList.includes(tagName)) {
            tagNameList += ("<br>&nbsp;&nbsp;&nbsp;-&nbsp;" + tagName);
          }
        }
        else {
          tagNameList += ("<br>&nbsp;&nbsp;&nbsp;-&nbsp;" + tagName);
        }
      });
      this.translate.get("TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME", { NodeName: tagNameList }).subscribe(res => {
        dashboardUserModalDeleteRef = this.modal.confirm()
          .size('lg')
          .showClose(true)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_DELETE'))
          .okBtnClass('btn btn-default')
          .body(`
					<div class="panel panel-warning">
						<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					</div>
        `)
          .open()
      });
      dashboardUserModalDeleteRef.result.then(
        (result) => {
          if (result) {
            let tagCollection: TagCollectionObjectDTO = { children: tagList };
            this.tagsService.deleteTags(tagCollection).subscribe(
              (data: any) => {
                this.translate.get("TR_TAGS_DELETED", { NodeName: data.data }).subscribe(res => {
                  this.alertService.success(res);
                });
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OBJECT_NOT_DELETED"); }
              }
            );
          }
        },
        () => { } //needed
      );
    }
  }

  private read61850Tag(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "Read61850Tag", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_61850_SERVER_READ"); }
      }
    );
  }

  private readICCPTag(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "ReadICCPTag", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_ICCP_SERVER_READ"); }
      }
    );
  }

  private operateTase2Control(objectNameParameter: string, objectCollectionKind: any, tagList: Array<TagObjectDTO>): void {
    const dashboardConfigTagValueQualityModalRef = this.modal.open(DashboardConfigTagValueQualityModal, overlayConfigFactory({ tagList: tagList, tagValueList: {} }, BSModalContext));
    dashboardConfigTagValueQualityModalRef.result.then(
      (result) => {
        if (result) {
          let tagValueList = dashboardConfigTagValueQualityModalRef.context.tagValueList;
          if (tagValueList != null && tagValueList.tags != null && tagValueList.tags.length != null && tagValueList.tags.length == 1) {
            this.editorsService.editorAction("Root", "OperateTase2Control", objectCollectionKind, objectNameParameter, tagValueList.tags[0].tagValue, "", "").subscribe(
              (data: any) => {
                this.alertService.success("TR_COMMAND_SUCCESS");
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_TASE2_OPERATE_CONTROL"); }
              }
            );
          }
        }
      },
      (error) => { this.alertService.debug(error.toString()); }
    );
  }

  private disconnectConnectFromOPCServer(objectNameParameter: string, objectCollectionKind: any, disconnectConnectCommand: string): void {
    this.editorsService.editorAction("Root", "DisconnectConnectFromOPCServer", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OPC_SERVER_NOT_DISCONNECTED_CONNECTED"); }
      }
    );
  }

  private disconnectConnectFromOPCAEServer(objectNameParameter: string, objectCollectionKind: any, disconnectConnectCommand: string): void {
    this.editorsService.editorAction("Root", "DisconnectConnectFromOPCAEServer", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OPCAE_SERVER_NOT_DISCONNECTED_CONNECTED"); }
      }
    );
  }

  private disconnectConnectFromOPCUAServer(objectNameParameter: string, objectCollectionKind: any, disconnectConnectCommand: string): void {
    this.editorsService.editorAction("Root", "DisconnectConnectFromOPCUAServer", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OPCUA_SERVER_NOT_DISCONNECTED_CONNECTED"); }
      }
    );
  }

  private getStatusFromOPCUAServer(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "GetOPCUaServerStatus", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        if (data.result) {
          let OPCServerStatusModalRef;
          this.translate.get(data.data).subscribe(res => {
            OPCServerStatusModalRef = this.modal.alert()
              .size('lg')
              .showClose(true)
              .title(this.translate.instant('TR_MENU_CMD_OPC_UA_SERVER_STATUS'))
              .okBtn(this.translate.instant('TR_CLOSE'))
              .okBtnClass('btn btn-default')
              .body(`
					      <div class="panel panel-primary">
						      <div class="panel-heading"><div class="glyphicon glyphicon-info-sign"></div>&nbsp;&nbsp;` + res + `</div>
					      </div>
              `).open()
          });
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OPCUA_SERVER_STATUS"); }
      }
    );
  }

  private readOPCDAMDO(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "ReadOPCDAMDO", objectCollectionKind, objectNameParameter).subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_READ_OPC_DA_MDO"); }
      }
    );
  }

  private disconnectConnectFrom61850Server(objectNameParameter: string, objectCollectionKind: any, disconnectConnectCommand: string): void {
    this.editorsService.editorAction("Root", "DisconnectConnectFrom61850Server", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_61850_SERVER_NOT_DISCONNECTED_CONNECTED"); }
      }
    );
  }

  private restart61850Server(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "Restart61850Server", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_61850_SERVER_NOT_RESTARTED"); }
      }
    );
  }

  private performWriteAction(objectNameParameter: string, objectCollectionKind: any) {
    this.editorsService.editorAction("Root", "PerformWriteAction", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_PERFORM_WRITE_ACTION"); }
      }
    );
  }

  private save61850ModelToFile(objectNameParameter: string, objectCollectionKind: any): void {
    let fileName = objectNameParameter + ".icd";
    fileName = fileName.replace(/^.*[\\\/]/, '');
    this.editorsService.editorAction("Root", "Save61850ModelToFile", objectCollectionKind, objectNameParameter, fileName, "", "").subscribe(
      (data: any) => {
        this.download.downloadClick(fileName, "CurrentWorkspace");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_61850_MODEL_NOT_SAVED"); }
      }
    );
  }

  private saveTase2ServerToFile(objectNameParameter: string, objectCollectionKind: any): void {
    let fileName = objectNameParameter + ".xml";
    fileName = fileName.replace(/^.*[\\\/]/, '');
    this.editorsService.editorAction("Root", "SaveTase2ServerToFile", objectCollectionKind, objectNameParameter, fileName, "", "").subscribe(
      (data: any) => {
        this.download.downloadClick(fileName, "CurrentWorkspace");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_TASE2_SERVER_NOT_SAVED"); }
      }
    );
  }

  private enableDisable61850RCB(objectNameParameter: string, objectCollectionKind: any, enableDisableCommand: string): void {
    this.editorsService.editorAction("Root", "EnableDisableRCB", objectCollectionKind, objectNameParameter, enableDisableCommand).subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK"); }
      }
    );
  }

  private readDataset(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "ReadDataset", objectCollectionKind, objectNameParameter).subscribe(
      (data: any) => {
        if (data.result)
          this.alertService.success("TR_DATASET_READ_SUCCEED");
        else
          this.alertService.error("TR_DATASET_READ_FAILED");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_VERIFY_DATASET"); }
      }
    );
  }

  private readOPCUAMDO(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "ReadOPCUAMDO", objectCollectionKind, objectNameParameter).subscribe(
      (data: any) => {
        if (data.result)
          this.alertService.success("TR_READ_SUCCEED");
        else
          this.alertService.error("TR_READ_FAILED");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_VERIFY_DATASET"); }
      }
    );
  }

  private enableDisableDSTS(objectNameParameter: string, objectCollectionKind: any, enableDisableCommand: string): void {
    this.editorsService.editorAction("Root", "EnableDisableDSTS", objectCollectionKind, objectNameParameter, enableDisableCommand).subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK"); }
      }
    );
  }

  private verifyDataset(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "VerifyDataset", objectCollectionKind, objectNameParameter).subscribe(
      (data: any) => {
        if (data.result)
          this.alertService.success("TR_DATASET_VERIFICATION_SUCCEED");
        else 
          this.alertService.error("TR_DATASET_VERIFICATION_FAILED");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_VERIFY_DATASET"); }
      }
    );
  }

  private resetAverageMdoUpdateRate(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "ResetAverageMdoUpdateRate", objectCollectionKind, objectNameParameter).subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_RESET_AVERAGE_MDO_UPDATE_RATE"); }
      }
    );
  }

  private reset61850RetryConnectCount(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "Reset61850RetryConnectCount", objectCollectionKind, objectNameParameter).subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_RESET_61850_RETRY_CONNECT_COUNT"); }
      }
    );
  }

  private deleteNode(node: TreeNodeDTO, parentObjectNameParameter: string, objectCollectionKind: any, ): void {
    if (node) {
      let dashboardUserModalDeleteRef;
      let deleteMessage: string = "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME";
      if (node.hasChildren)
        deleteMessage = "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_WITH_CHILDREN_NODENAME";
      this.translate.get(deleteMessage, { NodeName: node.nodeFullName }).subscribe(res => {
        dashboardUserModalDeleteRef = this.modal.confirm()
          .size('lg')
          .showClose(true)
          .title(this.translate.instant('TR_WARNING'))
          .okBtn(this.translate.instant('TR_DELETE'))
          .okBtnClass('btn btn-default')
          .body(`
					<div class="panel panel-warning">
						<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
					</div>
        `)
          .open()
      });
      dashboardUserModalDeleteRef.result.then(
        (result) => {
          if (result) {
            let nodeCollection: TreeNodeCollectionObjectDTO = { children: [node]};
            if (this.selectedNode == node)
              this.selectedNode = { nodeFullName: "", nodeCollectionKind: TreeNodeDTO.NodeCollectionKindEnum.ALL };
            this.nodesService.deleteNodes(nodeCollection, parentObjectNameParameter, <any>objectCollectionKind).subscribe(
              data => {
                this.translate.get("TR_OBJECT_NODENAME_DELETED", { NodeName: node.nodeFullName }).subscribe(res => {
                  this.alertService.success(res);
                });
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_OBJECT_NOT_DELETED"); }
              }
            );
          }
        },
        () => { } //needed
      );
    }
  }

  private onDropNode(dragDropData: any): void {
    // change index to dropTarget and dragSource
    //dragDropData
    let dragSource = dragDropData[0];
    let dropTarget = dragDropData[1];
    if (dragSource == null || dropTarget == null) {
      this.alertService.error("TR_ERROR_MAPPING");
      return;
    }

    //  User selected multiple source, system will prompt the multi-mapping popup because the target has a ClassName GTWDataType and it is not internal (memberClass != "GTWMasterDataObject")
    if (dragSource.length != null && dropTarget.nodeClassName == "GTWDataType" && dropTarget.memberClass != "GTWMasterDataObject") {
      if (dropTarget.nodeCollectionKind === "SDO")
        this.callEditorModal(EditorCommandsDTO.MENUCMDADDMAPPINGSDOS.toString(), this.extractFullNameFromTag(dragSource), dropTarget.nodeFullName, null, dropTarget);
      else
        this.callEditorModal(EditorCommandsDTO.MENUCMDADDMAPPINGMDOS.toString(), this.extractFullNameFromTag(dragSource), dropTarget.nodeFullName, null, dropTarget);
    }
    // User selected multiple source and the target is a UserFolder
    else if (dragSource.length != null && dropTarget.nodeClassName == "GTWUserFolder") {
      this.onNodeAction(EditorCommandsDTO.MENUCMDDROPONFOLDER.toString(), dropTarget.nodeFullName, this.extractFullNameFromTag(dragSource), dragSource, dropTarget);
    }
    // User selected multiple source but we can't only accept one because the target has not a ClassName GTWDataType or it is internal (memberClass == "GTWMasterDataObject")
    else if (dragSource.length != null && (dropTarget.nodeClassName != "GTWDataType" || dropTarget.memberClass == "GTWMasterDataObject")) {
      if (dropTarget.nodeCollectionKind === "SDO")
        this.onNodeAction(EditorCommandsDTO.MENUCMDADDMAPPINGSDO.toString(), dropTarget.nodeFullName, dragSource[0].tagName, dragSource[0], dropTarget);
      else
        this.onNodeAction(EditorCommandsDTO.MENUCMDADDMAPPINGMDO.toString(), dropTarget.nodeFullName, dragSource[0].tagName, dragSource[0], dropTarget);
    }
    // User selected only one source
    else {
      if (dropTarget.nodeCollectionKind === "SDO")
        this.onNodeAction(EditorCommandsDTO.MENUCMDADDMAPPINGSDO.toString(), dropTarget.nodeFullName, dragSource.tagName, dragSource, dropTarget);
      else {
        if (dropTarget.nodeClassName == "GTWUserFolder") {
          this.onNodeAction(EditorCommandsDTO.MENUCMDDROPONFOLDER.toString(), dropTarget.nodeFullName, dragSource.tagName, dragSource, dropTarget);
        }
        else {
          this.onNodeAction(EditorCommandsDTO.MENUCMDADDMAPPINGMDO.toString(), dropTarget.nodeFullName, dragSource.tagName, dragSource, dropTarget);
        }
      }
    }
  }

  private disconnectConnectFromTase2Server(objectNameParameter: string, objectCollectionKind: any, disconnectConnectCommand: string): void {
    this.editorsService.editorAction("Root", "DisconnectConnectFromTase2Server", objectCollectionKind, objectNameParameter, disconnectConnectCommand, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_TASE2_SERVER_NOT_DISCONNECTED_CONNECTED"); }
      }
    );
  }

  private disconnectRestartTase2Server(objectNameParameter: string, objectCollectionKind: any, disconnectRestartCommand: string): void {
    this.editorsService.editorAction("Root", "DisconnectRestartTase2Server", objectCollectionKind, objectNameParameter, disconnectRestartCommand, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_TASE2_SERVER_NOT_RESTARTED"); }
      }
    );
  }

  private autoCreateTags(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "AutoCreateTags", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_AUTO_CREATE_TAGS"); }
      }
    );
  }
  private dropOnFolder(folderNameParameter: string, objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "DropOnFolder", objectCollectionKind, folderNameParameter, objectNameParameter, "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_DROP_ON_FOLDER"); }
      }
    );
  }
  private subscribeUnsubscribeGooseStream(objectNameParameter: string, isSubscribing: boolean ): void {
    this.editorsService.editorAction("Root", "SubscribeUnsubscribeGooseStream", <any>"", objectNameParameter, String(isSubscribing), "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM"); }
      }
    );
  }
  private createTHXMLPointFile(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "CreateTHXMLPointFile", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM"); }
      }
    );
  }
  private createDTMCSVPointFile(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "CreateDTMCSVPointFile", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM"); }
      }
    );
  }
  private switchToRChannel(objectNameParameter: string, objectCollectionKind: any): void {
    this.editorsService.editorAction("Root", "SwitchToRChannel", objectCollectionKind, objectNameParameter, "", "", "").subscribe(
      (data: any) => {
        this.alertService.success("TR_COMMAND_SUCCESS");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM"); }
      }
    );
  }
  private extractFullNameFromTag(tagList: any): any{
    let tagNameList: Array<string> = [];
    tagList.forEach((tag) => {
      tagNameList.push(tag.tagName);
    });
    return tagNameList;
  }
}