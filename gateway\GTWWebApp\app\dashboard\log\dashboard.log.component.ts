﻿/// <reference path="../config/dashboard.config.tag.editor.component.ts" />
import { Component, Input, AfterViewChecked, OnIni<PERSON>, ElementRef, ViewChild} from "@angular/core";
import { AuthenticationService } from "../../authentication/authentication.service";
import { Panel } from '../../modules/panel/panel';
import { LogEntryDTO } from "../../data/model/models";
import { DashboardLogGridComponent } from "./dashboard.log.grid.component";

@Component({
	selector: "dashboardLogComponent",
  styles: [`
			.panel-log{
				height: 100%;
        background-image: url(../../../images/background_7.svg), linear-gradient(#000000, #c5c5c5);
        background-size: 20%;
        margin: 0 2px 0 0;
        overflow-x: auto;
        overflow-y: hidden;
      }
      .panel-log-bg-color {
        height: 100%;
        background-image: linear-gradient(to bottom, rgba(200, 200, 175, 0.96), rgba(250, 250, 250, 0.90));
        min-width: 960px;
      }
      .panel-scroll{
        height: calc(100% - 82px);
        overflow-x: auto;
      }
			.layoutOptionsButton {
        position: absolute;
        width: 99%;
        padding: 6px;
        margin: 0px !important;
        background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(230, 230, 230, 0.8));
			}
			.header-log-grid {
        padding: 40px 0px 0px 0px;
        margin: 0px;
        background-color: transparent;
			}
      .grid-header {
        font-weight:bold;
      }
      .input-xs {
        height: 22px;
        padding: 2px 5px;
        font-size: 12px;
        line-height: 1.5; /* If Placeholder of the input is moved up, rem/modify this. */
        border-radius: 3px;
      }
      `],
  template: `
    <div class="panel-log">
      <div class="panel-log-bg-color">
        <div class="layoutOptionsButton panel panel-default">
          <div style="vertical-align:bottom;">
            <div (click)="extraPanelToggle()" style="display: inline-block;">
              <div *ngIf="isExtraPanelOpen" class="glyphicon glyphicon-chevron-down glyphicon-size cell" title="{{'TR_COLLAPSE' | translate}}"></div>
              <div *ngIf="!isExtraPanelOpen" class="glyphicon glyphicon-chevron-right glyphicon-size cell" title="{{'TR_EXPAND' | translate}}"></div>
              <span style="font-size:16px;font-weight: bold;margin: 2px;">{{'TR_LOG_PARAMETERS' | translate}}</span>
            </div>
            <div style="float: right;margin: -4px">
              <button class="btn btn-default btn-sm" style="margin-right: 4px;" (click)="copyToCSV()"><img src="../../images/download.svg" class="image-button"/>&nbsp;{{'TR_DOWNLOAD_BUFFERED_LOG_ENTRIES' | translate}}</button>
              <button class="btn btn-default btn-sm" style="margin-right: 4px;" (click)="clearDisplayLog()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CLEAR_DISPLAY' | translate}}</button>
              <button class="btn btn-default btn-sm" style="margin-right: 4px;" *ngIf="!isWSPaused" (click)="onClickPause(true)"><img src="../../images/pause.svg" class="image-button"/>&nbsp;{{'TR_PAUSE' | translate}}</button>
              <button class="btn btn-default btn-sm" style="margin-right: 4px;width: 74px" *ngIf="isWSPaused" (click)="onClickPause(false)"><img src="../../images/play.svg" class="image-button"/>&nbsp;{{'TR_RUN' | translate}}</button>
            </div>
          </div>
          <dashboardLogExtraComponent *ngIf="isExtraPanelOpen"></dashboardLogExtraComponent>
        </div>
        <div class="header-log-grid panel panel-default">
          <table style="table-layout:fixed; width:100%">
            <tr>
              <td style="width: 15%" class="grid-header">{{'TR_TIME_STAMP' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.timeStamp" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
              <td style="width: 10%" class="grid-header">{{'TR_SOURCE' | translate}}<br/><input type="text" class="form-control input-xs " [(ngModel)]="logEntryFilter.source" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
              <td style="width: 10%" class="grid-header">{{'TR_DEVICE' | translate}}<br/><input type="text" class="form-control input-xs " [(ngModel)]="logEntryFilter.name" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
              <td style="width: 10%" class="grid-header">{{'TR_CATEGORY' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.category" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
              <td style="width: 10%" class="grid-header">{{'TR_SEVERITY' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.severity" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
              <td style="width: 45%" class="grid-header">{{'TR_MESSAGE' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.message" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
            </tr>
          </table>
        </div>
        <div #scrollMe class="panel-scroll">
          <div>
            <dashboardLogGridComponent [logEntryFilter]="logEntryFilter" [isWSPaused]="isWSPaused" ></dashboardLogGridComponent>
          </div>
        </div>
      </div>
    </div>
    `
})
	
export class DashboardLogComponent implements OnInit{
  @ViewChild("scrollMe", { static: false }) private myScrollContainer: ElementRef;
  @ViewChild(DashboardLogGridComponent, { static: false }) private dashboardLogGridComponent: DashboardLogGridComponent;
  @Input("panel") panel: Panel;
  private logEntryFilter: LogEntryDTO = {};
	private isExtraPanelOpen: boolean;
  private isWSPaused: boolean = false;

  constructor(private authenticationService: AuthenticationService) { }

  public ngOnInit(): void {
    this.logEntryFilter = { timeStamp: "", source: "", category: "", severity: "", message: "" };
  }

  public ngAfterViewChecked(): void {
    if (!this.isWSPaused)
      this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
  }

	private extraPanelToggle(): void {
		this.isExtraPanelOpen = !this.isExtraPanelOpen;
	}

  private copyToCSV(): void {
    this.dashboardLogGridComponent.copyToCSV();
  }

  private clearDisplayLog(): void {
    this.dashboardLogGridComponent.clearLog();
  }

  private onClickPause(isWSPaused): void {
    try {
      this.isWSPaused = isWSPaused;
      if (!this.isWSPaused)
        this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    }
    catch (err) { }
  }
}