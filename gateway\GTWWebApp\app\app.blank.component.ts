﻿import { Component } from "@angular/core";
import { Router } from "@angular/router";
@Component({
	selector: 'appBlankComponent',
	template: `
			<div class="center">
				<button class="btn btn-default" (click)="goToDashboard()">{{'TR_GO_TO_DASHBOARD' | translate}}</button>
			</div>
		`,
	styles: [`
		.center {
			border: 3px solid #a31c3f;
			padding: 40px;
			position: absolute;
			top: 20%;
			right: 46%;
			background-color: gray;
			opacity: 0.8;
		}`]
})

export class AppBlankComponent {
	constructor(private router: Router) { }

	private goToDashboard(): void {
		this.router.navigate(["/dashboard"]);
	}
}