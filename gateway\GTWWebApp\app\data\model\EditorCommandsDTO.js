System.register([], function (exports_1, context_1) {
    "use strict";
    var EditorCommandsDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (EditorCommandsDTO) {
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDNONE"] = 'MENU_CMD_NONE'] = "MENUCMDNONE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSEPARATOR"] = 'MENU_CMD_SEPARATOR'] = "MENUCMDSEPARATOR";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDEDIT"] = 'MENU_CMD_EDIT'] = "MENUCMDEDIT";
                EditorCommandsDTO[EditorCommandsDTO["ME<PERSON>CMDEDITWORKSPACEPARAMETERS"] = 'MENU_CMD_EDIT_WORKSPACE_PARAMETERS'] = "MENUCMDEDITWORKSPACEPARAMETERS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDAUTOCREATETAGS"] = 'MENU_CMD_AUTO_CREATE_TAGS'] = "MENUCMDAUTOCREATETAGS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMBPCHANNEL"] = 'MENU_CMD_ADD_MBP_CHANNEL'] = "MENUCMDADDMBPCHANNEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTCPCHANNELOUTSTATIONSLAVE"] = 'MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE'] = "MENUCMDADDTCPCHANNELOUTSTATIONSLAVE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTCPCHANNELMASTER"] = 'MENU_CMD_ADD_TCP_CHANNEL_MASTER'] = "MENUCMDADDTCPCHANNELMASTER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDNP3UDPTCPCHANNELOUTSTATIONSLAVE"] = 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE'] = "MENUCMDADDDNP3UDPTCPCHANNELOUTSTATIONSLAVE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDNP3UDPTCPCHANNELMASTER"] = 'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER'] = "MENUCMDADDDNP3UDPTCPCHANNELMASTER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDNP3XMLDEVICE"] = 'MENU_CMD_ADD_DNP3_XML_DEVICE'] = "MENUCMDADDDNP3XMLDEVICE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDSERIALCHANNELOUTSTATIONSLAVE"] = 'MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE'] = "MENUCMDADDSERIALCHANNELOUTSTATIONSLAVE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDSERIALCHANNELMASTER"] = 'MENU_CMD_ADD_SERIAL_CHANNEL_MASTER'] = "MENUCMDADDSERIALCHANNELMASTER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMODEMPOOLCHANNEL"] = 'MENU_CMD_ADD_MODEM_POOL_CHANNEL'] = "MENUCMDADDMODEMPOOLCHANNEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMODEM"] = 'MENU_CMD_ADD_MODEM'] = "MENUCMDADDMODEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMODEMPOOL"] = 'MENU_CMD_ADD_MODEM_POOL'] = "MENUCMDADDMODEMPOOL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCCLIENT"] = 'MENU_CMD_ADD_OPC_CLIENT'] = "MENUCMDADDOPCCLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCUACLIENT"] = 'MENU_CMD_ADD_OPC_UA_CLIENT'] = "MENUCMDADDOPCUACLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCUACERTIFICATE"] = 'MENU_CMD_ADD_OPC_UA_CERTIFICATE'] = "MENUCMDADDOPCUACERTIFICATE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDEDITOPCUASERVER"] = 'MENU_CMD_EDIT_OPC_UA_SERVER'] = "MENUCMDEDITOPCUASERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCAECLIENT"] = 'MENU_CMD_ADD_OPC_AE_CLIENT'] = "MENUCMDADDOPCAECLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850CLIENT"] = 'MENU_CMD_ADD_61850_CLIENT'] = "MENUCMDADD61850CLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2CLIENT"] = 'MENU_CMD_ADD_TASE2_CLIENT'] = "MENUCMDADDTASE2CLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2SERVER"] = 'MENU_CMD_ADD_TASE2_SERVER'] = "MENUCMDADDTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2CLIENTSERVER"] = 'MENU_CMD_ADD_TASE2_CLIENT_SERVER'] = "MENUCMDADDTASE2CLIENTSERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850CONTROLTOOPCMAPPING"] = 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING'] = "MENUCMDADD61850CONTROLTOOPCMAPPING";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850CONTROLTOOPCMAPPINGITEM"] = 'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM'] = "MENUCMDADD61850CONTROLTOOPCMAPPINGITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850SERVER"] = 'MENU_CMD_ADD_61850_SERVER'] = "MENUCMDADD61850SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2LOGICALDEVICE"] = 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE'] = "MENUCMDADDTASE2LOGICALDEVICE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDRESTARTTASE2SERVER"] = 'MENU_CMD_RESTART_TASE2_SERVER'] = "MENUCMDRESTARTTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSHOWCONFIGTASE2SERVER"] = 'MENU_CMD_SHOW_CONFIG_TASE2_SERVER'] = "MENUCMDSHOWCONFIGTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSHOWCONFIGTASE2CLIENT"] = 'MENU_CMD_SHOW_CONFIG_TASE2_CLIENT'] = "MENUCMDSHOWCONFIGTASE2CLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISCONNECTTASE2SERVER"] = 'MENU_CMD_DISCONNECT_TASE2_SERVER'] = "MENUCMDDISCONNECTTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDRESTART61850SERVER"] = 'MENU_CMD_RESTART_61850_SERVER'] = "MENUCMDRESTART61850SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSAVETASE2SERVER"] = 'MENU_CMD_SAVE_TASE2_SERVER'] = "MENUCMDSAVETASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDODBCCLIENT"] = 'MENU_CMD_ADD_ODBC_CLIENT'] = "MENUCMDADDODBCCLIENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDODBCITEM"] = 'MENU_CMD_ADD_ODBC_ITEM'] = "MENUCMDADDODBCITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDEQMDO"] = 'MENU_CMD_ADD_EQ_MDO'] = "MENUCMDADDEQMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDINTERNALMDO"] = 'MENU_CMD_ADD_INTERNAL_MDO'] = "MENUCMDADDINTERNALMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850REPORT"] = 'MENU_CMD_ADD_61850_REPORT'] = "MENUCMDADD61850REPORT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDENABLERCB"] = 'MENU_CMD_ENABLE_RCB'] = "MENUCMDENABLERCB";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISABLERCB"] = 'MENU_CMD_DISABLE_RCB'] = "MENUCMDDISABLERCB";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDVERIFYDATASET"] = 'MENU_CMD_VERIFY_DATASET'] = "MENUCMDVERIFYDATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850GOOSE"] = 'MENU_CMD_ADD_61850_GOOSE'] = "MENUCMDADD61850GOOSE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850POLLEDDATASET"] = 'MENU_CMD_ADD_61850_POLLED_DATA_SET'] = "MENUCMDADD61850POLLEDDATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850POLLEDPOINTSET"] = 'MENU_CMD_ADD_61850_POLLED_POINT_SET'] = "MENUCMDADD61850POLLEDPOINTSET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850COMMANDPOINT"] = 'MENU_CMD_ADD_61850_COMMAND_POINT'] = "MENUCMDADD61850COMMANDPOINT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850COMMANDPOINTSET"] = 'MENU_CMD_ADD_61850_COMMAND_POINT_SET'] = "MENUCMDADD61850COMMANDPOINTSET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850WRITABLEPOINT"] = 'MENU_CMD_ADD_61850_WRITABLE_POINT'] = "MENUCMDADD61850WRITABLEPOINT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850WRITABLEPOINTSET"] = 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET'] = "MENUCMDADD61850WRITABLEPOINTSET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850DATASET"] = 'MENU_CMD_ADD_61850_DATASET'] = "MENUCMDADD61850DATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCHANGE61850DATASET"] = 'MENU_CMD_CHANGE_61850_DATASET'] = "MENUCMDCHANGE61850DATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDGOOSEMONITOR"] = 'MENU_CMD_ADD_GOOSE_MONITOR'] = "MENUCMDADDGOOSEMONITOR";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDREDUNDANTSLAVECHANNEL"] = 'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL'] = "MENUCMDADDREDUNDANTSLAVECHANNEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDREDUNDANTMASTERCHANNEL"] = 'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL'] = "MENUCMDADDREDUNDANTMASTERCHANNEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDELETEREDUNDANTCHANNEL"] = 'MENU_CMD_DELETE_REDUNDANT_CHANNEL'] = "MENUCMDDELETEREDUNDANTCHANNEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61400ALARMSNODE"] = 'MENU_CMD_ADD_61400_ALARMS_NODE'] = "MENUCMDADD61400ALARMSNODE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61400ALARMMDO"] = 'MENU_CMD_ADD_61400_ALARM_MDO'] = "MENUCMDADD61400ALARMMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADD61850ITEM"] = 'MENU_CMD_ADD_61850_ITEM'] = "MENUCMDADD61850ITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCONNECTTO61850SERVER"] = 'MENU_CMD_CONNECT_TO_61850_SERVER'] = "MENUCMDCONNECTTO61850SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISCONNECTFROM61850SERVER"] = 'MENU_CMD_DISCONNECT_FROM_61850_SERVER'] = "MENUCMDDISCONNECTFROM61850SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENU_CMD_EDIT_TASE2_EDIT_MODEL"] = 'MENU_CMD_EDIT_TASE2_EDIT_MODEL'] = "MENU_CMD_EDIT_TASE2_EDIT_MODEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDEDITTASE2DATAPOINTS"] = 'MENU_CMD_EDIT_TASE2_DATA_POINTS'] = "MENUCMDEDITTASE2DATAPOINTS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2DSTS"] = 'MENU_CMD_ADD_TASE2_DSTS'] = "MENUCMDADDTASE2DSTS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2POLLEDDATASET"] = 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET'] = "MENUCMDADDTASE2POLLEDDATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2POLLEDPOINTSET"] = 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET'] = "MENUCMDADDTASE2POLLEDPOINTSET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2COMMANDPOINT"] = 'MENU_CMD_ADD_TASE2_COMMAND_POINT'] = "MENUCMDADDTASE2COMMANDPOINT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2COMMANDPOINTSET"] = 'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET'] = "MENUCMDADDTASE2COMMANDPOINTSET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2ITEM"] = 'MENU_CMD_ADD_TASE2_ITEM'] = "MENUCMDADDTASE2ITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2DATASET"] = 'MENU_CMD_ADD_TASE2_DATASET'] = "MENUCMDADDTASE2DATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDMANAGETASE2DATASET"] = 'MENU_CMD_MANAGE_TASE2_DATASET'] = "MENUCMDMANAGETASE2DATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDMANAGETASE2DATASETFULLEDIT"] = 'MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT'] = "MENUCMDMANAGETASE2DATASETFULLEDIT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2DOMAIN"] = 'MENU_CMD_ADD_TASE2_DOMAIN'] = "MENUCMDADDTASE2DOMAIN";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDTASE2DATAATTRIBUTE"] = 'MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE'] = "MENUCMDADDTASE2DATAATTRIBUTE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDOPERATETASE2CONTROL"] = 'MENU_CMD_OPERATE_TASE2_CONTROL'] = "MENUCMDOPERATETASE2CONTROL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCONNECTTOTASE2SERVER"] = 'MENU_CMD_CONNECT_TO_TASE2_SERVER'] = "MENUCMDCONNECTTOTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCONNECTTODISCOVERTASE2SERVER"] = 'MENU_CMD_CONNECT_TO_DISCOVER_TASE2_SERVER'] = "MENUCMDCONNECTTODISCOVERTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISCONNECTFROMTASE2SERVER"] = 'MENU_CMD_DISCONNECT_FROM_TASE2_SERVER'] = "MENUCMDDISCONNECTFROMTASE2SERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSAVEMODELTOFILE"] = 'MENU_CMD_SAVE_MODEL_TO_FILE'] = "MENUCMDSAVEMODELTOFILE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCITEM"] = 'MENU_CMD_ADD_OPC_ITEM'] = "MENUCMDADDOPCITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMULTIPLEOPCITEM"] = 'MENU_CMD_ADD_MULTIPLE_OPC_ITEM'] = "MENUCMDADDMULTIPLEOPCITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMULTIPLEOPCUAITEM"] = 'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM'] = "MENUCMDADDMULTIPLEOPCUAITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCAEITEM"] = 'MENU_CMD_ADD_OPC_AE_ITEM'] = "MENUCMDADDOPCAEITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCUAITEM"] = 'MENU_CMD_ADD_OPC_UA_ITEM'] = "MENUCMDADDOPCUAITEM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDSESSION"] = 'MENU_CMD_ADD_SESSION'] = "MENUCMDADDSESSION";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMDO"] = 'MENU_CMD_ADD_MDO'] = "MENUCMDADDMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMULTIPLEMDO"] = 'MENU_CMD_ADD_MULTIPLE_MDO'] = "MENUCMDADDMULTIPLEMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMAPPINGSDO"] = 'MENU_CMD_ADD_MAPPING_SDO'] = "MENUCMDADDMAPPINGSDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMAPPINGSDOS"] = 'MENU_CMD_ADD_MAPPING_SDOS'] = "MENUCMDADDMAPPINGSDOS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMAPPINGMDO"] = 'MENU_CMD_ADD_MAPPING_MDO'] = "MENUCMDADDMAPPINGMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMAPPINGMDOS"] = 'MENU_CMD_ADD_MAPPING_MDOS'] = "MENUCMDADDMAPPINGMDOS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDROPONFOLDER"] = 'MENU_CMD_DROP_ON_FOLDER'] = "MENUCMDDROPONFOLDER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDSECTOR"] = 'MENU_CMD_ADD_SECTOR'] = "MENUCMDADDSECTOR";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDATATYPE"] = 'MENU_CMD_ADD_DATA_TYPE'] = "MENUCMDADDDATATYPE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDNPPROTO"] = 'MENU_CMD_ADD_DNP_PROTO'] = "MENUCMDADDDNPPROTO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDNPDESCP"] = 'MENU_CMD_ADD_DNP_DESCP'] = "MENUCMDADDDNPDESCP";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDDATASETELEMENT"] = 'MENU_CMD_ADD_DATASET_ELEMENT'] = "MENUCMDADDDATASETELEMENT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDOPCAEATTR"] = 'MENU_CMD_ADD_OPC_AE_ATTR'] = "MENUCMDADDOPCAEATTR";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDELETE"] = 'MENU_CMD_DELETE'] = "MENUCMDDELETE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCHANGEVALUE"] = 'MENU_CMD_CHANGE_VALUE'] = "MENUCMDCHANGEVALUE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSETVALUEANDQUALITY"] = 'MENU_CMD_SET_VALUE_AND_QUALITY'] = "MENUCMDSETVALUEANDQUALITY";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREADDNPPROTO"] = 'MENU_CMD_READ_DNP_PROTO'] = "MENUCMDREADDNPPROTO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDWRITEDNPPROTO"] = 'MENU_CMD_WRITE_DNP_PROTO'] = "MENUCMDWRITEDNPPROTO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREADDNPDESCP"] = 'MENU_CMD_READ_DNP_DESCP'] = "MENUCMDREADDNPDESCP";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDWRITEDNPDESCP"] = 'MENU_CMD_WRITE_DNP_DESCP'] = "MENUCMDWRITEDNPDESCP";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDRESET61850RETRYCONNECTCOUNT"] = 'MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT'] = "MENUCMDRESET61850RETRYCONNECTCOUNT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREAD61850MDO"] = 'MENU_CMD_READ_61850_MDO'] = "MENUCMDREAD61850MDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREADICCPMDO"] = 'MENU_CMD_READ_ICCP_MDO'] = "MENUCMDREADICCPMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREAD"] = 'MENU_CMD_READ_61850_MDO'] = "MENUCMDREAD";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDWRITEACTION"] = 'MENU_CMD_ADD_WRITE_ACTION'] = "MENUCMDADDWRITEACTION";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDMULTIPOINT"] = 'MENU_CMD_ADD_MULTI_POINT'] = "MENUCMDADDMULTIPOINT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDPERFORMWRITEACTION"] = 'MENU_CMD_PERFORM_WRITE_ACTION'] = "MENUCMDPERFORMWRITEACTION";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDWRITEMULTIPOINT"] = 'MENU_CMD_WRITE_MULTI_POINT'] = "MENUCMDWRITEMULTIPOINT";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSELECTDATAATTRIBUTE"] = 'MENU_CMD_SELECT_DATA_ATTRIBUTE'] = "MENUCMDSELECTDATAATTRIBUTE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSUBSCRIBEGOOSESTREAM"] = 'MENU_CMD_SUBSCRIBE_GOOSE_STREAM'] = "MENUCMDSUBSCRIBEGOOSESTREAM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDUNSUBSCRIBEGOOSESTREAM"] = 'MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM'] = "MENUCMDUNSUBSCRIBEGOOSESTREAM";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCREATETHXMLPOINTFILE"] = 'MENU_CMD_CREATE_THXML_POINT_FILE'] = "MENUCMDCREATETHXMLPOINTFILE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCREATEDTMCSVPOINTFILE"] = 'MENU_CMD_CREATE_DTM_CSV_POINT_FILE'] = "MENUCMDCREATEDTMCSVPOINTFILE";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCONNECTOPCSERVER"] = 'MENU_CMD_CONNECT_OPC_SERVER'] = "MENUCMDCONNECTOPCSERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISCONNECTOPCSERVER"] = 'MENU_CMD_DISCONNECT_OPC_SERVER'] = "MENUCMDDISCONNECTOPCSERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCONNECTOPCAESERVER"] = 'MENU_CMD_CONNECT_OPC_AE_SERVER'] = "MENUCMDCONNECTOPCAESERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISCONNECTOPCAESERVER"] = 'MENU_CMD_DISCONNECT_OPC_AE_SERVER'] = "MENUCMDDISCONNECTOPCAESERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDCONNECTOPCUASERVER"] = 'MENU_CMD_CONNECT_OPC_UA_SERVER'] = "MENUCMDCONNECTOPCUASERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISCONNECTOPCUASERVER"] = 'MENU_CMD_DISCONNECT_OPC_UA_SERVER'] = "MENUCMDDISCONNECTOPCUASERVER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDOPCUAGETSERVERSTATUS"] = 'MENU_CMD_OPC_UA_GET_SERVER_STATUS'] = "MENUCMDOPCUAGETSERVERSTATUS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDENABLEDSTS"] = 'MENU_CMD_ENABLE_DSTS'] = "MENUCMDENABLEDSTS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDDISABLEDSTS"] = 'MENU_CMD_DISABLE_DSTS'] = "MENUCMDDISABLEDSTS";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDSWITCHTORCHANNEL"] = 'MENU_CMD_SWITCH_TO_RCHANNEL'] = "MENUCMDSWITCHTORCHANNEL";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDADDUSERDEFINEDFOLDER"] = 'MENU_CMD_ADD_USER_DEFINED_FOLDER'] = "MENUCMDADDUSERDEFINEDFOLDER";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREADDATASET"] = 'MENU_CMD_READ_DATASET'] = "MENUCMDREADDATASET";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREADOPCUAMDO"] = 'MENU_CMD_READ_OPC_UA_MDO'] = "MENUCMDREADOPCUAMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDREADOPCDAMDO"] = 'MENU_CMD_READ_OPC_DA_MDO'] = "MENUCMDREADOPCDAMDO";
                EditorCommandsDTO[EditorCommandsDTO["MENUCMDRESETAVERAGEMDOUPDATERATE"] = 'MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE'] = "MENUCMDRESETAVERAGEMDOUPDATERATE";
            })(EditorCommandsDTO || (EditorCommandsDTO = {}));
            exports_1("EditorCommandsDTO", EditorCommandsDTO);
        }
    };
});
//# sourceMappingURL=EditorCommandsDTO.js.map