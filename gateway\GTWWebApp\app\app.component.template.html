﻿<div class="content-background-color">
</div>
<div class="content-background">
</div>
<nav class="navbar navbar-inverse navbar-fixed-top" style="min-width: 600px">
  <div class="container-fluid">
    <div class="navbar-header">
      <button type="button" class="navbar-toggle collapsed" (click)="toggleNavigationState()">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <div class="navbar-brand">
        <div style="display:inline-block">
          <a [routerLink]="['dashboard']" title="{{'TR_DASHBOARD' | translate}}" style="border:0;">
            <img src="../images/GTWLogo.png" class="logo">
          </a>
        </div>
        <div class="logo-text-container">
          <a [routerLink]="['dashboard']" title="{{'TR_DASHBOARD' | translate}}" class="logo-text">{{title}}</a>
        </div>
      </div>
    </div>
    <div class="collapse navbar-collapse" [ngClass]="{ 'in': isNavigationIn }">
      <ul class="nav navbar-nav">
        <li *ngIf="(this.globalDataService.SDGConfig?.gtwDoAuth && this.authenticationService.userApiConfig?.username != null)"><a style="color: #ccb00b;font-weight: bold;" title="{{'TR_' + this.authenticationService.role | translate}}">{{'TR_HI' | translate}} {{this.authenticationService.userApiConfig?.username}}</a></li>
        <li class="dropdown" dropdown>
          <a [routerLink]="['dashboard']" routerLinkActive="active">{{'TR_DASHBOARD' | translate}}</a>
          <div class="dropdown-menu" *ngIf="(this.router.url === '/dashboard')">
            <div class="dropdown-item"><dashboardManagerComponent></dashboardManagerComponent></div>
          </div>
        </li>
        <li class="dropdown" *ngIf="this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'" dropdown>
          <a>{{'TR_SETTINGS' | translate}}</a>
          <div class="dropdown-menu">
            <div class="dropdown-item" *ngIf="this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'"><img src="images/config.svg" class="menu-icon" /><a [routerLink]="['settings']" routerLinkActive="active">{{'TR_SYSTEM_SETTINGS' | translate}}</a></div>
            <div class="dropdown-item" *ngIf="this.authenticationService.role | checkRole:'SU_ROLE'"><img src="images/users.svg" class="menu-icon" /><a [routerLink]="['users']" routerLinkActive="active">{{'TR_USERS' | translate}}</a></div>
          </div>
        </li>
        <li class="dropdown" dropdown>
          <a>{{'TR_LOG' | translate}}</a>
          <div class="dropdown-menu">
            <div class="dropdown-item"><img src="images/log.svg" class="menu-icon" /><a [routerLink]="['monitorLog']" routerLinkActive="active">{{'TR_MONITOR_LOG' | translate}}</a></div>
            <div class="dropdown-item" *ngIf="(this.globalDataService.SDGConfig?.gtwDoAudit && this.authenticationService.role | checkRole:'SU_ROLE')"><img src="images/auditLog.svg" class="menu-icon" /><a [routerLink]="['auditLog']" routerLinkActive="active">{{'TR_AUDIT_LOG' | translate}}</a></div>
            <div class="dropdown-item" *ngIf="(this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE')"><img src="images/soeLog.svg" class="menu-icon" /><a [routerLink]="['soeLog']" routerLinkActive="active">{{'TR_SOE_LOG' | translate}}</a></div>
          </div>
        </li>
        <li class="dropdown" dropdown>
          <a>{{'TR_HELP' | translate}}</a>
          <div class="dropdown-menu">
            <div class="dropdown-item"><img src="images/help.svg" class="menu-icon" /><a href="../help/SDG_Implementers_Guide.pdf" target="_blank">{{'TR_HELP' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/support.svg" class="menu-icon" /><a href="../help/Content/QUICK-START/Quick-Start-Menu.htm" target="_blank">{{'TR_QUICK_START' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/help.svg" class="menu-icon" /><a (click)="quickStartVisible()">{{'TR_QUICK_START_POPUP' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/iniFile.svg" class="menu-icon" /><a (click)="iniHelpVisible()">{{'TR_INI_FILE_PARAMETER_HELP' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/support.svg" class="menu-icon" /><a [routerLink]="['support']" routerLinkActive="active">{{'TR_SUBMIT_SUPPORT_REQUEST' | translate}}</a></div>
            <div class="dropdown-item" *ngIf="this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'"><img src="images/license.svg" class="menu-icon" /><a [routerLink]="['license']" routerLinkActive="active">{{'TR_LICENSE' | translate}}</a></div>
            <div class="dropdown-item" *ngIf="this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'"><img src="images/help.svg" class="menu-icon" /><a href="../help/TMW_Licensing_Guide.pdf" target="_blank">{{'TR_LICENSING_HELP' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/requestQuote.svg" class="menu-icon" /><a href="https://www.trianglemicroworks.com/sales/sales-request" title="Request Quote" target="_blank" style="border:0">{{'TR_REQUEST_QUOTE' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/about.svg" class="menu-icon" /><a [routerLink]="['update']" routerLinkActive="active">{{'TR_UPDATE' | translate}}</a></div>
            <div class="dropdown-item"><img src="images/about.svg" class="menu-icon" /><a [routerLink]="['about']" routerLinkActive="active">{{'TR_ABOUT' | translate}}</a></div>
          </div>
        </li>
        <li *ngIf="(this.globalDataService.SDGConfig?.gtwDoAuth && this.authenticationService.userApiConfig?.username != null)"><a (click)="logoff()" style="cursor: pointer;">{{'TR_LOGOFF' | translate}}</a></li>
        <li *ngIf="(this.globalDataService.SDGConfig?.gtwDoAuth && this.authenticationService.userApiConfig?.username != null)"><a style="cursor: pointer;"><appPasswordComponent></appPasswordComponent></a></li>
      </ul>
    </div>
    <div><a href="https://www.trianglemicroworks.com/" title="Triangle MicroWorks" target="_blank" style="border:0"><img src="../images/TMWLogo.png" class="logo-tmw"></a></div>
  </div>
  <div class="navbar-red"></div>
</nav>
<!--Wait for the SDGConfig to Load since content pages are API depend on the SDGConfig data-->
<ng-container *ngIf="globalDataService.SDGConfig !== null ">
  <div style="margin-bottom:10px; min-width : 350px">
    <contextMenuComponent></contextMenuComponent>
    <router-outlet></router-outlet>
    <helpQuickStartComponent #helpQuickStart></helpQuickStartComponent>
    <helpIniComponent #helpIni></helpIniComponent>
    <appInfoComponent #appInfoHttpsCertIsTmwSigned></appInfoComponent>
    <appInfoComponent #appInfoUseWebSSL></appInfoComponent>
    <appInfoComponent #appInfoValidBrowser></appInfoComponent>

    <tooltipComponent></tooltipComponent>
  </div>
  <div class="status-bar">
    <appHealthComponent *ngIf="this.authenticationService.role | checkRole:'VIEWER_ROLE'"></appHealthComponent>
    <alertStatusBarComponent></alertStatusBarComponent>
  </div>
</ng-container>
<loaderComponent></loaderComponent>
