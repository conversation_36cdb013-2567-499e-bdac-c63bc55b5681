System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var core_1, BASE_PATH, COLLECTION_FORMATS;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            exports_1("BASE_PATH", BASE_PATH = new core_1.InjectionToken('basePath'));
            exports_1("COLLECTION_FORMATS", COLLECTION_FORMATS = {
                'csv': ',',
                'tsv': '   ',
                'ssv': ' ',
                'pipes': '|'
            });
        }
    };
});
//# sourceMappingURL=variables.js.map