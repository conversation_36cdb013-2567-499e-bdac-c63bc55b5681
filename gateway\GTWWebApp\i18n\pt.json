{"TR_61850_CANT_DELETE_DATASET_DISCONNECTED": "Não é possível excluir o conjunto de dados '{{arg1}}' enquanto desconectado. Por favor, conecte e tente novamente. ", "TR_61850_CANT_DELETE_DATASET_IN_USE": "Não é possível excluir o conjunto de dados '{{arg1}}' porque pertence a um ou mais blocos de controle. Exclua os blocos de controle e tente novamente. ", "TR_61850_CANT_DELETE_NON_DYNAMIC_DATASET": "O conjunto de dados '{{arg1}}' não é dinâmico e não pode ser excluído.", "TR_61850_CLIENT_DUP_LIC": "Falha ao adicionar o cliente IEC 61850 (pode ser duplicado ou sem licença)", "TR_61850_CLIENT_DUPLICATE": "Não é possível adicionar o cliente 61850: '{{arg1}}'. Nome duplicado. ", "TR_61850_CLIENT_FAILED_DUPLICATE": "Falha ao criar o cliente 61850 porque possui um nome duplicado: '{{arg1}}'", "TR_61850_CREATE_SDO_FAILED_DUPLICATE": "Não foi possível criar o SDO {{arg1}}.  Nome de tag duplicado?", "TR_61850_CREATE_SDO_FAILED_INVALID_TAG": "Não foi possível criar o SDO {{arg1}}.  Nome de etiqueta inválido?", "TR_61850_CREATE_SDO_FAILED_LIC_LIMIT": "Não foi possível criar o SDO {{arg1}}.  O limite de licença foi atingido ", "TR_61850_DATASET_VERIFY_FAILED": "Falha na verificação do conjunto de dados para '{{arg1}}'. Verifique se as definições do conjunto de dados do cliente e do servidor correspondem, caso contrário os relatórios não funcionarão corretamente. ", "TR_61850_DATASET_VERIFY_SUCCEED": "Conjunto de dados '{{arg1}}' 'verificado com sucesso!", "TR_61850_INVALID_MODEL": "Erro: modelo 61850 inválido", "TR_61850_LOAD_COMMUNICATION": "Falha ao carregar a configuração do arquivo SCL. Verifique se o caminho do arquivo SCL, o nome do IED e a seção de comunicação do arquivo SCL são todos válidos. ", "TR_61850_MD_BIND_MISMATCH": "Não foi possível vincular o SDO ao MDO. Tipo incompatível?", "TR_61850_MDO_DELETE": "61850 MDOs não podem ser excluídos", "TR_61850_MDO_SLAVE_MAP_FAILED": "Não é possível mapear um ponto não MDO para este ponto escravo", "TR_61850_NO_POINTS_FOUND": "Não há pontos válidos", "TR_61850_NO_POLLED": "Não há mais IEC 61850 PolledPointSets disponíveis", "TR_61850_POLLED_ADD": "Não foi possível adicionar o {{arg1}} PolledPointSet no servidor 61850: {{arg2}}.", "TR_61850_POLLED_DELETE": "Não é possível excluir o conjunto de dados pesquisados '{{arg1}}', pois ele possui itens existentes: {{arg2}}", "TR_61850_POLLED_DELETE_EXISTING": "Não é possível excluir o conjunto de pontos pesquisados '{{arg1}}', pois possui itens existentes: {{arg2}}", "TR_61850_REPORT_DELETE_ERROR": "Não é possível excluir o bloco de controle de relatórios '{{arg1}}', ele atualmente possui um encadeamento de nova tentativa em execução. Mude o 'Repetir Habilitar Contagem' para zero para parar o encadeamento e tente novamente. ", "TR_61850_REPORT_DELETE_EXISTING": "Não é possível excluir o bloco de controle de relatórios '{{arg1}}', pois possui itens existentes: {{arg2}}", "TR_61850_SERVER_DUP_LIC": "Falha ao adicionar o servidor IEC 61850 (pode estar duplicado ou sem licença)", "TR_61850_SERVER_DUPLICATE": "Não é possível adicionar o servidor 61850: '{{arg1}}'. Nome duplicado. ", "TR_61850_SERVER_FAILED_DUPLICATE": "Falha ao criar o servidor 61850 porque possui um nome duplicado: '{{arg1}}'", "TR_6T_CATEGORY_APP": "APP", "TR_6T_CATEGORY_C_APP": "C APP", "TR_6T_CATEGORY_C_CASM": "C CASM", "TR_6T_CATEGORY_C_CLIENT": "C CLIENT", "TR_6T_CATEGORY_C_CLIENTRPT": "C CLIENTRPT", "TR_6T_CATEGORY_C_CLIENTSTATE": "C CLIENTSTATE", "TR_6T_CATEGORY_C_DYNAMIC_DATASETS": "C DATASETS DINÂMICOS", "TR_6T_CATEGORY_C_EXTREF": "C EXTREF", "TR_6T_CATEGORY_C_SCL": "C SCL", "TR_6T_CATEGORY_C_STACK": "C STACK", "TR_6T_CATEGORY_C_TARGET": "C TARGET", "TR_6T_CATEGORY_C_TEST": "C TEST", "TR_6T_CATEGORY_C_TIME": "C TIME", "TR_6T_CATEGORY_C_TRANSLOW": "C TRANSLOW", "TR_6T_CATEGORY_C_TRANSPORT": "C TRANSPORT", "TR_6T_CATEGORY_CLIENTPARSEVALUES": "CLIENTPARSEVALUES", "TR_6T_CATEGORY_CONTROL": "CONTROL", "TR_6T_CATEGORY_DISCOVERY": "DISCOVERY", "TR_6T_CATEGORY_EXTREF": "EXTREF", "TR_6T_CATEGORY_GENERAL": "GERAL", "TR_6T_CATEGORY_GOOSE": "GOOSE", "TR_6T_CATEGORY_READ": "LER", "TR_6T_CATEGORY_READ_HANDLER": "LER MANUSEADOR", "TR_6T_CATEGORY_REPORT": "REPORT", "TR_6T_CATEGORY_TIME_SYNCH": "TIME SYNCH", "TR_6T_CATEGORY_WRITE": "WRITE", "TR_6T_CATEGORY_XML": "XML", "TR_6T_FILTER": "Filtro MMS", "TR_6T_LOW_LEVEL_STACK": "Pilha de baixo nível do MMS", "TR_6T_STANDARD_STACK": "Pilha padrão do MMS", "TR_MDO_CAN_NOT_CREATE": "MDO não encontrado, não pode criar", "TR_SDO_CREATE_DEFINED_ALREADY": "SDO j<PERSON> definido, não pode criar", "TR_SDO_BIND_FAIL": "Não foi possível vincular o SDO.", "TR_SDO_CREATE_FAIL": "Não foi possível criar o SDO.", "TR_SDO_CAN_NOT_SET_OPTIONS": "Não foi possí<PERSON> definir as opções do SDO {{arg1}}", "TR_ABOUT": "Sobre", "TR_ACCEPT_CLOCK_SYNC": "Aceitar sincronizações de relógio", "TR_ACCEPT_CLOCK_SYNC_DESC": "Se true e UseSystemTime for true, o relógio do sistema Windows será ajustado por uma sincronização de tempo recebida de um dispositivo mestre externo. Se a sincronização de hora falsa não ajustar o relógio do sistema Windows.Ao usar um relógio simulado, essa configuração não tem efeito e as sincronizações do relógio são sempre aceitas e ajusta o relógio simulado ", "TR_ACCESS_READ_AND_WRITE_USERS_OF_MANAGEMENT": "Acesse a leitura e gravação do gerenciamento de usuários", "TR_ACCESS_RIGHT": "<PERSON><PERSON><PERSON>sso", "TR_ACCESS_RIGHT_DESC": "Definição do direito de acesso", "TR_ACCESS_TO_CONFIG_UI_FOR_READ_AND_WRITE": "Acesso à configuração da interface do usuário para leitura / gravação", "TR_ACCESS_TO_CONFIG_UI_FOR_READ_ONLY": "Acesso à configuração da interface do usuário para somente leitura", "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_AND_WRITE": "Acesso à interface do usuário em tempo de execução para leitura e gravação", "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_ONLY": "Acesso à interface do usuário em tempo de execução para somente leitura", "TR_ACTION": "Ação", "TR_ACTION_NAME": "Nome da ação", "TR_ACTION_NAME_DESC": "Nome da ação", "TR_ACTION_VALUES": "Valores da ação", "TR_ACTION_VALUES_DESC": "Valores da ação", "TR_ACTIVATE_ONLINE": "Ativar on-line", "TR_ACTIVATE_PRODUCT_KEY_OFFLINE": "Chave do produto ativa offline", "TR_ACTIVATE_PRODUCT_KEY_ONLINE": "Chave do produto ativa online", "TR_ACTIVE": "Ativo", "TR_ACTIVE_DESC": "Ativo", "TR_ADD": "<PERSON><PERSON><PERSON><PERSON>", "TR_ADD_GOOSE": "Não foi possível adicionar o {{arg1}} GOOSE no servidor 61850: {{arg2}}.", "TR_ADD_ITEM": "Adicionar item", "TR_ADD_ITEM_DESC": "Adicionar item", "TR_ADD_NEW_USER": "Adicionar novo usuário", "TR_ADD_PROPERTY": "<PERSON><PERSON><PERSON><PERSON> propried<PERSON>", "TR_ADD_PROPERTY_DESC": "<PERSON><PERSON><PERSON><PERSON> propried<PERSON>", "TR_ADD_SUBSCRIPTION": "Adicionar assinatura", "TR_ADD_SUBSCRIPTION_DESC": "Adicionar assinatura", "TR_ALARM_ARRAY": "<PERSON><PERSON>", "TR_ALARM_ARRAY_DESC": "Definir a matriz do alarme", "TR_ALIAS_NAME": "Nome", "TR_ALIAS_NAME_DESC": "Especifica o nome.", "TR_APP_CERT_DIRECTORY": "Diretório de certificados do aplicativo", "TR_APP_CERT_DIRECTORY_DESC": "Especifica o diretório de certificados do aplicativo.", "TR_APP_ERROR": "Erro de aplicativo", "TR_APP_ERROR_DESC": "Registrar mensagens de erro do aplicativo", "TR_APP_START_STOP": "App. Iniciar / Parar", "TR_APP_START_STOP_DESC": "Registre as mensagens de inicialização / desligamento do aplicativo", "TR_APP_STATUS": "Status da aplicação", "TR_APP_STATUS_DESC": "Registre mensagens periódicas de status do aplicativo", "TR_APPL_AUTO_REQ_MODE": "Modo de solicitação automática", "TR_APPL_AUTO_REQ_MODE_DESC": "Cada bit ativa (1) ou desativa (0) uma solicitação automática. Este parâmetro é usado apenas para sessões master ou slave usando o protocolo DNP3.", "TR_APPL_DNPABS_RESP_TIMEOUT": "Tempo limite de resposta (ms) - DNP", "TR_APPL_DNPABS_RESP_TIMEOUT_DESC": "Tempo limite padrão da resposta do aplicativo DNP. Este valor é a quantidade máxima de tempo (em milissegundos) que será permitida antes que um comando seja cancelado devido ao tempo limite. Esse tempo começa quando a solicitação é enviada e termina quando a resposta final no nível do aplicativo é recebida. Esse valor geralmente pode ser substituído por pontos de dados específicos pela opção 'TO' no arquivo de mapeamento de pontos. ", "TR_APPL_IECABS_RESP_TIMEOUT": "Tempo limite de resposta (ms) - IEC", "TR_APPL_IECABS_RESP_TIMEOUT_DESC": "Tempo limite padrão da resposta do aplicativo IEC. Este valor é a quantidade máxima de tempo (em milissegundos) que será permitida antes que um comando seja cancelado devido ao tempo limite. Esse tempo começa quando a solicitação é enviada e termina quando a resposta final no nível do aplicativo é recebida. Esse valor geralmente pode ser substituído por pontos de dados específicos pela opção 'TO' no arquivo de mapeamento de pontos. ", "TR_APPL_INCR_RESP_TIMEOUT": "Tempo limite de resposta incremental (ms)", "TR_APPL_INCR_RESP_TIMEOUT_DESC": "Quantidade máxima de tempo a ser permitida entre mensagens de um dispositivo remoto quando uma solicitação está pendente para esse dispositivo. A mensagem não precisa ser uma resposta direta à solicitação pendente. Se nenhuma mensagem for recebida do dispositivo remoto dentro desse período, é assumido que o dispositivo encerrou o processamento da solicitação e a solicitação foi cancelada devido a um tempo limite no nível do aplicativo. Este timer é reiniciado toda vez que uma mensagem é recebida do dispositivo remoto. ", "TR_APPL_MBABS_RESP_TIMEOUT": "Tempo limite de resposta (ms) - Modbus", "TR_APPL_MBABS_RESP_TIMEOUT_DESC": "Tempo limite padrão da resposta do aplicativo Modbus. Este valor é a quantidade máxima de tempo (em milissegundos) que será permitida antes que um comando seja cancelado devido ao tempo limite. Esse tempo começa quando a solicitação é enviada e termina quando a resposta final no nível do aplicativo é recebida. Esse valor geralmente pode ser substituído por pontos de dados específicos pela opção 'TO' no arquivo de mapeamento de pontos. ", "TR_APPLICATION_FUNCTIONS": "Funções do aplicativo", "TR_APPLICATION_LAYER": "Camada de aplicação", "TR_ARE_YOU_SURE_TO_CLEAR_MODEL": "Tem certeza de que deseja limpar o modelo?", "TR_ARE_YOU_SURE_TO_DELETE_DATASET": "Tem certeza de que deseja excluir este conjunto de dados?", "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME": "Tem certeza que deseja excluir {{NodeName}}?", "TR_ARE_YOU_SURE_TO_REMOVE_MAPPING": "Tem certeza que deseja excluir mapeamento {{mapping}}?", "TR_ARE_YOU_SURE_TO_DELETE_USER_X": "Tem certeza que deseja excluir o usuário {{nome de usuário}}?", "TR_AREA_SPACE": "Espaço da área", "TR_AREA_SPACE_DESC": "Definir o espaço da área", "TR_ASDUORIGINATOR_ADDR": "Endereço do originador", "TR_ASDUORIGINATOR_ADDR_DESC": "Endereço do originador (para COT de 2 octetos).  Este parâmetro é usado apenas para sessões principais usando os perfis de protocolo IEC 60870-5-101 ou IEC 60870-5-104. ", "TR_ASDUSIZE_CMN_ADDR": "Número de octetos (bytes) no endereço comum do ASDU", "TR_ASDUSIZE_CMN_ADDR_DESC": "Número de octetos (bytes) no campo Endereço comum do ASDU (endereço do setor).  Este parâmetro é usado apenas para sessões master e slave usando os perfis de protocolo IEC 60870-5-101 ou IEC 60870-5-104. ", "TR_ASDUSIZE_COT": "Número de octetos (bytes) em causa de transmissão", "TR_ASDUSIZE_COT_DESC": "Número de octetos (bytes) no campo de causa de transmissão (COT) do ASDU.  Este parâmetro é usado apenas para sessões master e slave usando os perfis de protocolo IEC 60870-5-101 ou IEC 60870-5-104. ", "TR_ASDUSIZE_IOA": "Número de octetos (bytes) no número do ponto", "TR_ASDUSIZE_IOA_DESC": "Número de octetos (bytes) no campo Endereço do objeto de informações (número do ponto).  Este parâmetro é usado apenas para sessões master e slave usando os perfis de protocolo IEC 60870-5-101 ou IEC 60870-5-104. ", "TR_AUDIT": "Auditoria", "TR_AUDIT_LOG": "Registro de auditoria", "TR_AUTH_CONFIG": "Configuração de autenticação", "TR_AUTH_MODE": "Modo de autenticação", "TR_AUTH_SEC_USERS_LIST": "Usuários do SECAuth v5", "TR_AUTH_SEC_USERS_LIST_DESC": "Usuários do SECAuth v5", "TR_AUTHENTICATION_PASSWORD": "Senha de autenticação", "TR_AUTO_MAP_QUALITY_TIME": "Qualidade e tempo do mapa automático", "TR_AUTO_MAP_QUALITY_TIME_DESC": "Especifica se o mapeamento automático de qualidade e hora está ativo.", "TR_AUTO_SAVE": "Salvamento automático", "TR_AUTO_SAVE_ENABLE": "ativar", "TR_AUTO_SAVE_ENABLE_DESC": "Ativar salvamento automático", "TR_AUTO_SAVE_PRD": "Período de gravação automática", "TR_AUTO_SAVE_PRD_DESC": "Tempo máximo entre salvar os arquivos de configuração do aplicativo .ini e .csv em mili segundos. Um valor 0 desativará os salvamentos. O valor mais baixo para o período de economia é 60000 ms (1 minuto) ", "TR_AUTO_START_ENGINE_ON_GUI_EXIT": "Inicialização automática do mecanismo na saída da GUI", "TR_AUTO_START_SERVICE_ON_GUI_EXIT": "Mecanismo de serviço automático na saída da GUI", "TR_AVAILABLE_SERVERS": "Ser<PERSON><PERSON> disponí<PERSON>", "TR_AVAILABLE_SERVERS_DESC": "Ser<PERSON><PERSON> disponí<PERSON>", "TR_BLOCKED": "BLOCKED", "TR_BLOCKED_DESC": "Bloqueado", "TR_BUFFER_OVERFLOW": "Buffer Overflow", "TR_BUFFER_OVERFLOW_DESC": "Especifica se o estouro de buffer está ativo.", "TR_BUFFER_TIME": "Tempo de buffer (ms)", "TR_BUFFER_TIME_DESC": "Especifica o tempo atual do buffer.", "TR_CAN_NOT_SET_SIGNED_AND_DUAL_REGISTER": "Não é possível definir SIGNED e DualRegister. A opção DualRegister será desativada. Se a opção Dual for desejada, desative SIGNED. ", "TR_CANCEL": "<PERSON><PERSON><PERSON>", "TR_CANT_CREATE_MDO": "Não foi possível criar o {{arg1}} MDO.  Possível nome de etiqueta inválido. ", "TR_CANT_DELETE_POINT_IN_USE": "A equação: {{arg1}} está mapeada para outros pontos ou é usada em uma equação, não pode editar / excluir", "TR_CATEGORY": "Categoria", "TR_CENTRAL_AUTHORITY": "Autoridade Central", "TR_CERT_AUTH_CHAINING_VERIFICATION_DEPTH": "Profundidade de verificação de encadeamento da autoridade de certificação", "TR_CERT_AUTH_DIR_PATH": "Diretório para autoridade de certificação", "TR_CERT_AUTH_FILE_PATH": "Arquivo da autoridade de certificação", "TR_CERT_REVOCATION_FILE_PATH": "Arquivo da lista de revogação da autoridade de certificação", "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH": "Profundidade de verificação do encadeamento da CA", "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH_DESC": "Especifica a profundidade da verificação de encadeamento da autoridade de certificação", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR": "Caminho do diretório da autoridade de certificação", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR_DESC": "Caminho para o diretório de certificados da autoridade de certificação.", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE": "Arquivo da autoridade de certificação", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE_DESC": "Arquivo contendo certificados da autoridade de certificação.", "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE": "Arquivo de revogação de certificado", "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE_DESC": "Arquivo que contém a lista de revogação de certificados.", "TR_CHAN_TLSCOMMON_NAME": "Nome comum do TLS", "TR_CHAN_TLSCOMMON_NAME_DESC": "Nome comum a ser esperado nos certificados TLS recebidos (a seqüência vazia é desativada).", "TR_CHAN_TLSDH_FILE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TR_CHAN_TLSDH_FILE_DESC": "Arquivo que contém <PERSON><PERSON><PERSON>.", "TR_CHAN_TLSDSACERTIFICATE_FILE": "Arquivo de certificado público do DSA", "TR_CHAN_TLSDSACERTIFICATE_FILE_DESC": "Arquivo que contém o certificado da chave para as cifras do DSA TLS.", "TR_CHAN_TLSDSAPRIVATE_KEY_FILE": "Arquivo de chave privada do DSA", "TR_CHAN_TLSDSAPRIVATE_KEY_FILE_DESC": "Arquivo que contém a chave privada das cifras DSA TLS.", "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE": "Frase secreta da chave privada do DSA", "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE_DESC": "PassPhrase para descriptografar a chave privada para cifras DSA TLS.", "TR_CHAN_TLSENABLE": "Ativar TLS", "TR_CHAN_TLSENABLE_DESC": "Se verdadeiro, a conexão será estabelecida usando o TLS para segurança.", "TR_CHAN_TLSHANDSHAKE_TIMEOUT": "Tempo limite do handshake TLS (s)", "TR_CHAN_TLSHANDSHAKE_TIMEOUT_DESC": "Tempo máximo em milissegundos para aguardar a conclusão do handshake de conexão TLS.", "TR_CHAN_TLSRENEGOTIATION_COUNT": "TLS Max PDU antes de forçar a renegociação da cifra", "TR_CHAN_TLSRENEGOTIATION_COUNT_DESC": "Máximo de PDUs antes de forçar a renegociação de cifra.", "TR_CHAN_TLSRENEGOTIATION_SECONDS": "Renegociação TLS (s)", "TR_CHAN_TLSRENEGOTIATION_SECONDS_DESC": "Tempo máximo (segundos) antes de forçar a renegociação da cifra.", "TR_CHAN_TLSRSACERTIFICATE_FILE": "Arquivo de certificado público RSA", "TR_CHAN_TLSRSACERTIFICATE_FILE_DESC": "Arquivo que contém o certificado da chave para cifras RSA TLS.", "TR_CHAN_TLSRSAPRIVATE_KEY_FILE": "Arquivo de chave privada RSA", "TR_CHAN_TLSRSAPRIVATE_KEY_FILE_DESC": "Arquivo que contém a chave privada para as cifras RSA TLS.", "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE": "Frase-chave da chave privada RSA", "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "PassPhrase para descriptografar a chave privada para cifras RSA TLS.", "TR_CHANGE_PASSWORD": "<PERSON><PERSON><PERSON><PERSON>", "TR_CHANGE_VALUE": "Alterar valor", "TR_CHANGE_VALUE_AND_QUALITY": "Alterar valor e qualidade", "TR_CHANGE_VALUE_AND_QUALITY_OF": "Alterar valor e qualidade de", "TR_CHANGE_VALUE_OF_SELECTED": "Alterar valor das tags selecionadas", "TR_CHANNEL_DELETE_ONLY": "Não é possível excluir o último canal redundante: '{{arg1}}'. Pelo menos um canal redundante deve existir para o grupo de redundância. ", "TR_CHANNEL_DUPLICATE": "Não é possível adicionar o canal: '{{arg1}}'. Nome duplicado. ", "TR_CHANNEL_ENABLE_TLS": "Ativar TLS", "TR_CHANNEL_IP_PORT_NUMBER": "Número da porta IP", "TR_CHANNEL_LOCAL_IP": "IP local", "TR_CHANNEL_MBP_CARD_NUMBER": "Número do cartão", "TR_CHANNEL_MBP_RECEIVE_TIMEOUT": "Tempo limite de recebimento", "TR_CHANNEL_MBP_ROUTE_ADDRESS": "Rota / Endereço", "TR_CHANNEL_MBP_SLAVE_PATH": "<PERSON><PERSON><PERSON>", "TR_CHANNEL_NAME": "Nome alternativo", "TR_CHANNEL_NAME_DESC": "Especifique o nome do canal", "TR_CHANNEL_NO_MORE": "Não há mais canais disponíveis", "TR_CHANNEL_PROTOCOL_TYPE": "Protocolo de sessão", "TR_CHANNEL_PROTOCOL_TYPE_DESC": "Define o protocolo para o canal Aplica-se a todos os tipos de canais físicos", "TR_CHANNEL_START_DELAY": "Atraso no início do canal (ms)", "TR_CHANNEL_START_DELAY_DESC": "Atrase o tempo em milissegundos para escalonar o início de todos os canais (escravos e mestres). Afeta apenas a inicialização. ", "TR_CHANNEL_TCP_MODE": "Modo", "TR_CHECK_BACK_ID": "ID de retorno", "TR_CHNG_INDICATED": "CHNG_INDICATED", "TR_CHNG_INDICATED_DESC": "Uma alteração nos dados é indicada pela fonte dos dados.", "TR_CLEAR": "Limpar", "TR_CLEAR_LOG": "Limpar log", "TR_CLEAR_MODEL": "Limpar <PERSON>o", "TR_CLEAR_MODEL_DESC": "Limpar modelo atual.", "TR_CLEAR_SEARCH": "<PERSON><PERSON> pes<PERSON>a", "TR_CLIENT": "Cliente", "TR_CLIENT_AE_INVOKE_ID": "ID de chamada do AE", "TR_CLIENT_AE_QUALIFIER": "Qualificador AE", "TR_CLIENT_AP_INVOKE_ID": "AP Invoke ID", "TR_CLIENT_APP_ID": "ID do aplicativo", "TR_CLIENT_IP_ADDRESS": "Endereço IP do cliente", "TR_CLIENT_PRESENTATION_ADDRESS": "Seletor de apresentação", "TR_CLIENT_SERVER_CONNECTION_SETTINGS": "Configurações de conexão cliente / servidor", "TR_CLIENT_SESSION_ADDRESS": "<PERSON><PERSON><PERSON> de <PERSON>", "TR_CLIENT_TRANSPORT_ADDRESS": "Seletor de transporte", "TR_CLOSE": "<PERSON><PERSON><PERSON>", "TR_CLOSE_PANEL": "<PERSON><PERSON><PERSON>", "TR_COILS": "Bobinas", "TR_COLLAPSE_CHILDREN": "<PERSON><PERSON><PERSON><PERSON> filhos", "TR_COMMAND_KIND": "Tipo de comando", "TR_COMMAND_SENT_TO_SERVER": "Comando enviado ao servidor", "TR_COMMAND_SUCCESS": "Sucesso", "TR_CONFIG": "Configuração", "TR_CONFIG_VIEW_1": "Visualização de configuração nº 1", "TR_CONFIG_VIEW_2": "Visualização de configuração nº 2", "TR_CONFIG_VIEW_3": "Visualização de configuração nº 3", "TR_CONFIGURATION": "Configuração", "TR_CONFIGURATION_REVISION": "Revisão da configuração", "TR_CONFIGURATION_REVISION_DESC": "Especifica se a revisão da configuração está ativa.", "TR_CONFIRM_NEW_PASSWORD": "Confirmar nova senha", "TR_CONNECT_RECONNECT": "Conectar / Reconectar", "TR_CONNECT_TIMEOUT": "Tempo limite da conexão (ms)", "TR_CONTROL_BLOCK": "Bloco de controle", "TR_CONTROL_BLOCK_DESC": "Mostrar bloco de controle para o MDO", "TR_CONTROL_POINT": "Ponto de controle", "TR_CONTROL_POINT_DESC": "Definir o ponto de controle", "TR_CONTROL_POINT_LIST": "Pontos de controle", "TR_CONTROL_POINT_LIST_DESC": "Escolha um ponto de controle.", "TR_COPY_TO_CSV_FILE": "Copie a lista do arquivo CSV", "TR_COULD_NOT_SET_MDO_OPTIONS": "Erro: não foi possível definir as opções do MDO", "TR_CPU_ABBREVIATION": "CPU", "TR_CREATE_61400_ERROR": "Não há mais alarmes disponíveis", "TR_CREATE_61400MDO_ERROR": "Não foi possível adicionar o {{arg1}} MDO no servidor 61850: {{arg2}}. (Duplicado?)", "TR_CREATE_61850_CANT_DELETE": "Não é possível excluir o cliente {{arg1}} enquanto estiver conectado. Desconecte e tente novamente. ", "TR_CREATE_61850_CHANGE_DATASET_NAME_FAILED": "Falha ao alterar o nome do conjunto de dados. Erro: {{arg1}} ", "TR_CREATE_61850_CREATE_MDO_INVALID_TAG": "Não foi possível criar o {{arg1}} MDO. Nome de tag inválido?", "TR_CREATE_61850_DELETE": "MDO: '{{arg1}}' é mapeado para pontos escravos ou é usado em uma equação, não pode excluir", "TR_CREATE_61850_DISABLE_RPT_FAILED": "Falha ao desativar o RCB. Erro: {{arg1}}", "TR_CREATE_61850_FAILED_TO_REENABLE_RPT": "Falha ao reativar o bloco de controle. Erro: {{arg1}} ", "TR_CREATE_61850_LIST_POINTS_NOT_A_CONTROL": "Não é um bloco de controle válido", "TR_CREATE_61850_MDO": "O MDO deve ter um nome, não pode criar", "TR_CREATE_61850_MDO_EXISTS": "O MDO j<PERSON> definido, não pode criar", "TR_CREATE_61850_MDO_SET_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_CREATE_61850_NO_MORE_CLIENTS": "Falha ao ler o bloco de controle de relatório. Erro retornado: {{arg1}} ", "TR_CREATE_61850_SERVER_NO_ADD_MDO": "Não foi possível adicionar o {{arg1}} MDO no servidor 61850: {{arg2}}. (Duplicado?)", "TR_CREATE_61850_SET_DATASET_NAME_FAILED": "Falha ao definir o nome do conjunto de dados. Erro desconhecido.", "TR_CREATE_61850_SET_MDO_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_CREATE_61850_SET_MDO_PROPERTIES": "Falha ao definir propriedades do MDO (controle inválido)", "TR_CREATE_61850_SET_MDO_PROPERTIES_Control": "Falha ao definir propriedades do MDO no controle", "TR_CREATE_61850_SET_MDO_PROPERTIES_DATASET": "Falha ao definir propriedades do MDO no DataSetControl", "TR_CREATE_61850_SET_MDO_PROPERTIES_GOOSE": "Falha ao definir propriedades do MDO no controle GOOSE", "TR_CREATE_61850_SET_MDO_PROPERTIES_POINTSET": "Falha ao definir propriedades do MDO no controle PointSet", "TR_CREATE_61850_SET_MDO_PROPERTIES_REPORT": "Falha ao definir propriedades do MDO no controle de relatório", "TR_CREATE_C2V": "Criar C2V", "TR_CREATE_DATASET_FAILED_CONNECTED": "Não é possível criar um conjunto de dados enquanto desconectado. Por favor, conecte e tente novamente. ", "TR_CREATE_DATASET_FAILED_EMPTY": "Erro: o modelo está vazio", "TR_CREATE_DATASET_FAILED_ERROR": "CreateDataSet falhou com o erro {{arg1}}", "TR_CREATE_DATASET_FAILED_EXISTS": "O conjunto de dados {{arg1}} já existe. Tente excluir primeiro e depois criar novamente. ", "TR_CREATE_DTM_POINT": "C criado: \\ dtm_points.csv", "TR_CREATE_EQUATION_FAILED": "Não foi possível criar a equação {{arg1}} para {{arg2}}", "TR_CREATE_HTML_FILE": "Criou C:\\ test_harness_points.xml", "TR_CTRL_AT_DEVICE": "CTRL_AT_DEVICE", "TR_CTRL_AT_DEVICE_DESC": "Uma alteração nos dados é indicada devido à ação no dispositivo.", "TR_CTRL_BY_COMM": "CTRL_BY_COMM", "TR_CTRL_BY_COMM_DESC": "Uma alteração nos dados é indicada devido a uma solicitação através de comunicações.", "TR_CTRL_CONFIRM": "CTRL_CONFIRM", "TR_CTRL_CONFIRM_DESC": "Uma solicitação de controle foi confirmada por um dispositivo remoto, mas ainda não está completa.", "TR_CTRL_ERROR": "CTRL_ERROR", "TR_CTRL_ERROR_DESC": "Um dispositivo remoto respondeu para indicar um erro em uma operação de controle.", "TR_CTRL_PENDING": "CTRL_PENDING", "TR_CTRL_PENDING_DESC": "Uma solicitação de controle foi transmitida para um dispositivo remoto.", "TR_CURRENT_ENTRY_ID": "ID da entrada atual", "TR_CURRENT_ENTRY_ID_DESC": "<PERSON>ibir o <PERSON> da entrada atual.", "TR_CURRENT_INI_FILE": "Arquivo INI atual", "TR_CURRENT_PASSWORD": "<PERSON><PERSON> atual", "TR_CUSTOM_LOGS": "Logs personalizados", "TR_CUSTOM_LOGS_DESC": "Registrar mensagens de rastreamento para logs personalizados", "TR_DA_ITEM_PROPERTY_LIST": "Lista de propriedades do item DA", "TR_DA_ITEM_PROPERTY_LIST_DESC": "Lista de propriedades do item DA", "TR_DA_POINT_LIST": "Pontos de atributo de dados", "TR_DA_POINT_LIST_CHECKED": "Lista de pontos DA verificada", "TR_DA_POINT_LIST_DESC": "Especifica a lista de pontos do atributo de dados atual.", "TR_DA_QUALITY_DESC": "Defina a qualidade do atributo de dados", "TR_DA_QUALITY": "DA Quality", "TR_DA_SEARCH": "Digite um nome parcial para pesquisar (diferencia maiúsculas de minúsculas)", "TR_DA_SEARCH_DESC": "Digite um nome parcial para procurar (diferencia maiúsculas de minúsculas)", "TR_DA_TIME": "DA Time", "TR_DA_TIME_DESC": "Defina o tempo do atributo de dados", "TR_DA_VALUE": "Valor DA", "TR_DA_VALUE_DESC": "Defina o valor do atributo de dados", "TR_DASHBOARD": "<PERSON><PERSON>", "TR_DATA_ATTRIBUTE_SELECTION": "Seleção de atributo de dados", "TR_DATA_CHANGE": "Alteração de dados", "TR_DATA_CHANGE_DESC": "Especifica se a alteração de dados está ativa.", "TR_DATA_CHNG_MON": "Alteração de dados", "TR_DATA_FILE_PATH": "Caminho do arquivo de dados", "TR_DATA_POINTS_LIST": "Lista de pontos de dados", "TR_DATA_POINTS_LIST_DESC": "Mostrar a lista de pontos de dados para o domínio selecionado.", "TR_DATA_REFERENCE": "Referência de dados", "TR_DATA_REFERENCE_DESC": "Especifique a referência de dados.", "TR_DATA_SAVED": "Dados salvos", "TR_DATA_SET": "DataSet", "TR_DATA_SET_NAME": "Nome do conjunto de dados", "TR_DATA_TYPE": "Tipo de dados", "TR_DATA_UPDATE_CHANGE": "Alteração na atualização de dados", "TR_DATA_UPDATE_CHANGE_DESC": "Especifica se a alteração da atualização de dados está ativa.", "TR_DATABASE": "Banco de dados", "TR_DATASET_NAME": "Nome do conjunto de dados", "TR_DATASET_NAME_DESC": "Especifica se o nome do conjunto de dados está ativo.", "TR_DATATYPE_DELETE_IN_USE": "'{{arg1}}' 'não pode ser excluído (tente remover o mapeamento de seus pontos)", "TR_DATE_TIME": "Data / hora", "TR_DBAS_SECTOR_ADDRESS": "Endereço ASDU", "TR_DBAS_SECTOR_ADDRESS_DESC": "Endereço ASDU de cada setor", "TR_DEBUG": "Depuração", "TR_DELETE": "Excluir", "TR_DELETE_DATASET_FAILED": "A exclusão do conjunto de dados '{{arg1}}' falhou no servidor com erro desconhecido. Verifique os logs e verifique se o conjunto de dados não está em uso no servidor. ", "TR_DELETE_DATASET_FAILED2": "A exclusão do conjunto de dados '{{arg1}}' falhou no servidor com erro desconhecido. Verifique os logs e verifique se o conjunto de dados existe no servidor. ", "TR_DELETE_DATASET_FAILED3": "A exclusão do conjunto de dados '{{arg1}}' falhou com o erro {{arg2}}", "TR_DELETE_DATASET_NOT_FOUND": "Conjunto de dados {{arg1}} não encontrado.", "TR_DELETE_GOOSE": "Não é possível excluir o bloco de controle Goose '{{arg1}}', pois ele possui itens existentes: {{arg2}}", "TR_DELETE_MAPPING": "Excluir map<PERSON>", "TR_DESCRIPTION": "Descrição", "TR_DESTINATION_UDP_PORT": "Porta UDP de destino", "TR_DH_FILE_PATH": "Caminho do arquivo DH", "TR_DIAGNOSTICS_LOG_MASK": "Máscara de log de diagnóstico", "TR_DIAGNOSTICS_LOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para registrar dados de diagnóstico, como alteração no número de quadros transmitidos, no arquivo de log de eventos.  Se 0, nenhum dado de diagnóstico será registrado. ", "TR_DIAGNOSTICS_OPC_AELOG_MASK": "Diagnóstico OPC Alarme e máscara de evento", "TR_DIAGNOSTICS_OPC_AELOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para registrar dados de diagnóstico, como alteração no número de quadros transmitidos, através do alarme OPC e do Event Server. Se 0, nenhum dado de diagnóstico será relatado. ", "TR_DISABLE_SAVE_ON_EXIT_CHK": "<PERSON><PERSON><PERSON> ao <PERSON>", "TR_DISCRETE_INPUTS": "Entradas discretas", "TR_HORIZONTAL_DISPLAY": "Exibir horizontal", "TR_VERTICAL_DISPLAY": "Exibir vertical", "TR_DISPLAY_WARNING_ON_ROOT": "<PERSON><PERSON><PERSON>", "TR_DNPACTION_MASK0": "Máscara de ação da sessão DNP", "TR_DNPACTION_MASK0_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com a máscara de ação DNPActionPrd. \nDNP Definições: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no Manual. Este parâmetro é usado apenas para sessões principais usando o protocolo DNP3", "TR_DNPACTION_NOW": "A ação da sessão do DNP agora mascara", "TR_DNPACTION_NOW_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com a máscara de ação DNPActionPrd. \nDNP Definições: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no Manual. Este parâmetro é usado apenas para sessões principais usando o protocolo DNP3.", "TR_DNPACTION_PRD0": "Período de ação da sessão DNP (ms)", "TR_DNPACTION_PRD0_DESC": "Tempo entre as ações definidas no DNPActionMask. O período é desativado se definido como zero.Este parâmetro é usado apenas para sessões principais usando o protocolo DNP3. ", "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT": "Modo agressivo", "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT_DESC": "Ativar modo agressivo.", "TR_DNPAUTH_EXTRA_DIAGS": "Diagnóstico extra", "TR_DNPAUTH_EXTRA_DIAGS_DESC": "Saída de diagnóstico extra para o analisador de protocolo.", "TR_DNPAUTH_HMACALGORITHM": "Algoritmo HMAC", "TR_DNPAUTH_HMACALGORITHM_DESC": "Algoritmo HMAC para ser usado em desafios.", "TR_DNPAUTH_KEY_CHANGE_INTERVAL": "Intervalo de alteração de chave (ms)", "TR_DNPAUTH_KEY_CHANGE_INTERVAL_DESC": "Para mestre: intervalo de chave da sessão.  Quando o tempo desde a última alteração de chave atingir esse valor, as chaves da sessão serão atualizadas. Para sistemas que se comunicam com pouca frequência, isso pode ser definido como zero, usando apenas maxKeyChangeCount para determinar quando atualizar as chaves. \nPara Escravo: intervalo e contagem esperados de chaves da sessão. Quando esse tempo decorrido ou essa quantidade de mensagens de autenticação são enviadas ou recebidas, as chaves da sessão desse usuário serão invalidadas. O intervalo e a contagem devem ser 2 vezes o intervalo e a contagem de alteração da chave mestra. Para sistemas que se comunicam com pouca freqüência, DNPAuthKeyChangeInterval pode ser definido como zero, usando apenas DNPAuthMaxKeyChangeCount para determinar quando as chaves devem ser consideradas antigas e devem ser invalidadas. ", "TR_DNPAUTH_MAX_ERROR_COUNT": "Contagem máxima de erros (apenas para Sav2)", "TR_DNPAUTH_MAX_ERROR_COUNT_DESC": "Número de mensagens de erro a serem enviadas antes de desativar a transmissão da mensagem de erro", "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT": "Contagem máxima de alterações de chave", "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT_DESC": "ASDU de autenticação de sessão conta desde a última alteração de chave. Quando esse número de ASDUs de autenticação é transmitido ou recebido desde a última alteração de chave, as chaves de sessão são atualizadas.", "TR_DNPAUTH_OSNAME": "Nome da estação externa", "TR_DNPAUTH_OSNAME_DESC": "O nome da estação de saída desta sessão dnp. <PERSON><PERSON> deve ser configurado para corresponder ao mestre e à estação ", "TR_DNPAUTH_REPLY_TIMEOUT": "Tempo limite da resposta de autenticação (ms)", "TR_DNPAUTH_REPLY_TIMEOUT_DESC": "Como int esperar por qualquer resposta de autenticação.", "TR_DNPAUTH_SAV5ENABLE": "DNP Secure Authentication Version 5", "TR_DNPAUTH_SAV5ENABLE_DESC": "TRUE se for uma sessão do DNP Secure Authentication Versão 5.", "TR_DNPAUTH_USER_KEY": "Chave do usuário (deve ter 16, 24 ou 32 valores hexadecimais)", "TR_DNPAUTH_USER_KEY_CHANGE_METHOD": "Método de alteração de chave. (Espaço reservado no arquivo INI)", "TR_DNPAUTH_USER_KEY_CHANGE_METHOD_DESC": "Método de alteração de chave. (Espaço reservado no arquivo INI)", "TR_DNPAUTH_USER_KEY_DESC": "Chave do usuário (deve ter 16, 24 ou 32 valores hexadecimais).  Para cada chave, deve haver um número de usuário exclusivo DNPAuthUserNumber. ", "TR_DNPAUTH_USER_NAME": "Nome de usuário", "TR_DNPAUTH_USER_NAME_DESC": "O nome do usuário.", "TR_DNPAUTH_USER_NUMBER": "Número do usuário", "TR_DNPAUTH_USER_NUMBER_DESC": "Número do usuário: configuração para cada usuário.  A especificação diz que o número de usuário padrão é 1, fornecendo um número de usuário para o dispositivo ou 'qualquer' usuário, configure-o como primeiro usuário nesta matriz. Adicione outros números de usuário. Para cada número de usuário no arquivo ini, deve haver uma DNPAuthUserKey. ", "TR_DNPAUTO_ENABLE_UNSOL_CLASS1": "Classe 1", "TR_DNPAUTO_ENABLE_UNSOL_CLASS1_DESC": "Se o sistema de mensagens não solicitadas estiver definido, esse sinalizador indicará que a classe de eventos 1 deve ser ativada para relatórios não solicitados. Este parâmetro é usado apenas para sessões principais ", "TR_DNPAUTO_ENABLE_UNSOL_CLASS2": "Ativar automaticamente cancelamento de classe 2", "TR_DNPAUTO_ENABLE_UNSOL_CLASS2_DESC": "Se o sistema de mensagens não solicitadas estiver definido, esse sinalizador indicará que a classe de eventos 2 deve ser ativada para relatórios não solicitados. Este parâmetro é usado apenas para sessões principais ", "TR_DNPAUTO_ENABLE_UNSOL_CLASS3": "Ativar automaticamente cancelamento de classe 3", "TR_DNPAUTO_ENABLE_UNSOL_CLASS3_DESC": "Se o sistema de mensagens não solicitadas estiver definido, esse sinalizador indicará que a classe de eventos 3 deve estar ativada para relatórios não solicitados. Este parâmetro é usado apenas para sessões principais ", "TR_DNPCHANNEL_ACTION_MASK0": "Máscara de ação do canal DNP", "TR_DNPCHANNEL_ACTION_MASK0_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com o DNPChannelActionPrd. \nDefinições da máscara de ação DNP: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no manual. Este parâmetro é usado apenas para sessões principais usando o protocolo DNP3. ", "TR_DNPCHANNEL_ACTION_NOW": "Ação do canal DNP agora mascarar", "TR_DNPCHANNEL_ACTION_NOW_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com as definições de máscara de ação DNPActionPrd. \nDNP: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no manual. Este parâmetro é usado apenas para sessões principais usando o protocolo DNP3. ", "TR_DNPCHANNEL_ACTION_PRD0": "Período de ação do canal DNP (ms)", "TR_DNPCHANNEL_ACTION_PRD0_DESC": "Tempo entre as ações definidas no DNPChannelActionMask. O período é desativado se definido como zero.Este parâmetro é usado apenas para sessões principais usando o protocolo DNP3. ", "TR_DNPCHANNEL_RESPONSE_TIMEOUT": "Tempo limite de resposta do canal DNP (ms)", "TR_DNPCHANNEL_RESPONSE_TIMEOUT_DESC": "Para um mestre DNP, como esperar uma resposta a uma solicitação que foi realmente transmitida. Esse valor pode ser menor que o Tempo limite de resposta padrão da sessão para determinar rapidamente se um dispositivo específico não está respondendo, sem fazer com que as solicitações para outros dispositivos no mesmo canal também expirem. NOTA: Isso não é usado por um escravo DNP. ", "TR_DNPENABLE_SECURE_AUTHENTICATION": "Ativa a autenticação segura DNP", "TR_DNPENABLE_SECURE_AUTHENTICATION_DESC": "Ativa a autenticação segura DNP para esta sessão", "TR_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_INI_CSV_FILE": "Deseja salvar seus arquivos INI / CSV atuais", "TR_DOMAIN_DESTINATION": "Nó lógico para criar o conjunto de dados", "TR_DOMAIN_DESTINATION_DESC": "Escolha o nó do domínio no qual criar o conjunto de dados.", "TR_DOMAINS_LIST": "<PERSON><PERSON><PERSON>", "TR_DOMAINS_LIST_DESC": "Mostrar a lista de domínios para o modelo atual", "TR_DOWNLOAD_CURRENT_CSV_FILE": "Faça o download do arquivo CSV atual", "TR_DOWNLOAD_CURRENT_INI_FILE": "Baixe o arquivo INI atual", "TR_DOWNLOAD_FILE": "Baixar arquivo", "TR_DOWNLOAD_SELECTED_FILE": "Baixar arquivo selecionado", "TR_DS_CONDITIONS_DETECTED": "Incluir condições do DS detectadas", "TR_DS_CONDITIONS_DETECTED_DESC": "Incluir condições do DS detectadas", "TR_DS_CREATE_NEW": "Criar novo DS", "TR_DS_CREATE_NEW_DESC": "Criar novo DS.", "TR_DS_DELETE_DESC": "Excluir DS selecionado.", "TR_DS_LIST": "Lista de conjuntos de dados", "TR_DS_LIST_DESC": "Especifica a lista atual do conjunto de dados.", "TR_DS_MEMBER_LIST": "Lista de membros do conjunto de dados", "TR_DS_MEMBER_LIST_DESC": "Lista de membros do conjunto de dados.", "TR_DS_NAME": "Nome do conjunto de dados", "TR_DS_NAME_DESC": "Especifica o nome do conjunto de dados atual.", "TR_DS_SELECTED_DELETE": "Excluir DS selecionado", "TR_DSA": "DSA", "TR_DSA_PRIVATE_KEY_FILE": "Arquivo de chave privada do DSA", "TR_DSA_PRIVATE_KEY_PASS_PHRASE": "DSA Private PassPhrase", "TR_DSA_PUBLIC_CERT_FILE": "Arquivo de certificado público do DSA", "TR_DSLCT_CONFIRM": "DSLCT_CONFIRM", "TR_DSLCT_CONFIRM_DESC": "Uma operação de cancelamento foi confirmada por um dispositivo remoto.", "TR_DSLCT_PENDING": "DSLCT_PENDING", "TR_DSLCT_PENDING_DESC": "Uma operação de cancelamento foi transmitida a um dispositivo remoto para interromper uma operação de controle de 2 passagens entre a 1ª e a 2ª passagens.", "TR_DSN_LIST": "lista DSN", "TR_DSN_LIST_DESC": "lista DSN", "TR_DUAL_END_POINT_IP_PORT": "Porta IP de ponto final duplo", "TR_DUAL_REGISTER_TYPE": "Tipo de registro duplo", "TR_DUP_CHNG_MON": "Alteração na atualização de dados", "TR_EDIT": "<PERSON><PERSON>", "TR_EDIT_USER": "<PERSON><PERSON>", "TR_ELEMENT_INDEX_M103": "Índice do elemento (M103):", "TR_ELEMENT_INDEX_M103_DESC": "o elemento Index (M103)", "TR_EMAIL": "E-mail", "TR_ENABLE_EVENT_LOG_FILE": "Ativar log de sequência de eventos", "TR_ENABLE_EVENT_LOG_FILE_DESC": "Se verdadeiro, o log de sequência de eventos será ativado. Nota: ativar esse log pode prejudicar o desempenho do SDG. ", "TR_ENABLE_IEC_FULL_STACK_ADDRESSING": "Ativar endereçamento de pilha completa IEC", "TR_ENABLE_UNSOLICITED_EVENT_CLASS": "Ativar classe de evento não solicitada", "TR_ENABLE_UNSOLICITED_EVENT_CLASS_INFO": "Essas opções requerem que o bit 'habilitar automaticamente eventos não solicitados na inicialização remota ou dispositivo mestre 0x0100' seja definido na máscara do modo de solicitação automática", "TR_END_DATE": "Data de término", "TR_ENGINE_INI_FILE_NAME": "Nome do arquivo INI do mecanismo", "TR_ENTER_A_PARTIAL_OR_COMPLETE_NODE_NAME": "Insira uma juba de nó parcial ou completa", "TR_ENTER_FILTER": "Insira um filtro", "TR_ENTER_PRODUCT_KEY": "Insira a chave do produto", "TR_ENTRY_ID": "ID da entrada", "TR_ENTRY_ID_DESC": "Especifica se o ID da entrada está ativo.", "TR_EQUATION_SYNTAX": "Não foi possível modificar a equação {{arg1}} para {{arg2}}. Verifique se a sintaxe da equação está correta. ", "TR_EQUATION_ALLOC_MEMORY": "Não foi possível alocar memória para '{{arg1}}'", "TR_EQUATION_BLANK": "Equação: o nome da equação não pode ficar em branco", "TR_EQUATION_END_OF_COMMENT": "Fim do comentário não encontrado", "TR_EQUATION_FUNCTION_CONVERT_FAILED": "Não foi possível converter '{{arg1}}' em '{{arg2}}'", "TR_EQUATION_FUNCTION_FIVE_ARGS": "A função '{{arg1}}' 'pode ter apenas 5 argumentos", "TR_EQUATION_FUNCTION_FOUR_ARGS": "A função '{{arg1}}' 'pode ter apenas 4 argumentos", "TR_EQUATION_FUNCTION_FOUT_ARGS": "A função '{{arg1}}' deve ter 4 argumentos", "TR_EQUATION_FUNCTION_NO_ARGS": "A função '{{arg1}}' pode ter apenas um argumento", "TR_EQUATION_FUNCTION_ONE_ARGS": "A função '{{arg1}}' deve ter um argumento", "TR_EQUATION_FUNCTION_SIX_ARGS": "A função '{{arg1}}' pode ter apenas 6 argumentos", "TR_EQUATION_FUNCTION_THREE_ARGS": "A função '{{arg1}}' pode ter apenas 3 argumentos", "TR_EQUATION_FUNCTION_TO_MANY_ARGS": "Muitos argumentos para a função '{{arg1}}'", "TR_EQUATION_FUNCTION_TOO_MANY_ARGS": "Muitos argumentos para a função '{{arg1}}'", "TR_EQUATION_FUNCTION_TWO_ARG": "A função '{{arg1}}' deve ter pelo menos dois argumentos", "TR_EQUATION_FUNCTION_TWO_ARGS": "A função '{{arg1}}' pode ter apenas 2 argumentos", "TR_EQUATION_ILLEGAL_CHAR": "Caractere de controle ilegal dentro da string.", "TR_EQUATION_ILLEGAL_CHAR_IGNORED": "Caractere ilegal '{{arg1}}'; o caractere é ignorado", "TR_EQUATION_ILLEGAL_TAB": "TAB ilegal dentro da string.", "TR_EQUATION_MEMORY_ALLOCATION": "Não foi possível alocar memória para '{{arg1}}'", "TR_EQUATION_MISSING_QUOTE": "Citação final ausente antes do final da linha.", "TR_EQUATION_PRODUCES_BAD_TYPE": "A equação produz um tipo que não pode ser usado", "TR_EQUATION_TIME_SOURCE": "Origem do tempo da equação", "TR_EQUATION_TIME_SOURCE_DESC": "Especifica a origem da marcação de tempo para pontos de dados que são gerados como resultado de uma equação. Os valores possíveis são Atualização ou Relatado, em que Atualização significa a hora, relativa ao relógio do sistema SDG, no qual a equação foi calculada pela última vez. Reported especifica a hora relatada do evento mais recente que causou alterações no resultado da equação. O tempo relatado será relativo ao relógio do sistema do dispositivo escravo remoto, exceto na inicialização em que o relógio do sistema do SDG é usado até o primeiro evento com o tempo ser recebido. É importante observar que a pesquisa de dados estáticos ou eventos recebidos que não especificam um horário relatado podem fazer com que o valor de um ponto de dados específico seja alterado sem que o horário do evento seja modificado. Com base nas taxas de pesquisa do sistema e em outros parâmetros, isso pode resultar em tempos descontínuos sendo relatados, especialmente em equações que possuem entradas de vários dispositivos escravos. ", "TR_EQUATION_UNEXPCTED_TOKEN": "Token inesperado '{{arg1}}'", "TR_EQUATION_UNEXPECTED_EQUATION": "Fim inesperado da equação", "TR_EQUATION_UNKONWN_IDENTIFIER": "Identificador desconhecido '{{arg1}}'", "TR_EQUATION_WRONG_TYPES": "A correspondência incorreta de tipo entre '{{arg1}}' e '{{arg2}}' ('{{arg3}}' espera que um tipo {{arg4}} e {{arg5}} seja de { {arg6}}, tente tornar os tipos iguais usando um elenco ou um MDO diferente) ", "TR_EQUATIONS_LOG_MASK": "Máscara de log de equações", "TR_EQUATIONS_LOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) uma razão para registrar os resultados das equações. Se 0, nenhum resultado da equação será registrado. ", "TR_EQUATIONS_OPC_AELOG_MASK": "Equações OPC Alarm and Event Mask", "TR_EQUATIONS_OPC_AELOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para registrar os resultados das equações através do alarme OPC e do Servidor de Eventos. Se 0, nenhum resultado da equação será relatado. ", "TR_ERR": "Erro: {{arg1}}", "TR_ERROR": "Erro", "TR_ERROR_": "Erro: {{error}}", "TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED": "Erro: a lista de pontos DA 61850 não foi alterada", "TR_ERROR_61850_FC_DATASET_NOT_CHANGED": "Erro: a restrição funcional 61850 não foi alterada", "TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE": "Erro: 61850 membro do conjunto de dados GOOSE indisponível", "TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED": "Erro: 61850 o conjunto de dados GOOSE não foi alterado", "TR_ERROR_61850_IED_LIST_UNAVAILABLE": "Erro: 61850 lista de IED indisponível", "TR_ERROR_61850_MODEL_NOT_SAVED": "Erro: o modelo 61850 não foi salvo", "TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE": "Erro: 61850 relatório membro do conjunto de dados indisponível", "TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED": "Erro: 61850 o conjunto de dados do relatório não foi alterado", "TR_ERROR_61850_SERVER_NOT_DISCONNECTED_CONNECTED": "Erro: servidor 61850 não desconectado / conectado", "TR_ERROR_61850_SERVER_NOT_RESTARTED": "Erro: o servidor 61850 não foi reiniciado", "TR_ERROR_61850_SERVER_READ": "Erro: falha na leitura do servidor 61850", "TR_ERROR_ACTIVATE_OPC_ITEM_FAILED": "Erro: a ativação do item OPC falhou", "TR_ERROR_ARG1": "Erro: {{arg1}}", "TR_ERROR_AUTO_CREATE_TAGS": "Erro na criação automática de tags", "TR_ERROR_CAN_T_READ_AUDIT_DATABASE": "Erro: não é possível ler o banco de dados de auditoria", "TR_ERROR_CAN_T_READ_USERS_DATABASE": "Erro: Não é possível ler o banco de dados dos usuários.", "TR_ERROR_CAN_T_SAVE_LICENSE": "Erro: licença não salva", "TR_ERROR_CAN_T_SAVE_LICENSE_": "O erro não pode salvar a licença", "TR_ERROR_CAN_T_SAVE_LICENSE_PRODUCT_KEY_EXHAUSTED": "Erro de licença: Chave do produto esgotada", "TR_ERROR_CAN_T_SAVE_LICENSE_UNSPECIFIED_ERROR": "Erro de licença: erro não especificado", "TR_ERROR_COMMAND_FAILED": "Erro: comando falhou.", "TR_ERROR_DATA_NOT_SAVED": "Erro: dados não salvos", "TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK": "Erro: Não é possível ativar ou desativar o bloco de controle do relatório 61850", "TR_ERROR_FILE_NOT_DOWNLOADED": "Erro: o arquivo não foi baixado", "TR_ERROR_FILE_NOT_SAVED": "Erro: o arquivo não foi salvo.", "TR_ERROR_GOOSE_MONITOR_STREAMS_UNAVAILABLE": "Erro:", "TR_ERROR_IN_CSV_FILE": "Erro: o arquivo CSV não pode ser salvo.", "TR_ERROR_IN_INI_FILE": "Erro: o arquivo INI não pode ser salvo.", "TR_ERROR_IN_NEW_INI_FILE": "Erro: o arquivo INI deve ter uma extensão de arquivo .ini", "TR_ERROR_INVALID_EQUATION": "Erro: equação inválida", "TR_ERROR_INVALID_FILENAME": "Erro: nome de arquivo inválido", "TR_ERROR_NO_OPC_ITEM_SELECTED": "Erro: nenhum item OPC selecionado", "TR_ERROR_OBJECT_NOT_DELETED": "Erro: objeto não excluído.", "TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE": "Erro: informações da tabela ODBC indisponíveis", "TR_ERROR_OPC_ADD_ITEM_FAILED": "Erro: não é possível adicionar o item OPC", "TR_ERROR_OPC_LOAD_ITEM_FAILED": "Erro: não é possível carregar o item OPC", "TR_ERROR_OPC_SERVER_NOT_DISCONNECTED_CONNECTED": "Erro: o OPC Server não pode ser conectado ou desconectado", "TR_ERROR_OPCAE_SERVER_NOT_DISCONNECTED_CONNECTED": "Erro: o servidor OPC AE não pode ser conectado ou desconectado", "TR_ERROR_OPCUA_SERVER_NOT_DISCONNECTED_CONNECTED": "Erro: o servidor do OPC UA não pode ser conectado ou desconectado", "TR_ERROR_PERFORM_WRITE_ACTION": "Erro: falha na pré-formação da ação de gravação", "TR_ERROR_READ_OPC_ITEM_FAILED": "Erro: não é possível ler o item OPC", "TR_ERROR_REFRESH_OPC_PROPERTIES_FAILED": "Erro: <PERSON><PERSON> é possível atualizar as propriedades do item OPC", "TR_ERROR_RESET_61850_RETRY_CONNECT_COUNT": "Erro: não é possível redefinir a contagem de novas tentativas para 61850", "TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM": "Erro: não é possível cancelar a inscrição ou a inscrição no GOOSE Stream", "TR_ERROR_TASE2_OPERATE_CONTROL": "Erro: o controle de operação do ICCP falhou", "TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE": "Erro: o membro do conjunto de dados do relatório ICCP não está disponível", "TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED": "Erro: o conjunto de dados do relatório ICCP não foi alterado", "TR_ERROR_TASE2_SERVER_NOT_DISCONNECTED_CONNECTED": "Erro: servidor ICCP não desconectado / conectado", "TR_ERROR_TASE2_SERVER_NOT_RESTARTED": "Erro: servidor ICCP não reiniciado", "TR_ERROR_TASE2_SERVER_NOT_SAVED": "Erro: o servidor ICCP não pode ser salvo", "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER": "Erro <br /> A comunicação com o Monitor foi perdida. <br /> Atualize seu navegador.", "TR_ERROR_THE_GATEWAY_IS_NOT_RUNNING": "Erro: o gateway não está sendo executado", "TR_ERROR_USER_NOT_DELETED": "Erro: <PERSON><PERSON><PERSON><PERSON> não excluído.", "TR_ERROR_VERIFY_61850_DATASET": "Erro: não é possível verificar o conjunto de dados 61850", "TR_ERRORS_LOG_MASK": "Máscara de log de erros", "TR_ERRORS_LOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para registrar erros, como alterações no número de falhas da soma de verificação da camada de link ou no status on-line / off-line da sessão de link, no arquivo de log de eventos .  Se 0, nada será registrado. ", "TR_ERRORS_OPC_AELOG_MASK": "Erros de alarme e evento OPC", "TR_ERRORS_OPC_AELOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para relatar erros, como alterações no número de falhas na soma de verificação da camada de link ou no status on-line / off-line da sessão de link, através do alarme OPC e Servidor de Eventos. Se 0, nenhum erro será relatado. ", "TR_EU_Type": "Tipo UE", "TR_EU_Type_DESC": "Definir o tipo da UE", "TR_EVENT_CODE_DETECTED": "Incluir código do evento detectado", "TR_EVENT_CODE_DETECTED_DESC": "Incluir código do evento detectado", "TR_EVENT_LOG_FILE_NAME": "Nome do arquivo de log de sequência de eventos", "TR_EVENT_LOG_FILE_NAME_DESC": "Nome e caminho do arquivo de log de sequência de eventos.  Consulte o manual para obter uma descrição das palavras-chave disponíveis da propriedade% xxx. ", "TR_EVENT_LOG_RECORD_FORMAT": "Formato de registro de log de sequência de eventos", "TR_EVENT_LOG_RECORD_FORMAT_DESC": "Formato de registro do log de sequência de eventos.  Consulte o manual para obter uma descrição das palavras-chave disponíveis da propriedade% xxx. ", "TR_EVENT_NAME": "Nome do evento", "TR_EVENT_NAME_DESC": "Defina o nome do evento", "TR_EVENT_SPACE": "Espaço para Eventos", "TR_EVENT_SPACE_DESC": "Definir o espaço para eventos", "TR_EXCEPTION_STATUS": "Status de exceção", "TR_EXECUTE_SQL": "Executar / testar consulta SQL", "TR_EXECUTE_SQL_DESC": "Executar / testar consulta SQL", "TR_EXPAND_CHILDREN": "<PERSON><PERSON><PERSON> fi<PERSON>", "TR_EXPIRES": "Expira", "TR_EXPRESSION": "Expressão:", "TR_EXPRESSION_DESC": "a expressão da tag", "TR_EXTRA": "Extra", "TR_FAILED_TO_ADD_MDO_OPC_SERVER": "Não foi possível adicionar o {{arg1}} MDO no servidor OPC: {{arg2}}. (Duplicado?)", "TR_FAILED_TO_APPLY_ALIAS_TO_61850_TAG": "Falha ao aplicar o alias '{{arg1}}' à tag 61850 (duplicado?)", "TR_FAILED_TO_APPLY_ALIAS_TO_ICCP_TAG": "Falha ao aplicar o alias '{{arg1}}' à tag ICCP (duplicado?)", "TR_FAILED_TO_APPLY_ALIAS_TO_OPC_TAG": "Falha ao aplicar o alias '{{arg1}}' à tag OPC (duplicado?)", "TR_FC_CF": "CF - Configuração", "TR_FC_CF_DESC": "Habilite a restrição funcional CF - Configuração", "TR_FC_SP": "SP - Pontos de ajuste", "TR_FC_SP_DESC": "Habilite a restrição funcional SP - Set Points", "TR_FC_SV": "SV - Substituição", "TR_FC_SV_DESC": "Habilite a restrição funcional SV - Substituição", "TR_FILE": "Arquivo", "TR_FILE_ALREADY_EXISTS_OVERWRITE_IT": "O arquivo selecionado já existe na pasta de destino, você deseja substituí-lo?", "TR_FILE_DOWNLOADED": "Arqui<PERSON> baixado", "TR_FILE_SAVED": "Arquivo salvo.", "TR_FILTER": "Filtro", "TR_FUNCTION_M103": "Função (M103):", "TR_FUNCTION_M103_DESC": "a função (M103) da tag", "TR_FUNCTIONAL_CONSTRAINT": "Restrição funcional", "TR_FUNCTIONAL_CONSTRAINT_DESC": "Especifica a restrição funcional", "TR_GATEWAY": "Gateway", "TR_GATEWAY_ABBREVIATION": "G:", "TR_GATEWAY_API_HOST_AND_HTTP_PORT": "Monitorar o host da API e a porta HTTP", "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION": "Tempo limite de expiração da autenticação do usuário (segundos)", "TR_GATEWAY_CONTROL_LOG_MASK": "Máscara de log de controle", "TR_GATEWAY_CONTROL_LOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para registrar dados de controle, como alterações em pollEnabled ou GeneralInterrogationPeriod, no arquivo de log de eventos.  Se 0, nada será registrado. ", "TR_GATEWAY_CONTROL_OPC_AELOG_MASK": "Controle de alarme de OPC e máscara de evento", "TR_GATEWAY_CONTROL_OPC_AELOG_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo para registrar dados de controle, como alterações em pollEnabled ou GeneralInterrogationPeriod, através do alarme OPC e do Event Server. Se 0, nada será relatado. ", "TR_GATEWAY_ENABLE_AUDIT": "Ativar log de auditoria", "TR_GATEWAY_ENABLE_AUTHENTICATION": "Ativar autenticação do usuário", "TR_GATEWAY_ENABLE_TRACE": "Gateway ativar rastreamento", "TR_GATEWAY_EXE_NAME": "Nome do Gateway Exe", "TR_GATEWAY_HOST_AND_HTTP_PORT": "Host do gateway e porta HTTP (s)", "TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE": "Itens por página para listas de mapeamentos e tags na interface da web (0 para desativar)", "TR_GATEWAY_INI_FILE_PATH": "Caminho do arquivo INI do gateway", "TR_GATEWAY_IS_RUNNING": "O gateway está em execução.", "TR_GATEWAY_IS_STARTING": "O gateway está iniciando", "TR_GATEWAY_IS_STOPPED": "O gateway está parado.", "TR_GATEWAY_MANAGEMENT": "Gerenciamento de gateway", "TR_GATEWAY_TIME_ZONE_DB_PATH": "Caminho do banco de dados do fuso horário do gateway", "TR_GATEWAY_WEB_DIRECTORY": "Diretório da Web do gateway", "TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT": "Host da API do mecanismo e porta HTTP", "TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE": "Tamanho do bloco de atualização do Websocket do gateway", "TR_GATEWAY_WEBSOCKET_UPDATE_RATE": "Taxa de atualização do soquete da Web do gateway (segundos)", "TR_GCB_LIST": "Lista de relatórios", "TR_GCB_LIST_DESC": "Especifica a lista de relatórios atual.", "TR_GCB_NAME": "Nome do relatório", "TR_GCB_NAME_DESC": "Nome do relatório atual.", "TR_GENERAL_INTERROGATION": "Interrogatório geral", "TR_GENERAL_INTERROGATION_DESC": "Especifica se a interrogação geral está ativa.", "TR_GLOBAL_CREATE_TAG_AUTOMATIC": "Global de criação automática de tags", "TR_GLOBAL_CREATE_TAG_AUTOMATIC_DESC": "se true, as tags (e espaço de armazenamento) serão criadas automaticamente na recepção de novos pontos de dados nas mensagens de resposta de dispositivos remotos.", "TR_GOOD": "BOM", "TR_GOOD_DESC": "nenhum bit está definido", "TR_GOOSE_ADAPTER": "Adaptador GOOSE", "TR_GOOSE_ADAPTOR": "Adaptador GOOSE", "TR_GOOSE_MONTIOR_ADAPTOR": "Monitor de ganso '{{arg1}}': não foi possível encontrar o adaptador ({{arg2}}, {{arg3}}). O Goose Monitor não funcionará corretamente até que isso seja corrigido. ", "TR_GOOSE_MONTIOR_LOAD_SCL": "Goose Monitor '{{arg1}}': Não foi possível carregar o arquivo SCL: {{arg2}}. Verifique se o arquivo e o caminho estão corretos. ", "TR_GOOSE_MONTIOR_NO_MORE": "Não há mais GooseMonitors disponíveis", "TR_GOOSE_NAME": "Nome do GOOSE", "TR_GOOSE_STREAM_MANAGEMENT_ADAPTOR": "Gerenciamento do Goose Stream Adapter", "TR_GOOSEMONITOR_ADAPTER_DEVICE": "Caminho do sistema do dispositivo adaptador GOOSE", "TR_GOOSEMONITOR_ADAPTER_DEVICE_DESC": "Especifica um caminho do sistema do dispositivo adaptador GOOSE", "TR_GOOSEMONITOR_NAME": "GOOSE Monitor device name", "TR_GOOSEMONITOR_NAME_DESC": "Especifica o nome do dispositivo do GOOSE Monitor", "TR_GOOSEMONITOR_SCLFILE": "Arquivo SCOSE / ICD do GOOSE Monitor", "TR_GOOSEMONITOR_SCLFILE_DESC": "Especifica o arquivo SCL / ICD do GOOSE Monitor", "TR_GOOSEMONITOR_STREAM": "Fluxo GOOSE monitorado", "TR_GOOSEMONITOR_STREAM_DESC": "Especifica um fluxo GOOSE monitorado para um dispositivo GOOSE Monitor", "TR_GOOSEMONITOR_STREAM_THRESHOLD": "Tempo limite inválido (segundos)", "TR_GOOSEMONITOR_STREAM_THRESHOLD_DESC": "Especifica um tempo limite limite inválido monitorado do fluxo GOOSE em segundos", "TR_GTWDEFS_UPDTRSN_CHNG_INDICATED_DESC": "A fonte de dados indica que esta atualização é uma alteração.", "TR_GTWDEFS_UPDTRSN_CTRL_AT_DEVICE_DESC": "Os dados foram alterados como resultado de uma operação de controle executada localmente no dispositivo.", "TR_GTWDEFS_UPDTRSN_CTRL_BY_COMM_DESC": "Os dados foram alterados como resultado de uma operação de controle por meio de comunicações.", "TR_GTWDEFS_UPDTRSN_CTRL_CONFIRM_DESC": "Usado para pontos de controle: Uma operação de controle foi confirmada (por exemplo, o dispositivo remoto enviou confirmação de ter recebido uma operação de controle).  No entanto, embora confirmada, a operação de controle ainda não pode ser concluída. <PERSON><PERSON><PERSON> terminar, GTWDEFS_UPDTRSN_CTRL_BY_COMM será usado. ", "TR_GTWDEFS_UPDTRSN_CTRL_ERROR_DESC": "Usado para pontos de controle: ocorreu um erro com a operação de controle.", "TR_GTWDEFS_UPDTRSN_CTRL_PENDING_DESC": "Usado para pontos de controle: Uma operação de controle foi iniciada (por exemplo, enviada para um dispositivo remoto).  <PERSON><PERSON> pode ocorrer na segunda passagem de uma operação de 2 passagens ou na única passagem de uma operação de 1 passagem. ", "TR_GTWDEFS_UPDTRSN_DSLCT_CONFIRM_DESC": "Usado para pontos de controle: Uma operação de desmarcação (cancelamento de uma operação de seleção de 1ª passagem) foi confirmada (por exemplo, o dispositivo remoto enviou a confirmação de ter desmarcado a operação de controle).", "TR_GTWDEFS_UPDTRSN_DSLCT_PENDING_DESC": "Usado para pontos de controle: A operação de desmarcação (cancelamento de uma operação de seleção de 1ª passagem) foi iniciada (por exemplo, enviada para um dispositivo remoto).", "TR_GTWDEFS_UPDTRSN_NONE_DESC": "Pode ser usado como uma máscara de log / * para desativar o log", "TR_GTWDEFS_UPDTRSN_REFRESH_DESC": "Os dados estão sendo atualizados pela fonte de dados (sem solicitação); nenhum evento de alteração é necessariamente indicado.", "TR_GTWDEFS_UPDTRSN_REQUESTED_DESC": "Os dados foram solicitados; nenhum evento de alteração é necessariamente indicado.", "TR_GTWDEFS_UPDTRSN_SLCT_CONFIRM_DESC": "Usado para pontos de controle: Uma primeira passagem em uma operação de controle de duas passagens foi confirmada (por exemplo, o dispositivo remoto enviou a confirmação de ter recebido a operação de controle de primeira passagem).", "TR_GTWDEFS_UPDTRSN_SLCT_PENDING_DESC": "Usado para pontos de controle: A primeira passagem em uma operação de controle de duas passagens foi iniciada (por exemplo, enviada para um dispositivo remoto)", "TR_GTWDEFS_UPDTRSN_TEST_MODE_DESC": "Usado por alguns protocolos para indicar que o ponto ou dispositivo está operando em modo de teste.", "TR_GTWDEFS_UPDTRSN_UNKNOWN_DESC": "Os dados estão sendo atualizados para um desconhecido", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_ALL": "ALL", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_DATA": "<PERSON><PERSON>", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_HEALTH": "<PERSON><PERSON><PERSON>", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_PERFORMANCE": "Perf", "TR_GTWTYPES_TAG_PURPOSE_ALL": "Todos", "TR_GTWTYPES_TAG_PURPOSE_MASK_DATA": "Filtro de dados", "TR_GTWTYPES_TAG_PURPOSE_MASK_HEALTH": "Filtro de integridade", "TR_GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE": "Filtro de desempenho", "TR_HEALTH_DESC": "<PERSON><PERSON><PERSON>", "TR_HEALTH_VIEW": "Visão de integridade", "TR_HELP": "<PERSON><PERSON><PERSON>", "TR_HI": "Oi", "TR_HIGH_ORDER_INDEX": "Índice de registro de pedidos altos", "TR_HIGH_ORDER_INDEX_DESC": "Especifique o índice de registro de pedidos altos", "TR_HOLDING_REGISTERS": "Mantendo registros", "TR_I14AUTH_ENABLE": "Ativa a autenticação segura", "TR_I14AUTH_ENABLE_DESC": "Ativa a autenticação segura para este setor", "TR_I14AUTH_EXTRA_DIAGS": "Diagnóstico extra", "TR_I14AUTH_EXTRA_DIAGS_DESC": "Saída de diagnóstico extra para o analisador de protocolo.", "TR_I14AUTH_HMACALGORITHM": "Algoritmo HMAC", "TR_I14AUTH_HMACALGORITHM_DESC": "Algoritmo HMAC para ser usado em desafios.", "TR_I14AUTH_KEY_CHANGE_INTERVAL": "Intervalo de teclas", "TR_I14AUTH_KEY_CHANGE_INTERVAL_DESC": "Para mestre: intervalo de chave da sessão. Quando o tempo desde a última alteração de chave atingir esse valor, as chaves da sessão serão atualizadas. Para sistemas que se comunicam com pouca frequência, isso pode ser definido como zero, usando apenas maxKeyChangeCount para determinar quando atualizar as chaves. \nPara Escravo: intervalo e contagem esperados de chaves da sessão. Quando esse tempo decorrido ou essa quantidade de mensagens de autenticação são enviadas ou recebidas, as chaves da sessão desse usuário serão invalidadas. O intervalo e a contagem devem ser 2 vezes o intervalo e a contagem de alteração da chave mestra. Para sistemas que se comunicam com pouca frequência, I14AuthKeyChangeInterval pode ser definido como zero, usando apenas o I14AuthMaxKeyChangeCount para determinar quando as chaves devem ser consideradas antigas e devem ser invalidadas.", "TR_I14AUTH_MAX_KEY_CHANGE_COUNT": "Contagem de alterações de chave", "TR_I14AUTH_MAX_KEY_CHANGE_COUNT_DESC": "ASDU de autenticação de sessão conta desde a última alteração de chave. Quando esse número de ASDUs de autenticação é transmitido ou recebido desde a última alteração de chave, as chaves de sessão são atualizadas.", "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH": "Comprimento dos dados aleatórios do desafio", "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH_DESC": "Comprimento dos dados aleatórios do desafio para enviar na solicitação de desafio.", "TR_I14AUTH_REPLY_TIMEOUT": "Como aguardar qualquer resposta de autenticação (ms)", "TR_I14AUTH_REPLY_TIMEOUT_DESC": "Como int esperar por qualquer resposta de autenticação.", "TR_I14AUTH_SECURITY_STATS_IOA": "Número do ponto base para as estatísticas de segurança", "TR_I14AUTH_SECURITY_STATS_IOA_DESC": "Endereço do objeto de informações básicas (IOA) para as estatísticas de segurança.", "TR_I14AUTH_USER_KEY": "Chave do usuário (deve ter 32 valores hexadecimais)", "TR_I14AUTH_USER_KEY_DESC": "Chave do usuário (deve ter 32 valores hexadecimais).  Para cada chave, deve haver um número de usuário exclusivo I14AuthUserNumber. ", "TR_I14AUTH_USER_NAME": "Nome do usuário", "TR_I14AUTH_USER_NAME_DESC": "O nome do usuário.", "TR_I14AUTH_USER_NUMBER": "Número do usuário", "TR_I14AUTH_USER_NUMBER_DESC": "Número do usuário: configuração para cada usuário.  A especificação diz que o número de usuário padrão é 1, fornecendo um número de usuário para o dispositivo ou 'qualquer' usuário, configure-o como primeiro usuário nesta matriz. Adicione outros números de usuário. Para cada número de usuário no arquivo ini, deve haver uma I14AuthUserKey. ", "TR_I61400ALARMS_NAME": "Nome do nó de alarme", "TR_I61400ALARMS_NAME_DESC": "Especifica o nome do nó SDG Alarms", "TR_I61400EVENT_ALARMS_ARRAY_NAME": "Nome da matriz de alarmes de eventos", "TR_I61400EVENT_ALARMS_ARRAY_NAME_DESC": "Especifica o nome da matriz de alarmes de eventos IEC 61400-25 (WALM)", "TR_I61400STATUS_ALARMS_ARRAY_NAME": "Nome da matriz de alarmes de status", "TR_I61400STATUS_ALARMS_ARRAY_NAME_DESC": "Especifica o nome da matriz de alarmes de status IEC 61400-25 (WALM)", "TR_I61850AUTH_MECHANISM": "Mecanismo de autorização ('Nenhum' ou 'Senha' ou 'Certificado')", "TR_I61850AUTH_MECHANISM_DESC": "Mecanismo de autorização IEC 61850 ('<PERSON><PERSON><PERSON>' ou '<PERSON><PERSON>' ou 'Certificado')", "TR_I61850AUTH_PASSWORD": "<PERSON><PERSON>", "TR_I61850AUTH_PASSWORD_DESC": "Senha de autorização IEC 61850", "TR_I61850CLIENT_AEINVOKE_ID": "ID de chamada do AE", "TR_I61850CLIENT_AEINVOKE_ID_DESC": "Especifica o ID de chamada do AE para este cliente IEC 61850", "TR_I61850CLIENT_AEQUALIFIER": "Qualificador AE", "TR_I61850CLIENT_AEQUALIFIER_DESC": "Especifica o qualificador AE para este cliente IEC 61850", "TR_I61850CLIENT_APINVOKE_ID": "AP Invoke ID", "TR_I61850CLIENT_APINVOKE_ID_DESC": "Especifica o ID de chamada do AP para este cliente IEC 61850", "TR_I61850CLIENT_APP_ID": "ID do aplicativo", "TR_I61850CLIENT_APP_ID_DESC": "Especifica o ID do aplicativo deste cliente IEC 61850", "TR_I61850CLIENT_CONNECT_TIMEOUT": "Tempo limite de conexão do MMS (ms)", "TR_I61850CLIENT_CONNECT_TIMEOUT_DESC": "Especifica o tempo limite de conexão do MMS para o IEC 61850 Client.  Depois de iniciar uma tentativa de conexão, é assim que se espera o sucesso.  O comprimento deste parâmetro dependerá da sua topologia de rede.  Este valor também é usado para especificar o tempo limite para as mensagens de solicitação 61850 ", "TR_I61850CLIENT_IPADDRESS": "Endereço IP do cliente", "TR_I61850CLIENT_IPADDRESS_DESC": "Especifica o endereço IP deste cliente IEC 61850.  <PERSON><PERSON> pode ser útil para selecionar um adaptador de rede diferente ", "TR_I61850CLIENT_MMSCOMMON_NAME": "Nome comum do MMS", "TR_I61850CLIENT_MMSCOMMON_NAME_DESC": "Especifica o nome comum do MMS", "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE": "Arquivo de Chave Privada", "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE_DESC": "Especifica o arquivo de chave privada do MMS", "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE": "PassPhrase de chave privada", "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE_DESC": "Especifica a frase secreta da chave privada do MMS", "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE": "Arquivo de certificado público", "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE_DESC": "Especifica o arquivo de certificado público do MMS", "TR_I61850CLIENT_NAME": "Nome do cliente", "TR_I61850CLIENT_NAME_DESC": "IEC 61850 Nome do cliente", "TR_I61850CLIENT_PRESENTATION_ADDRESS": "Endereço da apresentação", "TR_I61850CLIENT_PRESENTATION_ADDRESS_DESC": "Especifica o endereço de apresentação deste cliente IEC 61850", "TR_I61850CLIENT_RECONNECT_RETRY_COUNT": "Reconecte a contagem de novas tentativas", "TR_I61850CLIENT_RECONNECT_RETRY_COUNT_DESC": "Especifica a contagem de novas tentativas de reconexão para o cliente IEC 61850 (0 = tentativa de reconexão para sempre) Uma conexão bem-sucedida fará com que o contador de limite interno seja redefinido para 0, resultando em tentativas de conexão continuada com o servidor IEC 61850 . ", "TR_I61850CLIENT_RECONNECT_TIME": "Reconecte o tempo limite (ms)", "TR_I61850CLIENT_RECONNECT_TIME_DESC": "Especifica o tempo limite de reconexão para o IEC 61850 Client (0 = sem reconexão), deve ser maior que I61850ClientConnectTimeout", "TR_I61850CLIENT_SESSION_ADDRESS": "Endereço da sessão", "TR_I61850CLIENT_SESSION_ADDRESS_DESC": "Especifica o endereço da sessão deste cliente IEC 61850", "TR_I61850CLIENT_TLSMAX_PDUS": "Máximo de PDUs antes de forçar a renegociação de cifra", "TR_I61850CLIENT_TLSMAX_PDUS_DESC": "Especifica a PDU TLS Max antes de forçar a renegociação da cifra", "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME": "Tempo máximo de espera de renegociação (ms)", "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME_DESC": "Especifica o tempo máximo de renegociação do TLS", "TR_I61850CLIENT_TLSRENEGOTIATION": "Renegocia<PERSON> (s)", "TR_I61850CLIENT_TLSRENEGOTIATION_DESC": "Tempo máximo (segundos) antes de forçar a renegociação da cifra", "TR_I61850CLIENT_TRANSPORT_ADDRESS": "Endereço de transporte", "TR_I61850CLIENT_TRANSPORT_ADDRESS_DESC": "Especifica o endereço de transporte deste cliente IEC 61850", "TR_I61850CLIENT_USE_SISCO_COMPATABILITY": "Usar compatibilidade ED1", "TR_I61850CLIENT_USE_SISCO_COMPATABILITY_DESC": "Especifica se as configurações de segurança usam compatibilidade ED1 ou Compatibilidade Sisco", "TR_I61850GOOSE_ADAPTER_NAME": "Nome do adaptador Goose", "TR_I61850GOOSE_ADAPTER_NAME_DESC": "Nome do adaptador Goose selecionado.", "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED": "Carregar modelo do arquivo", "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED_DESC": "O cliente IEC 61850 deve carregar o modelo do arquivo em I61850SCLFileName []", "TR_I61850POLLED_DATA_SET_NAME": "Nome do conjunto de dados pesquisados", "TR_I61850POLLED_DATA_SET_NAME_DESC": "Especifica o nome do conjunto de dados pesquisados", "TR_I61850POLLED_DATA_SET_PERIOD": "Período do conjunto de dados pesquisados (ms)", "TR_I61850POLLED_DATA_SET_PERIOD_DESC": "Especifica o período para ler o conjunto de dados pesquisados. Um valor zero significa desativar, ou seja, nenhuma pesquisa ocorrerá. ", "TR_I61850POLLED_POINT_SET_NAME": "Nome do conjunto de pontos pesquisados", "TR_I61850POLLED_POINT_SET_NAME_DESC": "Especifica o nome do conjunto de pontos pesquisados.", "TR_I61850POLLED_POINT_SET_PERIOD": "Per<PERSON><PERSON> definido no ponto de pesquisa (ms)", "TR_I61850POLLED_POINT_SET_PERIOD_DESC": "Especifica o período para ler o conjunto de pontos pesquisados. Um valor zero significa desativar, ou seja, nenhuma pesquisa ocorrerá. ", "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT": "Limpe o RCB antes de ativar ao reconectar", "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Limpe o RCB antes de ativar ao reconectar", "TR_I61850RCBPURGE_BEFORE1ST_ENABLE": "Eliminar RCB antes da primeira ativação", "TR_I61850RCBPURGE_BEFORE1ST_ENABLE_DESC": "Eliminar RCB antes da primeira ativação", "TR_I61850RCBRCBRETRY_ENABLE_COUNT": "Número de vezes que o SDG deve tentar novamente a ativação", "TR_I61850RCBRCBRETRY_ENABLE_COUNT_DESC": "O número de vezes que o SDG deve tentar novamente habilitar este relatório quando falha na ativação do servidor ativo. -1 = para sempre, 0 = nunca", "TR_I61850RCBRETRY_ENABLE_PERIOD": "tentando novamente ativar o período RCB (ms)", "TR_I61850RCBRETRY_ENABLE_PERIOD_DESC": "O período para tentar novamente ativar os RCBs em milissegundos.", "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW": "Estouro de buffer incluído", "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW_DESC": "Especifica a propriedade incluída Buffer Overflow", "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME": "Tempo de buffer (ms)", "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME_DESC": "Especifica o tempo de buffer de um bloco de controle de relatório", "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV": "Revisão de configuração incluída", "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV_DESC": "Especifica a propriedade incluída na Revisão de Configuração", "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE": "Monitorar alteração de dados", "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE_DESC": "Especifica a propriedade de alteração de dados do monitor do bloco de controle de relatório", "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF": "Referência de dados incluída", "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF_DESC": "Especifica a propriedade incluída da Referência de Dados", "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME": "Nome do conjunto de dados incluído", "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME_DESC": "Especifica a propriedade incluída no Nome do Conjunto de Dados", "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME": "Nome do conjunto de dados", "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME_DESC": "Especifica o nome de um conjunto de dados do bloco de controle de relatório em um servidor IEC 61850", "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE": "Monitorar alteração na atualização de dados", "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE_DESC": "Especifica a propriedade de alteração de atualização de dados do monitor do bloco de controle de relatório", "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID": "ID da entrada incluído", "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID_DESC": "Especifica a propriedade incluída no ID da entrada", "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG": "Interrogatório geral suportado", "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG_DESC": "Especifica a propriedade suportada Interrogação Geral do Bloco de Controle de Relatório", "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED": "Período de integridade monitorado", "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED_DESC": "Especifica a propriedade monitorada do período de integridade do bloco de controle de relatório", "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD": "Período de integridade (ms)", "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD_DESC": "Especifica o período de atualizações de integridade de um bloco de controle de relatório", "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE": "Monitorar alterações na qualidade", "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE_DESC": "Especifica a propriedade Alteração da qualidade do monitor do bloco de controle de relatório", "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL": "Motivo para inclusão incluído", "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL_DESC": "Especifica o motivo da propriedade Incl incluída", "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM": "Número de sequência incluído", "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM_DESC": "Especifica a propriedade incluída no Número de Sequência", "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP": "Registro de data e hora incluído", "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP_DESC": "Especifica a propriedade incluída no registro de data e hora", "TR_I61850SCLCLIENT_IEDNAME": "Modelo de IED do cliente a ser carregado", "TR_I61850SCLCLIENT_IEDNAME_DESC": "Modelo opcional de IED do cliente para carregar no arquivo SCL", "TR_I61850SCLFILE_IEDNAME": "Modelo de IED do servidor a ser carregado", "TR_I61850SCLFILE_IEDNAME_DESC": "Modelo IED do servidor opcional para carregar no arquivo SCL", "TR_I61850SCLFILE_NAME": "Nome do arquivo SCL", "TR_I61850SCLFILE_NAME_DESC": "Nome do arquivo SCL opcional para carregar o modelo.  Se no mesmo diretório que o arquivo INI, o caminho não é necessário. ", "TR_I61850SERVER_AEINVOKE_ID": "ID de chamada do AE", "TR_I61850SERVER_AEINVOKE_ID_DESC": "Especifica o ID de chamada do AE para o servidor IEC 61850", "TR_I61850SERVER_AEQUALIFIER": "Qualificador AE", "TR_I61850SERVER_AEQUALIFIER_DESC": "Especifica o qualificador AE para o servidor IEC 61850", "TR_I61850SERVER_APINVOKE_ID": "AP Qualifier", "TR_I61850SERVER_APINVOKE_ID_DESC": "Especifica o qualificador de ponto de acesso para o servidor IEC 61850", "TR_I61850SERVER_APP_ID": "ID do aplicativo", "TR_I61850SERVER_APP_ID_DESC": "Especifica o ID do aplicativo do servidor IEC 61850 ao qual se conectar", "TR_I61850SERVER_GOOSE_ADAPTER_NAME": "Nome do adaptador Goose", "TR_I61850SERVER_GOOSE_ADAPTER_NAME_DESC": "Nome do adaptador Goose selecionado.", "TR_I61850SERVER_IPADDRESS": "Endereço IP do servidor", "TR_I61850SERVER_IPADDRESS_DESC": "Especifica o endereço IP do servidor IEC 61850 ao qual se conectar", "TR_I61850SERVER_IPPORT": "Porta IP do servidor", "TR_I61850SERVER_IPPORT_DESC": "Define o número da porta TCP / IP a ser usada", "TR_I61850SERVER_PRESENTATION_ADDRESS": "Endereço da apresentação", "TR_I61850SERVER_PRESENTATION_ADDRESS_DESC": "Especifica o endereço de apresentação do servidor IEC 61850 ao qual se conectar", "TR_I61850SERVER_SCLFILE_IEDNAME": "modelo IED", "TR_I61850SERVER_SCLFILE_IEDNAME_DESC": "Modelo IED opcional para carregar no arquivo SCL", "TR_I61850SERVER_SESSION_ADDRESS": "Endereço da sessão", "TR_I61850SERVER_SESSION_ADDRESS_DESC": "Especifica o endereço da sessão do servidor IEC 61850 ao qual conectar", "TR_I61850SERVER_TRANSPORT_ADDRESS": "Endereço de transporte", "TR_I61850SERVER_TRANSPORT_ADDRESS_DESC": "Especifica o endereço de transporte do servidor IEC 61850 ao qual se conectar", "TR_I61850TIME_ZONE_BIAS": "Deslocamento do fuso horário em minutos", "TR_I61850TIME_ZONE_BIAS_DESC": "Um deslocamento em minutos para ajustar para servidores que não enviam hora no UTC", "TR_ICCP_CONFIG": "Configuração ICCP", "TR_ID": "ID", "TR_IECACTION_MASK0": "Máscara de ação IEC", "TR_IECACTION_MASK0_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com as definições da máscara de ação IECActionPrd. \nIEC: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no manual. Este parâmetro é usado apenas para sessões principais usando o perfil de protocolo IEC 60870-5. ", "TR_IECACTION_NOW": "Máscara IEC de ação agora", "TR_IECACTION_NOW_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com a IECActionPrd. \nDefinições da máscara de ação da IEC: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no Manual. Este parâmetro é usado apenas para sessões principais usando o perfil de protocolo IEC 60870-5. ", "TR_IECACTION_PRD0": "Período de ação da IEC (ms)", "TR_IECACTION_PRD0_DESC": "Tempo entre as ações definidas no IECActionMask.O período é desativado se definido como zero.Este parâmetro é usado apenas para sessões principais usando o perfil de protocolo IEC 60870-5. ", "TR_IECACTION_RETRY_COUNT0": "Contagem de novas tentativas de ação IEC", "TR_IECACTION_RETRY_COUNT0_DESC": "Quantas vezes para tentar novamente a máscara de ação em caso de falha.Se o tempo de nova tentativa for 0, isso não terá efeito.Este parâmetro é usado apenas para sessões principais usando o perfil de protocolo IEC 60870-5. ", "TR_IECACTION_RETRY_TIME0": "Tempo de nova tentativa de ação IEC (ms)", "TR_IECACTION_RETRY_TIME0_DESC": "O período entre novas tentativas desta máscara de ação.Se o tempo for 0, isso não terá efeito.Este parâmetro é usado apenas para sessões principais usando o perfil de protocolo IEC 60870-5. ", "TR_IED_CLIENT": "Cliente IED", "TR_IED_SERVER": "Servidor IED", "TR_IGNORE_DST": "Ignorar DST", "TR_IGNORE_DST_DESC": "Se true e UseTimeZoneClock for true, as alterações no horário de verão são ignoradas para exibição do horário", "TR_ILLEGAL_CHARACTER": "Caractere ilegal", "TR_IN_TRANSIT": "IN_TRANSIT", "TR_IN_TRANSIT_DESC": "Em trânsito / bate-papo", "TR_INFORMATION": "Informações", "TR_INFORMATION_OBJECT_ADDRESS": "Endereço do objeto de informações:", "TR_INFORMATION_OBJECT_ADDRESS_DESC": "o endereço do objeto de informações da tag", "TR_INPUT_REGISTERS": "Registros de entrada", "TR_INSTALL_V2C": "Instalar V2C", "TR_INTEG_PERIOD": "Período de integridade", "TR_INTEG_PRD_MON": "Período de integridade monitorado", "TR_INTEGRITY_PERIOD": "Período de integridade (ms)", "TR_INTEGRITY_PERIOD_DESC": "Especifica o período de integridade atual.", "TR_INTEGRITY_PERIOD_MONITORED": "Período de integridade monitorado", "TR_INTEGRITY_PERIOD_MONITORED_DESC": "Especifica se o período de integridade monitorado está ativo.", "TR_INTERNAL_MDO_ALREADY_DEFINED": "O MDO j<PERSON> definido, não pode criar", "TR_INTERNAL_SAVE_BLANK_NAME": "Erro: o nome não pode ficar em branco", "TR_INTERNAL_SET_MDO_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_INTERNAL_SET_OPTIONS_MDO": "Não foi poss<PERSON><PERSON> definir as opções internas do MDO {{arg1}}", "TR_INTERROGATION_COMMAND_AND_TOTALS_COUNTERS": "Comando de interrogação e contadores de totais", "TR_INTERVAL_DESC": "Especifica o intervalo atual.", "TR_INVALID": "INVALID", "TR_INVALID_DESC": "<PERSON>v<PERSON><PERSON><PERSON>", "TR_INVALID_TIME": "INVALID_TIME", "TR_INVALID_TIME_DESC": "Tempo decorrido / inválido", "TR_INVOICE": "<PERSON><PERSON>", "TR_IP_ADDRESS": "Endereço IP", "TR_IS_ACTIVE": "Ativo", "TR_IS_INCORRECT": "está incorreto", "TR_IS_LICENSED": "Licenciado", "TR_IS_REDUNDANCY_GROUP": "É um grupo de redundância", "TR_IS_REDUNDANCY_GROUP_DESC": "Este canal é um grupo de redundância", "TR_IS_REDUNDANT_CHANNEL": "É canal redundante", "TR_IS_REDUNDANT_CHANNEL_DESC": "Este canal é redundante", "TR_IS_REQUIRED": "é obrigatório.", "TR_IS_REQUIRED_NUMERICAL": "é obrigatório (numérico).", "TR_IS_XML_CLIENT": "É XML Client", "TR_IS_XML_CLIENT_DESC": "Se o cliente OPC for um cliente XML", "TR_ISRV61850SERVER_AEINVOKE_ID": "ID de chamada do AE", "TR_ISRV61850SERVER_AEINVOKE_ID_DESC": "Especifica o ID de chamada do AE para o servidor IEC 61850", "TR_ISRV61850SERVER_AEQUALIFIER": "Qualificador AE", "TR_ISRV61850SERVER_AEQUALIFIER_DESC": "Especifica o qualificador AE para o servidor IEC 61850", "TR_ISRV61850SERVER_APINVOKE_ID": "AP Invoke ID", "TR_ISRV61850SERVER_APINVOKE_ID_DESC": "Especifica o ID de chamada do AP para o servidor IEC 61850", "TR_ISRV61850SERVER_APP_ID": "ID do aplicativo", "TR_ISRV61850SERVER_APP_ID_DESC": "Especifica o ID do aplicativo do servidor IEC 61850", "TR_ISRV61850SERVER_AUTH_MECHANISM": "Mecanismo de autorização", "TR_ISRV61850SERVER_AUTH_MECHANISM_DESC": "Mecanismo de autorização de servidor IEC 61850 ('Aucun' ou 'Mot de passe' ou 'Certificat')", "TR_ISRV61850SERVER_AUTH_PASSWORD": "<PERSON><PERSON>", "TR_ISRV61850SERVER_AUTH_PASSWORD_DESC": "Senha de autorização do servidor IEC 61850", "TR_ISRV61850SERVER_ICDFILE": "ICD FileName", "TR_ISRV61850SERVER_ICDFILE_DESC": "Nome do arquivo ICD para o servidor IEC 61850.  Se no mesmo diretório que o arquivo INI o caminho não for necessário. ", "TR_ISRV61850SERVER_IPADDRESS": "Endereço IP", "TR_ISRV61850SERVER_IPADDRESS_DESC": "Especifica o endereço IP do servidor IEC 61850", "TR_ISRV61850SERVER_IPPORT": "Porta IP para escutar", "TR_ISRV61850SERVER_IPPORT_DESC": "Especifica a porta IP para escutar este servidor IEC 61850", "TR_ISRV61850SERVER_NAME": "61850 Nome do servidor", "TR_ISRV61850SERVER_NAME_DESC": "Nome para o servidor IEC 61850", "TR_ISRV61850SERVER_PRESENTATION_ADDRESS": "Endereço da apresentação", "TR_ISRV61850SERVER_PRESENTATION_ADDRESS_DESC": "Especifica o endereço de apresentação do servidor IEC 61850", "TR_ISRV61850SERVER_SESSION_ADDRESS": "Endereço da sessão", "TR_ISRV61850SERVER_SESSION_ADDRESS_DESC": "Especifica o endereço da sessão do servidor IEC 61850", "TR_ISRV61850SERVER_TRANSPORT_ADDRESS": "Endereço de transporte", "TR_ISRV61850SERVER_TRANSPORT_ADDRESS_DESC": "Especifica o endereço de transporte do servidor IEC 61850", "TR_ITEM_ATTRIBUTES": "Atributos do item", "TR_ITEM_DESCRIPTION": "Descrição do item", "TR_ITEM_DESCRIPTION_DESC": "Defina a descrição do item", "TR_ITEM_ID": "ID do item", "TR_ITEM_ID_DESC": "Defina o ID do item", "TR_ITEM_NAME": "Nome do item", "TR_ITEM_NAME_DESC": "Defina o nome do item", "TR_ITEM_PARENT_BROWSER": "Nave<PERSON><PERSON> de itens", "TR_ITEM_TYPE": "Tipo de item", "TR_ITEM_TYPE_DESC": "Tipo de item", "TR_ITEM_VALUE_TYPE": "Tipo de valor do item", "TR_ITEM_VALUE_TYPE_DESC": "Defina o tipo de valor do item", "TR_ITEMS_PARENT_BROWSER_DESC": "Nave<PERSON><PERSON> de itens", "TR_KEY_ID": "ID da chave", "TR_LANGUAGE": "Idioma", "TR_LICENSE": "Licença", "TR_LICENSE_DEMO": "A sessão {{arg2}} está configurada como o protocolo {{arg2}} que não está licenciado no momento. Esta sessão foi definida como inativa ", "TR_LICENSE_DEMO_EXPIRES": "A licença de demonstração do SCADA Data Gateway expirará em {{arg1}}", "TR_LICENSE_DESC": "Registrar mensagens de rastreamento para o código de licenciamento", "TR_LICENSE_EXCEPTION": "Erro do License Manager: {{arg1}} Entre em contato com o suporte ao cliente da Triangle Microworks.", "TR_LICENSE_FAILED_SEVERE": "Erro desconhecido do License Manager. Entre em contato com o suporte ao cliente da Triangle Microworks. ", "TR_LICENSE_INFORMATION": "Informações da licença", "TR_LICENSE_LOST": "A conexão da licença para o aplicativo foi perdida. Todas as sessões estão desativadas. ", "TR_LICENSE_MANAGER": "Gerenciador de licenças", "TR_LICENSE_OTPIONS": "Opções de licença", "TR_LICENSE_REACQUIRED": "A conexão de licença para o aplicativo foi readquirida. Todas as sessões foram reativadas. ", "TR_LICENSE_TYPE": "Tipo de licença", "TR_LINK_CLASS_PENDING_CNT": "Contagem de classes pendentes", "TR_LINK_CLASS_PENDING_CNT_DESC": "Para um link de comunicação mestre desequilibrado, o número total de quadros de solicitação consecutivos de classe 1 e classe 2 que podem ser enviados para um dispositivo quando uma mensagem de resposta da camada de aplicação estiver pendente deste dispositivo antes de passar para o próximo dispositivo em uma rede multiponto.  Este parâmetro não tem efeito se apenas um dispositivo estiver configurado para um canal de comunicação.  Se este parâmetro for definido como zero, o dispositivo ainda será pesquisado conforme descrito para o parâmetro M870CNFG_LINK_CLASS1_POLL_CNT.Este parâmetro se aplica apenas às sessões IEC 60870-5-101 e IEC 60870-5-103 Master. ", "TR_LINK_CLASS1PENDING_DLY": "Atraso pendente de classe 1 (ms)", "TR_LINK_CLASS1PENDING_DLY_DESC": "Para um link de comunicação mestre desequilibrado, o atraso mínimo em milissegundos após o envio da solicitação de dados da classe 1 quando uma resposta da camada de aplicativo está pendente para esta sessão.  Este parâmetro pode ser usado para limitar a largura de banda em uma mídia compartilhada como a Ethernet ou para evitar tributar o dispositivo de destino com sobrecarga de comunicação desnecessária.Este parâmetro se aplica apenas às sessões IEC 60870-5-101 e IEC 60870-5-103 Master. ", "TR_LINK_CLASS1POLL_CNT": "Contagem de pesquisas de classe 1", "TR_LINK_CLASS1POLL_CNT_DESC": "Para um link de comunicação mestre desequilibrado, o número total de quadros de solicitação consecutivos de classe 1 que podem ser enviados para um dispositivo antes de passar para o próximo dispositivo em uma rede multiponto (a classe 2 é sempre limitada a um quadro de solicitação a menos que uma resposta da camada de aplicativo esteja pendente).  Este parâmetro não tem efeito se apenas um dispositivo estiver configurado para um canal de comunicação.  Em uma topologia de rede multiponto, esse parâmetro é usado para equilibrar a pesquisa entre todos os dispositivos e impedir que um dispositivo capture todas as mensagens de pesquisa. Este parâmetro se aplica apenas às sessões IEC 60870-5-101 e IEC 60870-5-103 Master. ", "TR_LINK_CLASS1POLL_DLY": "Atraso na pesquisa de classe 1 (ms)", "TR_LINK_CLASS1POLL_DLY_DESC": "Para um link de comunicação mestre desequilibrado, o atraso mínimo em milissegundos após o envio da solicitação de dados da classe 1 quando uma resposta da camada de aplicativo não está pendente para esta sessão.  Este parâmetro pode ser usado para limitar a largura de banda em uma mídia compartilhada como a Ethernet ou para evitar tributar o dispositivo de destino com sobrecarga de comunicação desnecessária.Este parâmetro se aplica apenas às sessões IEC 60870-5-101 e IEC 60870-5-103 Master. ", "TR_LINK_CLASS2PENDING_DLY": "Atraso pendente de classe 2 (ms)", "TR_LINK_CLASS2PENDING_DLY_DESC": "Para um link de comunicação mestre desequilibrado, o atraso mínimo em milissegundos após o envio da solicitação de dados da classe 2 quando uma resposta da camada de aplicativo está pendente para esta sessão.  Este parâmetro pode ser usado para limitar a largura de banda em uma mídia compartilhada como a Ethernet ou para evitar tributar o dispositivo de destino com sobrecarga de comunicação desnecessária.Este parâmetro se aplica apenas às sessões IEC 60870-5-101 e IEC 60870-5-103 Master. ", "TR_LINK_CLASS2POLL_DLY": "Atraso na pesquisa de classe 2 (ms)", "TR_LINK_CLASS2POLL_DLY_DESC": "Para um link de comunicação mestre desequilibrado, o atraso mínimo em milissegundos após o envio da solicitação de dados da classe 2 quando uma resposta da camada de aplicativo não está pendente para esta sessão.  Este parâmetro pode ser usado para limitar a largura de banda em uma mídia compartilhada como a Ethernet ou para evitar tributar o dispositivo de destino com sobrecarga de comunicação desnecessária.Este parâmetro se aplica apenas às sessões IEC 60870-5-101 e IEC 60870-5-103 Master. ", "TR_LINK_CNFM_TIMEOUT": "T1 - Tempo limite de confirmação da camada de link (ms)", "TR_LINK_CNFM_TIMEOUT_DESC": "Tempo máximo para aguardar a confirmação do quadro. Para uma sessão IEC 60870-5-104, este é o parâmetro T1.  Este parâmetro não se aplica a conexões da camada de link (sessões) quando o GATEWAY está agindo como um escravo desequilibrado. ", "TR_LINK_CONFIRM_MODE": "Modo de confirmação de link", "TR_LINK_CONFIRM_MODE_DESC": "Solicite ao dispositivo remoto que envie uma camada de link de dados para confirmar o último quadro enviado.  Observe que essa configuração é independente de o dispositivo remoto exigir que o dispositivo envie um link de dados para confirmar os quadros que recebe. Este parâmetro é usado apenas para sessões mestre ou escravo usando o protocolo DNP3. ", "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES": "K - Número máximo de quadros de transmissão não reconhecidos", "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES_DESC": "Número máximo de quadros de transmissão não reconhecidos.", "TR_LINK_LAYER": "vincular camada", "TR_LINK_MAX_RETRIES": "Número máximo de tentativas de retransmissão de dados", "TR_LINK_MAX_RETRIES_DESC": "Número máximo de tentativas para retransmitir quadros da camada de enlace de dados que não foram confirmados.  Este parâmetro não se aplica a conexões da camada de link (sessões) quando o GATEWAY está agindo como um escravo desequilibrado. ", "TR_LINK_MODE": "Modo de transmissão do link de dados", "TR_LINK_MODE_DESC": "Modo de transmissão do link de dados. Necessário para cada canal de comunicação. ", "TR_LINK_SEND_ACK_DELAY": "T2 - Tempo máximo de espera para enviar um quadro de reconhecimento (ms)", "TR_LINK_SEND_ACK_DELAY_DESC": "Tempo máximo de espera para enviar um quadro de reconhecimento.  Para uma sessão IEC 60870-5-104, esse é o parâmetro T2 da Cláusula 5.1 da IEC 60870-5-104. (O período máximo de tempo após o recebimento do último APDU I-FORMAT antes de transmitir um APDU S-FORMAT.) Este parâmetro não se aplica a conexões da camada de link (sessões) quando o GATEWAY está agindo como um escravo desequilibrado. ", "TR_LINK_SIZE_ADDRESS": "Tamanho do endereço do link (bytes)", "TR_LINK_SIZE_ADDRESS_0_NOT_ALLOW_IN_UNBALANCED_LINK": "O endereço de tamanho do link 0 não é permitido em um link de desequilíbrio", "TR_LINK_SIZE_ADDRESS_DESC": "Número de octetos (bytes) no campo Endereço do link. Observe que um valor 0 é válido apenas para sessões cujo modo de link é equilibrado. Este parâmetro é usado apenas para sessões master e slave IEC60870-5-101 ", "TR_LINK_TEST_FRAME_INTERVAL": "T3 - Intervalo do quadro de teste (ms)", "TR_LINK_TEST_FRAME_INTERVAL_DESC": "Hora do intervalo do quadro de teste. Para uma sessão IEC 60870-5-104, este é o parâmetro T3. É recomendável que T3 seja maior que T1. Este parâmetro não se aplica a conexões da camada de link (sessões) quando o GATEWAY está agindo como um escravo desequilibrado. ", "TR_LINK_WRECIEVED_UN_ACK_FRAMES": "W - Número máximo de quadros de recebimento não reconhecidos", "TR_LINK_WRECIEVED_UN_ACK_FRAMES_DESC": "Número máximo de quadros de recebimento não reconhecidos.", "TR_LN_DESTINATION": "Nó lógico para criar o conjunto de dados", "TR_LN_DESTINATION_DESC": "Escolha o nó lógico no qual criar conjunto de dados.", "TR_LOAD_CONFIG_FROM_SERVER": "Carregar configuração do servidor IED / SCL", "TR_LOAD_CONFIG_FROM_SERVER_DESC": "Carregar configuração do servidor IED / SCL.", "TR_LOAD_MODEL": "Carregar modelo", "TR_LOAD_MODEL_DESC": "Carregar modelo do arquivo selecionado.", "TR_LOCAL_UDP_PORT": "Porta UDP local", "TR_LOCK_SCROLL": "Bloquear rolagem", "TR_LOG": "Log", "TR_LOG_MASKS_EVENT_LOG": "Log Masks Event Log", "TR_LOG_PARAMETERS": "Parâmetros de log", "TR_LOGIN": "<PERSON><PERSON>", "TR_LOGIN_BUTTON": "<PERSON><PERSON>", "TR_LOGOFF": "<PERSON><PERSON><PERSON>", "TR_LOGS": "Logs", "TR_LOW_ORDER_INDEX": "Índice de registro de pedidos baixos", "TR_LOW_ORDER_INDEX_DESC": "Especifique o índice de registro de pedidos baixos", "TR_M103APPL_BLOCKING_ACTION_MASK": "Bloqueando a máscara de ação", "TR_M103APPL_BLOCKING_ACTION_MASK_DESC": "Cada bit ativa (1) ou desativa (0) uma solicitação automática a ser enviada como resultado de um dispositivo escravo saindo do modo de bloqueio. Este parâmetro é usado apenas para sessões principais IEC60870-5-103. ", "TR_M103APPL_EOI_ACTION_MASK": "Máscara de ação EOI", "TR_M103APPL_EOI_ACTION_MASK_DESC": "Cada bit ativa (1) ou desativa (0) uma solicitação automática a ser enviada como resultado do recebimento de uma mensagem de inicialização de um dispositivo escravo. Este parâmetro é usado apenas para sessões principais IEC60870-5-103. ", "TR_M103APPL_ONLINE_ACTION_MASK": "Máscara de ação online", "TR_M103APPL_ONLINE_ACTION_MASK_DESC": "Cada bit ativa (1) ou desativa (0) uma solicitação automática a ser enviada como resultado de um dispositivo escravo ficar online. Este parâmetro é usado apenas para sessões principais IEC60870-5-103. ", "TR_M14APPL_EOI_ACTION_MASK": "Máscara de ação EOI", "TR_M14APPL_EOI_ACTION_MASK_DESC": "Cada bit ativa (1) ou desativa (0) uma solicitação automática a ser enviada como resultado do recebimento de uma mensagem de inicialização de um dispositivo escravo. Este parâmetro é usado apenas para sessões mestre IEC60870-5 101.104. ", "TR_M14APPL_ONLINE_ACTION_MASK": "Máscara de ação online", "TR_M14APPL_ONLINE_ACTION_MASK_DESC": "Cada bit ativa (1) ou desativa (0) uma solicitação automática a ser enviada como resultado de um dispositivo escravo ficar online. Este parâmetro é usado apenas para sessões mestre IEC60870-5 101.104. ", "TR_MAIN_PARAMETERS": "Parâmetros <PERSON>rin<PERSON>", "TR_MAINTENANCE_PLAN_EXPIRES": "O plano de manutenção expira", "TR_MANAGE_CONFIGURATION FILE": "Gerenciar arquivos de configuração", "TR_MAP_FAILED": "Houve problemas ao carregar o arquivo do mapa de pontos (.csv).  Corrija os problemas manualmente, abrindo o arquivo CSV com um editor de texto e corrigindo os problemas. As informações de erro são encontradas no visor do analisador de protocolo. ", "TR_MAPPING": "Mapeamento", "TR_MASTER_DATA_OBJECT": "Objeto de dados mestre", "TR_MAX_NUM_THREADS": "Número máximo de usuários", "TR_MAX_NUM_UNACK_FRAMES": "Número máximo de quadros não reconhecidos", "TR_MAX_SOEQSIZE": "Contagem máxima da fila SOE", "TR_MAX_SOEQSIZE_DESC": "O número máximo de itens permitidos na fila SOE (e no arquivo). Se esse limite for excedido, os itens mais antigos serão removidos. Qualquer valor especificado menor que 1000 será padronizado como 1000 ", "TR_MBACTION_PRD0": "Período de ação da sessão Modbus (ms)", "TR_MBACTION_PRD0_DESC": "Tempo entre as ações definidas no MBActionMask. O período é desativado se definido como zero.Este parâmetro é usado apenas para sessões master usando o protocolo Modbus. ", "TR_MBCHANNEL_ACTION_MASK0": "Máscara de ação de canal Modbus", "TR_MBCHANNEL_ACTION_MASK0_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com o MBChannelActionPrd.\nMB Definições da máscara de ação: \nEste parâmetro é usado apenas para sessões principais usando o protocolo Modbus.", "TR_MBCHANNEL_ACTION_NOW": "Ação do canal Modbus agora mascara", "TR_MBCHANNEL_ACTION_NOW_DESC": "Use esta máscara para forçar eventos únicos ou periódicos em conjunto com o MBChannelActionPrd.\nMB Definições da máscara de ação: \nVeja a seção 4.2 'Nomes de tags predefinidos para monitoramento e controle' no manual. Este parâmetro é usado apenas para sessões master usando o protocolo Modbus. ", "TR_MBCHANNEL_ACTION_PRD0": "Período de ação do canal Modbus (ms)", "TR_MBCHANNEL_ACTION_PRD0_DESC": "Tempo entre as ações definidas no MBChannelActionMask. O período é desativado se definido como zero.Este parâmetro é usado apenas para sessões master usando o protocolo Modbus. ", "TR_MDO": "MDO:", "TR_MDO_ALREADY_EXITS": "MDO j<PERSON> definido, n<PERSON> pode criar", "TR_MDO_CREATE_ALREADY": "MDO j<PERSON> definido, n<PERSON> pode criar", "TR_MDO_DESC": "MDO", "TR_MDO_EDITOR_CREATE_INVALID_TAG": "Não foi possível criar o MDO: {{arg1}}. Pode ser um nome de tag inválido ou um tipo inválido. ", "TR_MDO_EDITOR_CREATE_MDO_FOUND": "MDO não encontrado, não pode criar", "TR_MDO_EDITOR_CREATE_MDO_USER_TAG": "Não foi possível definir o nome da marca do usuário MDO {{arg1}} (possivelmente uma duplicata, consulte os logs)", "TR_MDO_EDITOR_CREATE_SDO": "Não foi possível criar o SDO.", "TR_MDO_EDITOR_CREATE_TAG": "Falha ao definir o nome da tag do usuário como '{{arg1}}' (possivelmente uma duplicata)", "TR_MDO_EDITOR_CREATE_TAG_BAD": "Não foi possível criar o {{arg1}} MDO. Pode ser um nome de marca ou um tipo inválido.", "TR_MDO_EDITOR_MDO_SET_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_MDO_EDITOR_OPTIONS": "Não foi possí<PERSON> definir as opções do SDO {{arg1}}", "TR_MDO_EDITOR_OPTIONS_SDO": "Não foi possí<PERSON> definir as opções do SDO {{arg1}}", "TR_MDO_EDITOR_SDO_BIND": "Não foi possível vincular o SDO.", "TR_MDO_EDITOR_SDO_DEFINED": "SDO j<PERSON> definido, não pode criar", "TR_MDO_EDITOR_SET_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO '{{arg1}}'", "TR_MDO_LIST": "MDO", "TR_MDO_LIST_DESC": "Lista de MDO", "TR_MDO_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_MEMORY_ABBREVIATION": "Mem.", "TR_MENU": "<PERSON><PERSON>", "TR_MENU_CMD_ADD_61400_ALARM_MDO": "Adicionar 61400 MDO de alarme", "TR_MENU_CMD_ADD_61400_ALARMS_NODE": "Adicionar nó de alarme IEC 61400", "TR_MENU_CMD_ADD_61850_CLIENT": "Adicionar cliente IEC 61850", "TR_MENU_CMD_ADD_61850_COMMAND_POINT": "Adicionar ponto de controle IEC 61850", "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING": "Adicione o controle 61850 ao mapeamento OPC", "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM": "Adicione o controle 61850 ao item de mapeamento OPC", "TR_MENU_CMD_ADD_61850_GOOSE": "Adicione o bloco de controle IEC 61850 Goose", "TR_MENU_CMD_ADD_61850_ITEM": "Adicionar item 61850", "TR_MENU_CMD_ADD_61850_POLLED_DATA_SET": "Adicionar conjunto de dados pesquisados IEC 61850", "TR_MENU_CMD_ADD_61850_POLLED_POINT_SET": "Adicionar conjunto de pontos pesquisados IEC 61850", "TR_MENU_CMD_ADD_61850_REPORT": "Adicionar bloco de controle de relatório IEC 61850", "TR_MENU_CMD_ADD_61850_SERVER": "Adicionar servidor IEC 61850", "TR_MENU_CMD_ADD_61850_WRITABLE_POINT": "Adicionar ponto gravável IEC 61850", "TR_MENU_CMD_ADD_DATA_TYPE": "Adicionar tipo de dados", "TR_MENU_CMD_ADD_DATASET_ELEMENT": "Adicionar elemento do conjunto de dados", "TR_MENU_CMD_ADD_DNP_DESCP": "Adicione um descritor DNP", "TR_MENU_CMD_ADD_DNP_PROTO": "Adicionar Protocolo DNP", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL": "Adicionar canal DNP3 UDP / TCP", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER": "Adicionar mestre de canal DNP3 UDP / TCP", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE": "Adicionar escravo de terceirização de canal UDP / TCP DNP3", "TR_MENU_CMD_ADD_DNP3_XML_DEVICE": "Adicionar dispositivo XML DNP3", "TR_MENU_CMD_ADD_EQ_MDO": "Adicionar equação MDO", "TR_MENU_CMD_ADD_GOOSE_MONITOR": "Adicionar monitor Goose", "TR_MENU_CMD_ADD_INTERNAL_MDO": "Adicionar MDO interno", "TR_MENU_CMD_ADD_MAPPING_SDO": "Adicionar SDO", "TR_MENU_CMD_ADD_MAPPING_SDOS": "Adicionar SDOS", "TR_MENU_CMD_ADD_MBP_CHANNEL": "Adicionar canal ModbusPlus", "TR_MENU_CMD_ADD_MDO": "Adicionar MDO", "TR_MENU_CMD_ADD_MODEM": "Adicionar modem", "TR_MENU_CMD_ADD_MODEM_POOL": "Adicionar pool de modem", "TR_MENU_CMD_ADD_MODEM_POOL_CHANNEL": "Adicionar canal de pool de modem", "TR_MENU_CMD_ADD_MULTI_POINT": "<PERSON><PERSON><PERSON><PERSON>", "TR_MENU_CMD_ADD_ODBC_CLIENT": "Adicionar cliente ODBC", "TR_MENU_CMD_ADD_ODBC_ITEM": "Adicionar item ODBC", "TR_MENU_CMD_ADD_OPC_AE_ATTR": "Adicionar atributo OPC AE", "TR_MENU_CMD_ADD_OPC_AE_CLIENT": "Adicionar cliente OPC AE", "TR_MENU_CMD_ADD_OPC_AE_ITEM": "Adicionar item de OPC AE", "TR_MENU_CMD_ADD_OPC_CLIENT": "Adicionar cliente OPC", "TR_MENU_CMD_ADD_OPC_ITEM": "ADICIONAR Item OPC", "TR_MENU_CMD_ADD_OPC_UA_CLIENT": "Adicionar cliente OPC UA", "TR_MENU_CMD_ADD_REDUNDANT_CHANNEL": "Adicionar canal redundante", "TR_MENU_CMD_ADD_SECTOR": "<PERSON><PERSON><PERSON><PERSON> setor", "TR_MENU_CMD_ADD_SERIAL_CHANNEL": "Adicionar canal serial", "TR_MENU_CMD_ADD_SERIAL_CHANNEL_MASTER": "Adicionar mestre de canal serial", "TR_MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE": "Adicionar escravo de terceirização de canal serial", "TR_MENU_CMD_ADD_SESSION": "<PERSON><PERSON><PERSON><PERSON>", "TR_MENU_CMD_ADD_TASE2": "Adicionar cliente / servidor ICCP", "TR_MENU_CMD_ADD_TASE2_COMMAND_POINT": "ADICIONAR ponto de comando ICCP", "TR_MENU_CMD_ADD_TASE2_DSTS": "ADICIONE ICCP DSTS", "TR_MENU_CMD_ADD_TASE2_ITEM": "ADICIONAR Item ICCP", "TR_MENU_CMD_ADD_TASE2_LOGICAL_DEVICE": "Adicionar dispositivo lógico ICCP", "TR_MENU_CMD_ADD_TASE2_POLLED_DATA_SET": "ADICIONAR conjunto de dados pesquisados ICCP", "TR_MENU_CMD_ADD_TASE2_POLLED_POINT_SET": "ADICIONAR conjunto de pontos pesquisados ICCP", "TR_MENU_CMD_ADD_TCP_CHANNEL": "Adicionar canal TCP", "TR_MENU_CMD_ADD_TCP_CHANNEL_MASTER": "Adicionar mestre de canal TCP", "TR_MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE": "Adicionar escravo de terceirização de canal TCP", "TR_MENU_CMD_ADD_WRITE_ACTION": "Adicionar ação de gravação", "TR_MENU_CMD_AUTO_CREATE_TAGS": "Criar tags automaticamente", "TR_MENU_CMD_CHANGE_VALUE": "Alterar valor", "TR_MENU_CMD_CONNECT_OPC_AE_SERVER": "Conecte o servidor OPC AE", "TR_MENU_CMD_CONNECT_OPC_SERVER": "Conecte o servidor OPC", "TR_MENU_CMD_CONNECT_OPC_UA_SERVER": "Conecte o servidor OPC UA", "TR_MENU_CMD_CONNECT_TO_61850_SERVER": "Conecte-se a um servidor 61850", "TR_MENU_CMD_CREATE_DTM_CSV_POINT_FILE": "Criar arquivo de ponto DTM CSV", "TR_MENU_CMD_CREATE_THXML_POINT_FILE": "Criar arquivo de ponto THXML", "TR_MENU_CMD_DELETE": "Excluir", "TR_MENU_CMD_DELETE_REDUNDANT_CHANNEL": "Excluir canal redundante", "TR_MENU_CMD_DISCONNECT_OPC_AE_SERVER": "Desconecte o servidor OPC AE", "TR_MENU_CMD_DISCONNECT_OPC_SERVER": "Desconecte o servidor OPC", "TR_MENU_CMD_DISCONNECT_OPC_UA_SERVER": "Desconecte o servidor OPC UA", "TR_MENU_CMD_EDIT": "<PERSON><PERSON>", "TR_MENU_CMD_NONE": "N / A", "TR_MENU_CMD_PERFORM_WRITE_ACTION": "Executar ação de gravação", "TR_MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT": "Redefinir contagem de novas tentativas de conexão", "TR_MENU_CMD_RESTART_61850_SERVER": "Reinicie o servidor 61850", "TR_MENU_CMD_SAVE_MODEL_TO_FILE": "Salvar modelo no arquivo ICD", "TR_MENU_CMD_SUBSCRIBE_GOOSE_STREAM": "Inscrever-se no GOOSE Stream", "TR_MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM": "Cancelar inscrição no GOOSE Stream", "TR_MESSAGE": "Mensagem", "TR_MMS": "MMS", "TR_MMS_COMMON_NAME": "Nome comum", "TR_MMS_PRIVATE_KEY_FILE": "Arquivo de chave privada", "TR_MMS_PRIVATE_KEY_PASS": "PassPhrase de chave privada", "TR_MMS_PUB_CERT_FILE": "Arquivo de certificado público", "TR_MODBUS_ACTION_COILS": "<PERSON>r bob<PERSON>", "TR_MODBUS_ACTION_COILS_DESC": "<PERSON>r bob<PERSON>", "TR_MODBUS_ACTION_DISCRETE_INPUTS": "Entradas discretas", "TR_MODBUS_ACTION_DISCRETE_INPUTS_DESC": "Entradas Discretas", "TR_MODBUS_ACTION_EXCEPTION_STATUS": "Exception_Status", "TR_MODBUS_ACTION_EXCEPTION_STATUS_DESC": "Exception_Status", "TR_MODBUS_ACTION_HOLDING_REGISTERS": "Holding Registers", "TR_MODBUS_ACTION_HOLDING_REGISTERS_DESC": "Holding Registers", "TR_MODBUS_ACTION_INPUT_REGISTERS": "Registros de Entrada", "TR_MODBUS_ACTION_INPUT_REGISTERS_DESC": "Registros de Entrada", "TR_MODBUS_ACTION_PRD": "Periodo (ms)", "TR_MODBUS_ACTION_PRD_DESC": "Periode (ms)", "TR_MODBUS_HIGH_INDEX": "Erro: o índice de ordem superior especificado não existe. O Holding Register MDO deve existir ", "TR_MODBUS_LOW_INDEX": "Erro: o índice de ordem baixa especificado não existe. O Holding Register MDO deve existir. ", "TR_MODBUS_MAP_EQUATION": "MDO: '{{arg1}}' é mapeado para pontos escravos ou é usado em uma equação, não pode ser excluído. Você pode remover a dependência e tentar novamente. ", "TR_MODBUS_REG_EXISTS": "Erro: Um registro duplo com esse nome já existe. Forneça um nome exclusivo. ", "TR_MODBUS_SAME_INDEX": "Erro: os índices de pedidos altos e baixos não podem ser os mesmos", "TR_MODEL_FILE_NOT_FOUND": "Arquivo de modelo não encontrado: '{{arg1}}'", "TR_MODEM_BAUD": "Taxa de transmissão", "TR_MODEM_BAUD_DESC": "Define a taxa de transmissão do modem correspondente", "TR_MODEM_COM_PORT": "Porta com modem", "TR_MODEM_COM_PORT_DESC": "Define uma porta de modem", "TR_MODEM_DATA_BITS": "Bits de dados", "TR_MODEM_DATA_BITS_DESC": "Define o número de bits de dados para o modem correspondente", "TR_MODEM_DIALING_MODE": "<PERSON><PERSON> de discagem", "TR_MODEM_DIALING_MODE_DESC": "Define o modo de discagem do Modem: 'pulse' - use discagem por pulso. 'Tone' - use discagem por tom.", "TR_MODEM_ENABLE": "Ativar modem", "TR_MODEM_ENABLE_DESC": "Se verdadeiro, o modem estará Ativado e Disponível para uso.", "TR_MODEM_HANGUP_STRING": "Hangup string", "TR_MODEM_HANGUP_STRING_DESC": "Define a seqüência de interrupção do modem", "TR_MODEM_INIT_STRING": "Sequência de inicialização", "TR_MODEM_INIT_STRING_DESC": "Define a sequência de inicialização do modem", "TR_MODEM_NAME": "Nome alternativo do modem", "TR_MODEM_NAME_DESC": "Define um nome alternativo do modem", "TR_MODEM_NO_DIAL_OUT": "Recebendo apenas chamadas telefônicas", "TR_MODEM_NO_DIAL_OUT_DESC": "Se verdadeiro, o modem será configurado para receber apenas chamadas telefônicas.", "TR_MODEM_PARITY": "Paridade", "TR_MODEM_PARITY_DESC": "Define a paridade para o modem ModemChannel correspondente", "TR_MODEM_POOL_NAME": "Nome do pool de modems", "TR_MODEM_POOL_NAME_DESC": "Define um pool de modems que contém modems para um canal", "TR_MODEM_READ_CMD_TIMEOUT": "Tempo limite de leitura do comando (segundos)", "TR_MODEM_READ_CMD_TIMEOUT_DESC": "Especifica o tempo limite em segundos que o SDG espera que um modem responda a um comando.", "TR_MODEM_RESP_TERMINATOR_CHAR": "Caractere de encerramento de resposta", "TR_MODEM_RESP_TERMINATOR_CHAR_DESC": "Define o caractere usado para finalizar uma resposta do modem: 'none' - não use um caractere para finalizar a resposta (não assume respostas de comando). 'Line feed' - use um caractere 'line feed'. para finalizar a resposta. 'retorno de carro' - use um caractere 'retorno de carro' para finalizar a resposta. ", "TR_MODEM_SERIAL_MODE": "Modo serial", "TR_MODEM_SERIAL_MODE_DESC": "Define o modo serial do Modem: 'hardware' - use o controle de fluxo de hardware ('none' e 'windows' não funcionam para modems).", "TR_MODEM_SETUP": "Configuração do modem", "TR_MODEM_STOP_BITS": "Stop bits", "TR_MODEM_STOP_BITS_DESC": "Define o número de bits de parada para o modem correspondente", "TR_MODEM_WRITE_CMD_TIMEOUT": "Tempo limite de gravação do comando (segundos)", "TR_MODEM_WRITE_CMD_TIMEOUT_DESC": "Especifica o tempo limite em segundos que o SDG espera para enviar um comando para um modem.", "TR_MONITOR": "Monitor", "TR_MONITOR_ABBREVIATION": "M:", "TR_MONITOR_ENABLE_TRACE": "Monitor ativar rastreamento", "TR_MONITOR_HOST_AND_HTTP_PORT": "Monitorar o host e a porta HTTP (s)", "TR_MONITOR_IS_RE_STARTING": "O monitor está reiniciando.", "TR_MONITOR_LOG": "Monitor Log", "TR_MONITOR_MANAGEMENT": "Monitorar o gerenciamento", "TR_MONITOR_VIEW_1": "Monitor View # 1", "TR_MONITOR_VIEW_2": "Monitor View # 2", "TR_MONITOR_VIEW_3": "Monitor # 3", "TR_NAME": "Nome", "TR_NAME_DESC": "Especifica o nome atual.", "TR_NATIVE_DATA_TYPE": "Tipo de dados nativo", "TR_NATIVE_DATA_TYPE_DESC": "Defina o tipo de dados nativo", "TR_NEW_GATEWAY": "Nova configuração de gateway", "TR_NEW_PASSWORD": "Nova senha", "TR_NEW_VALUE": "Novo valor", "TR_NO": "Não", "TR_NO_EDITOR": "Nenhum editor dispon<PERSON><PERSON> para alterar o valor", "TR_NO_FILE_TO_DOWNLOAD": "Nenhum arquivo para download", "TR_NO_GOOSE": "Não há mais IEC 61850 GOOSEs disponíveis", "TR_NO_LICENSE_INFORMATION_SAVED": "Nenhuma informação de licença salva", "TR_NO_POLLED": "Não há mais IEC 61850 PolledDataSets disponíveis", "TR_NO_RESULTS": "Sem resultados", "TR_NO_SECTORS": "Não há mais setores disponíveis", "TR_NO_SESSIONS": "Não há mais sessões disponíveis", "TR_NODE_LIST": "Lista de nós", "TR_NODE_LIST_DESC": "Escolha os nós a serem adicionados ao novo conjunto de dados.", "TR_NODE_LIST_SDO_DESC": "Escolha nós para adicionar ao novo SDO.", "TR_NODE_NAME": "Nome do nó", "TR_NODE_NAME_DESC": "Defina o nome do nó", "TR_NODE_SDO_LIST": "Lista de nós", "TR_NONE": "NENHUM", "TR_NONE_DESC": "Remover seleção", "TR_NOT_TOPICAL": "NOT_TOPICAL", "TR_NOT_TOPICAL_DESC": "Não tópico (off-line / sem data)", "TR_NUM_THREADS": "Number of Theard", "TR_OBJECT_NODENAME_DELETED": "Objeto {{NodeName}} excluído", "TR_OBJECT_TAGNAME_ALREADY_EXISTS": "O objeto {{tagName}} já existe.", "TR_OBJECT_TAGNAME_CAN_T_BE_DELETED": "O objeto {{tagName}} não pode ser excluído.", "TR_OBJECT_TAGNAME_CAN_T_BE_EDITED": "O objeto {{tagName}} não pode ser editado.", "TR_OBJECT_TAGNAME_CAN_T_BE_OPERATED_ON": "O objeto {{tagName}} não pode ser operado.", "TR_OBJECT_TAGNAME_DELETED": "Objeto {{tagName}} excluído.", "TR_ODBC_CLIENT_DUPLICATE": "Não é possível adicionar o cliente ODBC: '{{arg1}}'. Nome duplicado. ", "TR_ODBC_DELETE_EQUATION": "MDO: '{{}}' está mapeado para outros pontos ou é usado em uma equação, não pode excluir", "TR_ODBC_EXECUTE_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ExecuteSql :: Exceção capturada: {{arg3}}", "TR_ODBC_EXECUTE_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ExecuteSql grave :: Exceção capturada", "TR_ODBC_FAILED_TO_FIND_QUERY": "Falha ao localizar a consulta", "TR_ODBC_LIST_DSNS_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListDataSourceNames :: Exceção capturada: {{arg3}}", "TR_ODBC_LIST_DSNS_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListDataSourceNames grave :: Exceção capturada", "TR_ODBC_LIST_TABLES_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListTables :: Exceção capturada: {{arg3}}", "TR_ODBC_LIST_TABLES_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListTables severas :: exceção detectada", "TR_ODBC_MAP": "MDO: '{{arg1}}' está mapeado para outros pontos ou é usado em uma equação, não pode excluir", "TR_ODBC_MDO_DELETE": "MDOs ODBC não podem ser excluídos", "TR_ODBC_NO_MORE": "Não há mais clientes odbc disponíveis", "TR_ODBC_NO_QUERIES": "Não há mais consultas disponíveis", "TR_ODBC_OPENDB_FAILED": "Não foi possível abrir o banco de dados", "TR_ODBC_QUERY_CHANGE": "A consulta SQL mudou; como resultado, as tags de consulta (MDOs) podem ser recriadas e qualquer mapeamento pode ser perdido", "TR_ODBC_ROOT_ODBC_SERVER_CONNECT": "Não foi possível conectar ao servidor ODBC: OPCServerName [{{arg1}}] = '{{arg2}}' OPCServerProgID [{{arg3}}] = '{{arg4}}' OPCServerNode [{{arg5} }] = '{{arg6}}' ", "TR_ODBC_SHOW_TABLES_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ShowTable :: Exceção capturada: {{arg3}}", "TR_ODBC_SHOW_TABLES_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ShowTable grave :: Exceção capturada", "TR_ODBCALIAS_NAME": "Nome", "TR_ODBCALIAS_NAME_DESC": "Nome alternativo para esta conexão ODBC", "TR_ODBCCONNECTION_STRING": "Cadeia de conexão ODBC", "TR_ODBCCONNECTION_STRING_DESC": "Especifica a cadeia de conexão para esta conexão ODBC", "TR_ODBCQUERY": "consulta SQL", "TR_ODBCQUERY_ALIAS_NAME": "<PERSON>me da consulta", "TR_ODBCQUERY_ALIAS_NAME_DESC": "O nome alternativo da consulta nesta conexão ODBC.", "TR_ODBCQUERY_ALWAYS_REFRESH_RS": "Sempre atualize o conjunto de registros", "TR_ODBCQUERY_ALWAYS_REFRESH_RS_DESC": "A consulta sempre lerá os dados do banco de dados quando o GetNextRecord / MoveToRecord MDO for alterado nessa conexão ODBC quando ODBCQueryAlwaysRefreshRS for verdadeiro. Se false, os dados serão lidos quando o ExecuteQuery mdo for alterado, mas não quando o GetNextRecord / MoveToRecord mdo for alterado. Observe que, se a tabela mudar no banco de dados, é necessário emitir um ExecuteQuery para atualizar o cache SDG local do conjunto de tabelas / registros ", "TR_ODBCQUERY_DESC": "A consulta SQL a ser executada nesta conexão ODBC.", "TR_OFFLINE_ACTIVATION": "Ativação offline", "TR_OFFLINE_ACTIVATION_NEW_LICENSE": "Ativação offline", "TR_OFFLINE_ACTIVATION_STEP1_TEXT": "Etapa 1: se este computador NÃO tiver acesso à Internet, clique neste botão para criar um arquivo C2V que pode ser usado para ativar a chave. Transfira o arquivo C2V para um computador com acesso à Internet e navegue até: ", "TR_OFFLINE_ACTIVATION_STEP2_TEXT": "Etapa 2: no portal do cliente, efetue login com a chave do produto e selecione a ativação offline. Faça o upload do arquivo C2V para o portal. Clique no botão Gerar e faça o download do arquivo V2C. Um arquivo V2C será salvo no computador conectado à Internet.  Transfira esse arquivo para este computador. Clique no botão Instalar V2C e navegue até o arquivo V2C. Consulte o Guia de licenciamento para mais detalhes. ", "TR_OK": "OK", "TR_OLD_VALUE": "Valor antigo", "TR_ONLINE_ACTIVATION": "Ativação online (requer acesso à Internet)", "TR_ONLINE_ACTIVATION_TEXT": "Se este computador tiver acesso à Internet, clique neste botão para ativar a chave do produto online.", "TR_ONLY_EXT_REF": "Mostrar apenas ExtRef", "TR_ONLY_EXT_REF_DESC": "Especifica se apenas ExtRef. Deve ser exibido", "TR_OPC": "OPC", "TR_OPC_61850_SERVER_CONTROL_IS_IN_MIDDLE_OPERATION_AND_CANNOT_BE_DELETED_NOW": "O controle SDO '{{arg1}}' está no meio de uma operação e não pode ser excluído no momento.", "TR_OPC_ADD_CAUSE_POINT": "Adicionar ponto de causa", "TR_OPC_ADD_CAUSE_POINT_DESC": "Adicionar ponto de causa", "TR_OPC_AE_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Falha ao carregar o elemento do espaço de endereço do OPC AE. Erro desconhecido.", "TR_OPC_AE_ITEM_LIST_UNAVAILABLE": "Lista de itens indisponível", "TR_OPC_AE_SERVER_LIST_UNAVAILABLE": "Lista de servidores indisponível", "TR_OPC_AESUBSCRIPTION_MASK": "Máscara de assinatura OPC Data Access AE", "TR_OPC_AESUBSCRIPTION_MASK_DESC": "Cada bit ativa (1) / desativa (0) um motivo pelo qual uma assinatura de item através do SDG OPC Data Access Server deve ativar as notificações de Alarme e Evento OPC para o item. Observe que essa máscara substitui todas as outras máscaras, se ativada. Se 0, nenhuma notificação de alarme e evento OPC ocorrerá como resultado de uma assinatura do item. ", "TR_OPC_CANCEL_REQUEST_POINT": "Cancelar ponto de solicitação", "TR_OPC_CANCEL_REQUEST_POINT_DESC": "Defina o ponto de solicitação de cancelamento", "TR_OPC_CANCEL_RESPONSE_POINT": "Cancelar ponto de resposta", "TR_OPC_CANCEL_RESPONSE_POINT_DESC": "Defina o ponto de resposta de cancelamento", "TR_OPC_CANT_ADD_MOD_SERVER_DUPLICATE": "Não foi possível adicionar o {{arg1}} MDO no servidor OPC: {{arg2}}. (Duplicado?)", "TR_OPC_CANT_ADD_SERVER_DUP": "Não foi possível adicionar o {{arg1}} MDO no servidor OPC: {{arg2}}. (Duplicado?)", "TR_OPC_CLIENT_CONNECT": "Não foi possível conectar-se ao servidor OPC:\n OPCServerName [{{arg1}}] = '{{arg2}}' \n OPCServerProgID [{{arg3}}] = '{{arg4}}' \n OPCServerNode [{{arg5}}] = '{{arg6}}' ", "TR_OPC_CLIENT_IS_EMPTY": "O cliente está vazio", "TR_OPC_CLIENT_IS_NOT_CONNECTED_CANNOT_SELECT_POINTS_UNTIL_CONNECTED.": "O cliente OPC {{arg1}} não está conectado - não pode selecionar pontos até estar conectado.", "TR_OPC_CLIENT_NAME": "Nome do cliente", "TR_OPC_COMMAND_TERM_POINT": "Ponto de comando", "TR_OPC_COMMAND_TERM_POINT_DESC": "Defina o ponto do termo de comando", "TR_OPC_DESC": "Registre mensagens de rastreio para OPC", "TR_OPC_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Falha ao carregar o elemento do espaço de endereço OPC. Erro desconhecido.", "TR_OPC_EXCEPTION_BROWSE_EVENT": "Ocorreu uma exceção grave no BrowseEventSpace", "TR_OPC_ITEM_LIST_UNAVAILABLE": "Lista de itens indisponível", "TR_OPC_MDO_OPTIONS": "Opções de MDO", "TR_OPC_MDO_OPTIONS_DESC": "Defina as opções do MDO", "TR_OPC_MDO_OPTIONS_SET": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_OPC_NO_MORE_CLIENTS": "Não há mais clientes opc disponíveis", "TR_OPC_OPERATE_REQUEST_POINT": "Operate Request point", "TR_OPC_OPERATE_REQUEST_POINT_DESC": "Defina o ponto de solicitação de operação", "TR_OPC_OPERATE_RESPONSE_POINT": "Operar ponto de resposta", "TR_OPC_OPERATE_RESPONSE_POINT_DESC": "Defina o ponto de resposta da operação", "TR_OPC_SELECT_REQUEST_POINT": "Selecione o ponto de solicitação", "TR_OPC_SELECT_REQUEST_POINT_DESC": "Defina o ponto de solicitação de seleção", "TR_OPC_SELECT_RESPONSE_POINT": "Selecione o ponto de resposta", "TR_OPC_SELECT_RESPONSE_POINT_DESC": "Definir ponto de resposta de seleção", "TR_OPC_SERVER_LIST_UNAVAILABLE": "Lista de servidores indisponível", "TR_OPC_SERVER_PROG_ID_OR_XML_SERVER_PATH": "ID do programa do servidor ou caminho do servidor XML", "TR_OPC_SERVER_PROG_ID_OR_XML_SERVER_PATH_DESC": "Defina o ID do Prog do Servidor ou o Caminho do Servidor XML", "TR_OPC_SET_MDO_OPTIONS": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_OPC_STARTUP": "Inicialização do OPC", "TR_OPC_STARTUP_DESC": "Registre as mensagens de rastreamento para a inicialização do opc", "TR_OPC_STD_EXCEPTION_BROWSE_EVENT": "Ocorreu uma exceção no BrowseEventSpace: {{arg1}}", "TR_OPC_UA_CLIENT_CERTIFICATE_FILE": "Arquivo de certificado", "TR_OPC_UA_CLIENT_CERTIFICATE_FILE_DESC": "Especifica o arquivo de certificado Opc UA do cliente", "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES": "Descartar alterações mais antigas", "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES_DESC": "Quando ocorrerem mais alterações de dados do que este item pode armazenar, o valor mais antigo será descartado.", "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH": "Máximo de notificações por publicação", "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH_DESC": "Número máximo de itens reportados; se 0, o servidor relatará o maior número possível.", "TR_OPC_UA_CLIENT_NAME": "Nome deste cliente do OPC UA", "TR_OPC_UA_CLIENT_NAME_DESC": "Especifica o nome deste cliente do OPC UA. Os nomes devem ser exclusivos. Se não especificado, o padrão será OpcUaClient-'index '", "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE": "Arquivo de Chave Privada", "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE_DESC": "Especifica o arquivo de chave privada Opc UA do cliente", "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE": "Frase-chave da chave privada", "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE_DESC": "Especifica a senha da chave privada do Opc UA do cliente", "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE": "Tamanho da fila de publicação", "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE_DESC": "O número de alterações que podem ser armazenadas para esse item no servidor até o próximo ciclo de publicação.", "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL": "Intervalo de publicação (ms)", "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL_DESC": "Hora em que o servidor enviará modificações.", "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT": "<PERSON><PERSON><PERSON> contagem", "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT_DESC": "Especifica a contagem de novas tentativas de reconexão para o OPC UA Client (0 = tentativa de reconexão permanente) A configuração da atualização do cliente MDO fará com que o contador de limite interno seja redefinido para 0, resultando em 0 tentativas de conexão continuadas no OPC Servidor UA. ", "TR_OPC_UA_CLIENT_RECONNECT_TIME": "Reconecte o tempo limite (ms)", "TR_OPC_UA_CLIENT_RECONNECT_TIME_DESC": "Especifica o tempo limite de reconexão para o OPC UA Client (0 = sem reconexão)", "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL": "Intervalo de amostragem (ms)", "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL_DESC": "Intervalo que o item armazena alterações, o padrão é 5 por segundo.", "TR_OPC_UA_CLIENT_SECURITY_MODE": "Modo de segurança", "TR_OPC_UA_CLIENT_SECURITY_MODE_DESC": "Seleciona o modo de segurança para uma conexão do OPC UA Client.", "TR_OPC_UA_CLIENT_SECURITY_POLICY": "Política de Segurança", "TR_OPC_UA_CLIENT_SECURITY_POLICY_DESC": "Seleciona a política de segurança para uma conexão do OPC UA Client.", "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE": "Tipo de <PERSON>", "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE_DESC": "Seleciona o tipo de token de segurança para uma conexão do OPC UA Client.", "TR_OPC_UA_CLIENT_SERVER_URL": "URL do servidor OPC UA ao qual se conectar", "TR_OPC_UA_CLIENT_SERVER_URL_DESC": "Especifica o URL do OPC UA Server ao qual se conectar.", "TR_OPC_UA_CLIENT_SESSION_TIMEOUT": "Tempo limite da sessão (ms)", "TR_OPC_UA_CLIENT_SESSION_TIMEOUT_DESC": "Tempo até a sessão expirar se não houver comunicação.", "TR_OPC_UA_CONNECTION_SETTINGS": "Configurações de conexão do OPC UA", "TR_OPC_UA_SECURITY_SETTINGS": "Configurações de segurança do OPC UA", "TR_OPC_UA_SERVER_LIST_UNAVAILABLE": "Lista de servidores indisponível", "TR_OPCAE_CLIENT_CONNECT": "Não foi possível conectar ao servidor OPC AE: \n OPCServerName [{{arg1}}] = '{{arg2}}' \n OPCServerProgID [{{arg3}}] = '{{arg4}} '\n OPCServerNode [{{arg5}}] =' {{arg6}} '", "TR_OPCAE_CLIENT_DUPLICATE": "Não é possível adicionar o cliente OPC AE: '{{arg1}}'. Nome duplicado. ", "TR_OPCAE_NO_MORE": "Não há mais clientes opc AE disponíveis", "TR_OPCAESERVER_BUFFER_TIME": "Tempo de buffer", "TR_OPCAESERVER_BUFFER_TIME_DESC": "O tempo do buffer, especificado em milissegundos, indica o número de vezes que as notificações de eventos podem ser enviadas para o objeto de inscrição. Este parâmetro é o tempo mínimo distribuído entre duas notificações sucessivas de eventos. O valor 0 significa que todas as notificações de eventos devem ser enviadas imediatamente do servidor. Se o parâmetro MaxSize for maior que 0, ele instrui o servidor a enviar notificações de eventos mais rapidamente, a fim de manter o tamanho do buffer dentro de MaxSize. Especifica o tempo de buffer para a assinatura do OPC AE Server ", "TR_OPCAESERVER_MAX_SIZE": "<PERSON><PERSON><PERSON>", "TR_OPCAESERVER_MAX_SIZE_DESC": "Este parâmetro é o número máximo de eventos que podem ser especificados em uma chamada. O valor 0 significa que não há restrição para o número de eventos. Observe que, se o valor MaxSize for maior que 0, os eventos poderão ser enviados mais rapidamente do servidor que através do parâmetro BufferTime. Especifica o tamanho máximo para a assinatura do OPC AE Server ", "TR_OPCAESERVER_NAME": "Nome do servidor", "TR_OPCAESERVER_NAME_DESC": "Nome opcional para o servidor OPC AE se conectar, se não especificado, use o valor de OPCAEserverProgID. É altamente recomendável que este parâmetro seja definido como um cliente externo do OPC AE, talvez não seja possível procurar tags no SDG Servidor OPC AE se o nome do servidor OPC AE contiver um ou mais caracteres de ponto ('.'). Para contornar esse problema, defina esse nome alternativo (sem pontos para o servidor OPC AE externo e faça referência ao servidor por seu apelido. ", "TR_OPCAESERVER_NODE": "Nome do nó do OPC AE Server", "TR_OPCAESERVER_NODE_DESC": "Especifica o nome do nó do OPC AE Server ao qual se conectar", "TR_OPCAESERVER_PROG_ID": "Prog Prog", "TR_OPCAESERVER_PROG_ID_DESC": "Especifica o ID do PROG do OPC AE Server ao qual se conectar", "TR_OPCAESERVER_RECONNECT_RETRY_COUNT": "Reconecte a contagem de novas tentativas", "TR_OPCAESERVER_RECONNECT_RETRY_COUNT_DESC": "Especifica a contagem de novas tentativas de reconexão para o servidor externo OPC AE (0 = tentativa de reconexão permanente) A configuração da atualização do cliente MDO fará com que o contador de limite interno seja redefinido para 0, resultando em tentativas de conexão continuada. Servidor OPC AE. ", "TR_OPCAESERVER_RECONNECT_TIME": "Reconecte o tempo limite (ms)", "TR_OPCAESERVER_RECONNECT_TIME_DESC": "Especifica o tempo limite de reconexão para o OPC AE Server (0 = sem reconexão)", "TR_OPCAETIME_SOURCE": "Origem da hora do evento OPC", "TR_OPCAETIME_SOURCE_DESC": "Especifica a fonte da marcação de tempo para os pontos de dados de evento e alarme OPC. Os valores possíveis são Atualização ou Relatado, em que Atualização significa a hora, relativa ao relógio do sistema SDG, na qual o ponto de dados foi atualizado pela última vez. Relatado especifica o horário relatado do evento que causou a alteração dos dados. O tempo relatado será relativo ao relógio do sistema do dispositivo escravo remoto, exceto na inicialização em que o relógio do sistema do SDG é usado até o primeiro evento com o tempo ser recebido. É importante observar que a pesquisa de dados estáticos ou eventos recebidos que não especificam um horário relatado podem fazer com que o valor de um ponto de dados específico seja alterado sem que um evento seja gerado, portanto, o horário do evento não será alterado.  Nota: este parâmetro especifica como o tempo será fornecido pelo servidor SDG OPC AE ", "TR_OPCAEUSE_SIMPLE_EVENTS": "Usar alarme OPC e eventos simples", "TR_OPCAEUSE_SIMPLE_EVENTS_DESC": "Se verdadeiro, todos os eventos do OPC AE são relatados como eventos simples.", "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE": "Taxa de atualização do status do cliente OPC (ms)", "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE_DESC": "O intervalo no qual um cliente SDG OPC solicita informações de status de seu servidor.  Se o cliente não desejar solicitar atualizações de status, configure-o como 0. ", "TR_OPCDA_CLIENT_DUPLICATE": "Não é possível adicionar o cliente OPC DA: '{{arg1}}'. Nome duplicado. ", "TR_OPCSERVER_NAME": "Nome", "TR_OPCSERVER_NAME_DESC": "Nome opcional para o servidor OPC se conectar, se não especificado, use o valor de OPCserverProgID. É altamente recomendável que esse parâmetro seja definido como um cliente OPC externo, talvez não seja possível procurar tags no servidor SDG OPC se o nome do servidor OPC contiver um ou mais caracteres de ponto ('.'). Para contornar esse problema, defina esse nome alternativo (sem pontos para o servidor OPC externo e faça referência ao servidor por seu apelido. ", "TR_OPCSERVER_NODE": "Nome do nó", "TR_OPCSERVER_NODE_DESC": "Especifica o nome do nó do OPC Server ao qual se conectar", "TR_OPCSERVER_READ_PROPERTIES_TIME": "Propriedades do período de leitura (ms)", "TR_OPCSERVER_READ_PROPERTIES_TIME_DESC": "Especifica o período em que as propriedades são lidas (0 = não lê)", "TR_OPCSERVER_RECONNECT_DELAY": "Atraso de reconexão (ms)", "TR_OPCSERVER_RECONNECT_DELAY_DESC": "Se diferente de zero, especifica o tempo (em ms) para aguardar para redefinir a contagem de novas tentativas e continuar tentando se reconectar. Se zero, será ignorado e as novas tentativas não continuarão além do valor da contagem de novas tentativas. ", "TR_OPCSERVER_RECONNECT_RETRY_COUNT": "<PERSON><PERSON><PERSON> contagem", "TR_OPCSERVER_RECONNECT_RETRY_COUNT_DESC": "Especifica a contagem de novas tentativas de reconexão para o Servidor OPC externo (0 = tentativa de reconexão permanente) A configuração da atualização do cliente MDO fará com que o contador de limite interno seja redefinido para 0, resultando em tentativas de conexão continuada ao OPC servidor.", "TR_OPCSERVER_RECONNECT_TIME": "Reconecte o tempo de nova tentativa (ms)", "TR_OPCSERVER_RECONNECT_TIME_DESC": "Especifica o tempo de nova tentativa de reconexão para o cliente OPC tentar se reconectar ao servidor (0 = sem reconexão)", "TR_OPCSERVER_UPDATE_RATE": "Taxa de atualização (ms)", "TR_OPCSERVER_UPDATE_RATE_DESC": "Especifica a taxa de atualização para o OPC Server", "TR_OPCTIME_SOURCE": "OPC Time Source", "TR_OPCTIME_SOURCE_DESC": "Especifica a fonte da marca de tempo para pontos de dados OPC. Os valores possíveis são Atualização ou Relatado, em que Atualização significa a hora, relativa ao relógio do sistema SDG, na qual o ponto de dados foi atualizado pela última vez. Relatado especifica o horário relatado do evento mais recente que causou a alteração dos dados. O tempo relatado será relativo ao relógio do sistema do dispositivo escravo remoto, exceto na inicialização em que o relógio do sistema do SDG é usado até o primeiro evento com o tempo ser recebido. É importante observar que a pesquisa de dados estáticos ou eventos recebidos que não especificam um horário relatado podem fazer com que o valor de um ponto de dados específico seja alterado sem que um evento seja gerado, portanto, o horário do evento não será alterado.  Nota: este parâmetro especifica como o tempo será fornecido pelo SDG OPC DA Server ", "TR_OPCUA_CLIENT_DUPLICATE": "Não é possível adicionar o cliente OPC UA: '{{arg1}}'. Nome duplicado. ", "TR_OPCUA_NO_MORE": "Não há mais clientes OPC UA disponíveis", "TR_OPCXML_DA_SERVER_NAME": "OPC Xml Da Server Name", "TR_OPCXML_DA_SERVER_NAME_DESC": "Especifica o nome usado por um cliente OPC XML DA para conectar-se ao SDG.  Com os padrões do arquivo INI, um opc xml local da client pode se conectar ao SDG da seguinte maneira: http: // localhost: 8081 / SDG ", "TR_OPCXML_DA_SERVER_PORT": "Porta do servidor OPC Xml DA", "TR_OPCXML_DA_SERVER_PORT_DESC": "A porta TCP / IP para o servidor OPC XML DA.  Com os padrões do arquivo INI, um opc xml local da client pode se conectar ao SDG da seguinte maneira: http: // localhost: 8081 / SDG ", "TR_OPEN_FILE": "{{arg1}} ({{arg2}}) Registre muito int (mais de {{arg3}} bytes)", "TR_OPERATION_DESCRIPTION": "Especifique a operação", "TR_OPERATION_HELP": "Operação Ajuda", "TR_OPERATION_LIST": "Operações", "TR_OPERATION_LIST_DESC": "Lista de operações possíveis", "TR_OPTION": "Opção", "TR_OPTIONAL_ITEMS_TO_INCUDE_IN_REPORT": "Itens de operação a serem incluídos no relatório", "TR_OPTIONS": "Opções", "TR_OPTIONS_EDITOR": "Editor de opções", "TR_OVERALL": "G<PERSON>", "TR_OVERFLOW": "OVERFLOW", "TR_OVERFLOW_DESC": "Estouro / rolagem", "TR_PARAMETERS": "Parâmetros", "TR_PARSE_OPTION_VALIDATION_FAILED": "Falha ao validar o nome da categoria / condição / sub condição '{{arg1}}'", "TR_PARSE_OPTION_VALIDATION_FAILED2": "Falha ao validar o nome da categoria / condição / sub condição '{{arg1}}'", "TR_PASSWORD": "<PERSON><PERSON>", "TR_PASSWORD_IS_REQUIRED": "A senha é necessária.", "TR_PASSWORD_IS_REQUIRED_CHARACTERS_MINIMUM": "A senha é necessária (6 caracteres no mínimo).", "TR_PAUSE": "Pausar", "TR_PERFORMANCE_DESC": "<PERSON><PERSON><PERSON><PERSON>", "TR_PERFORMANCE_VIEW": "Visualização de desempenho", "TR_PHYS_COM_BAUD": "Taxa de transmissão", "TR_PHYS_COM_BAUD_DESC": "Define a taxa de transmissão para o PhysComChannel serial correspondente", "TR_PHYS_COM_CHANNEL": "Canal físico, por exemplo, porta COM, nome do IP / nó remoto", "TR_PHYS_COM_CHANNEL_DESC": "Define o canal de comunicação. \nExemplos: 'COM1', '************' ou 'ROCKYHILLSUBSTATION' (para um canal TCP / IP) Na porta serial - é o nome da porta com o qual usar No cliente TCP - este é o nome do host ou o endereço IP para configurar a conexão TCP para No servidor TCP - esse é o nome do host ou o endereço IP para aceitar a conexão TCP de Talvez *.**. * indicando aceitar conexão de qualquer cliente. Também pode ser uma lista de ';' nomes de host ou endereços IP separadospara permitir conexões. No dispositivo de ponto final duplo TCP - este é o nome do host ou o endereço IP para aceitar ou conectar a conexão TCP. Também pode ser uma lista de ';' ou ',' nomes de host ou endereços IP separados para permitir conexões, tentarão se conectar apenas ao primeiro da lista.", "TR_PHYS_COM_CHNL_NAME": "Nome do canal", "TR_PHYS_COM_CHNL_NAME_DESC": "Nome alternativo para o canal de comunicações. Deve ser especificado e ser único. ", "TR_PHYS_COM_DATA_BITS": "Bits de dados", "TR_PHYS_COM_DATA_BITS_DESC": "Define o número de bits de dados para o PhysComChannel serial correspondente", "TR_PHYS_COM_DEST_UDPPORT": "Porta UDP de destino", "TR_PHYS_COM_DEST_UDPPORT_DESC": "No Master - se TCP e UDP estiver configurado, isso especificará a porta UDP / IP de destino para enviar solicitações de transmissão em datagramas UDP para. Se UDP ONLY estiver configurado, isso especificará a porta UDP / IP de destino para enviar todas as solicitações nos datagramas UDP para. Isso deve corresponder ao 'localUDPPort' no escravo. Slave - se TCP e UDP não forem usados. Se UDP ONLY estiver configurado, isso especificará o destino UDP / Porta IP para a qual enviar respostas. Pode ser WINTCP_UDP_PORT_SRC = 2, indicando usar a porta src de uma solicitação UDP recebida do mestre. ", "TR_PHYS_COM_DIAL_OUT": "Ativar discagem", "TR_PHYS_COM_DIAL_OUT_DESC": "Se verdadeiro, o modem será configurado para discar e atender chamadas telefônicas. Se falso, o modem será configurado apenas para atender chamadas telefônicas. ", "TR_PHYS_COM_DUAL_END_POINT_IP_PORT": "Número da porta IP do ponto final duplo", "TR_PHYS_COM_DUAL_END_POINT_IP_PORT_DESC": "Se o Dual End Point for suportado, uma escuta será feita no PhysComIpPort e uma solicitação de conexão será enviada para esse número de porta quando necessário. Isso deve corresponder ao ipPort no dispositivo remoto. O estado normal é escutar, conexão será feita quando houver dados a serem enviados. ", "TR_PHYS_COM_INIT_UNSOL_UDPPORT": "Porta UDP não solicitada", "TR_PHYS_COM_INIT_UNSOL_UDPPORT_DESC": "No master - Não usado. No Slave - se TCP e UDP não forem usados. Se o UDP ONLY estiver configurado, isso especificará a porta UDP / IP  de destino para enviar a resposta nula não solicitada inicial. Após receber uma solicitação UDP do mestre, destUDPPort (que) pode indicar o uso da porta src) será usado para todas as respostas. Isso não deve ser WINTCP_UDP_PORT_NONE (0), WINTCP_UDP_PORT_ANY (1) ou WINTCP_UDP_PORT_SRC (2) para um escravo que suporta UDP. ", "TR_PHYS_COM_IP_CONNECT_TIMEOUT": "Tempo limite da conexão (ms)", "TR_PHYS_COM_IP_CONNECT_TIMEOUT_DESC": "Define o tempo limite da conexão TCP / IP a ser usado se PhysComChannel especificar um endereço IP. Para IEC 60870-5-104, este é o parâmetro T0. Observe que esse parâmetro deve ser definido como o valor mais baixo que funcione de maneira confiável.  Nos casos em que não é possível estabelecer uma conexão, o processo será bloqueado pelo período especificado. ", "TR_PHYS_COM_IP_MODE": "Modo IP", "TR_PHYS_COM_IP_MODE_DESC": "Define o modo de conexão a ser usado se PhysComChannel especificar um endereço IP", "TR_PHYS_COM_IP_PORT": "Número da porta IP", "TR_PHYS_COM_IP_PORT_DESC": "Define o número da porta TCP / IP a ser usada se PhysComChannel especificar um endereço IP", "TR_PHYS_COM_LOCAL_IP_ADDRESS": "Endereço IP local", "TR_PHYS_COM_LOCAL_IP_ADDRESS_DESC": "No cliente - Endereço ao qual ligar o soquete. Isso permite que você especifique qual endereço IP enviar como endereço de origem nas mensagens TCP se houver vários endereços IP, por exemplo, quando houver várias placas de interface de rede (NICs).  se '0.0.0.0' for usado, a pilha TCP escolherá qual endereço IP usar. Se um endereço que não estiver presente for especificado, a ligação falhará e a pilha TCP escolherá qual endereço. Este endereço também é usado para o DNP Master ao enviar datagramas UDP. A vinculação deste endereço não garante o envio em uma NIC específica. Isso é determinado pela tabela de roteamento IP, dependendo do destino Endereço IP. Você pode exibir esta tabela digitando 'route print' em  uma janela de comando. É possível adicionar rotas manuais para fazer com que uma determinada  NIC seja usada. 'rota adicione gateway destIPAddress'. Inserir 'rota?' 'para obter mais detalhes. No servidor - não usado atualmente para ouvintes. (Nota: esse endereço É usado para DNP Outstation se configurado apenas para UDP) ", "TR_PHYS_COM_LOCAL_UDPPORT": "Porta UDP local", "TR_PHYS_COM_LOCAL_UDPPORT_DESC": "Porta local para enviar e receber datagramas UDP em. Se estiver definido como WINTCP_UDP_PORT_NONE = 0, o UDP não será ativado. Para a rede DNP, o UDP deve ser suportado. Não é necessário para nenhum dos dispositivos atuais. Protocolos IEC ou modbus. No mestre - Se estiver definido como WINTCP_UDP_PORT_ANY = 1, uma porta disponível não especificada será usada. No escravo - deve ser escolhido para corresponder à porta UDP que o mestre usa para enviar o datagrama mensagens para. Não deve ser WINTCP_UDP_PORT_ANY = 1 ou WINTCP_UDP_PORT_SRC = 2. ", "TR_PHYS_COM_MBPCARD_NUMBER": "Número do cartão MBP do canal", "TR_PHYS_COM_MBPCARD_NUMBER_DESC": "Define o número do cartão Modbus Plus a ser usado se PhysComType especificar um canal Modbus Plus.", "TR_PHYS_COM_MBPRECIEVE_TIMEOUT": "Tempo limite de recebimento do Modbus Plus", "TR_PHYS_COM_MBPRECIEVE_TIMEOUT_DESC": "Define o tempo limite do Modbus Plus Receive a ser usado se PhysComType especificar um canal Modbus Plus.", "TR_PHYS_COM_MBPROUTE_PATH": "Caminho da rota Modbus Plus", "TR_PHYS_COM_MBPROUTE_PATH_DESC": "Especifica o caminho da rota Modbus Plus a ser usado se PhysComType especificar um canal Modbus Plus. Este parâmetro se aplica apenas ao modbus plus masters. ", "TR_PHYS_COM_MBPSLAVE_PATH": "Caminho do escravo do Modbus Plus", "TR_PHYS_COM_MBPSLAVE_PATH_DESC": "Define o caminho secundário do Modbus Plus a ser usado se PhysComType especificar um canal Modbus Plus.  Este parâmetro se aplica apenas ao modbus plus slaves. ", "TR_PHYS_COM_MODBUS_RTU": "Ativar Modbus RTU", "TR_PHYS_COM_MODBUS_RTU_DESC": "Se verdadeiro, o canal será configurado para comunicação serial modbus RTU.", "TR_PHYS_COM_MODEM_ANSWER_TIME": "Tempo de espera da resposta (segundos)", "TR_PHYS_COM_MODEM_ANSWER_TIME_DESC": "Define a quantidade de tempo que o modem aguarda uma resposta após discar (em segundos)", "TR_PHYS_COM_MODEM_IDLE_TIME": "Tempo de espera para desligar após ocioso (segundos)", "TR_PHYS_COM_MODEM_IDLE_TIME_DESC": "Define a quantidade de tempo que o modem espera para desligar após o canal ficar ocioso (em segundos)", "TR_PHYS_COM_MODEM_POOL": "Pool de modem", "TR_PHYS_COM_MODEM_POOL_DESC": "O pool de modems que este canal usa", "TR_PHYS_COM_MODEM_REDIAL_LIMIT": "Re-dial times", "TR_PHYS_COM_MODEM_REDIAL_LIMIT_DESC": "Define o número de vezes que o modem discará antes de concluir que a comunicação falhou. Se a discagem falhar (ou seja, o limite for atingido), o canal do modem MDO ChannelRedialLimitControl será TRUE, configure este MDO (ChannelRedialLimitControl) como false para iniciar a discagem novamente. Um valor 0 fará com que o modem tente discar para sempre. Observe que definir esse valor como 0 amarrará o modem para sempre, caso a conexão nunca seja estabelecida. ", "TR_PHYS_COM_PARITY": "Paridade", "TR_PHYS_COM_PARITY_DESC": "Define a paridade para o PhysComChannel serial correspondente", "TR_PHYS_COM_PHONE_NUMBER": "Número de telefone", "TR_PHYS_COM_PHONE_NUMBER_DESC": "Número de telefone para discar em um modem", "TR_PHYS_COM_PROTOCOL": "Tipo de protocolo de canal", "TR_PHYS_COM_PROTOCOL_DESC": "Define o protocolo para o canal Aplica-se a todos os tipos de canais físicos", "TR_PHYS_COM_SERIAL_MODE": "Controle de fluxo", "TR_PHYS_COM_SERIAL_MODE_DESC": "Define o modo do canal serial: 'none' - não usa controle de fluxo. 'Hardware' - usa controle de fluxo de hardware. 'Windows' - usa controle de fluxo e parâmetros seriais (taxa de transmissão, paridade, etc.) conforme especificado com o comando MODE. ", "TR_PHYS_COM_STOP_BITS": "Stop Bits", "TR_PHYS_COM_STOP_BITS_DESC": "Define o número de bits de parada para o PhysComChannel serial correspondente", "TR_PHYS_COM_VALIDATE_UDPADDRESS": "Validar endereço UDP", "TR_PHYS_COM_VALIDATE_UDPADDRESS_DESC": "Validar ou não o endereço de origem do datagrama UDP recebido.", "TR_PHYS_OFFLINE_POLL_PERIOD": "Período de pesquisa off-line (ms)", "TR_PHYS_OFFLINE_POLL_PERIOD_DESC": "O período em que as sessões no canal são pesquisadas se estiverem offline.  Observe que este parâmetro se aplica apenas às sessões mestre serial DNP e Modbus neste canal. Um valor zero (0) desativará esse recurso. ", "TR_PLEASE_SELECT_A_FILE": "Selecione um arquivo", "TR_PLEASE_SELECT_A_PROPERTY": "Selecione uma propriedade", "TR_PLEASE_SELECT_AT_LEAST_ONE_ROLE": "Selecione pelo menos uma função.", "TR_POINT_IN_USE_CANT_DELETE": "MDO: '{{arg1}}' é mapeado para pontos escravos ou principais ou é usado em uma equação, não pode excluir", "TR_POINT_MAP": "Mapa de pontos", "TR_POINT_MAP_FILE": "Arquivo de mapa de pontos", "TR_POINT_MAP_FILE_DESC": "Especifica o arquivo de mapeamento de dados de pontos.  Este valor pode ter o caminho completo para o arquivo ou apenas o nome do arquivo.  Se apenas o nome do arquivo for especificado, ele deverá estar localizado no mesmo diretório que o arquivo INI. ", "TR_POINT_TYPE": "Tipo de ponto", "TR_POINT_TYPE_DESC": "Tipo de ponto", "TR_POLLED_DATA_SET_NAME": "Nome do conjunto de dados pesquisado", "TR_POLLED_DS_DS_ID": "Nome do conjunto de dados", "TR_POLLED_DS_DS_ID_DESC": "Especifique o nome do conjunto de dados", "TR_POLLED_DS_ID": "Nome do conjunto de dados pesquisados", "TR_POLLED_DS_ID_DESC": "Especifique o nome do conjunto de dados pesquisados", "TR_POLLED_DS_PERIOD": "<PERSON><PERSON><PERSON> pes<PERSON>", "TR_POLLED_DS_PERIOD_DESC": "Especifica o período pesquisado.", "TR_POLLED_DS_SET_PERIOD": "DS Transfer Set Count", "TR_POLLED_DS_SET_PERIOD_DESC": "Especifique a contagem do conjunto de transferências DS", "TR_POLLED_POINT_SET_DESC": "Especifique o período da pesquisa", "TR_POLLED_POINT_SET_ID": "Nome do conjunto de pontos pesquisados", "TR_POLLED_POINT_SET_ID_DESC": "Especifique o nome do conjunto de pontos pesquisados", "TR_POLLED_POINT_SET_PERIOD": "Período da votação", "TR_POLLED_POINT_SET_PERIOD_DESC": "Especifique o período do conjunto de pontos", "TR_PRODUCT_KEY_EXHAUSTED": "Chave do produto esgotada", "TR_PRODUCT_KEY_FOR_NEW_LICENSE": "Esta chave do produto é para uma nova licença", "TR_PRODUCT_KEY_FOR_UPDATE_LICENSE": "Esta chave do produto atualizará a licença atual (ou seja, atualização do plano de manutenção)", "TR_PROG_ID": "Prog Prog", "TR_PROPERTIES": "<PERSON><PERSON><PERSON><PERSON>", "TR_PROPERTY_DESC": "Defina a propriedade", "TR_PROTO_ANALYZER": "Analis<PERSON> de <PERSON>o", "TR_PROTO_ANALYZER_DESC": "Registrar mensagens de rastreio para o Protocol Analyzer", "TR_PROTOCOL_ANALYSER": "Analis<PERSON> de <PERSON>o", "TR_PROTOCOL_ANALYSER_PARAMETERS": "Parâmetros do analisador de protocolo", "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT": "Limpar antes de ativar ao reconectar", "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Especifica se a limpeza antes da ativação na reconexão está ativa.", "TR_QUAL_CHNG_MON": "Alteração da qualidade", "TR_QUALITY": "Qualidade", "TR_QUALITY_CHANGE": "Alteração da qualidade", "TR_QUALITY_CHANGE_DESC": "Especifica se a alteração de qualidade está ativa.", "TR_QUALITY_ENUM_BLOCKED_DESC": "Bloquear", "TR_QUALITY_ENUM_GOOD_DESC": "Bo<PERSON>", "TR_QUALITY_ENUM_IN_TRANSIT_DESC": "em trânsito", "TR_QUALITY_ENUM_INVALID_DESC": "<PERSON>v<PERSON><PERSON><PERSON>", "TR_QUALITY_ENUM_INVALID_TIME_DESC": "<PERSON><PERSON>", "TR_QUALITY_ENUM_NOT_TOPICAL_DESC": "Não tópico", "TR_QUALITY_ENUM_OVERFLOW_DESC": "<PERSON><PERSON><PERSON>", "TR_QUALITY_ENUM_REF_ERROR_DESC": "Erro de referência", "TR_QUALITY_ENUM_SUBSTITUTED_DESC": "Substituído", "TR_QUALITY_ENUM_TEST_DESC": "<PERSON>e", "TR_QUALITY_ENUM_UNINITIALIZED_DESC": "Não inicializado", "TR_QUERY_RESULT_DESC": "Especifica os resultados da consulta", "TR_QUERY_RESULTS": "Resultados da consulta", "TR_RCB_DATASET_NAME": "Nome do conjunto de dados", "TR_RCB_DATASET_NAME_DESC": "Especifique o nome do conjunto de dados", "TR_RCB_LIST": "Lista de relatórios", "TR_RCB_LIST_DESC": "Especifica a lista de relatórios atual.", "TR_RCB_NAME": "Nome do relatório", "TR_RCB_NAME_DESC": "Especifique o nome do relatório", "TR_RE_START_MONITOR": "Reinicie o Monitor", "TR_READ": "<PERSON>r", "TR_READ_CONFIG_FROM_SERVER": "Ler configuração do servidor", "TR_READ_CONFIG_FROM_SERVER_DESC": "Leia a configuração do servidor.", "TR_READ_DESC": "<PERSON><PERSON>", "TR_READ_GOOSE": "Aviso: Não foi possível ler o relatório do ganso '{{arg1}}', erro = {{arg2}}", "TR_READ_POINT_MAP_AT_STARTUP": "Mapa de pontos de leitura na inicialização", "TR_READ_POINT_MAP_AT_STARTUP_DESC": "se true, o arquivo de mapeamento de pontos será lido na inicialização", "TR_REASON_FOR_INCLUSION": "Motivo da inclusão", "TR_REASON_FOR_INCLUSION_DESC": "Especifica se o motivo da inclusão está ativo.", "TR_RECONNECT_RETRY_COUNT": "Reconecte a contagem de novas tentativas", "TR_RECONNECT_TIME": "Tempo de reconexão (ms)", "TR_RECONNECTION_SETTINGS": "Configurações de reconexão", "TR_REF_ERROR": "REF_ERROR", "TR_REF_ERROR_DESC": "Erro de referência (ADC)", "TR_REFRESH": "REFRESH", "TR_REFRESH_AREA_SPACE": "Atualizar espaço na área", "TR_REFRESH_AREA_SPACE_DESC": "Atualizar espaço na área", "TR_REFRESH_DESC": "Os dados estão sendo atualizados pela fonte de dados sem solicitação direta.  Nenhuma mudança é necessariamente indicada. ", "TR_REFRESH_ITEM_PARENT": "Atualizar item", "TR_REFRESH_ITEM_PARENT_DESC": "Atualizar item", "TR_REFRESH_LIST_DESC": "<PERSON><PERSON><PERSON><PERSON> lista", "TR_REFRESH_PROPERTIES": "<PERSON><PERSON><PERSON><PERSON> propriedades", "TR_REFRESH_PROPERTIES_DESC": "<PERSON><PERSON><PERSON><PERSON> propriedades", "TR_REFRESH_SERVER_LIST": "Atualizar lista de servidores", "TR_REGISTER": "Registrar", "TR_REMEMBER_ME": "Lembre-se de mim", "TR_REMOTE_IP_NODE_NAME": "Nome do IP / <PERSON><PERSON>", "TR_REMOVE_SUBSCRIPTION": "Remover assinatura", "TR_REMOVE_SUBSCRIPTION_DESC": "Remover assinatura", "TR_REPORT_BY_EXCEPTION": "Relatório por exceção", "TR_REPORT_BY_EXCEPTION_DESC": "Especifica o relatório atual por exceção.", "TR_REPORT_NAME": "Nome do relatório", "TR_REPORT_NONE_LEFT": "Não há mais relatórios IEC 61850 disponíveis", "TR_REQUEST_DATA_TYPE": "Solicitar tipo de dados", "TR_REQUEST_DATA_TYPE_DESC": "Defina o tipo de dados da solicitação", "TR_REQUESTED": "REQUESTED", "TR_REQUESTED_DESC": "Os dados estão sendo atualizados porque foram solicitados.", "TR_REQUIRES_RESTART": "Requer reinicialização", "TR_RESUMED": "<PERSON><PERSON><PERSON><PERSON>", "TR_RETRY_ENABLE_COUNT": "Repetir Habilitar Contagem (-1 = para sempre, 0 = nunca)", "TR_RETRY_ENABLE_COUNT_DESC": "Especifica a contagem atual de ativação de nova tentativa.", "TR_RETRY_ENABLE_PERIOD": "Repetir período de ativação (ms)", "TR_RETRY_ENABLE_PERIOD_DESC": "Especifica o período de ativação da nova tentativa atual.", "TR_RETRY_FAILED_TRANSACTION": "Repetir transação com falha", "TR_ROLE": "Funçõe", "TR_RR_COILS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_COILS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_COILS_START_INDEX": "Iniciar <PERSON><PERSON>", "TR_RR_COILS_START_INDEX_DESC": "Iniciar <PERSON><PERSON>", "TR_RR_DISCRETE_INPUTS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_DISCRETE_INPUTS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_DISCRETE_INPUTS_START_INDEX": "Iniciar <PERSON><PERSON>", "TR_RR_DISCRETE_INPUTS_START_INDEX_DESC": "Iniciar <PERSON><PERSON>", "TR_RR_HOLDING_REGISTERS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_HOLDING_REGISTERS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_HOLDING_REGISTERS_START_INDEX": "Iniciar <PERSON><PERSON>", "TR_RR_HOLDING_REGISTERS_START_INDEX_DESC": "Iniciar <PERSON><PERSON>", "TR_RR_INPUT_REGISTERS_END_INDEX": "<PERSON><PERSON><PERSON>", "TR_RR_INPUT_REGISTERS_END_INDEX_DESC": "<PERSON><PERSON><PERSON>", "TR_RR_INPUT_REGISTERS_START_INDEX": "Iniciar <PERSON><PERSON>", "TR_RR_INPUT_REGISTERS_START_INDEX_DESC": "Iniciar <PERSON><PERSON>", "TR_RSA": "TLS RSA", "TR_RSA_PRIVATE_KEY_FILE": "Arquivo de chave privada RSA", "TR_RSA_PRIVATE_KEY_PASS_PHRASE": "RSA Private PassPhrase", "TR_RSA_PUBLIC_CERT_FILE": "Arquivo de certificado público RSA", "TR_RUN_SELECTED_INI_FILE": "Executar arquivo INI selecionado", "TR_RUNTIME_PARAMETERS": "Parâmetros de tempo de execução", "TR_SAVE": "<PERSON><PERSON>", "TR_SAVE_GATEWAY": "Salvar INI / CSV atual", "TR_SAVE_INI / CSV": "Salvar INI / CSV atual", "TR_SAVE_UNMAPPED_POINTS": "Salvar pontos não mapeados", "TR_SAVE_UNMAPPED_POINTS_DESC": "se true, as tags não mapeadas serão salvas no arquivo de mapeamento de pontos", "TR_SBO": "SBO", "TR_SCL_CATEGORY _...FISICA": "...FISICA", "TR_SCL_CATEGORY_ ~~~ TRANSPORT": "~~~ TRANSPORT", "TR_SCL_CATEGORY _ +++ USER": "+++ USER", "TR_SCL_CATEGORY _ === APPLICATION": "=== APPLICATION", "TR_SCL_CATEGORY_CYCLIC_DATA": "DADOS CÍCLICOS", "TR_SCL_CATEGORY_CYCLIC_HDRS": "CYCLIC HDRS", "TR_SCL_CATEGORY _--- DATA_LINK": "--- DATA_LINK", "TR_SCL_CATEGORY_EVENT_DATA": "DADOS DO EVENTO", "TR_SCL_CATEGORY_EVENT_HDRS": "EVENT HDRS", "TR_SCL_CATEGORY_MMI": "MMI", "TR_SCL_CATEGORY_SECURITY_DATA": "DADOS DE SEGURANÇA", "TR_SCL_CATEGORY_SECURITY_HDRS": "SECURITY HDRS", "TR_SCL_CATEGORY_STATIC_DATA": "DADOS ESTÁTICOS", "TR_SCL_CATEGORY_STATIC_HDRS": "HDRS ESTÁTICO", "TR_SCL_CATEGORY_TARGET": "TARGET", "TR_SCL_DATABASE": "Banco de dados", "TR_SCL_FILE_NAME": "Arquivo SCL", "TR_SCL_FILTER": "Filtro SCL", "TR_SCL_PROTOCOL_LAYER": "Camada de protocolo", "TR_SDG_CATEGORY_61850": "61850", "TR_SDG_CATEGORY_870": "870", "TR_SDG_CATEGORY_DNP": "DNP", "TR_SDG_CATEGORY_EQUATION": "EQUATION", "TR_SDG_CATEGORY_MODBUS": "MODBUS", "TR_SDG_CATEGORY_ODBC": "ODBC", "TR_SDG_CATEGORY_OPC": "OPC", "TR_SDG_CATEGORY_OPC_DEEP": "OPC DEEP", "TR_SDG_CATEGORY_OPC_SU": "OPC SU", "TR_SDG_CATEGORY_OPC_UA": "OPC UA", "TR_SDG_CATEGORY_TASE2": "ICCP", "TR_SDG_FILTER": "Filtro SDG", "TR_SDG_MMS": "MMS", "TR_SDG_OPC": "OPC", "TR_SDG_OTHER": "Outro", "TR_SDG_SCL": "SCL", "TR_SDO_DESC": "Nome do SDO", "TR_SDO_NAME": "Nome do SDO", "TR_SDO_NAME_DESC": "O nome do SDO", "TR_SDO_OPTIONS": "Opções de SDO", "TR_SDO_OPTIONS_DESC": "As opções do SDO", "TR_SEARCH": "Pesquisa", "TR_SEARCH_CRITERIA": "Critérios de pesquisa", "TR_SECTOR_DUPLICATE": "Falha ao adicionar setor. O setor no endereço {{arg1}} já existe. ", "TR_SECURITY": "Segurança", "TR_SECURITY_PARAMETERS": "Parâmetros de segurança", "TR_SECURITY_SETUP": "Configuração de segurança", "TR_SELECT_ALL": "Selecionar tudo", "TR_SELECT_LANGUAGE": "Alterar idioma", "TR_SELECT_ONE": "Selecione um:", "TR_SEQUENCE_NUMBER": "Número de sequência", "TR_SEQUENCE_NUMBER_DESC": "Especifica se o número da sequência está ativo.", "TR_SERIAL_PORT_SETUP": "Configuração da porta serial", "TR_SERVER": "<PERSON><PERSON><PERSON>", "TR_SERVER_AE_INVOKE_ID": "ID de chamada do AE", "TR_SERVER_AE_QUALIFIER": "Qualificador AE", "TR_SERVER_AP_INVOKE_ID": "AP Invoke ID", "TR_SERVER_APP_ID": "ID do aplicativo", "TR_SERVER_IP_ADDRESS": "Endereço IP do servidor", "TR_SERVER_IP_PORT": "Porta TCP do servidor", "TR_SERVER_LIST": "Lista de servidores", "TR_SERVER_LIST_DESC": "Defina a lista de servidores", "TR_SERVER_NAME": "Nome do servidor", "TR_SERVER_NAME_DESC": "Defina o nome do servidor", "TR_SERVER_NODE": "Nó do servidor", "TR_SERVER_NODE_DESC": "Defina o nó do servidor", "TR_SERVER_PRESENTATION_ADDRESS": "Seletor de apresentação", "TR_SERVER_SESSION_ADDRESS": "<PERSON><PERSON><PERSON> de <PERSON>", "TR_SERVER_SUPPORTED_FEATURES": "Recursos suportados pelo servidor", "TR_SERVER_TRANSPORT_ADDRESS": "Seletor de transporte", "TR_SERVICE_INI_FILE_NAME": "Nome do arquivo INI do serviço", "TR_SERVICE_PARAMETERS": "Parâmetros de serviço", "TR_SESSION_CONFIG": "Configuração da sessão", "TR_SESSION_DUPLICATE": "Falha ao adicionar sessão. A sessão {{arg1}} já existe. ", "TR_SESSION_LINK_ADDRESS": "Endereço do link", "TR_SESSION_LINK_ADDRESS_DESC": "Endereço do link de dados do componente escravo ou dispositivo remoto. Cada índice identifica uma sessão exclusiva, que é uma conexão da camada de link entre um dispositivo Mestre e um Escravo. Defina como 0xffff (65535) para que a sessão seja uma 'sessão de transmissão'.", "TR_SESSION_LOCAL_ADDRESS": "Endereço de origem / local", "TR_SESSION_LOCAL_ADDRESS_DESC": "Endereço do link de dados do dispositivo local. Este parâmetro é usado apenas para sessões mestre ou escravo usando o protocolo DNP3. ", "TR_SESSION_NAME": "Nome da sessão", "TR_SESSION_NAME_DESC": "Nome da sessão", "TR_SET_MDO_OPTIONS_FAILED": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_SET_TAGNAME_FAILED": "Falha ao definir o nome da tag do usuário como {{arg1}} (pode ser uma duplicata)", "TR_SETTINGS": "Configurações", "TR_SEVERITY": "Gravidade", "TR_SHOW_ALL_FCS": "Mostrar todas as restrições funcionais", "TR_SHOW_ALL_FCS_DESC": "Alterar exibição de restrições funcionais.", "TR_SHOW_ONLY_ST_MX": "Mostrar apenas ST / MX", "TR_SISCO_COMPATABILITY": "Compatibilidade SISCO", "TR_SLAVE_MASTER_DATA_OBJECT": "Escravo / objeto de dados mestre", "TR_SLCT_CONFIRM": "SLCT_CONFIRM", "TR_SLCT_CONFIRM_DESC": "Uma 1ª passagem em uma operação de controle de 2 passagens foi confirmada por um dispositivo remoto.", "TR_SLCT_PENDING": "SLCT_PENDING", "TR_SLCT_PENDING_DESC": "Uma 1ª passagem em uma operação de controle de 2 passagens foi transmitida para um dispositivo remoto.", "TR_SOE_LOG": "Sequência de log de eventos", "TR_SOEQ": "SOEQ", "TR_SOEQFILE_NAME": "Nome do arquivo da fila SOE", "TR_SOEQFILE_NAME_DESC": "Nome e caminho do arquivo da sequência de eventos.", "TR_SOURCE": "Origem", "TR_START_DATE": "Data de início", "TR_START_GATEWAY": "Iniciar Gateway", "TR_STATUS": "STATUS", "TR_STATUS_CODE": "Código de status", "TR_STATUS_CODE_DESC": "Definir código de status", "TR_STOP_GATEWAY": "Parar gateway", "TR_SUBMIT_SUPPORT_REQUEST": "Enviar solicitação de suporte", "TR_SUBSCRIBED_STREAM": "Stream inscrito", "TR_SUBSCRIBED_STREAM_DESC": "Stream inscrito", "TR_SUBSTITUTED": "SUBSTITUTED", "TR_SUBSTITUTED_DESC": "Substituído (substituição ou forçado)", "TR_SUCCES_ARG1": "Sucesso: {{arg1}}", "TR_SUCCESS": "Sucesso", "TR_SUPER_USERS": "Superusuários", "TR_SYSTEM_LOGS": "Logs do sistema", "TR_SYSTEM_SETTINGS": "Configurações do sistema", "TR_SYSTEM_TRACE": "Rastreio do sistema", "TR_SYSTEMS_LOGS": "Logs do sistema", "TR_SYSTEMS_LOGS_DESC": "Logs do sistema", "TR_TABLE_INFO": "Informações da tabela", "TR_TABLE_INFO_DESC": "Especifica as informações da tabela", "TR_TABLE_LIST": "Lista de tabelas", "TR_TABLE_LIST_DESC": "Especifica a lista de tabelas", "TR_TAG_DESCRIPTION": "Descrição da tag:", "TR_TAG_DESCRIPTION_DESC": "Especifique a descrição da tag", "TR_TAG_EDIT_": "<PERSON><PERSON>", "TR_TAG_NAME": "Nome da tag:", "TR_TAG_NAME_DESC": "Especifique o nome da tag", "TR_TAG_OPTIONS": "Opções de tag:", "TR_TAG_OPTIONS_DESC": "Especifique as opções da tag", "TR_TAG_QUALITY": "Qualidade", "TR_TAG_QUALITY_DESC": "Defina a qualidade para o MDO", "TR_TAG_TIME": "Tag Time", "TR_TAG_TIME_DESC": "Defina a hora do MDO", "TR_TAG_VALUE_TYPE": "Tipo de valor da tag:", "TR_TAG_VALUE_TYPE_DESC": "O tipo do valor da tag", "TR_TARGET_LAYER": "Camada de destino", "TR_TARGET_LAYER_DESC": "Registrar mensagens de rastreamento para a camada de destino", "TR_TASE2_ADD_DS_TRANSFERSET": "Não foi possível adicionar o {{arg1}} DS Transfer Set no servidor ICCP em {{arg2}}. Verifique se o conjunto de dados selecionado existe no servidor e se há mais conjuntos de transferência disponíveis. ", "TR_TASE2_ADD_MDO": "Não foi possível adicionar o {{arg1}} MDO no servidor 61850: {{arg2}}. (Duplicado?)", "TR_TASE2_ADD_MDO_DUPLICATE": "Não foi possível adicionar o {{arg1}} MDO no servidor ICCP: {{arg2}}. (Duplicado?)", "TR_TASE2_CLIENT_DELETE": "O cliente '{{arg1}}' tem MDOs filhos que devem ser excluídos antes que o modelo possa ser carregado.", "TR_TASE2_CLIENT_DELETE_CLEAR_MODEL": "O cliente '{{arg1}}' tem MDOs filhos que devem ser excluídos antes que o modelo possa ser limpo.", "TR_TASE2_CLIENT_DUP_LIC": "Falha ao adicionar o cliente ICCP (pode estar duplicado ou sem licença)", "TR_TASE2_CLIENT_DUPLICATE": "Não é possível adicionar o cliente ICCP: '{{arg1}}'. Nome duplicado. ", "TR_TASE2_CLIENT_EXISTS": "Erro: o cliente com este nome já existe.", "TR_TASE2_CLIENT_NO_MORE": "Não há mais clientes ICCP disponíveis", "TR_TASE2_CREAT_MDO_NAME": "O MDO deve ter um nome, não pode criar", "TR_TASE2_CREATE_DS": "Falha ao criar o DataSet: {{arg1}} no servidor.Consulte erro no analisador de protocolo para obter mais informações. ", "TR_TASE2_CREATE_MDO_INVALID_TAG": "Não foi possível criar o {{arg1}} MDO.  Nome de etiqueta inválido?", "TR_TASE2_DELETE_DS_NOT_FOUND": "Conjunto de dados {{arg1}} não encontrado.", "TR_TASE2_DELETE_IN_USE": "MDO: '{{arg1}}' é mapeado para pontos escravos ou é usado em uma equação, não pode excluir", "TR_TASE2_DELETE_DS": "O DataSet não pode ser excluído", "TR_TASE2_DUPLICATE_DOMAIN": "Não é possível ter nomes de domínio duplicados. Digite um nome exclusivo. ", "TR_TASE2_MDO_ALREADY_DEFINED": "MDO j<PERSON> definido, n<PERSON> pode criar", "TR_TASE2_MDO_DELETE": "MDOs do ICCP não podem ser excluídos", "TR_TASE2_MDO_DUP_TAG": "Não foi possível definir o nome da marca do usuário MDO {{arg1}} (duplicado?)", "TR_TASE2_NO_CONTROL_BLOCK": "Não é um bloco de controle válido", "TR_TASE2_NO_EDIT_CONN": "Não é possível editar o servidor enquanto a conexão está ativa", "TR_TASE2_NO_EDIT_WITH_CLIENTS_CON": "Não é possível editar o servidor enquanto os clientes estão conectados", "TR_TASE2_NO_MORE": "Não há mais clientes ICCP disponíveis", "TR_TASE2_NO_MORE_DS_TRANSFER": "Não há mais conjuntos de transferência DS disponíveis", "TR_TASE2_NO_MORE_LD": "Não há mais dispositivos lógicos disponíveis", "TR_TASE2_NO_MORE_POLLED": "Não há mais ICCP PolledPointSets disponíveis", "TR_TASE2_NO_MORE_POLLED_DS": "Não foi possível adicionar o {{arg1}} PolledPointSet no servidor ICCP: {{arg2}}.", "TR_TASE2_NO_POINTS": "Nenhum ponto ICCP disponível", "TR_TASE2_NO_POLLED": "Nenhum item do conjunto de dados pesquisados disponível", "TR_TASE2_NONE_LEFT": "Não há mais clientes ICCP disponíveis", "TR_TASE2_OPT_MDO": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_TASE2_SELECT": "Falha ao selecionar o controle - nada foi feito.", "TR_TASE2_SELECT_INTEGER": "O valor de gravação não é válido - deve ser um número inteiro", "TR_TASE2_SELECT_WRONG_VALUE": "O valor de gravação não é válido - deve ser um número decimal", "TR_TASE2_SERVER_ADD_POLLED_DS": "Não foi possível adicionar {{arg1}} PolledDataSet no servidor ICCP: {{arg2}}.", "TR_TASE2_SERVER_DUP_LIC": "Falha ao adicionar o servidor ICCP (pode estar duplicado ou sem licença)", "TR_TASE2_SERVER_EXISTS": "Erro: o servidor com este nome já existe.", "TR_TASE2_SET_MDO": "Não foi possí<PERSON> definir as opções do MDO {{arg1}}", "TR_TASE2CLIENT_AEINVOKE_ID": "ID de chamada do AE", "TR_TASE2CLIENT_AEINVOKE_ID_DESC": "Especifica o ID de chamada do AE do cliente ICCP", "TR_TASE2CLIENT_AEQUALIFIER": "Qualificador AE", "TR_TASE2CLIENT_AEQUALIFIER_DESC": "Especifica o qualificador AE do cliente ICCP", "TR_TASE2CLIENT_APINVOKE_ID": "AP Invoke ID", "TR_TASE2CLIENT_APINVOKE_ID_DESC": "Especifica o ID de chamada do AP do cliente ICCP", "TR_TASE2CLIENT_APP_ID": "ID do aplicativo", "TR_TASE2CLIENT_APP_ID_DESC": "Especifica o ID do aplicativo do cliente ICCP", "TR_TASE2CLIENT_CONNECT_TIMEOUT": "Tempo limite do MMS (ms)", "TR_TASE2CLIENT_CONNECT_TIMEOUT_DESC": "Especifica o tempo limite de conexão do MMS para o cliente ICCP.  Após iniciar uma tentativa de conexão, é assim que se espera o sucesso. ", "TR_TASE2CLIENT_CONNECTING_PORT": "Porta IP", "TR_TASE2CLIENT_CONNECTING_PORT_DESC": "Especifica a porta TCP / IP à qual o cliente tentará se conectar no servidor", "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME": "DataSet Transfer Set Time buffer buffer (s)", "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME_DESC": "Especifica o atributo Buffer Time para um conjunto de transferência do DataSet", "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL": "Intervalo do conjunto de transferências do DataSet (segundos)", "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL_DESC": "Especifica o atributo de intervalo de um conjunto de transferência do DataSet", "TR_TASE2CLIENT_DSTRANSFER_SET_RBE": "Relatório do conjunto de transferências do conjunto de dados por exceção", "TR_TASE2CLIENT_DSTRANSFER_SET_RBE_DESC": "Especifica o atributo Relatório por exceção de um conjunto de transferência do DataSet", "TR_TASE2CLIENT_INITIATE": "Iniciar uma cone<PERSON>", "TR_TASE2CLIENT_INITIATE_DESC": "O cliente deve iniciar uma conexão", "TR_TASE2CLIENT_PRESENTATION_ADDRESS": "Endereço da apresentação", "TR_TASE2CLIENT_PRESENTATION_ADDRESS_DESC": "Especifica o endereço de apresentação do cliente ICCP", "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT": "Reconecte a contagem de novas tentativas", "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT_DESC": "Especifica a contagem de novas tentativas de reconexão para o cliente ICCP (0 = tentativa de reconexão permanente) Uma conexão bem-sucedida fará com que o contador de limite interno seja redefinido para 0, resultando em tentativas contínuas de conexão com o servidor ICCP.", "TR_TASE2CLIENT_RECONNECT_TIME": "Reconecte o tempo limite (ms)", "TR_TASE2CLIENT_RECONNECT_TIME_DESC": "Especifica o tempo limite de reconexão para o cliente ICCP (0 = sem reconexão)", "TR_TASE2CLIENT_RFC_IP_ADDR": "Endereço IP da RFC do cliente ICCP", "TR_TASE2CLIENT_RFC_IP_ADDR_DESC": "Especifica o endereço IP da RFC do cliente ICCP", "TR_TASE2CLIENT_SESSION_ADDRESS": "Endereço da sessão", "TR_TASE2CLIENT_SESSION_ADDRESS_DESC": "Especifica o endereço da sessão do cliente ICCP", "TR_TASE2CLIENT_TRANSPORT_ADDRESS": "Endereço de transporte", "TR_TASE2CLIENT_TRANSPORT_ADDRESS_DESC": "Especifica o endereço de transporte do cliente ICCP", "TR_TASE2CLT_POLLED_POINT_SET_NAME": "Nome do conjunto de pontos pesquisados", "TR_TASE2CLT_POLLED_POINT_SET_NAME_DESC": "Especifica o nome do conjunto de pontos pesquisados", "TR_TASE2CLT_POLLED_POINT_SET_PERIOD": "Período definido do ponto de pesquisa (ms)", "TR_TASE2CLT_POLLED_POINT_SET_PERIOD_DESC": "Especifica o período para ler o conjunto de pontos pesquisados", "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME": "Domínio ICCP do conjunto de dados do conjunto de dados relatados", "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME_DESC": "Especifica o domínio de um conjunto de dados do conjunto de dados relatado em um servidor ICCP", "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD": "Período do conjunto de transferências do conjunto de dados (segundos)", "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD_DESC": "Especifica o período de atualizações de integridade de um conjunto de transferência do DataSet", "TR_TASE2CLT_REPORTED_DSNAME": "Nome do conjunto de dados relatados", "TR_TASE2CLT_REPORTED_DSNAME_DESC": "Especifica o nome de um conjunto de dados relatados em um servidor ICCP", "TR_TASE2SECURITY_ON": "Ativar <PERSON>", "TR_TASE2SECURITY_ON_DESC": "A segurança deve estar ativada", "TR_TASE2SERVER_AEINVOKE_ID": "ID de chamada do AE", "TR_TASE2SERVER_AEINVOKE_ID_DESC": "Especifica o ID de chamada do AE do servidor ICCP", "TR_TASE2SERVER_AEQUALIFIER": "Qualificador AE", "TR_TASE2SERVER_AEQUALIFIER_DESC": "Especifica o qualificador AE do servidor ICCP", "TR_TASE2SERVER_APINVOKE_ID": "AP Invoke ID", "TR_TASE2SERVER_APINVOKE_ID_DESC": "Especifica a identificação de chamada AP do servidor ICCP", "TR_TASE2SERVER_APP_ID": "ID do aplicativo", "TR_TASE2SERVER_APP_ID_DESC": "Especifica o ID do aplicativo do servidor ICCP", "TR_TASE2SERVER_IPADDRESS": "Endereço IP do servidor ICCP", "TR_TASE2SERVER_IPADDRESS_DESC": "Especifica o endereço IP do servidor ICCP ao qual o cliente tentará se conectar", "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID": "ID da tabela bilateral", "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID_DESC": "Especifica o ID da tabela bilateral", "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT": "Número de conjuntos de transferências DS", "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT_DESC": "Especifica o número de conjuntos de transferência DS", "TR_TASE2SERVER_LOGICAL_DEVICE_NAME": "Nome do dispositivo lógico", "TR_TASE2SERVER_LOGICAL_DEVICE_NAME_DESC": "Especifica o nome de um dispositivo lógico em um servidor ICCP", "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED": "Número máximo de clientes", "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED_DESC": "Especifica o número máximo de clientes com permissão para se conectar a este servidor. Não especificado significa não máx. ", "TR_TASE2SERVER_PRESENTATION_ADDRESS": "Endereço da apresentação", "TR_TASE2SERVER_PRESENTATION_ADDRESS_DESC": "Especifica o endereço de apresentação do servidor ICCP", "TR_TASE2SERVER_RFC_IP_ADDR": "Endereço IP RFC do servidor ICCP", "TR_TASE2SERVER_RFC_IP_ADDR_DESC": "Especifica o endereço IP da RFC do servidor ICCP", "TR_TASE2SERVER_SESSION_ADDRESS": "Endereço da sessão", "TR_TASE2SERVER_SESSION_ADDRESS_DESC": "Especifica o endereço da sessão do servidor ICCP", "TR_TASE2SERVER_SUPPORTED_FEATURES": "Recursos suportados pelo servidor ICCP", "TR_TASE2SERVER_SUPPORTED_FEATURES_DESC": "Especifica os blocos ICCP que são relatados para conectar os clientes como Supported_Features", "TR_TASE2SERVER_TRANSPORT_ADDRESS": "Endereço de transporte", "TR_TASE2SERVER_TRANSPORT_ADDRESS_DESC": "Especifica o endereço de transporte do servidor ICCP", "TR_TASE2SERVICE_ROLE": "Função de serviço", "TR_TASE2SERVICE_ROLE_DESC": "Especifica a função de serviço Tase2", "TR_TASE2SYNC_DATA_SETS": "Sincronizar conjuntos de dados", "TR_TASE2SYNC_DATA_SETS_DESC": "O cliente deve tentar sincronizar os conjuntos de dados na conexão", "TR_TASE2VERSION": "Versão do aplicativo ICCP", "TR_TASE2VERSION_DESC": "Especifica a versão do aplicativo ICCP", "TR_TEST": "TEST", "TR_TEST_DESC": "<PERSON>e", "TR_TEST_MODE": "<PERSON><PERSON> de teste", "TR_TEST_MODE_DESC": "O ponto de dados ou o dispositivo remoto está operando no modo de teste.", "TR_THE_CURRENT_PASSWORD_IS_REQUIRED": "A senha atual é necessária.", "TR_THE_NEW_PASSWORD_MUST_BE_AT_LEAST_CHARACTERS_LONG": "A nova senha deve ter pelo menos 6 caracteres.", "TR_THE_TWO_PASSWORD_FIELDS_DIDN_T_MATCH": "Os dois campos de senha não coincidem.", "TR_THE_USER_ADMIN_CAN_NOT_BE_DELETED": "O usuário Admin não pode ser excluído.", "TR_THE_USERNAME_X_IS_ALREADY_USED": "O nome de usuário {{nome de usuário}} já está sendo usado.", "TR_THIS_SYSTEM": "Este sistema", "TR_THRESHOLD": "<PERSON><PERSON>", "TR_TIME": "<PERSON><PERSON>", "TR_TIME_AND_TIMING": "Hora e ajuste", "TR_TIME_STAMP": "Registro de data e hora", "TR_TIME_STAMP_DESC": "Especifica se o carimbo de data / hora está ativo.", "TR_TIME_ZONE_BIAS": "Polarização do fuso horário", "TR_TIME_ZONE_NAME": "<PERSON><PERSON>", "TR_TIME_ZONE_NAME_DESC": "O nome do fuso horário a ser usado para o tempo de exibição do SDG (a string vazia / padrão será definida como UTC).  Nota: SDG usa UTC para horários internos.  UseTimeZoneClock deve ser verdadeiro. ", "TR_TLS": "TLS", "TR_TLS_COMMON_NAME": "Nome comum", "TR_TLS_CONFIG": "62351-3 Configuração de segurança", "TR_TLS_HANDSHAKE_TIMEOUT": "TLS Handshake Timeout", "TR_TLS_MAX_PDUS": "Máximo de PDUs antes de forçar a renegociação de cifra", "TR_TLS_MAX_RENEG_WAIT_TIME": "Tempo máximo de espera de renegociação (ms)", "TR_TLS_RENEGOTIATION": "Renegocia<PERSON> (s)", "TR_TLS_RENEGOTIATION_COUNT": "Contagem de renegociação", "TR_TLS_RENEGOTIATION_SECONDS": "Renegocia<PERSON> (s)", "TR_TRIGGER_OPTIONS": "Opções de acionamento", "TR_TS_CONDITIONS_DETECTED": "Incluir condições TS detectadas", "TR_TS_CONDITIONS_DETECTED_DESC": "Incluir condições TS detectadas", "TR_TS_SET_NAME": "Incluir nome do conjunto de transferências", "TR_TS_SET_NAME_DESC": "Incluir nome do conjunto de transferências", "TR_TS_SET_TIMESTAMP": "Incluir carimbo de data e hora definido na transferência", "TR_TS_SET_TIMESTAMP_DESC": "Incluir carimbo de data e hora do conjunto de transferências", "TR_TYPE": "Tipo", "TR_UNAUTHORIZED": "Não autorizado", "TR_UNINITIALIZED": "Não inicializado", "TR_UNINITIALIZED_DESC": "Não definido desde a inicialização", "TR_UNKNOWN": "Desconhecido", "TR_UNKNOWN_DESC": "Os dados estão sendo atualizados, mas o motivo da atualização é desconhecido.", "TR_UNLOCK_SCROLL": "Desbloquear rolagem", "TR_UNSELECT_ALL": "<PERSON><PERSON><PERSON> tudo", "TR_UNSOLICITED_UDP_PORT": "Porta UDP não solicitada", "TR_UNSPECIFIED_ERROR": "Erro não especificado", "TR_UPLOAD_FILE": "Carregar arquivo", "TR_UPLOAD_NEW_CONFIGURATION_FILE": "Carregar novo arquivo de configuração", "TR_UPLOAD_NEW_CSV_FILE": "Carregar novo arquivo CSV", "TR_UPLOAD_NEW_INI_FILE": "Carregar novo arquivo INI", "TR_USE": "Use", "TR_USE_DEFLATE": "Usar compressão gzip", "TR_USE_REPORTED_TIME": "Use Reported Time For MDOs", "TR_USE_REPORTED_TIME_DESC": "se verdadeiro, não atualize o tempo nas atualizações estáticas, isso se aplica aos MDOs que podem ser relatados como eventos", "TR_USE_SCL_FILE": "Usar arquivo SCL", "TR_USE_SYSTEM_TIME": "Usar relógio do sistema", "TR_USE_SYSTEM_TIME_DESC": "Se verdadeiro, sempre leia a data e a hora do relógio do sistema Windows, em vez de um relógio mantido internamente. O relógio interno é inicializado no relógio do sistema Windows na inicialização, mas será ajustado sempre que uma sincronização do relógio for recebida de um mestre externo. Você geralmente definiria UseSystemTime como true se você tiver um mecanismo de sincronização de relógio externo que sincronize o relógio do sistema Windows fora do SCADA Data Gateway; nesse caso, é recomendável que AcceptClockSync seja definido como false. ", "TR_USE_TIME_ZONE_CLOCK": "Usar relógio de fuso hor<PERSON>rio", "TR_USE_TIME_ZONE_CLOCK_DESC": "Se true, exibir a data e hora do SDG no TimeZoneName especificado", "TR_USE_WEB_SSL": "Use Web SSL / HTTPS", "TR_USER_TAG_NAME": "Nome da etiqueta do usuário", "TR_USER_TAG_NAME_DESC": "O nome da marca do usuário do MDO", "TR_USER_X_DELETED": "<PERSON><PERSON><PERSON><PERSON> {{nome de usuário}} excluído.", "TR_USERNAME": "Nome de usuário", "TR_USERNAME_IS_REQUIRED": "O nome de usuário é obrigatório.", "TR_USERNAME_IS_REQUIRED_CHARACTERS_MINIMUM": "O nome de usuário é obrigatório (4 caracteres no mínimo).", "TR_USERS": "Usuários", "TR_USERS_MANAGEMENT": "Gerenciamento de usuários", "TR_VALID_EQUATION_SUCCESS": "Sucesso: equação válida", "TR_VALIDATE_EQUATION": "Validar equação", "TR_VALIDATE_UDP_ADDRESS": "Validar endereço UDP", "TR_VALUE": "Valor", "TR_VERBOSE": "<PERSON><PERSON><PERSON>", "TR_VERIFY_CERTIFICATE": "Verifique o certificado HTTPS", "TR_VERSION": "Vers<PERSON>", "TR_VIEW": "Visualizar", "TR_VIEWS": "Visualizações especiais", "TR_WARNING": "Aviso", "TR_WARNING_DESC": "Aviso", "TR_WARNING_VIEW": "Visualização de aviso", "TR_WEB_SERVER": "Servidor Web", "TR_WEB_SERVER_DESC": "Registre as mensagens de rastreamento para o servidor da Web", "TR_WEBSOCKET_CLOSE": "Websocket {{websocketName}} fechar: {{reason}}", "TR_WEBSOCKET_OPEN": "Websocket {{websocketName}} aberto", "TR_WEBSOCKET_UPDATE_PARAMETERS": "Parâmetros de atualização do Websocket", "TR_WHAT_NAME_DO_YOU_TO_USE_FOR_YOUR_NEW_INI_CSV_FILE": "Que nome você deseja usar para o seu novo arquivo INI", "TR_WIN_TIMER_FAILED": "Ocorreu um erro fatal: {{arg1}}. O SDG agora tentará sair. Por favor, veja os registros para mais detalhes", "TR_YES": "<PERSON>m", "TR_YOUR_BROWSER_WILL_REFRESH_IN_X_SECONDS": "O seu navegador será atualizado em {{value}} segundos", "TR_ERROR_FAILED_TO_STOP_AFTER_120_SEC": "Não pode parar o motor após 120 segundoss", "TR_ARE_YOU_SURE_TO_DELETE_USER_": "Are you sure to delete user {{username}}?"}