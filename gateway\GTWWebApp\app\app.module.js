System.register(["@angular/core", "@angular/common/http", "@angular/platform-browser", "@angular/forms", "@angular/common", "ngx-modialog-7", "ngx-modialog-7/plugins/bootstrap", "@ngx-translate/core", "@ngx-translate/http-loader", "./modules/angular-split/index", "./modules/panel/panel.component", "./modules/panel/panel.service", "./modules/bitmask/bitmask.component", "./modules/bitmask/bitmask.editor.component", "./modules/bitmask/bitmask.advance-editor.component", "./modules/dragndrop/draggable.directive", "./modules/dragndrop/drop-target.directive", "./modules/collapsible-panel/collapsible-panel", "./modules/alert/alert.service", "./modules/alert/alert.status-bar.component", "./modules/alert/alert.log.modal", "./modules/context-menu/context-menu.service", "./modules/context-menu/context-menu.component", "./modules/context-menu/context-menu.directive", "./modules/dropdown/dropdown.directive", "./modules/uppercase/uppercase.directive", "./modules/download/download.component", "./modules/download/download.modal.component", "./modules/show-password/show-password.directive", "./modules/angular-tooltip/tooltip.service", "./modules/angular-tooltip/tooltip.component", "./modules/angular-tooltip/tooltip.directive", "./modules/tab/tab.component", "./modules/tab/tabset.component", "./modules/loader/loader.component", "./modules/loader/loader.service", "./modules/loader/loader-interceptor.service", "./modules/resizable/resizable.component", "./modules/resizable/resizable.directive", "./modules/combobox/combobox.editor.multiselect.component", "./app.routing", "./app.component", "./app.health.component", "./app.blank.component", "./app.info.component", "./dashboard/dashboard.component", "./dashboard/dashboard.manager.component", "./settings/settings.component", "./settings/settings.form.component", "./global/global.data.service", "./global/keys.pipe", "./global/safeHtml.pipe", "./global/translateKey.pipe", "./global/gtwImage.pipe", "./global/logEntryContain.pipe", "./global/gridContain.pipe", "./authentication/authentication.service", "./authentication/authentication.login.modal", "./authentication/authentication.password.modal", "./authentication/check.role.pipe", "./authentication/authentication-interceptor.service", "./app.password.component", "./license/license.component", "./user/user.grid.component", "./user/user.modal", "./user/user.reset.password.modal", "./dashboard/config/dashboard.config.component", "./dashboard/config/dashboard.config.devices.component", "./dashboard/config/dashboard.config.devices.search.component", "./dashboard/config/dashboard.config.devices.treeview.component", "./dashboard/config/dashboard.config.context-menu.directive", "./dashboard/config/dashboard.config.tags.grid.component", "./dashboard/config/dashboard.config.tag.editor.modal", "./dashboard/config/dashboard.config.tag.editor.component", "./dashboard/config/dashboard.config.tag.editor.advance.component", "./dashboard/config/dashboard.config.tag.editor.logic", "./dashboard/config/dashboard.config.tag.options.modal", "./dashboard/config/dashboard.config.import-export.modal", "./dashboard/config/dashboard.config.tag.value.quality.modal", "./dashboard/config/dashboard.config.tag.editor.group.pipe", "./dashboard/config/dashboard.config.grid.component", "./dashboard/config/dashboard.config.tags.grid.search.component", "./dashboard/config/dashboard.config.tag.tooltip.directive", "./dashboard/config/dashboard.config.device.virtual-node.component", "./dashboard/log/dashboard.log.component", "./dashboard/log/dashboard.log.grid.component", "./dashboard/log/dashboard.log.extra.component", "./log/log.audit.grid.component", "./log/log.monitor.component", "./log/log.monitor.grid.component", "./log/log.soe.component", "./help/help.component", "./help/help.about.component", "./help/help.update.component", "./help/help.quick-start.component", "./help/help.support.component", "./help/help.ini.component", "./data/variables", "./modules/grid/grid.component", "./modules/grid/grid.search.component", "./modules/grid/grid.pagination.component", "./modules/treeview/treeview.component", "./modules/treeview/dynamic-treeview.component", "./modules/treeview-select/dynamic-treeview-select.component", "./modules/checkbox-indeterminated/checkbox-indeterminated.directive"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, http_1, platform_browser_1, forms_1, common_1, ngx_modialog_7_1, bootstrap_1, core_2, http_loader_1, index_1, panel_component_1, panel_service_1, bitmask_component_1, bitmask_editor_component_1, bitmask_advance_editor_component_1, draggable_directive_1, drop_target_directive_1, collapsible_panel_1, alert_service_1, alert_status_bar_component_1, alert_log_modal_1, context_menu_service_1, context_menu_component_1, context_menu_directive_1, dropdown_directive_1, uppercase_directive_1, download_component_1, download_modal_component_1, show_password_directive_1, tooltip_service_1, tooltip_component_1, tooltip_directive_1, tab_component_1, tabset_component_1, loader_component_1, loader_service_1, loader_interceptor_service_1, resizable_component_1, resizable_directive_1, combobox_editor_multiselect_component_1, app_routing_1, app_component_1, app_health_component_1, app_blank_component_1, app_info_component_1, dashboard_component_1, dashboard_manager_component_1, settings_component_1, settings_form_component_1, global_data_service_1, keys_pipe_1, safeHtml_pipe_1, translateKey_pipe_1, gtwImage_pipe_1, logEntryContain_pipe_1, gridContain_pipe_1, authentication_service_1, authentication_login_modal_1, authentication_password_modal_1, check_role_pipe_1, authentication_interceptor_service_1, app_password_component_1, license_component_1, user_grid_component_1, user_modal_1, user_reset_password_modal_1, dashboard_config_component_1, dashboard_config_devices_component_1, dashboard_config_devices_search_component_1, dashboard_config_devices_treeview_component_1, dashboard_config_context_menu_directive_1, dashboard_config_tags_grid_component_1, dashboard_config_tag_editor_modal_1, dashboard_config_tag_editor_component_1, dashboard_config_tag_editor_advance_component_1, dashboard_config_tag_editor_logic_1, dashboard_config_tag_options_modal_1, dashboard_config_import_export_modal_1, dashboard_config_tag_value_quality_modal_1, dashboard_config_tag_editor_group_pipe_1, dashboard_config_grid_component_1, dashboard_config_tags_grid_search_component_1, dashboard_config_tag_tooltip_directive_1, dashboard_config_device_virtual_node_component_1, dashboard_log_component_1, dashboard_log_grid_component_1, dashboard_log_extra_component_1, log_audit_grid_component_1, log_monitor_component_1, log_monitor_grid_component_1, log_soe_component_1, help_component_1, help_about_component_1, help_update_component_1, help_quick_start_component_1, help_support_component_1, help_ini_component_1, variables_1, grid_component_1, grid_search_component_1, grid_pagination_component_1, treeview_component_1, dynamic_treeview_component_1, dynamic_treeview_select_component_1, checkbox_indeterminated_directive_1, AppModule;
    var __moduleName = context_1 && context_1.id;
    function HttpLoaderFactory(httpClient) {
        return new http_loader_1.TranslateHttpLoader(httpClient, "i18n/", ".json");
    }
    exports_1("HttpLoaderFactory", HttpLoaderFactory);
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (platform_browser_1_1) {
                platform_browser_1 = platform_browser_1_1;
            },
            function (forms_1_1) {
                forms_1 = forms_1_1;
            },
            function (common_1_1) {
                common_1 = common_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (http_loader_1_1) {
                http_loader_1 = http_loader_1_1;
            },
            function (index_1_1) {
                index_1 = index_1_1;
            },
            function (panel_component_1_1) {
                panel_component_1 = panel_component_1_1;
            },
            function (panel_service_1_1) {
                panel_service_1 = panel_service_1_1;
            },
            function (bitmask_component_1_1) {
                bitmask_component_1 = bitmask_component_1_1;
            },
            function (bitmask_editor_component_1_1) {
                bitmask_editor_component_1 = bitmask_editor_component_1_1;
            },
            function (bitmask_advance_editor_component_1_1) {
                bitmask_advance_editor_component_1 = bitmask_advance_editor_component_1_1;
            },
            function (draggable_directive_1_1) {
                draggable_directive_1 = draggable_directive_1_1;
            },
            function (drop_target_directive_1_1) {
                drop_target_directive_1 = drop_target_directive_1_1;
            },
            function (collapsible_panel_1_1) {
                collapsible_panel_1 = collapsible_panel_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (alert_status_bar_component_1_1) {
                alert_status_bar_component_1 = alert_status_bar_component_1_1;
            },
            function (alert_log_modal_1_1) {
                alert_log_modal_1 = alert_log_modal_1_1;
            },
            function (context_menu_service_1_1) {
                context_menu_service_1 = context_menu_service_1_1;
            },
            function (context_menu_component_1_1) {
                context_menu_component_1 = context_menu_component_1_1;
            },
            function (context_menu_directive_1_1) {
                context_menu_directive_1 = context_menu_directive_1_1;
            },
            function (dropdown_directive_1_1) {
                dropdown_directive_1 = dropdown_directive_1_1;
            },
            function (uppercase_directive_1_1) {
                uppercase_directive_1 = uppercase_directive_1_1;
            },
            function (download_component_1_1) {
                download_component_1 = download_component_1_1;
            },
            function (download_modal_component_1_1) {
                download_modal_component_1 = download_modal_component_1_1;
            },
            function (show_password_directive_1_1) {
                show_password_directive_1 = show_password_directive_1_1;
            },
            function (tooltip_service_1_1) {
                tooltip_service_1 = tooltip_service_1_1;
            },
            function (tooltip_component_1_1) {
                tooltip_component_1 = tooltip_component_1_1;
            },
            function (tooltip_directive_1_1) {
                tooltip_directive_1 = tooltip_directive_1_1;
            },
            function (tab_component_1_1) {
                tab_component_1 = tab_component_1_1;
            },
            function (tabset_component_1_1) {
                tabset_component_1 = tabset_component_1_1;
            },
            function (loader_component_1_1) {
                loader_component_1 = loader_component_1_1;
            },
            function (loader_service_1_1) {
                loader_service_1 = loader_service_1_1;
            },
            function (loader_interceptor_service_1_1) {
                loader_interceptor_service_1 = loader_interceptor_service_1_1;
            },
            function (resizable_component_1_1) {
                resizable_component_1 = resizable_component_1_1;
            },
            function (resizable_directive_1_1) {
                resizable_directive_1 = resizable_directive_1_1;
            },
            function (combobox_editor_multiselect_component_1_1) {
                combobox_editor_multiselect_component_1 = combobox_editor_multiselect_component_1_1;
            },
            function (app_routing_1_1) {
                app_routing_1 = app_routing_1_1;
            },
            function (app_component_1_1) {
                app_component_1 = app_component_1_1;
            },
            function (app_health_component_1_1) {
                app_health_component_1 = app_health_component_1_1;
            },
            function (app_blank_component_1_1) {
                app_blank_component_1 = app_blank_component_1_1;
            },
            function (app_info_component_1_1) {
                app_info_component_1 = app_info_component_1_1;
            },
            function (dashboard_component_1_1) {
                dashboard_component_1 = dashboard_component_1_1;
            },
            function (dashboard_manager_component_1_1) {
                dashboard_manager_component_1 = dashboard_manager_component_1_1;
            },
            function (settings_component_1_1) {
                settings_component_1 = settings_component_1_1;
            },
            function (settings_form_component_1_1) {
                settings_form_component_1 = settings_form_component_1_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            },
            function (keys_pipe_1_1) {
                keys_pipe_1 = keys_pipe_1_1;
            },
            function (safeHtml_pipe_1_1) {
                safeHtml_pipe_1 = safeHtml_pipe_1_1;
            },
            function (translateKey_pipe_1_1) {
                translateKey_pipe_1 = translateKey_pipe_1_1;
            },
            function (gtwImage_pipe_1_1) {
                gtwImage_pipe_1 = gtwImage_pipe_1_1;
            },
            function (logEntryContain_pipe_1_1) {
                logEntryContain_pipe_1 = logEntryContain_pipe_1_1;
            },
            function (gridContain_pipe_1_1) {
                gridContain_pipe_1 = gridContain_pipe_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (authentication_login_modal_1_1) {
                authentication_login_modal_1 = authentication_login_modal_1_1;
            },
            function (authentication_password_modal_1_1) {
                authentication_password_modal_1 = authentication_password_modal_1_1;
            },
            function (check_role_pipe_1_1) {
                check_role_pipe_1 = check_role_pipe_1_1;
            },
            function (authentication_interceptor_service_1_1) {
                authentication_interceptor_service_1 = authentication_interceptor_service_1_1;
            },
            function (app_password_component_1_1) {
                app_password_component_1 = app_password_component_1_1;
            },
            function (license_component_1_1) {
                license_component_1 = license_component_1_1;
            },
            function (user_grid_component_1_1) {
                user_grid_component_1 = user_grid_component_1_1;
            },
            function (user_modal_1_1) {
                user_modal_1 = user_modal_1_1;
            },
            function (user_reset_password_modal_1_1) {
                user_reset_password_modal_1 = user_reset_password_modal_1_1;
            },
            function (dashboard_config_component_1_1) {
                dashboard_config_component_1 = dashboard_config_component_1_1;
            },
            function (dashboard_config_devices_component_1_1) {
                dashboard_config_devices_component_1 = dashboard_config_devices_component_1_1;
            },
            function (dashboard_config_devices_search_component_1_1) {
                dashboard_config_devices_search_component_1 = dashboard_config_devices_search_component_1_1;
            },
            function (dashboard_config_devices_treeview_component_1_1) {
                dashboard_config_devices_treeview_component_1 = dashboard_config_devices_treeview_component_1_1;
            },
            function (dashboard_config_context_menu_directive_1_1) {
                dashboard_config_context_menu_directive_1 = dashboard_config_context_menu_directive_1_1;
            },
            function (dashboard_config_tags_grid_component_1_1) {
                dashboard_config_tags_grid_component_1 = dashboard_config_tags_grid_component_1_1;
            },
            function (dashboard_config_tag_editor_modal_1_1) {
                dashboard_config_tag_editor_modal_1 = dashboard_config_tag_editor_modal_1_1;
            },
            function (dashboard_config_tag_editor_component_1_1) {
                dashboard_config_tag_editor_component_1 = dashboard_config_tag_editor_component_1_1;
            },
            function (dashboard_config_tag_editor_advance_component_1_1) {
                dashboard_config_tag_editor_advance_component_1 = dashboard_config_tag_editor_advance_component_1_1;
            },
            function (dashboard_config_tag_editor_logic_1_1) {
                dashboard_config_tag_editor_logic_1 = dashboard_config_tag_editor_logic_1_1;
            },
            function (dashboard_config_tag_options_modal_1_1) {
                dashboard_config_tag_options_modal_1 = dashboard_config_tag_options_modal_1_1;
            },
            function (dashboard_config_import_export_modal_1_1) {
                dashboard_config_import_export_modal_1 = dashboard_config_import_export_modal_1_1;
            },
            function (dashboard_config_tag_value_quality_modal_1_1) {
                dashboard_config_tag_value_quality_modal_1 = dashboard_config_tag_value_quality_modal_1_1;
            },
            function (dashboard_config_tag_editor_group_pipe_1_1) {
                dashboard_config_tag_editor_group_pipe_1 = dashboard_config_tag_editor_group_pipe_1_1;
            },
            function (dashboard_config_grid_component_1_1) {
                dashboard_config_grid_component_1 = dashboard_config_grid_component_1_1;
            },
            function (dashboard_config_tags_grid_search_component_1_1) {
                dashboard_config_tags_grid_search_component_1 = dashboard_config_tags_grid_search_component_1_1;
            },
            function (dashboard_config_tag_tooltip_directive_1_1) {
                dashboard_config_tag_tooltip_directive_1 = dashboard_config_tag_tooltip_directive_1_1;
            },
            function (dashboard_config_device_virtual_node_component_1_1) {
                dashboard_config_device_virtual_node_component_1 = dashboard_config_device_virtual_node_component_1_1;
            },
            function (dashboard_log_component_1_1) {
                dashboard_log_component_1 = dashboard_log_component_1_1;
            },
            function (dashboard_log_grid_component_1_1) {
                dashboard_log_grid_component_1 = dashboard_log_grid_component_1_1;
            },
            function (dashboard_log_extra_component_1_1) {
                dashboard_log_extra_component_1 = dashboard_log_extra_component_1_1;
            },
            function (log_audit_grid_component_1_1) {
                log_audit_grid_component_1 = log_audit_grid_component_1_1;
            },
            function (log_monitor_component_1_1) {
                log_monitor_component_1 = log_monitor_component_1_1;
            },
            function (log_monitor_grid_component_1_1) {
                log_monitor_grid_component_1 = log_monitor_grid_component_1_1;
            },
            function (log_soe_component_1_1) {
                log_soe_component_1 = log_soe_component_1_1;
            },
            function (help_component_1_1) {
                help_component_1 = help_component_1_1;
            },
            function (help_about_component_1_1) {
                help_about_component_1 = help_about_component_1_1;
            },
            function (help_update_component_1_1) {
                help_update_component_1 = help_update_component_1_1;
            },
            function (help_quick_start_component_1_1) {
                help_quick_start_component_1 = help_quick_start_component_1_1;
            },
            function (help_support_component_1_1) {
                help_support_component_1 = help_support_component_1_1;
            },
            function (help_ini_component_1_1) {
                help_ini_component_1 = help_ini_component_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (grid_component_1_1) {
                grid_component_1 = grid_component_1_1;
            },
            function (grid_search_component_1_1) {
                grid_search_component_1 = grid_search_component_1_1;
            },
            function (grid_pagination_component_1_1) {
                grid_pagination_component_1 = grid_pagination_component_1_1;
            },
            function (treeview_component_1_1) {
                treeview_component_1 = treeview_component_1_1;
            },
            function (dynamic_treeview_component_1_1) {
                dynamic_treeview_component_1 = dynamic_treeview_component_1_1;
            },
            function (dynamic_treeview_select_component_1_1) {
                dynamic_treeview_select_component_1 = dynamic_treeview_select_component_1_1;
            },
            function (checkbox_indeterminated_directive_1_1) {
                checkbox_indeterminated_directive_1 = checkbox_indeterminated_directive_1_1;
            }
        ],
        execute: function () {
            AppModule = (function () {
                function AppModule() {
                }
                AppModule = __decorate([
                    core_1.NgModule({
                        imports: [
                            http_1.HttpClientModule,
                            app_routing_1.Routing,
                            platform_browser_1.BrowserModule,
                            forms_1.FormsModule,
                            forms_1.ReactiveFormsModule,
                            bootstrap_1.BootstrapModalModule,
                            ngx_modialog_7_1.ModalModule.forRoot(),
                            index_1.AngularSplitModule,
                            core_2.TranslateModule.forRoot({
                                loader: {
                                    provide: core_2.TranslateLoader,
                                    useFactory: HttpLoaderFactory,
                                    deps: [http_1.HttpClient]
                                }
                            })
                        ],
                        declarations: [
                            app_component_1.AppComponent,
                            dashboard_component_1.DashboardComponent,
                            dashboard_manager_component_1.DashboardManagerComponent,
                            settings_component_1.SettingsComponent,
                            settings_form_component_1.SettingsFormComponent,
                            dashboard_config_component_1.DashboardConfigComponent,
                            dashboard_config_devices_component_1.DashboardConfigDevicesComponent,
                            dashboard_config_devices_search_component_1.DashboardConfigDevicesSearchComponent,
                            dashboard_config_devices_treeview_component_1.DashboardConfigDevicesTreeViewComponent,
                            dashboard_config_context_menu_directive_1.DashboardConfigContextMenuDirective,
                            dashboard_config_tags_grid_component_1.DashboardConfigTagsGridComponent,
                            dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal,
                            dashboard_config_tag_editor_group_pipe_1.DashboardConfigTagEditorGroupPipe,
                            dashboard_config_tag_editor_component_1.DashboardConfigTagEditorComponent,
                            dashboard_config_tag_editor_advance_component_1.DashboardConfigTagEditorAdvanceComponent,
                            dashboard_config_tag_options_modal_1.DashboardConfigTagOptionsModal,
                            dashboard_config_import_export_modal_1.DashboardConfigImportExportModal,
                            dashboard_config_tag_value_quality_modal_1.DashboardConfigTagValueQualityModal,
                            dashboard_config_grid_component_1.DashboardConfigGridComponent,
                            dashboard_config_tags_grid_search_component_1.DashboardConfigTagsGridSearchComponent,
                            dashboard_config_tag_tooltip_directive_1.DashboardConfigTagTooltipDirective,
                            dashboard_config_device_virtual_node_component_1.DashboardConfigDeviceVirtualNodeComponent,
                            dashboard_log_component_1.DashboardLogComponent,
                            dashboard_log_grid_component_1.DashboardLogGridComponent,
                            dashboard_log_extra_component_1.DashboardLogExtraComponent,
                            user_grid_component_1.UserGridComponent,
                            user_modal_1.UserModal,
                            user_reset_password_modal_1.UserResetPasswordModal,
                            app_health_component_1.AppHealthComponent,
                            app_blank_component_1.AppBlankComponent,
                            app_info_component_1.AppInfoComponent,
                            license_component_1.LicenseComponent,
                            authentication_login_modal_1.AuthenticationLoginModal,
                            authentication_password_modal_1.AuthenticationPasswordModal,
                            check_role_pipe_1.CheckRolePipe,
                            app_password_component_1.AppPasswordComponent,
                            help_component_1.HelpComponent,
                            help_about_component_1.HelpAboutComponent,
                            help_update_component_1.HelpUpdateComponent,
                            help_quick_start_component_1.HelpQuickStartComponent,
                            help_support_component_1.HelpSupportComponent,
                            help_ini_component_1.HelpIniComponent,
                            log_audit_grid_component_1.LogAuditGridComponent,
                            log_monitor_component_1.LogMonitorComponent,
                            log_monitor_grid_component_1.LogMonitorGridComponent,
                            log_soe_component_1.LogSoeComponent,
                            alert_status_bar_component_1.AlertStatusBarComponent,
                            alert_log_modal_1.AlertLogModal,
                            grid_component_1.GridComponent,
                            grid_search_component_1.GridSearchComponent,
                            grid_pagination_component_1.GridPaginationComponent,
                            treeview_component_1.TreeviewComponent,
                            dynamic_treeview_component_1.DynamicTreeviewComponent,
                            dynamic_treeview_select_component_1.DynamicTreeviewSelectComponent,
                            checkbox_indeterminated_directive_1.CheckboxIndeterminateDirective,
                            panel_component_1.PanelComponent,
                            bitmask_component_1.BitmaskComponent,
                            bitmask_editor_component_1.BitmaskEditorComponent,
                            bitmask_advance_editor_component_1.BitmaskAdvanceEditorComponent,
                            context_menu_component_1.ContextMenuComponent,
                            download_component_1.DownloadComponent,
                            download_modal_component_1.DownloadModalComponent,
                            combobox_editor_multiselect_component_1.ComboboxEditorMultiselectComponent,
                            keys_pipe_1.KeysPipe,
                            safeHtml_pipe_1.SafeHtmlPipe,
                            translateKey_pipe_1.TranslateKeyPipe,
                            gtwImage_pipe_1.GTWImagePipe,
                            logEntryContain_pipe_1.LogEntryContainPipe,
                            gridContain_pipe_1.GridContain,
                            resizable_component_1.ResizableComponent,
                            resizable_directive_1.ResizableDirective,
                            draggable_directive_1.DraggableDirective,
                            drop_target_directive_1.DropTargetDirective,
                            context_menu_directive_1.ContextMenuDirective,
                            tooltip_directive_1.TooltipDirective,
                            tooltip_component_1.TooltipComponent,
                            tab_component_1.TabComponent,
                            tabset_component_1.TabsetComponent,
                            loader_component_1.LoaderComponent,
                            dropdown_directive_1.DropdownDirective,
                            uppercase_directive_1.UppercaseDirective,
                            show_password_directive_1.ShowPassword,
                            collapsible_panel_1.CollapsiblePanel
                        ],
                        providers: [
                            http_1.HttpClientModule,
                            alert_service_1.AlertService,
                            panel_service_1.PanelService,
                            authentication_service_1.AuthenticationService,
                            global_data_service_1.GlobalDataService,
                            tooltip_service_1.TooltipService,
                            context_menu_service_1.ContextMenuService,
                            loader_service_1.LoaderService,
                            dashboard_config_tag_editor_logic_1.DashboardConfigTagEditorLogic,
                            check_role_pipe_1.CheckRolePipe,
                            logEntryContain_pipe_1.LogEntryContainPipe,
                            { provide: common_1.LocationStrategy, useClass: common_1.HashLocationStrategy },
                            { provide: variables_1.BASE_PATH, useValue: "rest" },
                            { provide: http_1.HTTP_INTERCEPTORS, useClass: loader_interceptor_service_1.LoaderInterceptorService, multi: true },
                            { provide: http_1.HTTP_INTERCEPTORS, useClass: authentication_interceptor_service_1.AuthenticationInterceptorService, multi: true }
                        ],
                        bootstrap: [app_component_1.AppComponent],
                        entryComponents: [user_modal_1.UserModal, user_reset_password_modal_1.UserResetPasswordModal, authentication_login_modal_1.AuthenticationLoginModal, authentication_password_modal_1.AuthenticationPasswordModal, dashboard_config_tag_editor_modal_1.DashboardConfigTagEditorModal, dashboard_config_tag_options_modal_1.DashboardConfigTagOptionsModal, dashboard_config_import_export_modal_1.DashboardConfigImportExportModal, dashboard_config_tag_value_quality_modal_1.DashboardConfigTagValueQualityModal, download_component_1.DownloadComponent, download_modal_component_1.DownloadModalComponent, alert_log_modal_1.AlertLogModal]
                    })
                ], AppModule);
                return AppModule;
            }());
            exports_1("AppModule", AppModule);
        }
    };
});
//# sourceMappingURL=app.module.js.map