import { Component, Input, Output, EventEmitter, HostListener } from "@angular/core";
import { EditorFieldObjectDTO } from "../../data/model/models";

@Component({
  selector: "dashboardConfigTagsGridSearchComponent",
  styles: [`
  .hide {
    display: none;
  }
  .title {
    font-size: 16px; 
    font-weight: bold;
    padding-left: 30px;
  }
  .panel-content {
    padding: 6px
  }
  .search-button {
    top: -2px !important;
    left: -6px;
    position: relative;
  }
  .panel-heading {
    background-color: transparent;
    position: relative;
    padding: 0 0 0 2px;
    top: 30px;
    margin-top: -30px;
  }
  .front {
    position: relative;
    z-index: 2;
  }`],
  template: `
      <div class="panel-heading">
        <div *ngIf="searchIsActive" class="round-button front" title="{{'TR_COLLAPSE' | translate}}" (click)="toggle()"><img [src]="'../../images/magnifier.svg'" class="image-button"/></div>
        <div *ngIf="!searchIsActive" class="round-button front" title="{{'TR_EXPAND' | translate}}" (click)="toggle()"><img [src]="'../../images/magnifier.svg'" class="image-button"/></div>
      </div>
      <div class="panel-content" [ngClass]="{hide: !searchIsActive}">
        <div class="title">
          {{'TR_SEARCH_CRITERIA' | translate}}
        </div>
		    <div class="form-group">
          <ng-container *ngFor="let searchField of searchCriteria?.searchFields">
            <div style="display:inline-block"><img *ngIf="searchField.searchHelp != ''" src="../../../images/help.svg" class="help-icon" title="{{ searchField.searchHelp | translate }}" /></div>
            <div style="display:inline-block;vertical-align: bottom;" class="col-form-label" title="{{ searchField.label | translate}}">{{ searchField.label | translate}}</div>
            <div *ngIf="(searchField.controlType === controlTypeEnum.Text)"><input type="text" class="form-control input-sm" [(ngModel)]="searchField.value"></div>
            <div *ngIf="(searchField.controlType === controlTypeEnum.Combobox)">
			        <select class="form-control input-sm" [(ngModel)]="searchField.value">
				          <option *ngFor="let item of searchField.controlSource" [ngValue]="item.value">{{item.text}}</option>
			        </select>
            </div>
          </ng-container>   
        </div>
        <div>
          <button class="btn btn-default btn-sm" style="float: right; margin: 10px;" (click)="onSearchClick(searchTypeEnum.DeepSearch)" title="{{ 'TR_DEEP_GRID_SEARCH' | translate }}"><img src="../../images/magnifier.svg" class="image-button" />&nbsp;{{ 'TR_DEEP_SEARCH' | translate }}</button>
          <button *ngIf="!isRecursive" class="btn btn-default btn-sm" style="float: right;margin: 10px;" (click)="onSearchClick(searchTypeEnum.Search)" title="{{ 'TR_GRID_SEARCH' | translate }}"><img src="../../images/magnifier.svg" class="image-button" />&nbsp;{{ 'TR_SEARCH' | translate }}</button>
          <button class="btn btn-default btn-sm" style="float: right; margin: 10px;" (click)="onSearchClick(searchTypeEnum.Clear)" title="{{ 'TR_CLEAR_SEARCH' | translate }}"><img src="../../images/delete.svg" class="image-button" />&nbsp;{{ 'TR_CLEAR_SEARCH' | translate }}</button>
        </div>
      </div>`
})

export class DashboardConfigTagsGridSearchComponent {
  @Output() onSearch: EventEmitter<object> = new EventEmitter();
  @Output() onSearchIsActive: EventEmitter<boolean> = new EventEmitter();
  @Input() searchCriteria: SearchCriteria;
  @Input() isRecursive: boolean;

  private searchIsActive: boolean = false;
  private controlTypeEnum = EditorFieldObjectDTO.ControlTypeEnum;
  private searchTypeEnum = SearchTypeEnum;

  private toggle(): void {
    this.searchIsActive = !this.searchIsActive;
    this.onSearchIsActive.emit(this.searchIsActive);
    if (!this.searchIsActive) {
      this.onSearch.emit(null);
    }
  }

  private onSearchClick(searchType: SearchTypeEnum): void {
    let searchFields: SearchField[] = this.searchCriteria.searchFields
    switch (searchType) {
      case SearchTypeEnum.Search:
        this.searchCriteria.searchType = searchType;
        this.onSearch.emit(this.searchCriteria);
        break;
      case SearchTypeEnum.DeepSearch:
        this.searchCriteria.searchType = searchType;
        this.onSearch.emit(this.searchCriteria);
        break;
      case SearchTypeEnum.Clear:
        this.searchCriteria.searchFields.forEach((searchField) => {
          searchField.value = "";
        });
        this.onSearch.emit();
        break;
    }
  }

  @HostListener('keypress', ['$event'])
  onKeyPress(event: KeyboardEvent) {
    let input = String.fromCharCode(event.keyCode);
    if (!((event.key == "$") || (event.key == ".") || (event.key == "*") || (event.key == "_") || (/[a-zA-Z0-9]/.test(input)))) {
      event.preventDefault();
      return false;
    }
  }
}

export class SearchCriteria  {
  constructor(
    public searchFields: Array<SearchField>,
    public searchType: SearchTypeEnum,
  ) { }
}

export class SearchField {
  constructor(
    public id: string,
    public label: string,
    public value: string,
    public controlType: EditorFieldObjectDTO.ControlTypeEnum,
    public controlSource: Array<Object>,
    public searchHelp: string = "",
  ) { }
}

export enum SearchTypeEnum {
  Clear,
  Search,
  DeepSearch
}
