  import {Directive, HostListener, HostBinding} from '@angular/core';

  @Directive({
    selector: '[dropdown]'
  })
  export class DropdownDirective {

    private isOpen:boolean = false;

    @HostBinding('class.open') get opened(){
      return this.isOpen;
    }
    constructor() { }

    @HostListener('mouseover')open(){
      this.isOpen = true;
    }

    @HostListener('mouseleave')close(){
      this.isOpen = false;
    }
  }
