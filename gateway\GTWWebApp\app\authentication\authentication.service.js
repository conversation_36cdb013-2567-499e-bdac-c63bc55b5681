System.register(["@angular/core", "../data/configuration", "@angular/router", "ngx-modialog-7/plugins/bootstrap", "ngx-modialog-7", "./authentication.login.modal", "./authentication.password.modal", "../global/global.data.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, configuration_1, router_1, bootstrap_1, ngx_modialog_7_1, authentication_login_modal_1, authentication_password_modal_1, global_data_service_1, AuthenticationService, UserRoleEnum;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (authentication_login_modal_1_1) {
                authentication_login_modal_1 = authentication_login_modal_1_1;
            },
            function (authentication_password_modal_1_1) {
                authentication_password_modal_1 = authentication_password_modal_1_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            }
        ],
        execute: function () {
            AuthenticationService = (function () {
                function AuthenticationService(modal, router, globalDataService) {
                    this.modal = modal;
                    this.router = router;
                    this.globalDataService = globalDataService;
                    this.userApiConfig = new configuration_1.Configuration({ apiKeys: { "Authorization": this.uuid() } });
                }
                Object.defineProperty(AuthenticationService.prototype, "role", {
                    get: function () {
                        if (this.userApiConfig.accessToken != null)
                            return this.userApiConfig.accessToken.toString();
                        else
                            return "";
                    },
                    enumerable: false,
                    configurable: true
                });
                AuthenticationService.prototype.login = function (authentication) {
                    this.userApiConfig.username = authentication.username;
                    this.userApiConfig.accessToken = UserRoleEnum[authentication.role];
                };
                AuthenticationService.prototype.logoff = function () {
                    this.userApiConfig = new configuration_1.Configuration();
                };
                AuthenticationService.prototype.onLoginFailed = function (route) {
                    var _this = this;
                    if (!this.authenticationLoginModalRef) {
                        this.authenticationLoginModalRef = this.modal.open(authentication_login_modal_1.AuthenticationLoginModal, ngx_modialog_7_1.overlayConfigFactory({ userApiConfig: this.userApiConfig }, bootstrap_1.BSModalContext));
                        this.router.navigate(["/blank"]);
                        this.authenticationLoginModalRef.result.then(function (data) {
                            if (data.status == 419) {
                                _this.modal.open(authentication_password_modal_1.AuthenticationPasswordModal, ngx_modialog_7_1.overlayConfigFactory({ authenticationService: _this, username: data.username, isPasswordReset: true }, bootstrap_1.BSModalContext));
                            }
                            else {
                                var isRememberMeActive = _this.authenticationLoginModalRef.context.isRememberMeActive;
                                _this.login(data);
                                if (route != null)
                                    _this.router.navigate([route]);
                                _this.authenticationLoginModalRef = null;
                            }
                        });
                    }
                };
                AuthenticationService.prototype.uuid = function () {
                    return '00000000-0000-0000-0000-000000000000'.replace(/[018]/g, function (c) {
                        return (+c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> +c / 4).toString(16);
                    });
                };
                AuthenticationService = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [bootstrap_1.Modal, router_1.Router, global_data_service_1.GlobalDataService])
                ], AuthenticationService);
                return AuthenticationService;
            }());
            exports_1("AuthenticationService", AuthenticationService);
            (function (UserRoleEnum) {
                UserRoleEnum[UserRoleEnum["NO_ROLE"] = 0] = "NO_ROLE";
                UserRoleEnum[UserRoleEnum["VIEWER_ROLE"] = 1] = "VIEWER_ROLE";
                UserRoleEnum[UserRoleEnum["OPERATOR_ROLE"] = 2] = "OPERATOR_ROLE";
                UserRoleEnum[UserRoleEnum["CONFIGURATOR_ROLE"] = 3] = "CONFIGURATOR_ROLE";
                UserRoleEnum[UserRoleEnum["SU_ROLE"] = 4] = "SU_ROLE";
            })(UserRoleEnum || (UserRoleEnum = {}));
            exports_1("UserRoleEnum", UserRoleEnum);
        }
    };
});
//# sourceMappingURL=authentication.service.js.map