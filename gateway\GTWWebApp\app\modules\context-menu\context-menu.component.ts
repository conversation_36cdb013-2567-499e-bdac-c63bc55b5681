import { Component, On<PERSON><PERSON>roy} from "@angular/core";
import { ContextMenuService } from "./context-menu.service";
import { ContextMenuItem, ContextMenuType } from "./context-menu-item";
import { Subject, Subscription } from 'rxjs';

@Component({
  selector: "contextMenuComponent",
  styles: [`
      .item {
        cursor: pointer;
        padding: 2px,
      }
      .item:hover {
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .separator {
        margin: 1px;
      }
      .disabled {
        opacity: 0.65; 
        cursor: not-allowed;
      }
      .not-license:after {
        content: ' (not licensed)';
        white-space: nowrap;
      }
      .logo {
          width: 14px;
          height: 14px;
          margin-top: -4px;
          margin-right: 3px;
          margin-left: -1px;
      }
`],
  host:{
    "(document:click)":"clickedOutside($event)",
  },
  template:`
      <div [ngStyle]="locationCss" class="panel panel-default">
        <div class="item" *ngFor="let item of contextMenuitems">
          <div *ngIf="item.type == contextMenuType.ACTION" [ngClass]="{'disabled': (!item.isLicensed)}" (click)="onClickItem(item)"><img src="{{'../images/' + item.icon}}" class="logo" *ngIf="(item.icon != null)"><div [ngClass]="{'not-license': (!item.isLicensed)}">{{item.title | translate}}</div></div>
          <div *ngIf="item.type == contextMenuType.ACTION_BOLD" (click)="onClickItem(item)"><img src="{{'../images/' + item.icon}}" class="logo" *ngIf="(item.icon != null)"><b><i>{{item.title | translate}}</i></b></div>
          <div *ngIf="item.type == contextMenuType.SEPARATOR"><hr class="separator"></div>
          <div *ngIf="item.type == contextMenuType.CHECKBOX" (click)="onClickItem(item)"><input type="checkbox" class="form-check" [checked]=item.value />&nbsp;<span>{{item.title | translate}}</span></div>
        </div>
      </div>
    `
})

export class ContextMenuComponent implements OnDestroy{
  private contextMenuitems: Array<ContextMenuItem> = [];
  private isShown = false;
  private mouseLocation: { left: number, top: number } = { left: 0, top: 0 };
  private contextMenuType = ContextMenuType;
  private contextMenuSubscriptionShow: Subscription;
  private contextMenuSubscriptionHide: Subscription;

  constructor(private contextMenuService: ContextMenuService) {
    this.contextMenuSubscriptionShow = contextMenuService.show.subscribe(e => this.showMenu(e.event, e.obj));
    this.contextMenuSubscriptionHide = contextMenuService.hide.subscribe(e => this.isShown = false);
  }

  ngOnDestroy() {
    this.contextMenuSubscriptionShow.unsubscribe();
    this.contextMenuSubscriptionHide.unsubscribe();
  }
  
  private get locationCss(){ 
    return {
      "z-index":"2",
      "position": "fixed",
      "padding": "4px",
      "display":this.isShown ?  "block":"none",
      left: (this.mouseLocation.left - 4) + "px",
      top: (this.mouseLocation.top - 4) + "px",
    };
  }

  private clickedOutside(event): void {
    if (event.target.id != "textSearch") 
      this.isShown = false;
  }
  
  private showMenu(event, contextMenuitems): void {
    this.isShown = true;
    this.contextMenuitems = contextMenuitems;
    this.mouseLocation = {
      left:event.clientX,
      top:event.clientY
    }
  }

  private onClickItem(item: ContextMenuItem): void {
    if (item.isLicensed)
      item.subject.next(item.title)
  }
}