System.register(["@angular/core", "./data/wsApi/wsApi", "./data/model/models", "./global/global.data.service", "./modules/alert/alert.service", "@ngx-translate/core", "@angular/router"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, wsApi_1, models_1, global_data_service_1, alert_service_1, core_2, router_1, AppHealthComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (global_data_service_1_1) {
                global_data_service_1 = global_data_service_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            }
        ],
        execute: function () {
            AppHealthComponent = (function () {
                function AppHealthComponent(router, healthWSApi, globalDataService, alertService, translate) {
                    this.router = router;
                    this.healthWSApi = healthWSApi;
                    this.globalDataService = globalDataService;
                    this.alertService = alertService;
                    this.translate = translate;
                    this.healthData = {};
                    this.isEngineRunning = true;
                    this.isEngineExitFailState = true;
                    this.EngineStateEnumDTO = models_1.EngineStateEnumDTO;
                    this.noLicenseDetail = "";
                    this.gracePeriodLicenseDetail = "";
                    this.warningOpen = true;
                    this.reconnectAttempts = 0;
                }
                AppHealthComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    this.getHealth();
                    this.translate.get("TR_RUNNING_NO_LICENSE_DETAIL", { URL_ENGINE: "https://" + this.globalDataService.SDGConfig.gtwHost + ":" + this.globalDataService.SDGConfig.monHttpPort, HOST: this.globalDataService.SDGConfig.gtwHost }).subscribe(function (res) {
                        _this.noLicenseDetail = res;
                    });
                    this.translate.get("TR_RUNNING_GRACE_PERIOD_LICENSE_DETAIL", { URL_ENGINE: "https://" + this.globalDataService.SDGConfig.gtwHost + ":" + this.globalDataService.SDGConfig.monHttpPort, HOST: this.globalDataService.SDGConfig.gtwHost }).subscribe(function (res) {
                        _this.gracePeriodLicenseDetail = res;
                    });
                };
                AppHealthComponent.prototype.ngOnDestroy = function () {
                    if (this.healthWSApi.websocket !== null && this.healthWSApi.websocket.readyState === WebSocket.OPEN)
                        this.healthWSApi.websocket.close();
                    if (this.healthServiceSubscription != null)
                        this.healthServiceSubscription.unsubscribe();
                };
                AppHealthComponent.prototype.getHealth = function () {
                    var _this = this;
                    this.healthServiceSubscription = this.healthWSApi.getHealthData().subscribe(function (event) {
                        if (event.type === 'message') {
                            _this.reconnectAttempts = 0;
                            _this.healthData = JSON.parse(event.data);
                            _this.globalDataService.healthData = _this.healthData;
                            _this.isEngineExitFailState = false;
                            if (_this.healthData != null && _this.healthData.engineState != models_1.EngineStateEnumDTO.RUNNING.toString() && _this.healthData.engineExitFailState != models_1.EngineExitFailStateEnumDTO.SUCCESS.toString())
                                _this.isEngineExitFailState = true;
                            if (_this.healthData != null && _this.healthData.engineState != models_1.EngineStateEnumDTO.RUNNING.toString() && _this.healthData.engineState != models_1.EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString() && _this.healthData.engineState != models_1.EngineStateEnumDTO.RUNNING_NO_LICENSE.toString() && _this.isEngineRunning) {
                                _this.isEngineRunning = false;
                                window.setTimeout(function () { _this.checkIsEngineRunningState(); }, 300000);
                            }
                            else if (_this.healthData != null && (_this.healthData.engineState == models_1.EngineStateEnumDTO.RUNNING.toString() || _this.healthData.engineState == models_1.EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString())) {
                                _this.isEngineRunning = true;
                            }
                        }
                    }, function (error) {
                        _this.healthData = {};
                        _this.globalDataService.healthData = {};
                        if (error != "restart") {
                            _this.displayRefreshModal();
                        }
                    });
                };
                AppHealthComponent.prototype.checkIsEngineRunningState = function () {
                    if (!this.isEngineRunning) {
                        var broadcastEvent = {
                            messageKey: "TR_ENGINE_NOT_RESPONDING",
                            messageLogMask: 3,
                            messageText: "Gateway Engine is not running (check the SDG Engine logs or System logs for possible errors).",
                            messageTime: new Date().toUTCString(),
                            messageType: models_1.BroadcastEventTypeEnumDTO.MessageError,
                            parameters: null
                        };
                        this.alertService.show(broadcastEvent, false, -1);
                        this.router.navigate(["/config"]);
                    }
                };
                AppHealthComponent.prototype.displayRefreshModal = function () {
                    var broadcastEvent = {
                        messageKey: "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER",
                        messageLogMask: 3,
                        messageText: "The connection with the Gateway Monitor was lost.<br /> Please refresh your browser when the monitor is running again.",
                        messageTime: new Date().toUTCString(),
                        messageType: models_1.BroadcastEventTypeEnumDTO.MessageError,
                        parameters: null
                    };
                    this.alertService.show(broadcastEvent, false, -1);
                };
                AppHealthComponent.prototype.closeWarning = function () {
                    this.warningOpen = false;
                };
                AppHealthComponent = __decorate([
                    core_1.Component({
                        selector: "appHealthComponent",
                        template: "\n  <div *ngIf=\"(this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_NO_LICENSE.toString() && warningOpen)\" class=\"alert blinking\" [innerHtml]=\"this.noLicenseDetail\"></div>\n  <div *ngIf=\"(this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_IN_GRACE_PERIOD.toString() && warningOpen )\" class=\"alert blinking\" [innerHtml]=\"this.gracePeriodLicenseDetail\"></div>\n  <div *ngIf=\"(this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_NO_LICENSE.toString() || this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_IN_GRACE_PERIOD.toString()) && warningOpen\">\n    <div class=\"button-close round-button warning\" title=\"Close\" (click)=\"closeWarning()\"><img class=\"image-button\" src=\"../../images/close.svg\"></div>\n  </div>\n  <div style=\"float: right;\">\n\t\t<div>{{'TR_STATUS' | translate}}&nbsp;</div>\n\t\t<div title=\"{{'TR_MONITOR' | translate}}\">\n\t\t\t<div>{{'TR_MONITOR_ABBREVIATION' | translate}}</div>\n\t\t\t<div *ngIf=\"this.healthData?.monitorOk\" class=\"glyphicon glyphicon-ok-sign\" style=\"color:green;font-size: 12px\"></div>\n\t\t\t<div *ngIf=\"!this.healthData?.monitorOk\" class=\"glyphicon glyphicon-exclamation-sign\" style=\"color:red;font-size: 12px;\"></div>\n\t\t</div>\n\t\t<div title=\"{{'TR_ENGINE' | translate}}\">\n\t\t\t<div>{{'TR_ENGINE_ABBREVIATION' | translate}}</div>\n\t\t\t<div *ngIf=\"this.healthData?.engineState == EngineStateEnumDTO?.RUNNING.toString()\" class=\"glyphicon glyphicon-ok-sign\" style=\"color:green;font-size: 12px;\"></div>\n\t\t\t<div *ngIf=\"this.healthData?.engineState==null || this.healthData?.engineState == EngineStateEnumDTO?.NOTRUNNING.toString()\" class=\"glyphicon glyphicon-exclamation-sign\" style=\"color:red;font-size: 12px;\"></div>\n\t\t\t<div *ngIf=\"this.healthData?.engineState != EngineStateEnumDTO?.RUNNING.toString() && this.healthData?.engineState != EngineStateEnumDTO?.NOTRUNNING.toString() && this.healthData?.engineState != null\" class=\"blinking glyphicon glyphicon-ok-sign\" style=\"font-size: 12px;\">\n        <div style=\"font-size: 10px;font-weight: bold;vertical-align: text-top;\">({{ ('TR_' + this.healthData?.engineState) | translate}})</div>\n\t\t\t</div>\n\t\t</div>\n\t\t<div style=\"width:10px;\"></div>\n\t\t<div><img src=\"../../images/cpu.svg\" class=\"image\" title=\"{{'TR_CPU_ABBREVIATION' | translate}}\"/></div>\n\t\t<div title=\"{{'TR_OVERALL' | translate}}\">{{this.healthData?.sysCpu > 0 ? this.healthData?.sysCpu + '%' : '0%'}}/</div>\n\t\t<div title=\"{{'TR_THIS_SYSTEM' | translate}}\">{{this.healthData?.engineCpu > 0 ? this.healthData?.engineCpu + '%' : '0%'}}</div>\n\t\t<div style=\"width:10px;\"></div>\n\t\t<div><img src=\"../../images/memory.svg\" class=\"image\" title=\"{{'TR_MEMORY_ABBREVIATION' | translate}}\"/></div>\n\t\t<div title=\"{{'TR_OVERALL' | translate}}\">{{this.healthData?.sysMem > 0 ?  this.healthData?.sysMem + '%' : '0%'}}/</div>\n\t\t<div title=\"{{'TR_THIS_SYSTEM' | translate}}\">{{this.healthData?.engineMem > 0 ? this.healthData?.engineMem + '%' : '0%'}}&nbsp;&nbsp;</div>\n\t</div>\n  ",
                        styles: ["\n\t\tdiv {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t\t.before-box {\n\t\t\tclear: left;\n\t\t}\n\t\t.after-box {\n\t\t\tclear: left;\n\t\t}\n    .blinking{\n        animation:blinkingText 0.8s infinite;\n    }\n    @keyframes blinkingText{\n        0%{     color: orange;    }\n        50%{    color: green;     }\n        100%{   color: orange;    }\n    }\n    .warning{\n      position: fixed;\n      bottom: 70px;\n      right: 8px;\n      z-index: 999;\n    }\n    .alert {\n      position: fixed;\n      bottom: 4px;\n      right: 4px;\n      z-index: 999;\n      font-weight: bold;\n      background-color: red;\n      border: 2px solid gray;\n      font-size: 14px;\n    }\n"]
                    }),
                    __metadata("design:paramtypes", [router_1.Router, wsApi_1.HealthWSApi, global_data_service_1.GlobalDataService, alert_service_1.AlertService, core_2.TranslateService])
                ], AppHealthComponent);
                return AppHealthComponent;
            }());
            exports_1("AppHealthComponent", AppHealthComponent);
        }
    };
});
//# sourceMappingURL=app.health.component.js.map