﻿import { Component, OnInit } from "@angular/core";
import { MiscService } from "../data/api/api";
import { SDGAboutDTO } from "../data/model/models";
import { AlertService } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";

@Component({
  selector: "helpAboutComponent",
  styles: [`
   .center{
      text-align: center
    }
	`],
  template: `
		<div>
		  <div class="panel panel-default panel-main" style="width:99%; margin-left:6px; margin-top:60px">
        <div class="panel-heading"><img src="../../images/about.svg" class="module-icon" />{{'TR_ABOUT' | translate}}</div>
	      <div class="panel-body center" [innerHTML]="aboutContent"></div>
      </div>
		</div>`
})

export class HelpAboutComponent implements OnInit {
  private aboutContent: string = "";

  constructor(private miscService: MiscService, private alertService: AlertService, private authenticationService: AuthenticationService) {
  }

  public ngOnInit(): void {
    this.miscService.getAbout().subscribe(
      data => {
        let aboutDTO: SDGAboutDTO;
        aboutDTO = data;
        this.aboutContent = aboutDTO.aboutContent;
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_READ_ABOUT_CONTENT"); }
      }
    );
  }
}