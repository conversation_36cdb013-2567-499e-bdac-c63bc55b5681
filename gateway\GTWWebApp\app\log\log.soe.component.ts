﻿import { Component, Input, AfterView<PERSON>he<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild } from "@angular/core";
import { FileService } from "../data/api/api";
import { AuthenticationService } from "../authentication/authentication.service";
import { Panel } from '../modules/panel/panel';
import { LogEntryDTO } from "../data/model/models";
import { AlertService } from "../modules/alert/alert.service";
import { KeysPipe } from "../global/keys.pipe";
import { DownloadComponent } from "../modules/download/download.component";

@Component({
  selector: "logSoeComponent",
  providers: [KeysPipe],
  styles: [`
      .download-log {
        position: absolute;
        top: 106px;
        z-index: 999;
      }
      `],
  template: `
  		<div style="min-width: 960px">
		    <div class="panel panel-default panel-main" style="width:99%; margin-left:6px; margin-top:60px">
          <div class="panel-heading"> <img src="../../images/soeLog.svg" class= "module-icon" /> {{ 'TR_SOE_LOG' | translate }}</div>
	        <div class="panel-body" style="min-height:40px">
            <div class="download-log" *ngIf="this.authenticationService.role | checkRole:'OPERATOR_ROLE'">
              <select class="input-sm" [(ngModel)]="selectedLogFileName" [ngClass]="{'placeholder':selectedLogFileName==''}">
                <option value="" disabled selected>{{'TR_SELECT' | translate}}</option>
                <option *ngFor="let logFileName of logFileNameList" [ngValue]="logFileName.value">{{logFileName.value}}</option>
              </select>
              <button type="button" id="downloadFile" (click)="onlogFileDownloadClick()" class="btn btn-default btn-sm"><img src="../../images/download.svg" class="image-button"/>&nbsp;{{ 'TR_DOWNLOAD_SELECTED_LOG_FILE' | translate }}</button>
            </div>
	        </div>
        </div>
		</div>
    <downloadComponent #download></downloadComponent>`
})

export class LogSoeComponent implements OnInit {
  @ViewChild("download", { static: false }) download: DownloadComponent;
  private logFileNameList: string[];
  private selectedLogFileName: string = "";

  constructor(private authenticationService: AuthenticationService, private fileService: FileService, private alertService: AlertService, private keysPipe: KeysPipe) { }

  public ngOnInit(): void {
    this.getFileNameList();
  }

  private getFileNameList(): void {
    this.fileService.filesGet("SOELogs", ".log .txt").subscribe(
      (data: any) => {
        if (data.result)
          this.logFileNameList = this.keysPipe.transformJson(data.files);
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED"); }
      }
    );
  }

  private onlogFileDownloadClick(): void {
    if (this.selectedLogFileName != undefined && this.selectedLogFileName != "") {
      this.download.downloadClick(this.selectedLogFileName, "SOELogs");
    }
    else {
      this.alertService.error("TR_PLEASE_SELECT_A_FILE");
    }
  }
}