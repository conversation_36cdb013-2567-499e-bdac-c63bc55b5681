﻿import { Component, ComponentFactory<PERSON><PERSON><PERSON><PERSON>, ElementRef, Injector } from "@angular/core";
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { TagPurposeFilterEnumDTO } from "../../data/model/models";
import { AlertService } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { DownloadModalComponent } from "../../modules/download/download.modal.component";
import { TagsService, MappingsService, FileService } from "../../data/api/api";
import { Column } from "../../modules/grid/column";
import { GlobalObjectToCSVUtil } from "../../global/global.objectToCSV.util";
import { ViewChild } from "@angular/core";

@Component({
  selector: "dashboardConfigImportExportModal",
  styles: [`
      .tag-modal-content {
		    padding: 10px;
      }
      label {
        margin: 0px 60px 0px 6px;
      }
      fieldset {
        border-radius: 6px;
        border: 1px Gray solid;
        margin-bottom: 20px;
        padding: 0px 8px 8px 8px; 
      }
      legend {
        width: auto;
        margin-bottom: 4px;
      }
      .summary {
        padding: 4px;
        background-color: whitesmoke;
        width: 180px;
        border-radius: 5px;
        border: 1px gray solid;
        box-shadow: 1px 1px grey;
      }
      .tag-modal-heading {
        background-color: #d8d8d8;
        padding: 8px 10px 6px 10px;
        font-size: 22px;
        font-weight: bold;
        border-bottom: 1px solid #a31c3f;
        overflow-wrap: break-word;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }
    `],
  template: `
    <div class="container-fluid">
      <div class="tag-modal-heading">
       {{'TR_IMPORT_EXPORT_POINTS' | translate}}
	    </div>
      <div class="tag-modal-content">
        <fieldset><legend>{{'TR_EXPORT' | translate}}</legend>
          <input name="options" ng-control="options" type="radio" [value]="3"  [(ngModel)]="importExportCSV"><label>{{'TR_EXPORT_POINTS' | translate}}</label>
          <input name="options" ng-control="options" type="radio" [value]="4" [(ngModel)]="importExportCSV"><label>{{'TR_EXPORT_MAPPINGS' | translate}}</label>
          <br/>
          <input ng-control="checkbox" type="checkbox" [(ngModel)]="isRecursive" ><label>{{'TR_EXPORT_RECURSIVELY' | translate}}</label>
        </fieldset>
        <fieldset *ngIf="(this.context.nodeFullName === '')"><legend>{{'TR_IMPORT' | translate}}</legend>
          <input ng-control="options" type="radio" [value]="1"  [(ngModel)]="importExportCSV" ><label>{{'TR_IMPORT_POINTS' | translate}}</label>
          <input ng-control="options" type="radio" [value]="2" [(ngModel)]="importExportCSV" ><label>{{'TR_IMPORT_MAPPINGS' | translate}}</label>

          <div *ngIf="(importExportResults?.length > 0)">
            <b>{{'TR_RESULTS' | translate}}</b>
            <div style="font-size: 10px; display: flex; font-weight: bold; border-radius: 25px;">
              <div class="summary" style="margin-right: 20px">
                <span style="color: green">{{'TR_MDO_CREATED' | translate}}: {{importExportResultsSummary.mdo_created}}</span><br>
                <span style="color: orange">{{'TR_MDO_DUPLICATED' | translate}}: {{importExportResultsSummary.mdo_duplicate}}</span><br>
                <span style="color: red">{{'TR_MDO_FAILED' | translate}}: {{importExportResultsSummary.mdo_failed}}<br></span>
              </div>
              <div class="summary">
                <span style="color: green">{{'TR_SDO_CREATED' | translate}}: {{importExportResultsSummary.sdo_created}}</span><br>
                <span style="color: orange">{{'TR_SDO_DUPLICATED' | translate}}: {{importExportResultsSummary.sdo_duplicate}}</span><br>
                <span style="color: red">{{'TR_SDO_FAILED' | translate}}: {{importExportResultsSummary.sdo_failed}}<br></span>
              </div>
            </div>
            <div style="max-height: 500px; overflow-y: scroll;">
		          <gridComponent [rows]="importExportResults" [columns]="columns"></gridComponent>
            </div>
            <button class="btn btn-default btn-sm copy-button" (click)="copyToCSV()"><img src="../../images/download.svg" class="image-button"/>&nbsp;{{'TR_DOWNLOAD_RESULTS' | translate}}</button>
          </div>
        </fieldset>
      </div>
      <div style="display: inline-block; width:96%; text-align: center; margin: 10px;">
      <div class="form-group" style="display:inline-block; width:92%; text-align:center;">
	      <div style="display: table-cell;">
		      <button class="btn btn-default" (click)="close()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CLOSE' | translate}}</button>
	      </div>
	      <div style="display: table-cell;width:90%"></div>
		    <div style="display: table-cell;" *ngIf="(importExportCSV == 1 || importExportCSV == 2)">
			    <button class="btn btn-default" (click)="save()"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_SELECT_FILE' | translate}}</button>
		    </div>
		    <div style="display: table-cell;" *ngIf="(importExportCSV == 3 || importExportCSV == 4)">
			    <button class="btn btn-default" (click)="save()"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_EXPORT' | translate}}</button>
		    </div>
	    </div>
	    <alertStatusBarComponent></alertStatusBarComponent>
      <input class="importPointsFile" #uploadFile style="display: none;" type="file" accept=".csv" (change)="onImportExportFileUploadChange($event, 'IMPORT_POINTS')">
      <input class="importMappingsFile" #uploadFile style="display: none;" type="file" accept=".csv" (change)="onImportExportFileUploadChange($event, 'IMPORT_MAPPINGS')">
    </div>`
})

export class DashboardConfigImportExportModal implements CloseGuard, ModalComponent<ImportExportModalContext> {
  private importExportResults: Array<ImportExportResultObjectDTO>;
  private importExportResultsSummary: ImportExportResultSummaryObjectDTO = {};
  private columns: Array<Column>;
  private context: ImportExportModalContext;
  private importExportCSV: ImportExportCSV = ImportExportCSV.EXPORT_POINTS;
  private isRecursive: boolean;
  @ViewChild("uploadFile", { static: false }) uploadFile: ElementRef;

  constructor(public dialog: DialogRef<ImportExportModalContext>, private alertService: AlertService,
    private authenticationService: AuthenticationService, private translate: TranslateService, private componentFactoryResolver: ComponentFactoryResolver, private injector: Injector) {
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-lg";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
    this.columns = this.getColumns();
  }

  public beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private close() {
    this.dialog.close({ result: true });
  }

  private save() {
    if (this.importExportCSV == ImportExportCSV.EXPORT_POINTS || this.importExportCSV == ImportExportCSV.EXPORT_MAPPINGS) {
      let tagPurposeFilterEnum;
      let fileName: string;
      if (this.importExportCSV == ImportExportCSV.EXPORT_POINTS) {
        tagPurposeFilterEnum = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV | TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
        fileName = (this.context.nodeFullName == "" ? "root" : this.context.nodeFullName) + "_points.csv";
      }
      else {
        tagPurposeFilterEnum = TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV | TagPurposeFilterEnumDTO.TagPurposeFilterEnum.GTWTYPES_TAG_PURPOSE_ALL;
        fileName = (this.context.nodeFullName == "" ? "root" : this.context.nodeFullName) + "_mappings.csv";
      }

      this.context.tagsService.getTags(
        this.context.nodeFullName,     //nodeFullName - Root
        null,   //sortColumn
        null,   //sortColumnDirection
        null,   //valueTypeFilter
        null,   //tagNameFilter
        null,   //tagDescription
        null,   //tagUserNameFilter
        "MDO",  //tagCollectionKindFilter
        tagPurposeFilterEnum,   //tagPuposeFilter
        this.isRecursive,  //recursive
        0,      //startIndex
        0,      //endIndex
        null,   //fieldSelectionMask
      ).subscribe(
        data => {
          const dynamicComponentFactory = this.componentFactoryResolver.resolveComponentFactory(DownloadModalComponent);
          const componentRef = dynamicComponentFactory.create(this.injector);
          componentRef.instance.downloadClick(fileName, "CurrentWorkspace", this.context.fileService);
          this.alertService.success("TR_FILE_DOWNLOADED");
        },
        error => {
          if (error.status == 401) {
            this.authenticationService.onLoginFailed("/");
          }
          else {
            this.alertService.error("TR_ERROR");
          }
        }
      );
    }
    else if (this.importExportCSV == ImportExportCSV.IMPORT_POINTS) {
      let element: HTMLElement = document.getElementsByClassName("importPointsFile")[0] as HTMLElement;
      element.click();
    }
    else if (this.importExportCSV == ImportExportCSV.IMPORT_MAPPINGS) {
      let element: HTMLElement = document.getElementsByClassName("importMappingsFile")[0] as HTMLElement;
      element.click();
    }
  }

  private copyToCSV(): void {
    GlobalObjectToCSVUtil.copyToCSV("ImportResults.csv", this.importExportResults);
  }

  private onImportExportFileUploadChange(event, importExportCSV: string): void {
    if (importExportCSV == "IMPORT_POINTS") {
      let fileList: FileList = event.target.files;
      if (fileList.length > 0) {
        let file: File = fileList[0];
        event.target.value = "";
        this.context.tagsService.postPoints(file).subscribe(
          (data: any) => {
            if (data != null && data != "") {
              let result = data.result;
              if (result)
                this.alertService.success("TR_SUCCESS");
              else
                this.alertService.error("TR_ERROR");
              this.importExportResults = data.messages;
              this.importExportResultsSummary.mdo_created = data.mdo_created;
              this.importExportResultsSummary.mdo_failed = data.mdo_failed;
              this.importExportResultsSummary.mdo_duplicate = data.mdo_duplicate;
              this.importExportResultsSummary.sdo_created = data.sdo_created;
              this.importExportResultsSummary.sdo_failed = data.sdo_failed;
              this.importExportResultsSummary.sdo_duplicate = data.sdo_duplicate;
            }
          },
          error => {
            if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR"); }
          }
        );
      }
    }
    else if (importExportCSV == "IMPORT_MAPPINGS") {
      let fileList: FileList = event.target.files;
      if (fileList.length > 0) {
        let file: File = fileList[0];
        this.context.mappingsService.postMappings(file).subscribe(
          (data: any) => {
            if (data != null && data != "") {
              let result = data.result;
              if (result)
                this.alertService.success("TR_SUCCESS");
              else
                this.alertService.error("TR_ERROR");
              this.importExportResults = data.messages;
              this.importExportResultsSummary.mdo_created = data.mdo_created;
              this.importExportResultsSummary.mdo_failed = data.mdo_failed;
              this.importExportResultsSummary.mdo_duplicate = data.mdo_duplicate;
              this.importExportResultsSummary.sdo_created = data.sdo_created;
              this.importExportResultsSummary.sdo_failed = data.sdo_failed;
              this.importExportResultsSummary.sdo_duplicate = data.sdo_duplicate;
            }
          },
          error => {
            if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR"); }
          }
        );
      }
    }
  }

  private getColumns(): Array<Column> {
    return [
      new Column("lineNumber", "TR_LINE", "", ""),
      new Column("isSuccess", "TR_SUCCESS", "", ""),
      new Column("path", "TR_POINT_PATH", "", ""),
      new Column("message", "TR_MESSAGE", "action$innerHTML", ""),
      new Column("messageDetail", "TR_MESSAGE_DETAIL", "", "")
    ];
  }

  private removeBRFromMessages(importExportResults: Array<ImportExportResultObjectDTO>): Array<ImportExportResultObjectDTO> {
    importExportResults.forEach((result) => {
      result.message.replace("<br/>", "");
    });
    return importExportResults;
  }
}

export class ImportExportModalContext extends BSModalContext {
  nodeFullName: string = "";
  tagsService: TagsService = null;
  mappingsService: MappingsService = null;
  fileService: FileService = null;
}

export interface ImportExportResultSummaryObjectDTO {
  mdo_created?: number;
  mdo_failed?: number;
  mdo_duplicate?: number;
  sdo_created?: number;
  sdo_failed?: number;
  sdo_duplicate?: number;
}

export enum ImportExportCSV {
  NONE = 0,
  IMPORT_POINTS = 1,
  IMPORT_MAPPINGS = 2,
  EXPORT_POINTS = 3,
  EXPORT_MAPPINGS = 4
}

export interface ImportExportResultObjectDTO {
  path?: string;
  isSuccess: boolean;
  message?: string;
  messageDetail?: string;
  lineNumber?: number;
}