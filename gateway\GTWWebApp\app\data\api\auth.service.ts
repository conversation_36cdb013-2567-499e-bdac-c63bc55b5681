/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { SDGCheckAuthDTO } from '../model/sDGCheckAuthDTO';
import { UserObjectDTO } from '../model/userObjectDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class AuthService {

    protected basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Add a user.
     * Add a user.
     * @param username user name
     * @param password user password
     * @param isactive user is active
     * @param role user role
     * @param email user email
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public addUser(username: string, password: string, isactive: boolean, role: number, email?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public addUser(username: string, password: string, isactive: boolean, role: number, email?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public addUser(username: string, password: string, isactive: boolean, role: number, email?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public addUser(username: string, password: string, isactive: boolean, role: number, email?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling addUser.');
        }

        if (password === null || password === undefined) {
            throw new Error('Required parameter password was null or undefined when calling addUser.');
        }

        if (isactive === null || isactive === undefined) {
            throw new Error('Required parameter isactive was null or undefined when calling addUser.');
        }

        if (role === null || role === undefined) {
            throw new Error('Required parameter role was null or undefined when calling addUser.');
        }


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (password !== undefined) {
            formParams = formParams.append('password', <any>password) || formParams;
        }
        if (email !== undefined) {
            formParams = formParams.append('email', <any>email) || formParams;
        }
        if (isactive !== undefined) {
            formParams = formParams.append('isactive', <any>isactive) || formParams;
        }
        if (role !== undefined) {
            formParams = formParams.append('role', <any>role) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/user/${encodeURIComponent(String(username))}`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Change password of user.
     * Change password of user.
     * @param username user name
     * @param oldpassword old user password
     * @param password user password
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public changeUserPassword(username: string, oldpassword: string, password: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public changeUserPassword(username: string, oldpassword: string, password: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public changeUserPassword(username: string, oldpassword: string, password: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public changeUserPassword(username: string, oldpassword: string, password: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling changeUserPassword.');
        }

        if (oldpassword === null || oldpassword === undefined) {
            throw new Error('Required parameter oldpassword was null or undefined when calling changeUserPassword.');
        }

        if (password === null || password === undefined) {
            throw new Error('Required parameter password was null or undefined when calling changeUserPassword.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (username !== undefined) {
            formParams = formParams.append('username', <any>username) || formParams;
        }
        if (oldpassword !== undefined) {
            formParams = formParams.append('oldpassword', <any>oldpassword) || formParams;
        }
        if (password !== undefined) {
            formParams = formParams.append('password', <any>password) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/change_user_password`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 
     * 
     * @param token session token
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public checkAuth(token: string, observe?: 'body', reportProgress?: boolean): Observable<SDGCheckAuthDTO>;
    public checkAuth(token: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<SDGCheckAuthDTO>>;
    public checkAuth(token: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<SDGCheckAuthDTO>>;
    public checkAuth(token: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (token === null || token === undefined) {
            throw new Error('Required parameter token was null or undefined when calling checkAuth.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (token !== undefined) {
            formParams = formParams.append('token', <any>token) || formParams;
        }

        return this.httpClient.post<SDGCheckAuthDTO>(`${this.basePath}/check_auth`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete a user.
     * Delete a user.
     * @param username user name
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteUser(username: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public deleteUser(username: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public deleteUser(username: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public deleteUser(username: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling deleteUser.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'text/plain'
        ];

        return this.httpClient.delete<any>(`${this.basePath}/user/${encodeURIComponent(String(username))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get a user.
     * Get a user.
     * @param username user name
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getUser(username: string, observe?: 'body', reportProgress?: boolean): Observable<UserObjectDTO>;
    public getUser(username: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<UserObjectDTO>>;
    public getUser(username: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<UserObjectDTO>>;
    public getUser(username: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling getUser.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'text/plain'
        ];

        return this.httpClient.get<UserObjectDTO>(`${this.basePath}/user/${encodeURIComponent(String(username))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get all users.
     * 
     * @param userFilter user filter (i.e. user name must contain this, empty string returns all)
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getUsers(userFilter?: string, observe?: 'body', reportProgress?: boolean): Observable<Array<UserObjectDTO>>;
    public getUsers(userFilter?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<UserObjectDTO>>>;
    public getUsers(userFilter?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<UserObjectDTO>>>;
    public getUsers(userFilter?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {


        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (userFilter !== undefined && userFilter !== null) {
            queryParameters = queryParameters.set('userFilter', <any>userFilter);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<Array<UserObjectDTO>>(`${this.basePath}/users`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Ask user db if token is valid
     * Ask user db if token is valid.
     * @param token token GUID
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public isTokenValid(token: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public isTokenValid(token: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public isTokenValid(token: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public isTokenValid(token: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (token === null || token === undefined) {
            throw new Error('Required parameter token was null or undefined when calling isTokenValid.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'text/plain'
        ];

        return this.httpClient.get<any>(`${this.basePath}/is_token_valid/${encodeURIComponent(String(token))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Login to SDG.
     * Login to SDG.
     * @param username user name
     * @param password user password
     * @param isForcingUsersLogoff is forcing other users with same or lesser role to logoff
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loginUser(username: string, password: string, isForcingUsersLogoff?: boolean, observe?: 'body', reportProgress?: boolean): Observable<SDGCheckAuthDTO>;
    public loginUser(username: string, password: string, isForcingUsersLogoff?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<SDGCheckAuthDTO>>;
    public loginUser(username: string, password: string, isForcingUsersLogoff?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<SDGCheckAuthDTO>>;
    public loginUser(username: string, password: string, isForcingUsersLogoff?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling loginUser.');
        }

        if (password === null || password === undefined) {
            throw new Error('Required parameter password was null or undefined when calling loginUser.');
        }


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (username !== undefined) {
            formParams = formParams.append('username', <any>username) || formParams;
        }
        if (password !== undefined) {
            formParams = formParams.append('password', <any>password) || formParams;
        }
        if (isForcingUsersLogoff !== undefined) {
            formParams = formParams.append('isForcingUsersLogoff', <any>isForcingUsersLogoff) || formParams;
        }

        return this.httpClient.post<SDGCheckAuthDTO>(`${this.basePath}/login_user`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Log off remote user from SDG.
     * Log off remote user from SDG.
     * @param remoteUsername user name
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public logoffRemoteUser(remoteUsername: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public logoffRemoteUser(remoteUsername: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public logoffRemoteUser(remoteUsername: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public logoffRemoteUser(remoteUsername: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (remoteUsername === null || remoteUsername === undefined) {
            throw new Error('Required parameter remoteUsername was null or undefined when calling logoffRemoteUser.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (remoteUsername !== undefined) {
            formParams = formParams.append('remoteUsername', <any>remoteUsername) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/logoff_remote_user`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Log off from SDG.
     * Logoff from SDG.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public logoffUser(observe?: 'body', reportProgress?: boolean): Observable<any>;
    public logoffUser(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public logoffUser(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public logoffUser(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        return this.httpClient.post<any>(`${this.basePath}/logoff_user`,
            null,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Force a user Log off from SDG.
     * Force Logoff from SDG.
     * @param session session guid
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public logoffUserForce(session: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public logoffUserForce(session: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public logoffUserForce(session: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public logoffUserForce(session: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (session === null || session === undefined) {
            throw new Error('Required parameter session was null or undefined when calling logoffUserForce.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (session !== undefined) {
            formParams = formParams.append('session', <any>session) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/logoff_user_force`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Reset password of user.
     * Reset password of user.
     * @param username user name
     * @param password user password
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public resetUserPassword(username: string, password: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public resetUserPassword(username: string, password: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public resetUserPassword(username: string, password: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public resetUserPassword(username: string, password: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling resetUserPassword.');
        }

        if (password === null || password === undefined) {
            throw new Error('Required parameter password was null or undefined when calling resetUserPassword.');
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (username !== undefined) {
            formParams = formParams.append('username', <any>username) || formParams;
        }
        if (password !== undefined) {
            formParams = formParams.append('password', <any>password) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/reset_user_password`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a user.
     * Update a user.
     * @param username user name
     * @param isactive user is active
     * @param role user role
     * @param email user email
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateUser(username: string, isactive: boolean, role: number, email?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public updateUser(username: string, isactive: boolean, role: number, email?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public updateUser(username: string, isactive: boolean, role: number, email?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public updateUser(username: string, isactive: boolean, role: number, email?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (username === null || username === undefined) {
            throw new Error('Required parameter username was null or undefined when calling updateUser.');
        }

        if (isactive === null || isactive === undefined) {
            throw new Error('Required parameter isactive was null or undefined when calling updateUser.');
        }

        if (role === null || role === undefined) {
            throw new Error('Required parameter role was null or undefined when calling updateUser.');
        }


        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/x-www-form-urlencoded'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (email !== undefined) {
            formParams = formParams.append('email', <any>email) || formParams;
        }
        if (isactive !== undefined) {
            formParams = formParams.append('isactive', <any>isactive) || formParams;
        }
        if (role !== undefined) {
            formParams = formParams.append('role', <any>role) || formParams;
        }

        return this.httpClient.put<any>(`${this.basePath}/user/${encodeURIComponent(String(username))}`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
