﻿{
  "TR_61850_CANT_DELETE_DATASET_DISCONNECTED": "Impossible de supprimer le jeu de données '{{arg1}}' tant qu'il est déconnecté. Veuillez vous connecter et réessayer. ",
  "TR_61850_CANT_DELETE_DATASET_IN_USE": "Impossible de supprimer le jeu de données '{{arg1}}' car il appartient à un ou plusieurs blocs de contrôle. Supprimer les blocs de contrôle et réessayer. ",
  "TR_61850_CANT_DELETE_NON_DYNAMIC_DATASET": "Le jeu de données '{{arg1}}' n'est pas dynamique et ne peut pas être supprimé.",
  "TR_61850_CLIENT_DUP_LIC": "Impossible d'ajouter le client CEI 61850 (il peut s'agir d'un duplicata ou d'aucune licence)",
  "TR_61850_CLIENT_DUPLICATE": "Impossible d'ajouter le client 61850: '{{arg1}}'. Nom en double. ",
  "TR_61850_CLIENT_FAILED_DUPLICATE": "La création du client 61850 a échoué car son nom est dupliqué: '{{arg1}}",
  "TR_61850_CREATE_SDO_FAILED_DUPLICATE": "Impossible de créer le {{arg1}} SDO.  Nom de tag en double?",
  "TR_61850_CREATE_SDO_FAILED_INVALID_TAG": "Impossible de créer le {{arg1}} SDO.  Nom de balise invalide?",
  "TR_61850_CREATE_SDO_FAILED_LIC_LIMIT": "Impossible de créer le {{arg1}} SDO.  La limite de licence a été atteinte ",
  "TR_61850_DATASET_VERIFY_FAILED": "La vérification du jeu de données a échoué pour '{{arg1}}'. Assurez-vous que les définitions du jeu de données client et serveur correspondent, sinon les rapports ne fonctionneraient pas correctement. ",
  "TR_61850_DATASET_VERIFY_SUCCEED": "Le jeu de données '{{arg1}}' a été vérifié!",
  "TR_61850_INVALID_MODEL": "Erreur: modèle 61850 non valide",
  "TR_61850_LOAD_COMMUNICATION": "Impossible de charger la configuration à partir du fichier SCL. Assurez-vous que le chemin du fichier SCL, le nom de l’IED et la section de communication du fichier SCL sont tous valides. ",
  "TR_61850_MD_BIND_MISMATCH": "Impossible de lier SDO à MDO. Incompatibilité de type?",
  "TR_61850_MDO_DELETE": "Les MDO 61850 ne peuvent pas être supprimés",
  "TR_61850_MDO_SLAVE_MAP_FAILED": "Impossible de mapper un point non MDO sur ce point esclave",
  "TR_61850_NO_POINTS_FOUND": "Aucun point valide",
  "TR_61850_NO_POLLED": "Plus de PolledPointSets IEC 61850 disponibles",
  "TR_61850_POLLED_ADD": "Impossible d'ajouter {{arg1}} PolledPointSet sur le serveur 61850: {{arg2}}.",
  "TR_61850_POLLED_DELETE": "Impossible de supprimer le jeu de données interrogées '{{arg1}}', il contient des éléments existants: {{arg2}}",
  "TR_61850_POLLED_DELETE_EXISTING": "Impossible de supprimer '{{arg1}}' ensemble de points interrogés, il contient des éléments existants: {{arg2}}",
  "TR_61850_REPORT_DELETE_ERROR": "Impossible de supprimer le bloc de contrôle de rapport '{{arg1}}', un thread d'activation de la nouvelle tentative est en cours d'exécution. Modifiez le nombre de tentatives d’activation à zéro pour arrêter le fil et réessayer. ",
  "TR_61850_REPORT_DELETE_EXISTING": "Impossible de supprimer le bloc de contrôle de rapport '{{arg1}}', il contient des éléments existants: {{arg2}}",
  "TR_61850_SERVER_DUP_LIC": "Impossible d'ajouter le serveur CEI 61850 (il peut s'agir d'un duplicata ou d'aucune licence)",
  "TR_61850_SERVER_DUPLICATE": "Impossible d'ajouter le serveur 61850: '{{arg1}}'. Nom en double. ",
  "TR_61850_SERVER_FAILED_DUPLICATE": "Impossible de créer le serveur 61850 car il porte un nom en double: '{{arg1}}",
  "TR_6T_CATEGORY_APP": "APP",
  "TR_6T_CATEGORY_C_APP": "C APP",
  "TR_6T_CATEGORY_C_CASM": "C CASM",
  "TR_6T_CATEGORY_C_CLIENT": "C CLIENT",
  "TR_6T_CATEGORY_C_CLIENTRPT": "C CLIENTRPT",
  "TR_6T_CATEGORY_C_CLIENTSTATE": "C CLIENTSTATE",
  "TR_6T_CATEGORY_C_DYNAMIC_DATASETS": "C DONNEES DYNAMIQUES",
  "TR_6T_CATEGORY_C_EXTREF": "C EXTREF",
  "TR_6T_CATEGORY_C_SCL": "C SCL",
  "TR_6T_CATEGORY_C_STACK": "C STACK",
  "TR_6T_CATEGORY_C_TARGET": "C CIBLE",
  "TR_6T_CATEGORY_C_TEST": "C TEST",
  "TR_6T_CATEGORY_C_TIME": "C TIME",
  "TR_6T_CATEGORY_C_TRANSLOW": "C TRANSLOW",
  "TR_6T_CATEGORY_C_TRANSPORT": "C TRANSPORT",
  "TR_6T_CATEGORY_CLIENTPARSEVALUES": "CLIENT PARSE VALUES",
  "TR_6T_CATEGORY_CONTROL": "CONTROL",
  "TR_6T_CATEGORY_DISCOVERY": "DECOUVERTE",
  "TR_6T_CATEGORY_EXTREF": "EXTREF",
  "TR_6T_CATEGORY_GENERAL": "GENERAL",
  "TR_6T_CATEGORY_GOOSE": "GOOSE",
  "TR_6T_CATEGORY_READ": "READ",
  "TR_6T_CATEGORY_READ_HANDLER": "READ HANDLER",
  "TR_6T_CATEGORY_REPORT": "REPORT",
  "TR_6T_CATEGORY_TIME_SYNCH": "TIME SYNCH",
  "TR_6T_CATEGORY_WRITE": "WRITE",
  "TR_6T_CATEGORY_XML": "XML",
  "TR_6T_FILTER": "Filtre MMS",
  "TR_6T_LOW_LEVEL_STACK": "Pile faible niveau MMS",
  "TR_6T_STANDARD_STACK": "Pile standard MMS",
  "TR_MDO_CAN_NOT_CREATE": "MDO introuvable, impossible de créer",
  "TR_SDO_CREATE_DEFINED_ALREADY": "SDO déjà défini, impossible de créer",
  "TR_SDO_BIND_FAIL": "Impossible de lier SDO.",
  "TR_SDO_CREATE_FAIL": "Impossible de créer le SDO.",
  "TR_SDO_CAN_NOT_SET_OPTIONS": "Impossible de définir les options SDO {{arg1}}",
  "TR_ABOUT": "à propos de",
  "TR_ACCEPT_CLOCK_SYNC": "Accepter les synchronisations d'horloge",
  "TR_ACCEPT_CLOCK_SYNC_DESC": "Si la valeur est true et si UseSystemTime est true, l'horloge système Windows sera ajustée par une synchronisation de l'heure reçue d'un périphérique maître externe. Si la synchronisation de l'heure est fausse, l'horloge système de Windows ne sera pas ajustée.Lorsque vous utilisez une horloge simulée, ce paramètre est sans effet et les synchronisations d’horloge sont toujours acceptées et ajustez l’horloge simulée ",
  "TR_ACCESS_READ_AND_WRITE_USERS_OF_MANAGEMENT": "Accéder à la lecture et à l'écriture de la gestion des utilisateurs",
  "TR_ACCESS_RIGHT": "Droit d'accès",
  "TR_ACCESS_RIGHT_DESC": "Définition du droit d'accès",
  "TR_ACCESS_TO_CONFIG_UI_FOR_READ_AND_WRITE": "Accès à la configuration pour lecture / écriture",
  "TR_ACCESS_TO_CONFIG_UI_FOR_READ_ONLY": "Accès à la configuration pour lecture seule",
  "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_AND_WRITE": "Accès à l'interface utilisateur d'exécution pour la lecture et l'écriture",
  "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_ONLY": "Accès à l'interface utilisateur d'exécution en lecture seule",
  "TR_ACTION": "Action",
  "TR_ACTION_NAME": "Nom de l'action",
  "TR_ACTION_NAME_DESC": "Nom de l'action",
  "TR_ACTION_VALUES": "Valeurs d'action",
  "TR_ACTION_VALUES_DESC": "Valeurs d'action",
  "TR_ACTIVATE_ONLINE": "Activer en ligne",
  "TR_ACTIVATE_PRODUCT_KEY_OFFLINE": "Clé de produit active hors ligne",
  "TR_ACTIVATE_PRODUCT_KEY_ONLINE": "Clé de produit active en ligne",
  "TR_ACTIVE": "Actif",
  "TR_ACTIVE_DESC": "Actif",
  "TR_ADD": "Ajouter",
  "TR_ADD_GOOSE": "Impossible d'ajouter {{arg1}} GOOSE sur le serveur 61850: {{arg2}}.",
  "TR_ADD_ITEM": "Ajouter un élément",
  "TR_ADD_ITEM_DESC": "Ajouter un élément",
  "TR_ADD_NEW_USER": "Ajouter un nouvel utilisateur",
  "TR_ADD_PROPERTY": "Ajouter une propriété",
  "TR_ADD_PROPERTY_DESC": "Ajouter une propriété",
  "TR_ADD_SUBSCRIPTION": "Ajouter un abonnement",
  "TR_ADD_SUBSCRIPTION_DESC": "Ajouter un abonnement",
  "TR_ALARM_ARRAY": "Tableau d'alarmes",
  "TR_ALARM_ARRAY_DESC": "Définir le tableau d'alarme",
  "TR_ALIAS_NAME": "Nom",
  "TR_ALIAS_NAME_DESC": "Spécifie le nom.",
  "TR_APP_CERT_DIRECTORY": "Répertoire de certificats d'application",
  "TR_APP_CERT_DIRECTORY_DESC": "Spécifie le répertoire du certificat d'application.",
  "TR_APP_ERROR": "Erreur d'application",
  "TR_APP_ERROR_DESC": "Enregistrer les messages d'erreur d'application",
  "TR_APP_START_STOP": "App. Démarrer / Arrêter",
  "TR_APP_START_STOP_DESC": "Enregistrer les messages de démarrage / arrêt de l'application",
  "TR_APP_STATUS": "Statut App.",
  "TR_APP_STATUS_DESC": "Enregistrer les messages d'état périodiques de l'application",
  "TR_APPL_AUTO_REQ_MODE": "Mode de requête automatique",
  "TR_APPL_AUTO_REQ_MODE_DESC": "Chaque bit active (1) ou désactive (0) une requête automatique. Ce paramètre est utilisé uniquement pour les sessions maître ou esclave utilisant le protocole DNP3.",
  "TR_APPL_DNPABS_RESP_TIMEOUT": "Délai de réponse (ms) - DNP",
  "TR_APPL_DNPABS_RESP_TIMEOUT_DESC": "Délai de réponse de l'application DNP par défaut. Cette valeur correspond à la durée maximale (en millisecondes) autorisée avant l'annulation d'une commande en raison d'un délai d'expiration. Cette heure commence lorsque la demande est soumise et se termine lorsque la réponse finale au niveau de l'application est reçue. Cette valeur peut généralement être remplacée pour des points de données spécifiques par l'option 'TO' dans le fichier de mappage de points. ",
  "TR_APPL_IECABS_RESP_TIMEOUT": "Délai de réponse (ms) - IEC",
  "TR_APPL_IECABS_RESP_TIMEOUT_DESC": "Délai d'attente de réponse de l'application CEI par défaut. Cette valeur correspond à la durée maximale (en millisecondes) autorisée avant l'annulation d'une commande en raison d'un délai d'expiration. Cette heure commence lorsque la demande est soumise et se termine lorsque la réponse finale au niveau de l'application est reçue. Cette valeur peut généralement être remplacée pour des points de données spécifiques par l'option 'TO' dans le fichier de mappage de points. ",
  "TR_APPL_INCR_RESP_TIMEOUT": "Délai de réponse incrémentiel (ms)",
  "TR_APPL_INCR_RESP_TIMEOUT_DESC": "Durée maximale autorisée entre les messages d'un périphérique distant lorsqu'une demande est en attente pour ce périphérique. Le message ne doit pas nécessairement être une réponse directe à la demande en attente. Si aucun message n'est reçu du périphérique distant dans ce délai, le traitement de la demande est terminé et la requête est annulée en raison d'un délai d'attente au niveau de l'application. Ce temporisateur est redémarré chaque fois qu'un message est reçu du périphérique distant. ",
  "TR_APPL_MBABS_RESP_TIMEOUT": "Délai de réponse (ms) - Modbus",
  "TR_APPL_MBABS_RESP_TIMEOUT_DESC": "Délai de réponse par défaut de l'application Modbus. Cette valeur correspond à la durée maximale (en millisecondes) autorisée avant l'annulation d'une commande en raison d'un délai d'expiration. Cette heure commence lorsque la demande est soumise et se termine lorsque la réponse finale au niveau de l'application est reçue. Cette valeur peut généralement être remplacée pour des points de données spécifiques par l'option 'TO' dans le fichier de mappage de points. ",
  "TR_APPLICATION_FUNCTIONS": "Fonctions d'application",
  "TR_APPLICATION_LAYER": "Couche d'application",
  "TR_ARE_YOU_SURE_TO_CLEAR_MODEL": "Êtes-vous sûr de vouloir effacer le modèle?",
  "TR_ARE_YOU_SURE_TO_DELETE_DATASET": "Êtes-vous sûr de vouloir supprimer cet ensemble de données?",
  "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME": "Êtes-vous sûr de vouloir supprimer {{NodeName}}?",
  "TR_ARE_YOU_SURE_TO_REMOVE_MAPPING": "Êtes-vous sûr de vouloir supprimer the mappage {{mappage}}?",
  "TR_ARE_YOU_SURE_TO_DELETE_USER_X": "Êtes-vous sûr de vouloir supprimer l'utilisateur {{nom d'utilisateur}}?",
  "TR_AREA_SPACE": "Espace de la zone",
  "TR_AREA_SPACE_DESC": "Définir l'espace de la zone",
  "TR_ASDUORIGINATOR_ADDR": "Adresse de l'expéditeur",
  "TR_ASDUORIGINATOR_ADDR_DESC": "Adresse de l'expéditeur (pour le COT de 2 octets).  Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant les profils de protocole CEI 60870-5-101 ou CEI 60870-5-104. ",
  "TR_ASDUSIZE_CMN_ADDR": "Nombre d'octets (octets) dans l'adresse commune de l'ASDU",
  "TR_ASDUSIZE_CMN_ADDR_DESC": "Nombre d'octets (octets) dans le champ Adresse commune de l'ASDU (adresse de secteur).  Ce paramètre est utilisé uniquement pour les sessions maître et esclave utilisant les profils de protocole CEI 60870-5-101 ou CEI 60870-5-104. ",
  "TR_ASDUSIZE_COT": "Nombre d'octets (octets) en cause de la transmission",
  "TR_ASDUSIZE_COT_DESC": "Nombre d'octets (octets) dans le champ cause de transmission (COT) de l'ASDU.  Ce paramètre est utilisé uniquement pour les sessions maître et esclave utilisant les profils de protocole CEI 60870-5-101 ou CEI 60870-5-104. ",
  "TR_ASDUSIZE_IOA": "Nombre d'octets (octets) dans le nombre de points",
  "TR_ASDUSIZE_IOA_DESC": "Nombre d'octets (octets) dans le champ Adresse de l'objet d'information (numéro de point).  Ce paramètre est utilisé uniquement pour les sessions maître et esclave utilisant les profils de protocole CEI 60870-5-101 ou CEI 60870-5-104. ",
  "TR_AUDIT": "Audit",
  "TR_AUDIT_LOG": "Journal d'audit",
  "TR_AUTH_CONFIG": "Configuration de l'authentification",
  "TR_AUTH_MODE": "Mode d'authentification",
  "TR_AUTH_SEC_USERS_LIST": "Utilisateurs SECAuth v5",
  "TR_AUTH_SEC_USERS_LIST_DESC": "Utilisateurs SECAuth v5",
  "TR_AUTHENTICATION_PASSWORD": "Mot de passe d'authentification",
  "TR_AUTO_MAP_QUALITY_TIME": "Qualité et temps de la carte automatique",
  "TR_AUTO_MAP_QUALITY_TIME_DESC": "Spécifie si le mappage automatique de la qualité et de l'heure est actif.",
  "TR_AUTO_SAVE": "Sauvegarde automatique",
  "TR_AUTO_SAVE_ENABLE": "activer",
  "TR_AUTO_SAVE_ENABLE_DESC": "Activer l'enregistrement automatique",
  "TR_AUTO_SAVE_PRD": "Période de sauvegarde automatique",
  "TR_AUTO_SAVE_PRD_DESC": "Délai maximal entre l'enregistrement des fichiers de configuration de l'application .ini et .csv en millisecondes. Une valeur de 0 désactivera les sauvegardes. La valeur la plus basse pour la période de sauvegarde est 60000 ms (1 minute) ",
  "TR_AUTO_START_ENGINE_ON_GUI_EXIT": "Démarrage automatique du moteur à la sortie de l'interface graphique",
  "TR_AUTO_START_SERVICE_ON_GUI_EXIT": "Moteur de service automatique à la sortie de l'interface graphique",
  "TR_AVAILABLE_SERVERS": "Serveurs disponibles",
  "TR_AVAILABLE_SERVERS_DESC": "Serveurs disponibles",
  "TR_BLOCKED": "BLOCKED",
  "TR_BLOCKED_DESC": "Bloqué",
  "TR_BUFFER_OVERFLOW": "Dépassement de mémoire tampon",
  "TR_BUFFER_OVERFLOW_DESC": "Spécifie si le dépassement de tampon est actif.",
  "TR_BUFFER_TIME": "Temps de tampon (ms)",
  "TR_BUFFER_TIME_DESC": "Spécifie l'heure actuelle du tampon.",
  "TR_CAN_NOT_SET_SIGNED_AND_DUAL_REGISTER": "Impossible de définir SIGNED et DualRegister. L'option DualRegister sera désactivée. Si l’option Double est souhaitée, désactivez SIGNED. ",
  "TR_CANCEL": "Annuler",
  "TR_CANT_CREATE_MDO": "Impossible de créer le {{arg1}} MDO.  Nom de balise invalide possible. ",
  "TR_CANT_DELETE_POINT_IN_USE": "L'équation: {{arg1}} est mappée sur d'autres points ou est utilisée dans une équation, ne peut pas éditer / supprimer",
  "TR_CATEGORY": "Catégorie",
  "TR_CENTRAL_AUTHORITY": "Autorité centrale",
  "TR_CERT_AUTH_CHAINING_VERIFICATION_DEPTH": "Profondeur de vérification du chaînage des autorités de certification",
  "TR_CERT_AUTH_DIR_PATH": "Répertoire vers l'autorité de certification",
  "TR_CERT_AUTH_FILE_PATH": "Fichier de l'autorité de certification",
  "TR_CERT_REVOCATION_FILE_PATH": "Fichier de liste de révocation d'autorités de certification",
  "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH": "Profondeur de vérification du chaînage de l'autorité de certification",
  "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH_DESC": "Spécifie la profondeur de vérification de l'enchaînement des autorités de certification",
  "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR": "Chemin du répertoire de l'autorité de certification",
  "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR_DESC": "Chemin d'accès au répertoire des certificats d'autorité de certification.",
  "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE": "Fichier de l'autorité de certification",
  "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE_DESC": "Fichier contenant les certificats de l'autorité de certification.",
  "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE": "Fichier de révocation de certificat",
  "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE_DESC": "Fichier contenant la liste de révocation de certificats.",
  "TR_CHAN_TLSCOMMON_NAME": "Nom commun TLS",
  "TR_CHAN_TLSCOMMON_NAME_DESC": "Nom commun à attendre pour les certificats TLS entrants (la chaîne vide est désactivée).",
  "TR_CHAN_TLSDH_FILE": "Diffie Hellman File",
  "TR_CHAN_TLSDH_FILE_DESC": "Fichier contenant Diffie Hellman.",
  "TR_CHAN_TLSDSACERTIFICATE_FILE": "Fichier de certificat public DSA",
  "TR_CHAN_TLSDSACERTIFICATE_FILE_DESC": "Fichier contenant le certificat de clé pour les chiffrements DSA TLS.",
  "TR_CHAN_TLSDSAPRIVATE_KEY_FILE": "Fichier de clé privée DSA",
  "TR_CHAN_TLSDSAPRIVATE_KEY_FILE_DESC": "Fichier contenant la clé privée pour les chiffrements DSA TLS.",
  "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE": "Phrase de passe de clé privée DSA",
  "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE_DESC": "Phrase secrète pour le déchiffrement de la clé privée pour les chiffrements DSA TLS.",
  "TR_CHAN_TLSENABLE": "Activer TLS",
  "TR_CHAN_TLSENABLE_DESC": "Si la valeur est true, la connexion sera établie à l'aide de TLS pour la sécurité.",
  "TR_CHAN_TLSHANDSHAKE_TIMEOUT": "Délai de prise de contact TLS (secondes)",
  "TR_CHAN_TLSHANDSHAKE_TIMEOUT_DESC": "Temps maximal, en millisecondes, d'attente de l'établissement de la liaison TLS Connect.",
  "TR_CHAN_TLSRENEGOTIATION_COUNT": "PDU TLS Max avant de forcer la renégociation de chiffrement",
  "TR_CHAN_TLSRENEGOTIATION_COUNT_DESC": "Nombre maximal d'unités PDU avant de forcer la renégociation de chiffrement.",
  "TR_CHAN_TLSRENEGOTIATION_SECONDS": "Renégociation TLS (sec)",
  "TR_CHAN_TLSRENEGOTIATION_SECONDS_DESC": "Temps maximal (en secondes) avant de forcer la renégociation de chiffrement.",
  "TR_CHAN_TLSRSACERTIFICATE_FILE": "Fichier de certificat public RSA",
  "TR_CHAN_TLSRSACERTIFICATE_FILE_DESC": "Fichier contenant le certificat de clé pour les chiffrements RSA TLS.",
  "TR_CHAN_TLSRSAPRIVATE_KEY_FILE": "Fichier de clé privée RSA",
  "TR_CHAN_TLSRSAPRIVATE_KEY_FILE_DESC": "Fichier contenant la clé privée pour les chiffrements RSA TLS.",
  "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE": "Phrase de passe de clé privée RSA",
  "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "Phrase secrète pour le déchiffrement de la clé privée pour les chiffrements RSA TLS.",
  "TR_CHANGE_PASSWORD": "Changer le mot de passe",
  "TR_CHANGE_VALUE": "Changer la valeur",
  "TR_CHANGE_VALUE_AND_QUALITY": "Changer la valeur et la qualité",
  "TR_CHANGE_VALUE_AND_QUALITY_OF": "Changer la valeur et la qualité de",
  "TR_CHANGE_VALUE_OF_SELECTED": "Changer la valeur des tags sélectionnés",
  "TR_CHANNEL_DELETE_ONLY": "Impossible de supprimer le dernier canal redondant: '{{arg1}}'. Au moins un canal redondant doit exister pour le groupe de redondance. ",
  "TR_CHANNEL_DUPLICATE": "Impossible d'ajouter le canal: '{{arg1}}'. Nom en double. ",
  "TR_CHANNEL_ENABLE_TLS": "Activer TLS",
  "TR_CHANNEL_IP_PORT_NUMBER": "Numéro de port IP",
  "TR_CHANNEL_LOCAL_IP": "IP locale",
  "TR_CHANNEL_MBP_CARD_NUMBER": "Numéro de la carte",
  "TR_CHANNEL_MBP_RECEIVE_TIMEOUT": "Délai de réception",
  "TR_CHANNEL_MBP_ROUTE_ADDRESS": "Route / Adresse",
  "TR_CHANNEL_MBP_SLAVE_PATH": "Chemin d'accès esclave",
  "TR_CHANNEL_NAME": "Nom d'alias",
  "TR_CHANNEL_NAME_DESC": "Spécifiez le nom du canal",
  "TR_CHANNEL_NO_MORE": "Plus de canaux disponibles",
  "TR_CHANNEL_PROTOCOL_TYPE": "Protocole de session",
  "TR_CHANNEL_PROTOCOL_TYPE_DESC": "Définit le protocole du canal S'applique à tous les types de canaux physiques",
  "TR_CHANNEL_START_DELAY": "Délai de démarrage du canal (ms)",
  "TR_CHANNEL_START_DELAY_DESC": "Délai en millisecondes pour décaler tous les canaux (esclaves et maîtres). Affecte uniquement le démarrage. ",
  "TR_CHANNEL_TCP_MODE": "Mode",
  "TR_CHECK_BACK_ID": "ID de vérification",
  "TR_CHNG_INDICATED": "CHNG_INDICATED",
  "TR_CHNG_INDICATED_DESC": "Un changement dans les données est indiqué par la source des données.",
  "TR_CLEAR": "Clear",
  "TR_CLEAR_LOG": "Effacer le journal",
  "TR_CLEAR_MODEL": "Effacer le modèle",
  "TR_CLEAR_MODEL_DESC": "Effacer le modèle actuel.",
  "TR_CLEAR_SEARCH": "Effacer la recherche",
  "TR_CLIENT": "Client",
  "TR_CLIENT_AE_INVOKE_ID": "ID d'invocation AE",
  "TR_CLIENT_AE_QUALIFIER": "Qualificateur AE",
  "TR_CLIENT_AP_INVOKE_ID": "ID d'invocation AP",
  "TR_CLIENT_APP_ID": "ID de l'application",
  "TR_CLIENT_IP_ADDRESS": "Adresse IP du client",
  "TR_CLIENT_PRESENTATION_ADDRESS": "Sélecteur de présentation",
  "TR_CLIENT_SERVER_CONNECTION_SETTINGS": "Paramètres de connexion client / serveur",
  "TR_CLIENT_SESSION_ADDRESS": "Sélecteur de session",
  "TR_CLIENT_TRANSPORT_ADDRESS": "Sélecteur de transport",
  "TR_CLOSE": "Fermer",
  "TR_CLOSE_PANEL": "Fermer le panneau",
  "TR_COILS": "Bobines",
  "TR_COLLAPSE_CHILDREN": "Réduire les enfants",
  "TR_COMMAND_KIND": "Type de commande",
  "TR_COMMAND_SENT_TO_SERVER": "Commande envoyée au serveur",
  "TR_COMMAND_SUCCESS": "Succès",
  "TR_CONFIG": "Configuration",
  "TR_CONFIG_VIEW_1": "Vue de configuration n ° 1",
  "TR_CONFIG_VIEW_2": "Vue de configuration n ° 2",
  "TR_CONFIG_VIEW_3": "Vue de configuration n ° 3",
  "TR_CONFIGURATION": "Configuration",
  "TR_CONFIGURATION_REVISION": "Révision de la configuration",
  "TR_CONFIGURATION_REVISION_DESC": "Spécifie si la révision de configuration est active.",
  "TR_CONFIRM_NEW_PASSWORD": "Confirmer le nouveau mot de passe",
  "TR_CONNECT_RECONNECT": "Connect / Reconnect",
  "TR_CONNECT_TIMEOUT": "Délai de connexion (en ms)",
  "TR_CONTROL_BLOCK": "Bloc de contrôle",
  "TR_CONTROL_BLOCK_DESC": "Afficher le bloc de contrôle pour le MDO",
  "TR_CONTROL_POINT": "Point de contrôle",
  "TR_CONTROL_POINT_DESC": "Définir le point de contrôle",
  "TR_CONTROL_POINT_LIST": "Points de contrôle",
  "TR_CONTROL_POINT_LIST_DESC": "Choisissez un point de contrôle.",
  "TR_COPY_TO_CSV_FILE": "Copier la liste du fichier CSV",
  "TR_COULD_NOT_SET_MDO_OPTIONS": "Erreur: impossible de définir les options MDO",
  "TR_CPU_ABBREVIATION": "CPU",
  "TR_CREATE_61400_ERROR": "Plus d'alarmes disponibles",
  "TR_CREATE_61400MDO_ERROR": "Impossible d'ajouter {{arg1}} MDO sur le serveur 61850: {{arg2}}. (En double?)",
  "TR_CREATE_61850_CANT_DELETE": "Impossible de supprimer le client {{arg1}} tant qu'il est connecté. Veuillez déconnecter et réessayer. ",
  "TR_CREATE_61850_CHANGE_DATASET_NAME_FAILED": "Impossible de modifier le nom du fichier. Erreur: {{arg1}} ",
  "TR_CREATE_61850_CREATE_MDO_INVALID_TAG": "Impossible de créer {{arg1}} MDO. Nom de balise non valide?",
  "TR_CREATE_61850_DELETE": "MDO: '{{arg1}}' est mappé sur des points esclaves ou est utilisé dans une équation, ne peut pas supprimer",
  "TR_CREATE_61850_DISABLE_RPT_FAILED": "Impossible de désactiver le contrôle RCB. Erreur: {{arg1}}",
  "TR_CREATE_61850_FAILED_TO_REENABLE_RPT": "Impossible de réactiver le bloc de contrôle. Erreur: {{arg1}} ",
  "TR_CREATE_61850_LIST_POINTS_NOT_A_CONTROL": "Bloc de contrôle non valide",
  "TR_CREATE_61850_MDO": "MDO doit avoir un nom, ne peut pas créer",
  "TR_CREATE_61850_MDO_EXISTS": "MDO déjà défini, impossible de créer",
  "TR_CREATE_61850_MDO_SET_OPTIONS": "Impossible de définir les options MDO {{arg1}}",
  "TR_CREATE_61850_NO_MORE_CLIENTS": "Impossible de lire le bloc de contrôle de rapport. Erreur renvoyée: {{arg1}} ",
  "TR_CREATE_61850_SERVER_NO_ADD_MDO": "Impossible d'ajouter {{arg1}} MDO sur le serveur 61850: {{arg2}}. (Dupliquer?)",
  "TR_CREATE_61850_SET_DATASET_NAME_FAILED": "Impossible de définir le nom du fichier. Erreur inconnue.",
  "TR_CREATE_61850_SET_MDO_OPTIONS": "Impossible de définir les options MDO {{arg1}}",
  "TR_CREATE_61850_SET_MDO_PROPERTIES": "Impossible de définir les propriétés MDO (contrôle non valide)",
  "TR_CREATE_61850_SET_MDO_PROPERTIES_Control": "Impossible de définir les propriétés MDO sur Control",
  "TR_CREATE_61850_SET_MDO_PROPERTIES_DATASET": "Impossible de définir les propriétés MDO sur DataSetControl",
  "TR_CREATE_61850_SET_MDO_PROPERTIES_GOOSE": "Impossible de définir les propriétés MDO sur le contrôle GOOSE",
  "TR_CREATE_61850_SET_MDO_PROPERTIES_POINTSET": "Impossible de définir les propriétés MDO sur le contrôle PointSet",
  "TR_CREATE_61850_SET_MDO_PROPERTIES_REPORT": "Impossible de définir les propriétés MDO sur le contrôle de rapport",
  "TR_CREATE_C2V": "Créer C2V",
  "TR_CREATE_DATASET_FAILED_CONNECTED": "Impossible de créer un ensemble de données tant qu'il est déconnecté. Veuillez vous connecter et réessayer. ",
  "TR_CREATE_DATASET_FAILED_EMPTY": "Erreur: le modèle est vide",
  "TR_CREATE_DATASET_FAILED_ERROR": "CreateDataSet a échoué avec l'erreur {{arg1}}",
  "TR_CREATE_DATASET_FAILED_EXISTS": "Le jeu de données {{arg1}} existe déjà. Essayez d’abord d’effacer puis de créer à nouveau. ",
  "TR_CREATE_DTM_POINT": "Créé C:\\dtm_points.csv",
  "TR_CREATE_EQUATION_FAILED": "Impossible de créer l'équation {{arg1}} pour {{arg2}}",
  "TR_CREATE_HTML_FILE": "Créé C:\\test_harness_points.xml",
  "TR_CTRL_AT_DEVICE": "CTRL_AT_DEVICE",
  "TR_CTRL_AT_DEVICE_DESC": "Un changement dans les données est indiqué en raison d'une action sur l'appareil.",
  "TR_CTRL_BY_COMM": "CTRL_BY_COMM",
  "TR_CTRL_BY_COMM_DESC": "Un changement dans les données est indiqué suite à une requête via des communications.",
  "TR_CTRL_CONFIRM": "CTRL_CONFIRM",
  "TR_CTRL_CONFIRM_DESC": "Une demande de contrôle a été confirmée par un périphérique distant, mais n'est pas encore complète.",
  "TR_CTRL_ERROR": "CTRL_ERROR",
  "TR_CTRL_ERROR_DESC": "Un périphérique distant a répondu pour indiquer une erreur dans une opération de contrôle.",
  "TR_CTRL_PENDING": "CTRL_PENDING",
  "TR_CTRL_PENDING_DESC": "Une demande de contrôle a été transmise à un périphérique distant.",
  "TR_CURRENT_ENTRY_ID": "ID de l'entrée actuelle",
  "TR_CURRENT_ENTRY_ID_DESC": "Affiche l'ID de l'entrée actuelle.",
  "TR_CURRENT_INI_FILE": "Fichier INI actuel",
  "TR_CURRENT_PASSWORD": "Mot de passe actuel",
  "TR_CUSTOM_LOGS": "Journaux personnalisés",
  "TR_CUSTOM_LOGS_DESC": "Messages de trace de journal pour les journaux personnalisés",
  "TR_DA_ITEM_PROPERTY_LIST": "Liste des propriétés d'élément DA",
  "TR_DA_ITEM_PROPERTY_LIST_DESC": "Liste des propriétés d'élément DA",
  "TR_DA_POINT_LIST": "Points d'attribut de données",
  "TR_DA_POINT_LIST_CHECKED": "Liste de points DA cochée",
  "TR_DA_POINT_LIST_DESC": "Spécifie la liste de points d'attributs de données en cours.",
  "TR_DA_QUALITY_DESC": "Définir la qualité de l'attribut de données",
  "TR_DA_QUALITY": "Qualité DA",
  "TR_DA_SEARCH": "Entrez un nom partiel à rechercher (sensible à la casse)",
  "TR_DA_SEARCH_DESC": "Entrez un nom partiel à rechercher (sensible à la casse)",
  "TR_DA_TIME": "Heure DA",
  "TR_DA_TIME_DESC": "Définir le temps de l'attribut de données",
  "TR_DA_VALUE": "Valeur DA",
  "TR_DA_VALUE_DESC": "Définir la valeur de l'attribut de données",
  "TR_DASHBOARD": "Tableau de bord",
  "TR_DATA_ATTRIBUTE_SELECTION": "Sélection d'attribut de données",
  "TR_DATA_CHANGE": "Modification de données",
  "TR_DATA_CHANGE_DESC": "Spécifie si la modification des données est active.",
  "TR_DATA_CHNG_MON": "Modification de données",
  "TR_DATA_FILE_PATH": "Chemin du fichier de données",
  "TR_DATA_POINTS_LIST": "Liste des points de données",
  "TR_DATA_POINTS_LIST_DESC": "Affiche la liste des points de données pour le domaine sélectionné.",
  "TR_DATA_REFERENCE": "Référence de données",
  "TR_DATA_REFERENCE_DESC": "Spécifiez la référence de données.",
  "TR_DATA_SAVED": "Données sauvegardées",
  "TR_DATA_SET": "DataSet",
  "TR_DATA_SET_NAME": "Nom du dataset",
  "TR_DATA_TYPE": "Type de données",
  "TR_DATA_UPDATE_CHANGE": "Modification de la mise à jour des données",
  "TR_DATA_UPDATE_CHANGE_DESC": "Spécifie si la modification de la mise à jour des données est active.",
  "TR_DATABASE": "Base de données",
  "TR_DATASET_NAME": "Nom du jeu de données",
  "TR_DATASET_NAME_DESC": "Spécifie si le nom du jeu de données est actif.",
  "TR_DATATYPE_DELETE_IN_USE": "Impossible de supprimer '{{arg1}}' (essayez de dé-mapper ses points)",
  "TR_DATE_TIME": "Date / Heure",
  "TR_DBAS_SECTOR_ADDRESS": "Adresse ASDU",
  "TR_DBAS_SECTOR_ADDRESS_DESC": "Adresse ASDU de chaque secteur",
  "TR_DEBUG": "Debug",
  "TR_DELETE": "Supprimer",
  "TR_DELETE_DATASET_FAILED": "La suppression de l'ensemble de données '{{arg1}}' a échoué sur le serveur avec une erreur inconnue. Consultez les journaux et assurez-vous que le jeu de données n'est pas utilisé sur le serveur. ",
  "TR_DELETE_DATASET_FAILED2": "La suppression de l'ensemble de données '{{arg1}}' a échoué sur le serveur avec une erreur inconnue. Vérifiez les journaux et assurez-vous que le fichier existe sur le serveur. ",
  "TR_DELETE_DATASET_FAILED3": "La suppression de l'ensemble de données '{{arg1}}' a échoué avec l'erreur {{arg2}}",
  "TR_DELETE_DATASET_NOT_FOUND": "Jeu de données {{arg1}} non trouvé.",
  "TR_DELETE_GOOSE": "Impossible de supprimer le {{arg1}} 'Bloc de contrôle de l'oie, il contient des éléments existants: {{arg2}}",
  "TR_DELETE_MAPPING": "Supprimer le mappage",
  "TR_DESCRIPTION": "Description",
  "TR_DESTINATION_UDP_PORT": "Port UDP de destination",
  "TR_DH_FILE_PATH": "Chemin du fichier DH",
  "TR_DIAGNOSTICS_LOG_MASK": "Masque de journal de diagnostic",
  "TR_DIAGNOSTICS_LOG_MASK_DESC": "Chaque bit active (1) / désactive (0) le motif de la consignation des données de diagnostic, telles que la modification du nombre de trames transmises, dans le journal des événements.  Si 0, aucune donnée de diagnostic ne sera enregistrée. ",
  "TR_DIAGNOSTICS_OPC_AELOG_MASK": "Diagnostic masque d'alarme et d'événements OPC",
  "TR_DIAGNOSTICS_OPC_AELOG_MASK_DESC": "Chaque bit active (1) / désactive (0) un motif de journalisation des données de diagnostic, telles que la modification du nombre de trames transmises, via l'alarme OPC et le serveur d'événements. Si 0, aucune donnée de diagnostic ne sera rapportée. ",
  "TR_DISABLE_SAVE_ON_EXIT_CHK": "Désactiver l'enregistrement à la sortie",
  "TR_DISCRETE_INPUTS": "Entrées discrètes",
  "TR_HORIZONTAL_DISPLAY": "Afficher horizontalement",
  "TR_VERTICAL_DISPLAY": "Afficher vertical",
  "TR_DISPLAY_WARNING_ON_ROOT": "Afficher un avertissement à la racine",
  "TR_DNPACTION_MASK0": "Masque d'action de session DNP",
  "TR_DNPACTION_MASK0_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements périodiques en même temps que le DNPActionPrd.\n DNP masque d'action Définitions:\n Voir la section 4.2 'Noms de balises prédéfinis pour la surveillance et le contrôle' dans le manuel. Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant le protocole DNP3. ",
  "TR_DNPACTION_NOW": "Masque d'action de session DNP maintenant",
  "TR_DNPACTION_NOW_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements périodiques en même temps que le DNPActionPrd.\n DNP masque d'action Définitions:\n Voir la section 4.2 'Noms de balises prédéfinis pour la surveillance et le contrôle' dans le manuel. Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant le protocole DNP3. ",
  "TR_DNPACTION_PRD0": "Période d'action de session DNP (ms)",
  "TR_DNPACTION_PRD0_DESC": "Temps entre les actions définies dans le masque DNPAction. La période est désactivée si elle est définie sur zéro.Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant le protocole DNP3. ",
  "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT": "Mode agressif",
  "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT_DESC": "Activer le mode agressif.",
  "TR_DNPAUTH_EXTRA_DIAGS": "Diagnostics supplémentaires",
  "TR_DNPAUTH_EXTRA_DIAGS_DESC": "Emet des diagnostics supplémentaires vers l'analyseur de protocole.",
  "TR_DNPAUTH_HMACALGORITHM": "algorithme HMAC",
  "TR_DNPAUTH_HMACALGORITHM_DESC": "L'algorithme HMAC à utiliser dans les challenges.",
  "TR_DNPAUTH_KEY_CHANGE_INTERVAL": "Intervalle de changement de clé (ms)",
  "TR_DNPAUTH_KEY_CHANGE_INTERVAL_DESC": "Pour maître: intervalle de clé de session.  Lorsque le temps écoulé depuis le dernier changement de clé a atteint cette valeur, les clés de session sont mises à jour. Pour les systèmes qui communiquent rarement, cette valeur peut être définie à zéro, en utilisant uniquement maxKeyChangeCount pour déterminer quand mettre à jour les clés.\n \n Pour esclave: intervalle et nombre de clés de session attendus. Lorsque cette durée est écoulée ou que le nombre de messages d'authentification envoyés ou reçus est atteint, les clés de session de cet utilisateur sont invalidées. L'intervalle et le nombre doivent correspondre à 2 fois l'intervalle et le nombre de changements de clé principale. Pour les systèmes qui communiquent rarement, DNPAuthKeyChangeInterval peut être défini sur zéro, en utilisant uniquement DNPAuthMaxKeyChangeCount pour déterminer le moment où les clés doivent être considérées comme anciennes et doivent être invalidées. ",
  "TR_DNPAUTH_MAX_ERROR_COUNT": "Nombre maximal d'erreurs (uniquement pour Sav2)",
  "TR_DNPAUTH_MAX_ERROR_COUNT_DESC": "Nombre de messages d'erreur à envoyer avant la désactivation de la transmission des messages d'erreur",
  "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT": "Nombre maximal de changements de clé",
  "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT_DESC": "Nombre d'ASDU d'authentification de session depuis le dernier changement de clé, lorsque ce nombre d'ASDU d'authentification est transmis ou reçu depuis le dernier changement de clé, les clés de session sont mises à jour.",
  "TR_DNPAUTH_OSNAME": "Nom du poste de sortie",
  "TR_DNPAUTH_OSNAME_DESC": "Nom de la station sortante de cette session dnp. Celui-ci doit être configuré pour correspondre à la fois au maître et à la station externe ",
  "TR_DNPAUTH_REPLY_TIMEOUT": "Délai de réponse de l'authentification (ms)",
  "TR_DNPAUTH_REPLY_TIMEOUT_DESC": "Comment faire pour attendre toute réponse d'authentification.",
  "TR_DNPAUTH_SAV5ENABLE": "Authentification sécurisée DNP version 5",
  "TR_DNPAUTH_SAV5ENABLE_DESC": "TRUE s'il s'agit d'une session DNP Secure Authentication Version 5.",
  "TR_DNPAUTH_USER_KEY": "Clé utilisateur (doit être de 16, 24 ou 32 valeurs hexadécimales)",
  "TR_DNPAUTH_USER_KEY_CHANGE_METHOD": "Méthode de changement de clé. (Titulaire dans le fichier INI)",
  "TR_DNPAUTH_USER_KEY_CHANGE_METHOD_DESC": "Méthode de changement de clé. (Titulaire dans le fichier INI)",
  "TR_DNPAUTH_USER_KEY_DESC": "Clé utilisateur (doit être de 16, 24 ou 32 valeurs hex).  Il doit exister un numéro d'utilisateur unique DNPAuthUserNumber pour chaque clé. ",
  "TR_DNPAUTH_USER_NAME": "Nom d'utilisateur",
  "TR_DNPAUTH_USER_NAME_DESC": "Le nom de l'utilisateur.",
  "TR_DNPAUTH_USER_NUMBER": "Numéro d'utilisateur",
  "TR_DNPAUTH_USER_NUMBER_DESC": "Numéro d'utilisateur: Configuration pour chaque utilisateur.  La spécification indique que le numéro d'utilisateur par défaut est 1 et fournit un numéro d'utilisateur pour le périphérique ou un utilisateur 'quelconque'. Configurez-le en tant que premier utilisateur de ce tableau. Ajoutez tout autre numéro d'utilisateur. Pour chaque numéro d'utilisateur dans le fichier ini, il doit y avoir une DNPAuthUserKey. ",
  "TR_DNPAUTO_ENABLE_UNSOL_CLASS1": "Classe 1",
  "TR_DNPAUTO_ENABLE_UNSOL_CLASS1_DESC": "Si cette option est activée, la messagerie non sollicitée est définie, cet indicateur indiquera que la classe d'événement 1 doit être activée pour les rapports non sollicités. Ce paramètre n’est utilisé que pour les sessions maîtres ",
  "TR_DNPAUTO_ENABLE_UNSOL_CLASS2": "Activer automatiquement Unsol Classe 2",
  "TR_DNPAUTO_ENABLE_UNSOL_CLASS2_DESC": "Si cette option est activée, la messagerie non sollicitée est définie, cet indicateur indiquera que la classe d'événements 2 doit être activée pour les rapports non sollicités. Ce paramètre n’est utilisé que pour les sessions maîtres ",
  "TR_DNPAUTO_ENABLE_UNSOL_CLASS3": "Activer automatiquement Unsol Classe 3",
  "TR_DNPAUTO_ENABLE_UNSOL_CLASS3_DESC": "Si cette option est activée, la messagerie non sollicitée est définie, cet indicateur indiquera que la classe d'événement 3 doit être activée pour les rapports non sollicités. Ce paramètre n’est utilisé que pour les sessions maîtres ",
  "TR_DNPCHANNEL_ACTION_MASK0": "Masque d'action du canal DNP",
  "TR_DNPCHANNEL_ACTION_MASK0_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements périodiques en même temps que DNPChannelActionPrd.\n DNP Masque d'action Définitions:\n Voir la section 4.2 'Noms d'étiquette prédéfinis pour la surveillance et le contrôle' dans le manuel. Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant le protocole DNP3. ",
  "TR_DNPCHANNEL_ACTION_NOW": "Masque d'action de canal DNP maintenant",
  "TR_DNPCHANNEL_ACTION_NOW_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements chronologiques en conjonction avec le masque d'action DNPActionPrd.\n DNP Action mask Définitions:\n Voir la section 4.2 'Noms de balises prédéfinis pour la surveillance et le contrôle' dans le manuel. Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant le protocole DNP3. ",
  "TR_DNPCHANNEL_ACTION_PRD0": "Période d'action du canal DNP (ms)",
  "TR_DNPCHANNEL_ACTION_PRD0_DESC": "Temps entre les actions définies dans le DNPChannelActionMask. La période est désactivée si elle est définie sur zéro.Ce paramètre est utilisé uniquement pour les sessions maîtres utilisant le protocole DNP3. ",
  "TR_DNPCHANNEL_RESPONSE_TIMEOUT": "Délai de réponse du canal DNP (ms)",
  "TR_DNPCHANNEL_RESPONSE_TIMEOUT_DESC": "Pour un maître DNP, comment attendre une réponse à une demande réellement transmise. Cette valeur peut être raccourcie par rapport au délai de réponse par défaut de la session pour déterminer rapidement si un périphérique particulier ne répond pas, sans provoquer également l'expiration des demandes adressées aux autres périphériques du même canal. NOTE: Ceci n'est pas utilisé par un esclave DNP. ",
  "TR_DNPENABLE_SECURE_AUTHENTICATION": "Active l'authentification sécurisée DNP",
  "TR_DNPENABLE_SECURE_AUTHENTICATION_DESC": "Active l'authentification sécurisée DNP pour cette session",
  "TR_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_INI_CSV_FILE": "Voulez-vous enregistrer vos fichiers INI / CSV actuels",
  "TR_DOMAIN_DESTINATION": "Noeud logique pour créer le jeu de données sur",
  "TR_DOMAIN_DESTINATION_DESC": "Choisissez le nœud de domaine sur lequel créer le jeu de données.",
  "TR_DOMAINS_LIST": "Domaines",
  "TR_DOMAINS_LIST_DESC": "Afficher la liste des domaines pour le modèle actuel",
  "TR_DOWNLOAD_CURRENT_CSV_FILE": "Télécharger le fichier CSV actuel",
  "TR_DOWNLOAD_CURRENT_INI_FILE": "Télécharger le fichier INI actuel",
  "TR_DOWNLOAD_FILE": "Télécharger le fichier",
  "TR_DOWNLOAD_SELECTED_FILE": "Télécharger le fichier sélectionné",
  "TR_DS_CONDITIONS_DETECTED": "Inclure les conditions DS détectées",
  "TR_DS_CONDITIONS_DETECTED_DESC": "Inclure les conditions DS détectées",
  "TR_DS_CREATE_NEW": "Créer un nouveau DS",
  "TR_DS_CREATE_NEW_DESC": "Créer un nouveau DS.",
  "TR_DS_DELETE_DESC": "Supprimer le DS sélectionné.",
  "TR_DS_LIST": "Liste des jeux de données",
  "TR_DS_LIST_DESC": "Spécifie la liste actuelle de jeu de données.",
  "TR_DS_MEMBER_LIST": "Liste des membres du jeu de données",
  "TR_DS_MEMBER_LIST_DESC": "Liste des membres de l'ensemble de données.",
  "TR_DS_NAME": "Nom du jeu de données",
  "TR_DS_NAME_DESC": "Spécifie le nom du jeu de données actuel.",
  "TR_DS_SELECTED_DELETE": "Supprimer le DS sélectionné",
  "TR_DSA": "DSA",
  "TR_DSA_PRIVATE_KEY_FILE": "Fichier de clé privée DSA",
  "TR_DSA_PRIVATE_KEY_PASS_PHRASE": "PassPhrase privé DSA",
  "TR_DSA_PUBLIC_CERT_FILE": "Fichier de certificat public DSA",
  "TR_DSLCT_CONFIRM": "DSLCT_CONFIRM",
  "TR_DSLCT_CONFIRM_DESC": "Une opération d'annulation a été confirmée par un périphérique distant.",
  "TR_DSLCT_PENDING": "DSLCT_PENDING",
  "TR_DSLCT_PENDING_DESC": "Une opération d'annulation a été transmise à un périphérique distant afin d'interrompre une opération de contrôle à 2 passages entre les 1er et 2e passages.",
  "TR_DSN_LIST": "Liste DSN",
  "TR_DSN_LIST_DESC": "Liste DSN",
  "TR_DUAL_END_POINT_IP_PORT": "Port IP de point de terminaison double",
  "TR_DUAL_REGISTER_TYPE": "Type de double registre",
  "TR_DUP_CHNG_MON": "Modification de la mise à jour des données",
  "TR_EDIT": "Modifier",
  "TR_EDIT_USER": "Editer l'utilisateur",
  "TR_ELEMENT_INDEX_M103": "Index d'élément (M103):",
  "TR_ELEMENT_INDEX_M103_DESC": "l'élément Index (M103)",
  "TR_EMAIL": "Email",
  "TR_ENABLE_EVENT_LOG_FILE": "Activer le journal de séquence d'événements",
  "TR_ENABLE_EVENT_LOG_FILE_DESC": "Si la valeur est true, le journal de séquence d'événements sera activé. Remarque: l'activation de ce journal peut dégrader les performances du SDG. ",
  "TR_ENABLE_IEC_FULL_STACK_ADDRESSING": "Activer l'adressage de pile complète IEC",
  "TR_ENABLE_UNSOLICITED_EVENT_CLASS": "Activer la classe d'événements non sollicités",
  "TR_ENABLE_UNSOLICITED_EVENT_CLASS_INFO": "Ces options nécessitent que le bit 'Activer automatiquement les événements non sollicités au démarrage du périphérique distant ou maître 0x0100' soit défini dans le masque de mode de requête automatique",
  "TR_END_DATE": "Date de fin",
  "TR_ENGINE_INI_FILE_NAME": "Nom du fichier INI du moteur",
  "TR_ENTER_A_PARTIAL_OR_COMPLETE_NODE_NAME": "Entrez un nom de noeud partiel ou complet",
  "TR_ENTER_FILTER": "Entrez un filtre",
  "TR_ENTER_PRODUCT_KEY": "Entrer la clé de produit",
  "TR_ENTRY_ID": "ID d'entrée",
  "TR_ENTRY_ID_DESC": "Spécifie si l'ID d'entrée est actif.",
  "TR_EQUATION_SYNTAX": "Impossible de modifier l'équation {{arg1}} pour {{arg2}}. Assurez-vous que la syntaxe de l'équation est correcte. ",
  "TR_EQUATION_ALLOC_MEMORY": "Impossible d'allouer de la mémoire pour '{{arg1}}'",
  "TR_EQUATION_BLANK": "Équation: le nom de l'équation ne peut pas être vide",
  "TR_EQUATION_END_OF_COMMENT": "Fin du commentaire introuvable",
  "TR_EQUATION_FUNCTION_CONVERT_FAILED": "Impossible de convertir '{{arg1}}' en '{{arg2}}'",
  "TR_EQUATION_FUNCTION_FIVE_ARGS": "La fonction '{{arg1}}' ne peut avoir que 5 arguments",
  "TR_EQUATION_FUNCTION_FOUR_ARGS": "La fonction '{{arg1}}' ne peut avoir que 4 arguments",
  "TR_EQUATION_FUNCTION_FOUT_ARGS": "La fonction '{{arg1}}' doit avoir 4 arguments",
  "TR_EQUATION_FUNCTION_NO_ARGS": "La fonction '{{arg1}}' ne peut avoir qu'un seul argument",
  "TR_EQUATION_FUNCTION_ONE_ARGS": "La fonction '{{arg1}}' doit avoir un argument",
  "TR_EQUATION_FUNCTION_SIX_ARGS": "La fonction '{{arg1}}' ne peut avoir que 6 arguments",
  "TR_EQUATION_FUNCTION_THREE_ARGS": "La fonction '{{arg1}}' ne peut avoir que 3 arguments",
  "TR_EQUATION_FUNCTION_TO_MANY_ARGS": "Trop d'arguments pour la fonction '{{arg1}}'",
  "TR_EQUATION_FUNCTION_TOO_MANY_ARGS": "Trop d'arguments pour la fonction '{{arg1}}'",
  "TR_EQUATION_FUNCTION_TWO_ARG": "La fonction '{{arg1}}' doit avoir au moins deux arguments",
  "TR_EQUATION_FUNCTION_TWO_ARGS": "La fonction '{{arg1}}' ne peut avoir que 2 arguments",
  "TR_EQUATION_ILLEGAL_CHAR": "Caractère de contrôle illégal dans la chaîne.",
  "TR_EQUATION_ILLEGAL_CHAR_IGNORED": "Caractère illégal '{{arg1}}'; le caractère est ignoré",
  "TR_EQUATION_ILLEGAL_TAB": "Onglet illégal à l'intérieur de la chaîne.",
  "TR_EQUATION_MEMORY_ALLOCATION": "Impossible d'allouer de la mémoire pour '{{arg1}}'",
  "TR_EQUATION_MISSING_QUOTE": "La citation est manquante avant la fin de la ligne.",
  "TR_EQUATION_PRODUCES_BAD_TYPE": "L'équation produit un type qui ne peut pas être utilisé",
  "TR_EQUATION_TIME_SOURCE": "Source temps de l'équation",
  "TR_EQUATION_TIME_SOURCE_DESC": "Spécifie la source de l'étiquette de temps pour les points de données générés à la suite d'une équation. Les valeurs possibles sont Update ou Reported, où Update signifie l'heure correspondant au dernier calcul de l'équation par rapport à l'horloge système SDG. Reported spécifie l'heure signalée du dernier événement ayant entraîné la modification du résultat de l'équation. L'heure rapportée sera relative à l'horloge système de l'esclave distant, sauf lors de l'initialisation, où l'horloge système du SDG est utilisée jusqu'à la réception du premier événement avec l'heure. Il est important de noter que l'interrogation de données statiques ou les événements reçus qui ne spécifient pas une heure rapportée peuvent entraîner la modification de la valeur d'un point de données spécifique sans la modification de l'heure de l'événement. Sur la base des fréquences d'interrogation du système et d'autres paramètres, des temps discontinus peuvent être signalés, en particulier dans les équations ayant des entrées provenant de plusieurs périphériques esclaves. ",
  "TR_EQUATION_UNEXPCTED_TOKEN": "Jeton inattendu '{{arg1}}'",
  "TR_EQUATION_UNEXPECTED_EQUATION": "Fin inattendue de l'équation",
  "TR_EQUATION_UNKONWN_IDENTIFIER": "Identificateur inconnu '{{arg1}}'",
  "TR_EQUATION_WRONG_TYPES": "La correspondance de type entre {{arg1}} 'et' {{arg2}} '(' {{arg3}} 'attend un type {{arg4}} et {{arg5}} est de { {arg6}} tapez, essayez de rendre les types identiques en utilisant un cast ou un MDO différent) ",
  "TR_EQUATIONS_LOG_MASK": "Masque du journal des équations",
  "TR_EQUATIONS_LOG_MASK_DESC": "Chaque bit active (1) / désactive (0) un motif permettant de consigner les résultats des équations. Si 0, aucun résultat d'équation ne sera enregistré. ",
  "TR_EQUATIONS_OPC_AELOG_MASK": "Masque d'équations et d'événements OPC d'équations",
  "TR_EQUATIONS_OPC_AELOG_MASK_DESC": "Chaque bit active (1) / désactive (0) une raison de consigner les résultats des équations via l'alarme OPC et le serveur d'événements. Si 0, aucun résultat d'équation ne sera rapporté. ",
  "TR_ERR": "Erreur: {{arg1}}",
  "TR_ERROR": "Erreur",
  "TR_ERROR_": "Erreur: {{erreur}}",
  "TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED": "Erreur: La liste de points DA 61850 n'a pas été modifiée",
  "TR_ERROR_61850_FC_DATASET_NOT_CHANGED": "Erreur: La contrainte fonctionnelle 61850 n'a pas été modifiée",
  "TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE": "Erreur: 61850 Membre de jeu de données GOOSE non disponible",
  "TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED": "Erreur: le jeu de données 61850 GOOSE n'a pas été modifié",
  "TR_ERROR_61850_IED_LIST_UNAVAILABLE": "Erreur: 61850 liste IED non disponible",
  "TR_ERROR_61850_MODEL_NOT_SAVED": "Erreur: le modèle 61850 n'a pas été enregistré",
  "TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE": "Erreur: 61850 membre du jeu de données du rapport non disponible",
  "TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED": "Erreur: l'ensemble de données du rapport 61850 n'a pas été modifié",
  "TR_ERROR_61850_SERVER_NOT_DISCONNECTED_CONNECTED": "Erreur: serveur 61850 non déconnecté / connecté",
  "TR_ERROR_61850_SERVER_NOT_RESTARTED": "Erreur: le serveur 61850 n'a pas été redémarré",
  "TR_ERROR_61850_SERVER_READ": "Erreur: la lecture du serveur 61850 a échoué",
  "TR_ERROR_ACTIVATE_OPC_ITEM_FAILED": "Erreur: L'activation de l'élément OPC a échoué",
  "TR_ERROR_ARG1": "Erreur: {{arg1}}",
  "TR_ERROR_AUTO_CREATE_TAGS": "Erreur lors de la création automatique de balises",
  "TR_ERROR_CAN_T_READ_AUDIT_DATABASE": "Erreur: Impossible de lire la base de données d'audit",
  "TR_ERROR_CAN_T_READ_USERS_DATABASE": "Erreur: Impossible de lire la base de données des utilisateurs.",
  "TR_ERROR_CAN_T_SAVE_LICENSE": "Erreur: licence non enregistrée",
  "TR_ERROR_CAN_T_SAVE_LICENSE_": "L'erreur ne parvient pas à enregistrer la licence",
  "TR_ERROR_CAN_T_SAVE_LICENSE_PRODUCT_KEY_EXHAUSTED": "Erreur de licence: clé de produit épuisée",
  "TR_ERROR_CAN_T_SAVE_LICENSE_UNSPECIFIED_ERROR": "Erreur de licence: erreur non spécifiée",
  "TR_ERROR_COMMAND_FAILED": "Erreur: La commande a échoué.",
  "TR_ERROR_DATA_NOT_SAVED": "Erreur: données non enregistrées",
  "TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK": "Erreur: Impossible d'activer ou de désactiver le contrôle de bloc 61850",
  "TR_ERROR_FAILED_TO_STOP_AFTER_120_SEC": "Impossible d'arrêter le moteur après 120 secondes",
  "TR_ERROR_FILE_NOT_DOWNLOADED": "Erreur: fichier non téléchargé",
  "TR_ERROR_FILE_NOT_SAVED": "Erreur: fichier non enregistré.",
  "TR_ERROR_GOOSE_MONITOR_STREAMS_UNAVAILABLE": "Erreur:",
  "TR_ERROR_IN_CSV_FILE": "Erreur: le fichier CSV ne peut pas être enregistré.",
  "TR_ERROR_IN_INI_FILE": "Erreur: le fichier INI ne peut pas être enregistré.",
  "TR_ERROR_IN_NEW_INI_FILE": "Erreur: le fichier INI doit avoir une extension de fichier .ini",
  "TR_ERROR_INVALID_EQUATION": "Erreur: équation non valide",
  "TR_ERROR_INVALID_FILENAME": "Erreur: nom de fichier invalide",
  "TR_ERROR_NO_OPC_ITEM_SELECTED": "Erreur: aucun élément OPC sélectionné",
  "TR_ERROR_OBJECT_NOT_DELETED": "Erreur: objet non supprimé.",
  "TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE": "Erreur: informations de table ODBC non disponibles",
  "TR_ERROR_OPC_ADD_ITEM_FAILED": "Erreur: impossible d'ajouter un élément OPC",
  "TR_ERROR_OPC_LOAD_ITEM_FAILED": "Erreur: impossible de charger un élément OPC",
  "TR_ERROR_OPC_SERVER_NOT_DISCONNECTED_CONNECTED": "Erreur: le serveur OPC ne peut pas être connecté ou déconnecté",
  "TR_ERROR_OPCAE_SERVER_NOT_DISCONNECTED_CONNECTED": "Erreur: Le serveur OPC AE ne peut pas être connecté ou déconnecté",
  "TR_ERROR_OPCUA_SERVER_NOT_DISCONNECTED_CONNECTED": "Erreur: Le serveur OPC UA ne peut pas être connecté ou déconnecté",
  "TR_ERROR_PERFORM_WRITE_ACTION": "Erreur: l'action de pré-écriture en écriture a échoué",
  "TR_ERROR_READ_OPC_ITEM_FAILED": "Erreur: impossible de lire le poste OPC",
  "TR_ERROR_REFRESH_OPC_PROPERTIES_FAILED": "Erreur: Impossible d'actualiser les propriétés de l'élément OPC",
  "TR_ERROR_RESET_61850_RETRY_CONNECT_COUNT": "Erreur: Impossible de réinitialiser le nombre de tentatives pour 61850",
  "TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM": "Erreur: impossible de se désabonner ou de s'abonner à GOOSE Stream",
  "TR_ERROR_TASE2_OPERATE_CONTROL": "Erreur: le contrôle de fonctionnement ICCP a échoué",
  "TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE": "Erreur: membre du jeu de données de rapport ICCP non disponible",
  "TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED": "Erreur: Le jeu de données du rapport ICCP n'a pas été modifié",
  "TR_ERROR_TASE2_SERVER_NOT_DISCONNECTED_CONNECTED": "Erreur: serveur ICCP non déconnecté / connecté",
  "TR_ERROR_TASE2_SERVER_NOT_RESTARTED": "Erreur: serveur ICCP non redémarré",
  "TR_ERROR_TASE2_SERVER_NOT_SAVED": "Erreur: le serveur ICCP ne peut pas être enregistré",
  "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER": "Erreur <br /> La communication avec le moniteur est perdue. <br /> Veuillez rafraîchir votre navigateur.",
  "TR_ERROR_THE_GATEWAY_IS_NOT_RUNNING": "Erreur: la passerelle n'est pas en cours d'exécution",
  "TR_ERROR_USER_NOT_DELETED": "Erreur: utilisateur non supprimé.",
  "TR_ERROR_VERIFY_61850_DATASET": "Erreur: Impossible de vérifier le jeu de données 61850",
  "TR_ERRORS_LOG_MASK": "Masque du journal des erreurs",
  "TR_ERRORS_LOG_MASK_DESC": "Chaque bit active (1) / désactive (0) un motif de journalisation des erreurs, telles que les modifications du nombre d'échecs de la somme de contrôle de la couche de liaison ou du statut de session de liaison en ligne / hors ligne, dans le journal des événements. .  Si 0, rien ne sera enregistré. ",
  "TR_ERRORS_OPC_AELOG_MASK": "Erreurs d'alarme et d'événement OPC",
  "TR_ERRORS_OPC_AELOG_MASK_DESC": "Chaque bit active (1) / désactive (0) un motif pour signaler des erreurs, telles que des modifications du nombre d'échecs de la somme de contrôle de la couche de liaison ou de l'état d'une session de liaison, via l'alarme OPC et Serveur d'événements. Si 0, aucune erreur ne sera signalée. ",
  "TR_EU_Type": "Type UE",
  "TR_EU_Type_DESC": "Définir le type d'UE",
  "TR_EVENT_CODE_DETECTED": "Inclure le code d'événement détecté",
  "TR_EVENT_CODE_DETECTED_DESC": "Inclure le code d'événement détecté",
  "TR_EVENT_LOG_FILE_NAME": "Nom du fichier du journal de séquence d'événements",
  "TR_EVENT_LOG_FILE_NAME_DESC": "Nom et chemin du fichier journal du journal des événements.  Voir le manuel pour une description des mots-clés de la propriété% xxx disponibles. ",
  "TR_EVENT_LOG_RECORD_FORMAT": "Format d'enregistrement du journal de séquence d'événements",
  "TR_EVENT_LOG_RECORD_FORMAT_DESC": "Format d'enregistrement du journal de séquence d'événements.  Voir le manuel pour une description des mots-clés de la propriété% xxx disponibles. ",
  "TR_EVENT_NAME": "Nom de l'événement",
  "TR_EVENT_NAME_DESC": "Définir le nom de l'événement",
  "TR_EVENT_SPACE": "Espace d'événement",
  "TR_EVENT_SPACE_DESC": "Définir l'espace de l'événement",
  "TR_EXCEPTION_STATUS": "Statut d'exception",
  "TR_EXECUTE_SQL": "Exécuter / Tester la requête SQL",
  "TR_EXECUTE_SQL_DESC": "Exécuter / Tester la requête SQL",
  "TR_EXPAND_CHILDREN": "Développer les enfants",
  "TR_EXPIRES": "Expires",
  "TR_EXPRESSION": "Expression:",
  "TR_EXPRESSION_DESC": "l'expression de la balise",
  "TR_EXTRA": "Extra",
  "TR_FAILED_TO_ADD_MDO_OPC_SERVER": "Impossible d'ajouter {{arg1}} MDO sur le serveur OPC: {{arg2}}. (Dupliquer?)",
  "TR_FAILED_TO_APPLY_ALIAS_TO_61850_TAG": "Impossible d'appliquer l'alias '{{arg1}}' à la balise 61850 (dupliquer?)",
  "TR_FAILED_TO_APPLY_ALIAS_TO_ICCP_TAG": "Impossible d'appliquer l'alias '{{arg1}}' à la balise ICCP (dupliquer?)",
  "TR_FAILED_TO_APPLY_ALIAS_TO_OPC_TAG": "Impossible d'appliquer l'alias '{{arg1}}' sur la balise OPC (en double?)",
  "TR_FC_CF": "CF - Configuration",
  "TR_FC_CF_DESC": "Activer la contrainte fonctionnelle CF - Configuration",
  "TR_FC_SP": "SP - Points de consigne",
  "TR_FC_SP_DESC": "Activer la contrainte fonctionnelle SP - Points de consigne",
  "TR_FC_SV": "SV - Substitution",
  "TR_FC_SV_DESC": "Activer la contrainte fonctionnelle SV - Substitution",
  "TR_FILE": "Fichier",
  "TR_FILE_ALREADY_EXISTS_OVERWRITE_IT": "Le fichier sélectionné existe déjà dans le dossier de destination, voulez-vous l'écraser?",
  "TR_FILE_DOWNLOADED": "Fichier téléchargé",
  "TR_FILE_SAVED": "Fichier enregistré.",
  "TR_FILTER": "Filtre",
  "TR_FUNCTION_M103": "Fonction (M103):",
  "TR_FUNCTION_M103_DESC": "la fonction (M103) de la balise",
  "TR_FUNCTIONAL_CONSTRAINT": "Contrainte fonctionnelle",
  "TR_FUNCTIONAL_CONSTRAINT_DESC": "Spécifie la contrainte fonctionnelle",
  "TR_GATEWAY": "Gateway",
  "TR_GATEWAY_ABBREVIATION": "G:",
  "TR_GATEWAY_API_HOST_AND_HTTP_PORT": "Hôte de l'API de surveillance et port HTTP",
  "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION": "Délai d'expiration d'authentification de l'utilisateur (secondes)",
  "TR_GATEWAY_CONTROL_LOG_MASK": "Masque du journal de contrôle",
  "TR_GATEWAY_CONTROL_LOG_MASK_DESC": "Chaque bit active (1) / désactive (0) un motif de journalisation des données de contrôle, telles que les modifications apportées à pollEnabled ou à GeneralInterrogationPeriod, dans le fichier journal des événements.  Si 0, rien ne sera enregistré. ",
  "TR_GATEWAY_CONTROL_OPC_AELOG_MASK": "Contrôle du masque d'alarme et d'événement OPC",
  "TR_GATEWAY_CONTROL_OPC_AELOG_MASK_DESC": "Chaque bit active (1) / désactive (0) un motif de journalisation des données de contrôle, telles que les modifications apportées à pollEnabled ou à GeneralInterrogationPeriod, via l'alarme OPC et le serveur d'événements. Si 0, rien ne sera signalé. ",
  "TR_GATEWAY_ENABLE_AUDIT": "Activer le journal d'audit",
  "TR_GATEWAY_ENABLE_AUTHENTICATION": "Activer l'authentification de l'utilisateur",
  "TR_GATEWAY_ENABLE_TRACE": "Trace d'activation de la passerelle",
  "TR_GATEWAY_EXE_NAME": "Nom de l'exécutable de la passerelle",
  "TR_GATEWAY_HOST_AND_HTTP_PORT": "Hôte de la passerelle et port Http (s)",
  "TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE": "Nombre d'éléments par page pour les mappages et les listes de balises sur l'interface utilisateur Web (0 à désactiver)",
  "TR_GATEWAY_INI_FILE_PATH": "Chemin du fichier INI de la passerelle",
  "TR_GATEWAY_IS_RUNNING": "La passerelle est en cours d'exécution.",
  "TR_GATEWAY_IS_STARTING": "La passerelle démarre",
  "TR_GATEWAY_IS_STOPPED": "La passerelle est arrêtée.",
  "TR_GATEWAY_MANAGEMENT": "Gestion de la passerelle",
  "TR_GATEWAY_TIME_ZONE_DB_PATH": "Chemin de la base de données de fuseau horaire de la passerelle",
  "TR_GATEWAY_WEB_DIRECTORY": "Répertoire Web de la passerelle",
  "TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT": "Hôte API du moteur et port HTTP",
  "TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE": "Taille du bloc de mise à jour de la passerelle Websocket",
  "TR_GATEWAY_WEBSOCKET_UPDATE_RATE": "Taux de mise à jour de la passerelle Websocket (secondes)",
  "TR_GCB_LIST": "Liste de rapports",
  "TR_GCB_LIST_DESC": "Spécifie la liste de rapports en cours.",
  "TR_GCB_NAME": "Nom du rapport",
  "TR_GCB_NAME_DESC": "Nom du rapport en cours.",
  "TR_GENERAL_INTERROGATION": "Interrogation générale",
  "TR_GENERAL_INTERROGATION_DESC": "Spécifie si l'interrogation générale est active.",
  "TR_GLOBAL_CREATE_TAG_AUTOMATIC": "Global Create Tags Auto",
  "TR_GLOBAL_CREATE_TAG_AUTOMATIC_DESC": "si la valeur est true, des balises (et de l'espace de stockage) sont automatiquement créés lors de la réception de nouveaux points de données dans les messages de réponse entrants provenant d'appareils distants.",
  "TR_GOOD": "BON",
  "TR_GOOD_DESC": "aucun bit n'est défini",
  "TR_GOOSE_ADAPTER": "Adaptateur GOOSE",
  "TR_GOOSE_ADAPTOR": "Adaptateur GOOSE",
  "TR_GOOSE_MONTIOR_ADAPTOR": "Moniteur Goose '{{arg1}}': Impossible de trouver l'adaptateur ({{arg2}}, {{arg3}}). Le moniteur Goose ne fonctionnera pas correctement tant que cela n’est pas corrigé.",
  "TR_GOOSE_MONTIOR_LOAD_SCL": "Moniteur Goose '{{arg1}}': Impossible de charger le fichier SCL: {{arg2}}. Assurez-vous que le fichier et le chemin sont corrects.",
  "TR_GOOSE_MONTIOR_NO_MORE": "Plus de GooseMonitors disponibles",
  "TR_GOOSE_NAME": "Nom GOOSE",
  "TR_GOOSE_STREAM_MANAGEMENT_ADAPTOR": "Gestion de l'adaptateur Goose Stream Adapter",
  "TR_GOOSEMONITOR_ADAPTER_DEVICE": "Chemin système du système d'adaptateur GOOSE",
  "TR_GOOSEMONITOR_ADAPTER_DEVICE_DESC": "Spécifie un chemin système du périphérique adaptateur GOOSE",
  "TR_GOOSEMONITOR_NAME": "Nom du périphérique GOOSE Monitor",
  "TR_GOOSEMONITOR_NAME_DESC": "Spécifie le nom du périphérique GOOSE Monitor",
  "TR_GOOSEMONITOR_SCLFILE": "Fichier SCL / ICD de GOOSE Monitor",
  "TR_GOOSEMONITOR_SCLFILE_DESC": "Spécifie le fichier SCL / ICD de GOOSE Monitor",
  "TR_GOOSEMONITOR_STREAM": "Flux GOOSE surveillé",
  "TR_GOOSEMONITOR_STREAM_DESC": "Spécifie un flux GOOSE surveillé pour un périphérique GOOSE Monitor",
  "TR_GOOSEMONITOR_STREAM_THRESHOLD": "Temps de seuil invalide (secondes)",
  "TR_GOOSEMONITOR_STREAM_THRESHOLD_DESC": "Spécifie une durée de seuil non valide du flux GOOSE surveillé en secondes",
  "TR_GTWDEFS_UPDTRSN_CHNG_INDICATED_DESC": "La source des données indique que cette mise à jour est une modification.",
  "TR_GTWDEFS_UPDTRSN_CTRL_AT_DEVICE_DESC": "Les données ont été modifiées à la suite d'une opération de contrôle exécutée localement sur le périphérique.",
  "TR_GTWDEFS_UPDTRSN_CTRL_BY_COMM_DESC": "Les données ont été modifiées à la suite d'une opération de contrôle effectuée par le biais de communications.",
  "TR_GTWDEFS_UPDTRSN_CTRL_CONFIRM_DESC": "Utilisé pour les points de contrôle: une opération de contrôle a été confirmée (par exemple, le périphérique distant a envoyé la confirmation d'avoir reçu une opération de contrôle).  Toutefois, bien que confirmée, l’opération de contrôle n’est peut-être pas encore terminée. Une fois terminé, GTWDEFS_UPDTRSN_CTRL_BY_COMM sera utilisé. ",
  "TR_GTWDEFS_UPDTRSN_CTRL_ERROR_DESC": "Utilisé pour les points de contrôle: une erreur s'est produite lors de l'opération de contrôle.",
  "TR_GTWDEFS_UPDTRSN_CTRL_PENDING_DESC": "Utilisé pour les points de contrôle: une opération de contrôle a été lancée (par exemple, envoyée à un périphérique distant).  Cela pourrait se produire lors du second passage d'une opération à 2 passes ou lors du seul passage d'une opération à une passe. ",
  "TR_GTWDEFS_UPDTRSN_DSLCT_CONFIRM_DESC": "Utilisé pour les points de contrôle: une opération de dé-sélection (annulation d'une opération de sélection au premier passage) a été confirmée (par exemple, le périphérique distant a envoyé la confirmation d'avoir désélectionné l'opération de contrôle).",
  "TR_GTWDEFS_UPDTRSN_DSLCT_PENDING_DESC": "Utilisé pour les points de contrôle: l'opération de dé-sélection (annulation d'une opération de sélection au premier passage) a été lancée (par exemple, envoyée à un périphérique distant).",
  "TR_GTWDEFS_UPDTRSN_NONE_DESC": "Peut être utilisé comme masque log / * pour désactiver la journalisation",
  "TR_GTWDEFS_UPDTRSN_REFRESH_DESC": "Les données sont actualisées par la source de données (sans demande); aucun événement de changement n'est nécessairement indiqué.",
  "TR_GTWDEFS_UPDTRSN_REQUESTED_DESC": "Les données ont été demandées; aucun événement de modification n'est nécessairement indiqué.",
  "TR_GTWDEFS_UPDTRSN_SLCT_CONFIRM_DESC": "Utilisé pour les points de contrôle: un premier passage dans une opération de contrôle à deux passes a été confirmé (par exemple, le périphérique distant a envoyé la confirmation d'avoir reçu l'opération de contrôle de premier passage).",
  "TR_GTWDEFS_UPDTRSN_SLCT_PENDING_DESC": "Utilisé pour les points de contrôle: le premier passage d'une opération de contrôle à deux passes a été initié (par exemple, envoyé à un périphérique distant)",
  "TR_GTWDEFS_UPDTRSN_TEST_MODE_DESC": "Utilisé par certains protocoles pour indiquer que le point ou le périphérique fonctionne en mode test.",
  "TR_GTWDEFS_UPDTRSN_UNKNOWN_DESC": "Les données sont en cours de mise à jour pour un inconnu",
  "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_ALL": "ALL",
  "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_DATA": "Données",
  "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_HEALTH": "Santé",
  "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_PERFORMANCE": "Perf",
  "TR_GTWTYPES_TAG_PURPOSE_ALL": "Tous",
  "TR_GTWTYPES_TAG_PURPOSE_MASK_DATA": "Filtre de données",
  "TR_GTWTYPES_TAG_PURPOSE_MASK_HEALTH": "Filtre de santé",
  "TR_GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE": "Filtre de performance",
  "TR_HEALTH_DESC": "Santé",
  "TR_HEALTH_VIEW": "Vue de la santé",
  "TR_HELP": "Aide",
  "TR_HI": "Bonjour",
  "TR_HIGH_ORDER_INDEX": "Index du registre d'ordre élevé",
  "TR_HIGH_ORDER_INDEX_DESC": "Spécifiez l'index du registre d'ordre élevé",
  "TR_HOLDING_REGISTERS": "Registres de stockage",
  "TR_I14AUTH_ENABLE": "Active l'authentification sécurisée",
  "TR_I14AUTH_ENABLE_DESC": "Active l'authentification sécurisée pour ce secteur",
  "TR_I14AUTH_EXTRA_DIAGS": "Diagnostic supplémentaire",
  "TR_I14AUTH_EXTRA_DIAGS_DESC": "Emet des diagnostics supplémentaires vers l'analyseur de protocole.",
  "TR_I14AUTH_HMACALGORITHM": "algorithme HMAC",
  "TR_I14AUTH_HMACALGORITHM_DESC": "L'algorithme HMAC à utiliser dans les challenges.",
  "TR_I14AUTH_KEY_CHANGE_INTERVAL": "Intervalle clé",
  "TR_I14AUTH_KEY_CHANGE_INTERVAL_DESC": "Pour maître: intervalle de clé de session.  Lorsque le temps écoulé depuis le dernier changement de clé a atteint cette valeur, les clés de session sont mises à jour. Pour les systèmes qui communiquent rarement, cette valeur peut être définie à zéro, en utilisant uniquement maxKeyChangeCount pour déterminer quand mettre à jour les clés.\n \n Pour esclave: intervalle et nombre de clés de session attendus. Lorsque cette durée est écoulée ou que le nombre de messages d'authentification envoyés ou reçus est atteint, les clés de session de cet utilisateur sont invalidées. L'intervalle et le nombre doivent correspondre à 2 fois l'intervalle et le nombre de changements de clé principale. Pour les systèmes qui communiquent rarement, I14AuthKeyChangeInterval peut être défini sur zéro, en utilisant uniquement I14AuthMaxKeyChangeCount pour déterminer le moment où les clés doivent être considérées anciennes et doivent être invalidées. ",
  "TR_I14AUTH_MAX_KEY_CHANGE_COUNT": "Nombre de modifications de clé",
  "TR_I14AUTH_MAX_KEY_CHANGE_COUNT_DESC": "Nombre d'ASDU d'authentification de session depuis le dernier changement de clé, lorsque ce nombre d'ASDU d'authentification est transmis ou reçu depuis le dernier changement de clé, les clés de session sont mises à jour.",
  "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH": "Longueur des données de challenge aléatoires",
  "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH_DESC": "Longueur des données de contestation aléatoire à envoyer à la demande de contestation.",
  "TR_I14AUTH_REPLY_TIMEOUT": "Comment faire pour attendre une réponse d'authentification (ms)",
  "TR_I14AUTH_REPLY_TIMEOUT_DESC": "Comment faire pour attendre toute réponse d'authentification.",
  "TR_I14AUTH_SECURITY_STATS_IOA": "Numéro du point de base pour les statistiques de sécurité",
  "TR_I14AUTH_SECURITY_STATS_IOA_DESC": "Adresse d'objet d'information de base (IOA) pour les statistiques de sécurité.",
  "TR_I14AUTH_USER_KEY": "Clé utilisateur (doit être 32 valeurs hex)",
  "TR_I14AUTH_USER_KEY_DESC": "Clé utilisateur (doit comporter 32 valeurs hexadécimales).  Il doit y avoir un numéro d'utilisateur unique pour chaque clé I14AuthUserNumber. ",
  "TR_I14AUTH_USER_NAME": "Nom de l'utilisateur",
  "TR_I14AUTH_USER_NAME_DESC": "Le nom de l'utilisateur.",
  "TR_I14AUTH_USER_NUMBER": "Numéro d'utilisateur",
  "TR_I14AUTH_USER_NUMBER_DESC": "Numéro d'utilisateur: Configuration pour chaque utilisateur.  La spécification indique que le numéro d'utilisateur par défaut est 1 et fournit un numéro d'utilisateur pour le périphérique ou un utilisateur 'quelconque'. Configurez-le en tant que premier utilisateur de ce tableau. Ajoutez tout autre numéro d'utilisateur. Pour chaque numéro d'utilisateur dans le fichier ini, il doit y avoir une clé I14AuthUserKey. ",
  "TR_I61400ALARMS_NAME": "Nom du nœud d'alarme",
  "TR_I61400ALARMS_NAME_DESC": "Spécifie le nom du noeud Alarmes SDG",
  "TR_I61400EVENT_ALARMS_ARRAY_NAME": "Nom du groupe d'alarmes d'événement",
  "TR_I61400EVENT_ALARMS_ARRAY_NAME_DESC": "Spécifie le nom du tableau d'alarmes d'événements CEI 61400-25 (WALM)",
  "TR_I61400STATUS_ALARMS_ARRAY_NAME": "Nom du groupe d'alarmes d'état",
  "TR_I61400STATUS_ALARMS_ARRAY_NAME_DESC": "Spécifie le nom du groupe d'alarmes d'état CEI 61400-25 (WALM)",
  "TR_I61850AUTH_MECHANISM": "Mécanisme d'autorisation ('Aucun' ou 'Mot de passe' ou 'Certificat')",
  "TR_I61850AUTH_MECHANISM_DESC": "Mécanisme d'autorisation IEC 61850 ('Aucun' ou 'Mot de passe' ou 'Certificat')",
  "TR_I61850AUTH_PASSWORD": "Mot de passe",
  "TR_I61850AUTH_PASSWORD_DESC": "Mot de passe d'autorisation CEI 61850",
  "TR_I61850CLIENT_AEINVOKE_ID": "ID d'invocation AE",
  "TR_I61850CLIENT_AEINVOKE_ID_DESC": "Spécifie l'ID d'appel AE pour ce client CEI 61850",
  "TR_I61850CLIENT_AEQUALIFIER": "Qualificateur AE",
  "TR_I61850CLIENT_AEQUALIFIER_DESC": "Spécifie le qualificateur AE pour ce client CEI 61850",
  "TR_I61850CLIENT_APINVOKE_ID": "ID d'invocation AP",
  "TR_I61850CLIENT_APINVOKE_ID_DESC": "Spécifie l'ID d'appel AP pour ce client CEI 61850",
  "TR_I61850CLIENT_APP_ID": "ID de l'application",
  "TR_I61850CLIENT_APP_ID_DESC": "Spécifie l'ID d'application de ce client CEI 61850",
  "TR_I61850CLIENT_CONNECT_TIMEOUT": "Délai d'expiration de la connexion MMS (ms)",
  "TR_I61850CLIENT_CONNECT_TIMEOUT_DESC": "Spécifie le délai d'expiration de la connexion MMS pour le client CEI 61850.  Après avoir démarré une tentative de connexion, voici comment attendre le succès.  La longueur de ce paramètre dépend de la topologie de votre réseau.  Cette valeur est également utilisée pour spécifier le délai d’expiration pour les messages de demande 61850 ",
  "TR_I61850CLIENT_IPADDRESS": "Adresse IP du client",
  "TR_I61850CLIENT_IPADDRESS_DESC": "Spécifie l'adresse IP de ce client CEI 61850.  Cela peut être utile pour sélectionner une autre carte réseau ",
  "TR_I61850CLIENT_MMSCOMMON_NAME": "Nom commun MMS",
  "TR_I61850CLIENT_MMSCOMMON_NAME_DESC": "Spécifie le nom commun MMS",
  "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE": "Fichier de clé privée",
  "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE_DESC": "Spécifie le fichier de clé privée MMS",
  "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE": "Phrase secrète de la clé privée",
  "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE_DESC": "Spécifie la phrase de passe de la clé privée MMS",
  "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE": "Fichier de certificat public",
  "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE_DESC": "Spécifie le fichier de certificat public MMS",
  "TR_I61850CLIENT_NAME": "Nom du client",
  "TR_I61850CLIENT_NAME_DESC": "Nom du client CEI 61850",
  "TR_I61850CLIENT_PRESENTATION_ADDRESS": "Adresse de présentation",
  "TR_I61850CLIENT_PRESENTATION_ADDRESS_DESC": "Spécifie l'adresse de présentation de ce client CEI 61850",
  "TR_I61850CLIENT_RECONNECT_RETRY_COUNT": "Reconnecter le nombre de tentatives répétées",
  "TR_I61850CLIENT_RECONNECT_RETRY_COUNT_DESC": "Spécifie le décompte de nouvelles tentatives de reconnexion pour le client CEI 61850 (0 = tentative de reconnexion permanente). Une connexion réussie entraîne la réinitialisation du compteur de limites interne sur 0, ce qui permet de poursuivre la connexion au serveur CEI 61850 . ",
  "TR_I61850CLIENT_RECONNECT_TIME": "Délai de reconnexion (ms)",
  "TR_I61850CLIENT_RECONNECT_TIME_DESC": "Spécifie le délai de reconnexion pour le client CEI 61850 (0 = pas de reconnexion), doit être supérieur à I61850ClientConnectTimeout",
  "TR_I61850CLIENT_SESSION_ADDRESS": "Adresse de session",
  "TR_I61850CLIENT_SESSION_ADDRESS_DESC": "Spécifie l'adresse de session de ce client CEI 61850",
  "TR_I61850CLIENT_TLSMAX_PDUS": "Nombre maximal de PDU avant de forcer la renégociation de chiffrement",
  "TR_I61850CLIENT_TLSMAX_PDUS_DESC": "Spécifie la PDU TLS Max avant de forcer la renégociation de chiffrement",
  "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME": "Délai maximal de renégociation (ms)",
  "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME_DESC": "Spécifie la durée maximale de renégociation TLS",
  "TR_I61850CLIENT_TLSRENEGOTIATION": "Renégociation (sec)",
  "TR_I61850CLIENT_TLSRENEGOTIATION_DESC": "Temps maximal (en secondes) avant de forcer la renégociation de chiffrement",
  "TR_I61850CLIENT_TRANSPORT_ADDRESS": "Adresse de transport",
  "TR_I61850CLIENT_TRANSPORT_ADDRESS_DESC": "Spécifie l'adresse de transport de ce client CEI 61850",
  "TR_I61850CLIENT_USE_SISCO_COMPATABILITY": "Utiliser la compatibilité ED1",
  "TR_I61850CLIENT_USE_SISCO_COMPATABILITY_DESC": "Spécifie si les paramètres de sécurité utilisent la compatibilité ED1 ou la compatibilité Sisco",
  "TR_I61850GOOSE_ADAPTER_NAME": "Nom de l'adaptateur Goose",
  "TR_I61850GOOSE_ADAPTER_NAME_DESC": "Nom de l'adaptateur pour oie sélectionné.",
  "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED": "Charger le modèle à partir d'un fichier",
  "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED_DESC": "Le client CEI 61850 doit-il charger le modèle client à partir d'un fichier dans I61850SCLNomFichier []",
  "TR_I61850POLLED_DATA_SET_NAME": "Nom du fichier interrogé",
  "TR_I61850POLLED_DATA_SET_NAME_DESC": "Spécifie le nom du fichier interrogé",
  "TR_I61850POLLED_DATA_SET_PERIOD": "Période du fichier interrogé (ms)",
  "TR_I61850POLLED_DATA_SET_PERIOD_DESC": "Spécifie la période pour lire le fichier interrogé. Une valeur de zéro signifie désactiver, c'est-à-dire qu'il n'y aura pas d'interrogation. ",
  "TR_I61850POLLED_POINT_SET_NAME": "Nom de l'ensemble de points interrogés",
  "TR_I61850POLLED_POINT_SET_NAME_DESC": "Spécifie le nom de l'ensemble de points interrogés.",
  "TR_I61850POLLED_POINT_SET_PERIOD": "Période de définition du point interrogé (ms)",
  "TR_I61850POLLED_POINT_SET_PERIOD_DESC": "Spécifie la période pour lire l'ensemble de points interrogés. Une valeur de zéro signifie désactiver, c'est-à-dire qu'il n'y aura pas d'interrogation. ",
  "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT": "Purger le RCB avant d'activer la reconnexion",
  "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Purger le RCB avant d'activer la reconnexion",
  "TR_I61850RCBPURGE_BEFORE1ST_ENABLE": "Purger le RCB avant le 1er activé",
  "TR_I61850RCBPURGE_BEFORE1ST_ENABLE_DESC": "Purger le RCB avant le 1er activé",
  "TR_I61850RCBRCBRETRY_ENABLE_COUNT": "Nombre de fois que le SDG doit réactiver l'activation",
  "TR_I61850RCBRCBRETRY_ENABLE_COUNT_DESC": "Le nombre de fois que le SDG doit réessayer d'activer ce rapport lorsqu'il ne parvient pas à s'activer sur le serveur. -1 = pour toujours, 0 = jamais",
  "TR_I61850RCBRETRY_ENABLE_PERIOD": "nouvelle tentative d'activation de la période RCB (ms)",
  "TR_I61850RCBRETRY_ENABLE_PERIOD_DESC": "La période pour réessayer l'activation des RCB en millisecondes.",
  "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW": "Dépassement de mémoire tampon inclus",
  "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW_DESC": "Spécifie la propriété incluse de dépassement de mémoire tampon",
  "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME": "Temps de tampon (ms)",
  "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME_DESC": "Spécifie la durée de mémoire tampon d'un bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV": "Révision de la configuration incluse",
  "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV_DESC": "Spécifie la propriété incluse de révision de configuration",
  "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE": "Surveiller le changement de données",
  "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE_DESC": "Spécifie la propriété de modification des données du moniteur du bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF": "Référence de données incluse",
  "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF_DESC": "Spécifie la propriété incluse dans la référence de données",
  "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME": "Nom du fichier inclus",
  "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME_DESC": "Spécifie la propriété incluse dans le nom de l'ensemble de données",
  "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME": "Nom du fichier",
  "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME_DESC": "Spécifie le nom d'un ensemble de données de bloc de contrôle de rapport sur un serveur CEI 61850",
  "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE": "Surveiller la modification de la mise à jour des données",
  "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE_DESC": "Spécifie la propriété de modification de mise à jour des données de contrôle du bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID": "ID d'entrée inclus",
  "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID_DESC": "Spécifie la propriété incluse dans l'ID d'entrée",
  "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG": "Interrogation générale prise en charge",
  "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG_DESC": "Spécifie la propriété prise en charge d'interrogation générale du bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED": "Période d'intégrité surveillée",
  "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED_DESC": "Spécifie la propriété surveillée de la période d'intégrité du bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD": "Période d'intégrité (ms)",
  "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD_DESC": "Spécifie la période de mise à jour de l'intégrité d'un bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE": "Surveiller le changement de qualité",
  "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE_DESC": "Spécifie la propriété de surveillance de la qualité du changement du bloc de contrôle de rapport",
  "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL": "Motif de l'inclusion incluse",
  "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL_DESC": "Spécifie le motif de la propriété Incl include",
  "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM": "Numéro de séquence inclus",
  "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM_DESC": "Spécifie la propriété incluse dans le numéro de séquence",
  "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP": "Horodatage inclus",
  "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP_DESC": "Spécifie la propriété incluse de l'horodatage",
  "TR_I61850SCLCLIENT_IEDNAME": "Modèle d'IED client à charger",
  "TR_I61850SCLCLIENT_IEDNAME_DESC": "Modèle facultatif d'IED client à charger dans un fichier SCL",
  "TR_I61850SCLFILE_IEDNAME": "Modèle d'IED serveur à charger",
  "TR_I61850SCLFILE_IEDNAME_DESC": "Modèle facultatif d'IED serveur à charger dans un fichier SCL",
  "TR_I61850SCLFILE_NAME": "Nom du fichier SCL",
  "TR_I61850SCLFILE_NAME_DESC": "Nom de fichier SCL facultatif pour charger le modèle.  S'il se trouve dans le même répertoire que le fichier INI, le chemin n'est pas requis. ",
  "TR_I61850SERVER_AEINVOKE_ID": "ID d'invocation AE",
  "TR_I61850SERVER_AEINVOKE_ID_DESC": "Spécifie l'ID d'appel AE pour le serveur CEI 61850",
  "TR_I61850SERVER_AEQUALIFIER": "Qualificateur AE",
  "TR_I61850SERVER_AEQUALIFIER_DESC": "Spécifie le qualificateur AE pour le serveur CEI 61850",
  "TR_I61850SERVER_APINVOKE_ID": "Qualificateur AP",
  "TR_I61850SERVER_APINVOKE_ID_DESC": "Spécifie le qualificateur AP pour le serveur CEI 61850",
  "TR_I61850SERVER_APP_ID": "ID de l'application",
  "TR_I61850SERVER_APP_ID_DESC": "Spécifie l'ID d'application du serveur CEI 61850 auquel se connecter",
  "TR_I61850SERVER_GOOSE_ADAPTER_NAME": "Nom de l'adaptateur Goose",
  "TR_I61850SERVER_GOOSE_ADAPTER_NAME_DESC": "Nom de l'adaptateur pour oie sélectionné.",
  "TR_I61850SERVER_IPADDRESS": "Adresse IP du serveur",
  "TR_I61850SERVER_IPADDRESS_DESC": "Spécifie l'adresse IP du serveur CEI 61850 auquel se connecter",
  "TR_I61850SERVER_IPPORT": "Port IP du serveur",
  "TR_I61850SERVER_IPPORT_DESC": "Définit le numéro de port TCP / IP à utiliser",
  "TR_I61850SERVER_PRESENTATION_ADDRESS": "Adresse de présentation",
  "TR_I61850SERVER_PRESENTATION_ADDRESS_DESC": "Spécifie l'adresse de présentation du serveur CEI 61850 auquel se connecter",
  "TR_I61850SERVER_SCLFILE_IEDNAME": "Modèle IED",
  "TR_I61850SERVER_SCLFILE_IEDNAME_DESC": "Modèle facultatif d'IED à charger dans un fichier SCL",
  "TR_I61850SERVER_SESSION_ADDRESS": "Adresse de session",
  "TR_I61850SERVER_SESSION_ADDRESS_DESC": "Spécifie l'adresse de session du serveur CEI 61850 auquel se connecter",
  "TR_I61850SERVER_TRANSPORT_ADDRESS": "Adresse de transport",
  "TR_I61850SERVER_TRANSPORT_ADDRESS_DESC": "Spécifie l'adresse de transport du serveur CEI 61850 auquel se connecter",
  "TR_I61850TIME_ZONE_BIAS": "Décalage de fuseau horaire en minutes",
  "TR_I61850TIME_ZONE_BIAS_DESC": "Un décalage en minutes à ajuster pour les serveurs qui n'envoient pas l'heure au format UTC",
  "TR_ICCP_CONFIG": "Configuration ICCP",
  "TR_ID": "ID",
  "TR_IECACTION_MASK0": "Masque d'action IEC",
  "TR_IECACTION_MASK0_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements périodiques en même temps que le IECActionPrd.\n Casque d'action IEC Définitions:\n Voir la section 4.2",
  "TR_IECACTION_NOW": "Masque d'action IEC",
  "TR_IECACTION_NOW_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements périodiques en même temps que le IECActionPrd.\n Casque d'action IEC Définitions:\n Voir la section 4.2",
  "TR_IECACTION_PRD0": "Période d'action CEI (ms)",
  "TR_IECACTION_PRD0_DESC": "Temps entre les actions définies dans le masque IECAction.La période est désactivée si elle est définie sur zéro.Ce paramètre n’est utilisé que pour les sessions maîtres utilisant le profil de protocole CEI 60870-5. ",
  "TR_IECACTION_RETRY_COUNT0": "Nombre de tentatives d'action IEC",
  "TR_IECACTION_RETRY_COUNT0_DESC": "Combien de fois réessayer le masque d'action en cas d'échec.Si le délai de nouvelle tentative est 0, cela n'a aucun effet.Ce paramètre n’est utilisé que pour les sessions maîtres utilisant le profil de protocole CEI 60870-5. ",
  "TR_IECACTION_RETRY_TIME0": "Temps de tentative d'action IEC (ms)",
  "TR_IECACTION_RETRY_TIME0_DESC": "La période entre les tentatives de ce masque d'action.Si le temps est 0, cela n'a aucun effet.Ce paramètre n’est utilisé que pour les sessions maîtres utilisant le profil de protocole CEI 60870-5. ",
  "TR_IED_CLIENT": "IED client",
  "TR_IED_SERVER": "IED serveur",
  "TR_IGNORE_DST": "Ignore DST",
  "TR_IGNORE_DST_DESC": "Si true et UseTimeZoneClock sont true, les modifications de l'heure d'été ne sont pas prises en compte pour l'affichage de l'heure",
  "TR_ILLEGAL_CHARACTER": "Caractère illégal",
  "TR_IN_TRANSIT": "IN_TRANSIT",
  "TR_IN_TRANSIT_DESC": "En transit / bavardage",
  "TR_INFORMATION": "Information",
  "TR_INFORMATION_OBJECT_ADDRESS": "Adresse de l'objet d'information:",
  "TR_INFORMATION_OBJECT_ADDRESS_DESC": "l'adresse de l'objet d'information de la balise",
  "TR_INPUT_REGISTERS": "Registres d'entrée",
  "TR_INSTALL_V2C": "Installer V2C",
  "TR_INTEG_PERIOD": "Période d'intégrité",
  "TR_INTEG_PRD_MON": "Période d'intégrité surveillée",
  "TR_INTEGRITY_PERIOD": "Période d'intégrité (ms)",
  "TR_INTEGRITY_PERIOD_DESC": "Spécifie la période d'intégrité actuelle.",
  "TR_INTEGRITY_PERIOD_MONITORED": "Période d'intégrité surveillée",
  "TR_INTEGRITY_PERIOD_MONITORED_DESC": "Spécifie si la période d'intégrité surveillée est active.",
  "TR_INTERNAL_MDO_ALREADY_DEFINED": "MDO déjà défini, impossible de créer",
  "TR_INTERNAL_SAVE_BLANK_NAME": "Erreur: le nom ne peut pas être vide",
  "TR_INTERNAL_SET_MDO_OPTIONS": "Impossible de définir les options MDO {{arg1}}",
  "TR_INTERNAL_SET_OPTIONS_MDO": "Impossible de définir les options MDO internes {{arg1}}",
  "TR_INTERROGATION_COMMAND_AND_TOTALS_COUNTERS": "Commande d'interrogation et compteurs de totaux",
  "TR_INTERVAL_DESC": "Spécifie l'intervalle actuel.",
  "TR_INVALID": "INVALID",
  "TR_INVALID_DESC": "invalide",
  "TR_INVALID_TIME": "INVALID_TIME",
  "TR_INVALID_TIME_DESC": "Temps écoulé / invalide",
  "TR_INVOICE": "Facture",
  "TR_IP_ADDRESS": "Adresse IP",
  "TR_IS_ACTIVE": "Actif",
  "TR_IS_INCORRECT": "est incorrect",
  "TR_IS_LICENSED": "sous licence",
  "TR_IS_REDUNDANCY_GROUP": "Est un groupe de redondance",
  "TR_IS_REDUNDANCY_GROUP_DESC": "Ce canal est-il un groupe de redondance",
  "TR_IS_REDUNDANT_CHANNEL": "Le canal est-il redondant",
  "TR_IS_REDUNDANT_CHANNEL_DESC": "Ce canal est-il redondant",
  "TR_IS_REQUIRED": "est requis.",
  "TR_IS_REQUIRED_NUMERICAL": "est obligatoire (numérique).",
  "TR_IS_XML_CLIENT": "Est le client XML",
  "TR_IS_XML_CLIENT_DESC": "Si le client OPC est un client XML",
  "TR_ISRV61850SERVER_AEINVOKE_ID": "ID d'invocation AE",
  "TR_ISRV61850SERVER_AEINVOKE_ID_DESC": "Spécifie l'ID d'appel AE pour le serveur CEI 61850",
  "TR_ISRV61850SERVER_AEQUALIFIER": "Qualificateur AE",
  "TR_ISRV61850SERVER_AEQUALIFIER_DESC": "Spécifie le qualificateur AE pour le serveur CEI 61850",
  "TR_ISRV61850SERVER_APINVOKE_ID": "ID d'invocation AP",
  "TR_ISRV61850SERVER_APINVOKE_ID_DESC": "Spécifie l'ID d'appel AP pour le serveur CEI 61850",
  "TR_ISRV61850SERVER_APP_ID": "ID de l'application",
  "TR_ISRV61850SERVER_APP_ID_DESC": "Spécifie l'ID de l'application du serveur CEI 61850",
  "TR_ISRV61850SERVER_AUTH_MECHANISM": "Mécanisme d'autorisation",
  "TR_ISRV61850SERVER_AUTH_MECHANISM_DESC": "Mécanisme d'autorisation du serveur CEI 61850 ('None' or 'Password' or 'Certificate')",
  "TR_ISRV61850SERVER_AUTH_PASSWORD": "Mot de passe",
  "TR_ISRV61850SERVER_AUTH_PASSWORD_DESC": "Mot de passe d'autorisation du serveur CEI 61850",
  "TR_ISRV61850SERVER_ICDFILE": "Nom de fichier ICD",
  "TR_ISRV61850SERVER_ICDFILE_DESC": "Nom de fichier ICD pour le serveur CEI 61850.  Si le même répertoire que le fichier INI, le chemin n’est pas requis. ",
  "TR_ISRV61850SERVER_IPADDRESS": "adresse IP",
  "TR_ISRV61850SERVER_IPADDRESS_DESC": "Spécifie l'adresse IP du serveur CEI 61850",
  "TR_ISRV61850SERVER_IPPORT": "Port IP sur lequel écouter",
  "TR_ISRV61850SERVER_IPPORT_DESC": "Spécifie le port IP à écouter pour ce serveur CEI 61850",
  "TR_ISRV61850SERVER_NAME": "61850 Nom du serveur",
  "TR_ISRV61850SERVER_NAME_DESC": "Nom du serveur CEI 61850",
  "TR_ISRV61850SERVER_PRESENTATION_ADDRESS": "Adresse de présentation",
  "TR_ISRV61850SERVER_PRESENTATION_ADDRESS_DESC": "Spécifie l'adresse de présentation du serveur CEI 61850",
  "TR_ISRV61850SERVER_SESSION_ADDRESS": "Adresse de session",
  "TR_ISRV61850SERVER_SESSION_ADDRESS_DESC": "Spécifie l'adresse de session du serveur CEI 61850",
  "TR_ISRV61850SERVER_TRANSPORT_ADDRESS": "Adresse de transport",
  "TR_ISRV61850SERVER_TRANSPORT_ADDRESS_DESC": "Spécifie l'adresse de transport du serveur CEI 61850",
  "TR_ITEM_ATTRIBUTES": "Attributs d'élément",
  "TR_ITEM_DESCRIPTION": "Description de l'article",
  "TR_ITEM_DESCRIPTION_DESC": "Définir la description de l'article",
  "TR_ITEM_ID": "ID d'élément",
  "TR_ITEM_ID_DESC": "Définir l'ID d'article",
  "TR_ITEM_NAME": "Nom de l'élément",
  "TR_ITEM_NAME_DESC": "Définir le nom de l'élément",
  "TR_ITEM_PARENT_BROWSER": "Navigateur d'articles",
  "TR_ITEM_TYPE": "Type d'élément",
  "TR_ITEM_TYPE_DESC": "Type d'élément",
  "TR_ITEM_VALUE_TYPE": "Type de valeur d'élément",
  "TR_ITEM_VALUE_TYPE_DESC": "Définir le type de valeur de l'élément",
  "TR_ITEMS_PARENT_BROWSER_DESC": "Navigateur d'articles",
  "TR_KEY_ID": "ID de clé",
  "TR_LANGUAGE": "Langue",
  "TR_LICENSE": "Licence",
  "TR_LICENSE_DEMO": "La session {{arg2}} est configurée en tant que protocole {{arg2}} qui n'est actuellement pas sous licence. Cette session a été désactivée ",
  "TR_LICENSE_DEMO_EXPIRES": "La licence de démonstration de SCADA Data Gateway expirera le {{arg1}}",
  "TR_LICENSE_DESC": "Enregistrer les messages de trace pour le code de licence",
  "TR_LICENSE_EXCEPTION": "Erreur du gestionnaire de licence: {{arg1}} Veuillez contacter le support client de Triangle Microworks.",
  "TR_LICENSE_FAILED_SEVERE": "Une erreur inconnue du gestionnaire de licences s'est produite. Veuillez contacter le support client de Triangle Microworks. ",
  "TR_LICENSE_INFORMATION": "Informations de licence",
  "TR_LICENSE_LOST": "La connexion de licence pour l'application a été perdue. Toutes les sessions sont désactivées. ",
  "TR_LICENSE_MANAGER": "Gestionnaire de licences",
  "TR_LICENSE_OTPIONS": "Options de licence",
  "TR_LICENSE_REACQUIRED": "La connexion de licence pour l'application a été rétablie. Toutes les sessions ont été réactivées. ",
  "TR_LICENSE_TYPE": "Type de licence",
  "TR_LINK_CLASS_PENDING_CNT": "Nombre en attente de classe",
  "TR_LINK_CLASS_PENDING_CNT_DESC": "Pour une liaison de communication principale non équilibrée, nombre total de trames de demande consécutives de classe 1 et de classe 2 pouvant être envoyées à un périphérique lorsqu'un message de réponse de couche d'application est en attente de ce périphérique avant de passer au périphérique suivant. un réseau multi drop.  Ce paramètre n'a aucun effet si un seul périphérique est configuré pour un canal de communication.  Si ce paramètre est défini sur zéro, l'unité est toujours interrogée comme décrit pour le paramètre M870CNFG_LINK_CLASS1_POLL_CNT. Ce paramètre s'applique uniquement aux sessions maîtres CEI 60870-5-101 et CEI 60870-5-103. ",
  "TR_LINK_CLASS1PENDING_DLY": "Délai en attente de la classe 1 (ms)",
  "TR_LINK_CLASS1PENDING_DLY_DESC": "Pour une liaison de communication principale non équilibrée, délai minimal, en millisecondes, après l'envoi de la demande de données de classe 1 lorsqu'une réponse de la couche application est en attente pour cette session.  Ce paramètre peut être utilisé pour limiter la bande passante sur un support partagé tel que Ethernet ou pour éviter de surcharger le périphérique cible avec un temps de communication inutile.Ce paramètre s’applique uniquement aux sessions maîtres CEI 60870-5-101 et CEI 60870-5-103. ",
  "TR_LINK_CLASS1POLL_CNT": "Nombre d'interrogations de classe 1",
  "TR_LINK_CLASS1POLL_CNT_DESC": "Pour une liaison de communication principale non équilibrée, nombre total de trames de requête de classe 1 consécutives pouvant être envoyées à un périphérique avant de passer au périphérique suivant sur un réseau multipoint (la classe 2 est toujours limitée à une trame de requête sauf si une réponse de la couche application est en attente).  Ce paramètre n'a aucun effet si un seul périphérique est configuré pour un canal de communication.  Dans une topologie de réseau multipoint, ce paramètre est utilisé pour équilibrer l'interrogation entre tous les périphériques et empêcher un périphérique de capturer tous les messages d'interrogation. Ce paramètre s’applique uniquement aux sessions maîtres CEI 60870-5-101 et CEI 60870-5-103. ",
  "TR_LINK_CLASS1POLL_DLY": "Délai d'interrogation de classe 1 (ms)",
  "TR_LINK_CLASS1POLL_DLY_DESC": "Pour une liaison de communication maître déséquilibrée, délai minimal, en millisecondes, après l'envoi de la demande de données de classe 1 lorsqu'une réponse de couche application n'est pas en attente pour cette session.  Ce paramètre peut être utilisé pour limiter la bande passante sur un support partagé tel qu'Ethernet ou pour éviter de taxer le périphérique cible avec une surcharge de communication inutile.Ce paramètre s’applique uniquement aux sessions maîtres CEI 60870-5-101 et CEI 60870-5-103. ",
  "TR_LINK_CLASS2PENDING_DLY": "Délai en attente de la classe 2 (ms)",
  "TR_LINK_CLASS2PENDING_DLY_DESC": "Pour une liaison de communication maître non équilibrée, délai minimal, en millisecondes, après l'envoi de la demande de données de classe 2 lorsqu'une réponse de la couche application est en attente pour cette session.  Ce paramètre peut être utilisé pour limiter la bande passante sur un support partagé tel qu'Ethernet ou pour éviter de taxer le périphérique cible avec une surcharge de communication inutile.Ce paramètre s’applique uniquement aux sessions maîtres CEI 60870-5-101 et CEI 60870-5-103. ",
  "TR_LINK_CLASS2POLL_DLY": "Délai d'interrogation de classe 2 (ms)",
  "TR_LINK_CLASS2POLL_DLY_DESC": "Pour une liaison de communication maître déséquilibrée, délai minimal, en millisecondes, après l'envoi de la demande de données de classe 2 lorsqu'une réponse de couche application n'est pas en attente pour cette session.  Ce paramètre peut être utilisé pour limiter la bande passante sur un support partagé tel qu'Ethernet ou pour éviter de taxer le périphérique cible avec une surcharge de communication inutile.Ce paramètre s’applique uniquement aux sessions maîtres CEI 60870-5-101 et CEI 60870-5-103. ",
  "TR_LINK_CNFM_TIMEOUT": "T1 - Délai de confirmation de la couche de liaison (ms)",
  "TR_LINK_CNFM_TIMEOUT_DESC": "Délai maximal d'attente pour la confirmation de la trame. Pour une session CEI 60870-5-104, il s'agit du paramètre T1.  Ce paramètre ne s'applique pas aux connexions de couche liaison (sessions) lorsque GATEWAY agit en tant qu'esclave non équilibré. ",
  "TR_LINK_CONFIRM_MODE": "Mode de confirmation de liaison",
  "TR_LINK_CONFIRM_MODE_DESC": "Demander au périphérique distant d'envoyer une confirmation de couche de liaison de données de la dernière trame envoyée.  Notez que ce paramètre ne dépend pas du fait que le périphérique distant demande à ce périphérique d'envoyer une confirmation de liaison de données aux trames qu'il reçoit. Ce paramètre n’est utilisé que pour les sessions maître ou esclave utilisant le protocole DNP3. ",
  "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES": "K - Nombre maximal de trames de transmission non acquittées",
  "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES_DESC": "Nombre maximal de trames de transmission non acquittées.",
  "TR_LINK_LAYER": "lien couche",
  "TR_LINK_MAX_RETRIES": "Nombre maximal de tentatives de retransmission de données",
  "TR_LINK_MAX_RETRIES_DESC": "Nombre maximal de tentatives de retransmission de trames de couche liaison de données non confirmées.  Ce paramètre ne s'applique pas aux connexions de couche liaison (sessions) lorsque GATEWAY agit en tant qu'esclave non équilibré. ",
  "TR_LINK_MODE": "Mode de transmission de liaison de données",
  "TR_LINK_MODE_DESC": "Mode de transmission par liaison de données. Requis pour chaque canal de communication. ",
  "TR_LINK_SEND_ACK_DELAY": "T2 - Délai maximal d'attente pour l'envoi d'une trame d'acquittement (ms)",
  "TR_LINK_SEND_ACK_DELAY_DESC": "Délai d'attente maximal pour envoyer une trame d'accusé de réception.  Pour une session CEI 60870-5-104, il s'agit du paramètre T2 de la clause CEI 60870-5-104. (Durée maximale après la réception de la dernière APDU I-FORMAT avant de transmettre une APDU S-FORMAT.) Ce paramètre ne s'applique pas aux connexions de couche liaison (sessions) lorsque GATEWAY agit en tant qu'esclave non équilibré. ",
  "TR_LINK_SIZE_ADDRESS": "Taille de l'adresse du lien (octets)",
  "TR_LINK_SIZE_ADDRESS_0_NOT_ALLOW_IN_UNBALANCED_LINK": "L'adresse de la taille du lien 0 n'est pas autorisée dans un lien déséquilibré",
  "TR_LINK_SIZE_ADDRESS_DESC": "Nombre d'octets (octets) dans le champ Adresse du lien. Notez qu'une valeur de 0 n'est valide que pour les sessions dont le mode de liaison est équilibré. Ce paramètre n’est utilisé que pour les sessions maître et esclave CEI60870-5-101 ",
  "TR_LINK_TEST_FRAME_INTERVAL": "T3 - Intervalle de trame de test (ms)",
  "TR_LINK_TEST_FRAME_INTERVAL_DESC": "Heure de l'intervalle de la trame de test. Pour une session CEI 60870-5-104, il s'agit du paramètre T3. Il est recommandé que T3 soit supérieur à T1. Ce paramètre ne s'applique pas aux connexions de couche liaison (sessions) lorsque GATEWAY agit en tant qu'esclave non équilibré. ",
  "TR_LINK_WRECIEVED_UN_ACK_FRAMES": "W - Nombre maximal de trames de réception non acquittées",
  "TR_LINK_WRECIEVED_UN_ACK_FRAMES_DESC": "Nombre maximal de trames de réception non acquittées.",
  "TR_LN_DESTINATION": "Noeud logique pour créer le jeu de données sur",
  "TR_LN_DESTINATION_DESC": "Choisissez le nœud logique sur lequel créer le jeu de données.",
  "TR_LOAD_CONFIG_FROM_SERVER": "Charger configuration depuis serveur IED / SCL",
  "TR_LOAD_CONFIG_FROM_SERVER_DESC": "Charger la configuration à partir du serveur IED / SCL.",
  "TR_LOAD_MODEL": "Charger le modèle",
  "TR_LOAD_MODEL_DESC": "Charger le modèle à partir du fichier sélectionné.",
  "TR_LOCAL_UDP_PORT": "Port UDP local",
  "TR_LOCK_SCROLL": "Défiler le verrouillage",
  "TR_LOG": "Journaux",
  "TR_LOG_MASKS_EVENT_LOG": "Journal des événements de masques de journal",
  "TR_LOG_PARAMETERS": "Paramètres du journal",
  "TR_LOGIN": "Login",
  "TR_LOGIN_BUTTON": "Connexion",
  "TR_LOGOFF": "Déconnexion",
  "TR_LOGS": "Journaux",
  "TR_LOW_ORDER_INDEX": "Index du registre de commande faible",
  "TR_LOW_ORDER_INDEX_DESC": "Spécifiez l'index du registre de poids faible",
  "TR_M103APPL_BLOCKING_ACTION_MASK": "Masque d'action bloquant",
  "TR_M103APPL_BLOCKING_ACTION_MASK_DESC": "Chaque bit active (1) ou désactive (0) l'envoi d'une requête automatique à la suite de la sortie du mode blocage d'un périphérique esclave. Ce paramètre est uniquement utilisé pour les sessions maîtres IEC60870-5-103. ",
  "TR_M103APPL_EOI_ACTION_MASK": "Masque d'action EOI",
  "TR_M103APPL_EOI_ACTION_MASK_DESC": "Chaque bit active (1) ou désactive (0) l'envoi automatique d'une requête à la suite de la réception d'un message d'initialisation d'un équipement esclave. Ce paramètre est uniquement utilisé pour les sessions maîtres IEC60870-5-103. ",
  "TR_M103APPL_ONLINE_ACTION_MASK": "Masque d'action en ligne",
  "TR_M103APPL_ONLINE_ACTION_MASK_DESC": "Chaque bit active (1) ou désactive (0) l'envoi automatique d'une requête à la suite de la mise en ligne d'un périphérique esclave. Ce paramètre est uniquement utilisé pour les sessions maîtres IEC60870-5-103. ",
  "TR_M14APPL_EOI_ACTION_MASK": "Masque d'action EOI",
  "TR_M14APPL_EOI_ACTION_MASK_DESC": "Chaque bit active (1) ou désactive (0) l'envoi automatique d'une requête à la suite de la réception d'un message d'initialisation d'un équipement esclave. Ce paramètre n’est utilisé que pour les sessions maîtres IEC60870-5 101,104. ",
  "TR_M14APPL_ONLINE_ACTION_MASK": "Masque d'action en ligne",
  "TR_M14APPL_ONLINE_ACTION_MASK_DESC": "Chaque bit active (1) ou désactive (0) l'envoi automatique d'une requête à la suite de la mise en ligne d'un périphérique esclave. Ce paramètre n’est utilisé que pour les sessions maîtres IEC60870-5 101,104. ",
  "TR_MAIN_PARAMETERS": "Paramètres principaux",
  "TR_MAINTENANCE_PLAN_EXPIRES": "Le plan de maintenance expire",
  "TR_MANAGE_CONFIGURATION FILE": "Gérer les fichiers de configuration",
  "TR_MAP_FAILED": "Des problèmes sont survenus lors du chargement du fichier de carte de points (.csv).  Veuillez corriger le (s) problème (s) manuellement en ouvrant le fichier CSV avec un éditeur de texte et en corrigeant les problèmes. Les informations d'erreur se trouvent sur l'écran de l'analyseur de protocole. ",
  "TR_MAPPING": "Mapping",
  "TR_MASTER_DATA_OBJECT": "Objet de données maître",
  "TR_MAX_NUM_THREADS": "Nombre maximum de personnes",
  "TR_MAX_NUM_UNACK_FRAMES": "Nombre maximal de cadres non acquittés",
  "TR_MAX_SOEQSIZE": "Nombre maximal de files d'attente SOE",
  "TR_MAX_SOEQSIZE_DESC": "Le nombre maximal d'éléments autorisés dans la file d'attente SOE (et le fichier). Si cette limite est dépassée, les éléments les plus anciens sont supprimés. Toute valeur spécifiée inférieure à 1000 sera par défaut à 1000 ",
  "TR_MBACTION_PRD0": "Période d'action de session Modbus (ms)",
  "TR_MBACTION_PRD0_DESC": "Temps entre les actions définies dans le masque MBAction. La période est désactivée si elle est définie sur zéro.Ce paramètre n’est utilisé que pour les sessions maîtres utilisant le protocole Modbus. ",
  "TR_MBCHANNEL_ACTION_MASK0": "Masque d'action du canal Modbus",
  "TR_MBCHANNEL_ACTION_MASK0_DESC": "Utilisez ce masque pour forcer un événement temporel ou des événements périodiques en conjonction avec le MBChannelActionPrd.\n Masque d'action masquée Définitions:\n Ce paramètre n'est utilisé que pour les sessions maîtres utilisant le protocole Modbus.",
  "TR_MBCHANNEL_ACTION_NOW": "Masque d'action de canal Modbus maintenant",
  "TR_MBCHANNEL_ACTION_NOW_DESC": "Utilisez ce masque pour forcer un ou plusieurs événements chronologiques en conjonction avec le MBChannelActionPrd.\n Masque d'action Masque Définitions:\n Voir la section 4.2 'Noms d'étiquette prédéfinis pour la surveillance et le contrôle' dans le manuel. Ce paramètre n’est utilisé que pour les sessions maîtres utilisant le protocole Modbus.",
  "TR_MBCHANNEL_ACTION_PRD0": "Période d'action du canal Modbus (ms)",
  "TR_MBCHANNEL_ACTION_PRD0_DESC": "Temps entre les actions définies dans le masque MBChannelAction. La période est désactivée si elle est définie sur zéro.Ce paramètre n’est utilisé que pour les sessions maîtres utilisant le protocole Modbus. ",
  "TR_MDO": "MDO:",
  "TR_MDO_ALREADY_EXITS": "MDO déjà défini, impossible de créer",
  "TR_MDO_CREATE_ALREADY": "MDO déjà défini, impossible de créer",
  "TR_MDO_DESC": "MDO",
  "TR_MDO_EDITOR_CREATE_INVALID_TAG": "Impossible de créer MDO: {{arg1}}. Peut-être un nom de balise non valide ou un type non valide. ",
  "TR_MDO_EDITOR_CREATE_MDO_FOUND": "MDO introuvable, impossible de créer",
  "TR_MDO_EDITOR_CREATE_MDO_USER_TAG": "Impossible de définir le nom de balise utilisateur MDO {{arg1}} (éventuellement un doublon, voir les journaux)",
  "TR_MDO_EDITOR_CREATE_SDO": "Impossible de créer le SDO.",
  "TR_MDO_EDITOR_CREATE_TAG": "Impossible de définir le nom de la balise utilisateur sur '{{arg1}}' (éventuellement un doublon)",
  "TR_MDO_EDITOR_CREATE_TAG_BAD": "Impossible de créer le {{arg1}} MDO. Peut-être un nom de balise non valide ou un type non valide.",
  "TR_MDO_EDITOR_MDO_SET_OPTIONS": "Impossible de définir les options MDO {{arg1}}",
  "TR_MDO_EDITOR_OPTIONS": "Impossible de définir les options SDO {{arg1}}",
  "TR_MDO_EDITOR_OPTIONS_SDO": "Impossible de définir les options SDO {{arg1}}",
  "TR_MDO_EDITOR_SDO_BIND": "Impossible de lier SDO.",
  "TR_MDO_EDITOR_SDO_DEFINED": "SDO déjà défini, impossible de créer",
  "TR_MDO_EDITOR_SET_OPTIONS": "Impossible de définir les options MDO '{{arg1}}'",
  "TR_MDO_LIST": "MDO",
  "TR_MDO_LIST_DESC": "Liste des MDO",
  "TR_MDO_OPTIONS": "Impossible de définir les options MDO {{arg1}}",
  "TR_MEMORY_ABBREVIATION": "Mem.",
  "TR_MENU": "Menu",
  "TR_MENU_CMD_ADD_61400_ALARM_MDO": "Ajouter 61400 MDO d'alarme",
  "TR_MENU_CMD_ADD_61400_ALARMS_NODE": "Ajouter un noeud d'alarme CEI 61400",
  "TR_MENU_CMD_ADD_61850_CLIENT": "Ajouter un client CEI 61850",
  "TR_MENU_CMD_ADD_61850_COMMAND_POINT": "Ajouter un point de contrôle CEI 61850",
  "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING": "Ajouter le contrôle 61850 au mappage OPC",
  "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM": "Ajouter le contrôle 61850 à l'élément de mappage OPC",
  "TR_MENU_CMD_ADD_61850_GOOSE": "Ajouter un bloc de contrôle d'oie IEC 61850",
  "TR_MENU_CMD_ADD_61850_ITEM": "Ajouter un élément 61850",
  "TR_MENU_CMD_ADD_61850_POLLED_DATA_SET": "Ajouter un jeu de données interrogées CEI 61850",
  "TR_MENU_CMD_ADD_61850_POLLED_POINT_SET": "Ajouter un ensemble de points d'interrogation CEI 61850",
  "TR_MENU_CMD_ADD_61850_REPORT": "Ajouter un bloc de contrôle de rapport CEI 61850",
  "TR_MENU_CMD_ADD_61850_SERVER": "Ajouter un serveur CEI 61850",
  "TR_MENU_CMD_ADD_61850_WRITABLE_POINT": "Ajouter un point inscriptible CEI 61850",
  "TR_MENU_CMD_ADD_DATA_TYPE": "Ajouter un type de données",
  "TR_MENU_CMD_ADD_DATASET_ELEMENT": "Ajouter un élément de jeu de données",
  "TR_MENU_CMD_ADD_DNP_DESCP": "Ajouter un descripteur DNP",
  "TR_MENU_CMD_ADD_DNP_PROTO": "Ajouter DNP Proto",
  "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL": "Ajouter un canal UDP / TCP DNP3",
  "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER": "Ajouter un maître de canal DNP3 UDP / TCP",
  "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE": "Ajouter un esclave de sortie de canal DNP3 UDP / TCP",
  "TR_MENU_CMD_ADD_DNP3_XML_DEVICE": "Ajouter un périphérique XML DNP3",
  "TR_MENU_CMD_ADD_EQ_MDO": "Ajouter l'équation MDO",
  "TR_MENU_CMD_ADD_GOOSE_MONITOR": "Ajouter un moniteur d'oie",
  "TR_MENU_CMD_ADD_INTERNAL_MDO": "Ajouter un MDO interne",
  "TR_MENU_CMD_ADD_MAPPING_SDO": "Ajouter un SDO",
  "TR_MENU_CMD_ADD_MAPPING_SDOS": "Ajouter SDOS",
  "TR_MENU_CMD_ADD_MBP_CHANNEL": "Ajouter un canal ModbusPlus",
  "TR_MENU_CMD_ADD_MDO": "Ajouter un MDO",
  "TR_MENU_CMD_ADD_MODEM": "Ajouter un modem",
  "TR_MENU_CMD_ADD_MODEM_POOL": "Ajouter un pool de modems",
  "TR_MENU_CMD_ADD_MODEM_POOL_CHANNEL": "Ajouter un canal de pool de modems",
  "TR_MENU_CMD_ADD_MULTI_POINT": "Ajouter plusieurs points",
  "TR_MENU_CMD_ADD_ODBC_CLIENT": "Ajouter un client ODBC",
  "TR_MENU_CMD_ADD_ODBC_ITEM": "Ajouter un élément ODBC",
  "TR_MENU_CMD_ADD_OPC_AE_ATTR": "Ajouter un attribut OPC AE",
  "TR_MENU_CMD_ADD_OPC_AE_CLIENT": "Ajouter un client OPC AE",
  "TR_MENU_CMD_ADD_OPC_AE_ITEM": "Ajouter un élément OPC AE",
  "TR_MENU_CMD_ADD_OPC_CLIENT": "Ajouter un client OPC",
  "TR_MENU_CMD_ADD_OPC_ITEM": "ADD Item OPC",
  "TR_MENU_CMD_ADD_OPC_UA_CLIENT": "Ajouter un client OPC UA",
  "TR_MENU_CMD_ADD_REDUNDANT_CHANNEL": "Ajouter un canal redondant",
  "TR_MENU_CMD_ADD_SECTOR": "Ajouter un secteur",
  "TR_MENU_CMD_ADD_SERIAL_CHANNEL": "Ajouter un canal série",
  "TR_MENU_CMD_ADD_SERIAL_CHANNEL_MASTER": "Ajouter un maître de canal série",
  "TR_MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE": "Ajouter un esclave de sortie de canal série",
  "TR_MENU_CMD_ADD_SESSION": "Ajouter une session",
  "TR_MENU_CMD_ADD_TASE2": "Ajouter un client / serveur ICCP",
  "TR_MENU_CMD_ADD_TASE2_COMMAND_POINT": "ADD ICCP Command Point",
  "TR_MENU_CMD_ADD_TASE2_DSTS": "ADD ICCP DSTS",
  "TR_MENU_CMD_ADD_TASE2_ITEM": "ADD ICCP Item",
  "TR_MENU_CMD_ADD_TASE2_LOGICAL_DEVICE": "Ajouter un périphérique logique ICCP",
  "TR_MENU_CMD_ADD_TASE2_POLLED_DATA_SET": "ADD ICCP polled datasetet",
  "TR_MENU_CMD_ADD_TASE2_POLLED_POINT_SET": "AJOUTER l'ensemble de points d'interrogation ICCP",
  "TR_MENU_CMD_ADD_TCP_CHANNEL": "Ajouter un canal TCP",
  "TR_MENU_CMD_ADD_TCP_CHANNEL_MASTER": "Ajouter le maître de canal TCP",
  "TR_MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE": "Ajouter un esclave de sortie de canal TCP",
  "TR_MENU_CMD_ADD_WRITE_ACTION": "Ajouter une action d'écriture",
  "TR_MENU_CMD_AUTO_CREATE_TAGS": "Créer automatiquement des balises",
  "TR_MENU_CMD_CHANGE_VALUE": "Modifier la valeur",
  "TR_MENU_CMD_CONNECT_OPC_AE_SERVER": "Connecter le serveur OPC AE",
  "TR_MENU_CMD_CONNECT_OPC_SERVER": "Connecter le serveur OPC",
  "TR_MENU_CMD_CONNECT_OPC_UA_SERVER": "Connecter un serveur UA OPC",
  "TR_MENU_CMD_CONNECT_TO_61850_SERVER": "Se connecter à un serveur 61850",
  "TR_MENU_CMD_CREATE_DTM_CSV_POINT_FILE": "Créer un fichier de points DTM CSV",
  "TR_MENU_CMD_CREATE_THXML_POINT_FILE": "Créer un fichier de points THXML",
  "TR_MENU_CMD_DELETE": "Supprimer",
  "TR_MENU_CMD_DELETE_REDUNDANT_CHANNEL": "Supprimer le canal redondant",
  "TR_MENU_CMD_DISCONNECT_OPC_AE_SERVER": "Déconnecter le serveur OPC AE",
  "TR_MENU_CMD_DISCONNECT_OPC_SERVER": "Déconnecter le serveur OPC",
  "TR_MENU_CMD_DISCONNECT_OPC_UA_SERVER": "Déconnecter le serveur OPC UA",
  "\",": null,
  "TR_MENU_CMD_EDIT": "Modifier",
  "TR_MENU_CMD_NONE": "N / A",
  "TR_MENU_CMD_PERFORM_WRITE_ACTION": "Effectuer une action d'écriture",
  "TR_MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT": "Réinitialiser le nombre de tentatives de connexion,",
  "TR_MENU_CMD_RESTART_61850_SERVER": "Redémarrez le serveur 61850",
  "TR_MENU_CMD_SAVE_MODEL_TO_FILE": "Enregistrer le modèle dans le fichier ICD",
  "TR_MENU_CMD_SUBSCRIBE_GOOSE_STREAM": "S'abonner au flux GOOSE",
  "TR_MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM": "Se désabonner du flux GOOSE",
  "TR_MESSAGE": "Message",
  "TR_MMS": "MMS",
  "TR_MMS_COMMON_NAME": "Nom commun",
  "TR_MMS_PRIVATE_KEY_FILE": "Fichier de clé privée",
  "TR_MMS_PRIVATE_KEY_PASS": "PassPhrase de clé privée",
  "TR_MMS_PUB_CERT_FILE": "Fichier de certificat public",
  "TR_MODBUS_ACTION_COILS": "Lire les bobines",
  "TR_MODBUS_ACTION_COILS_DESC": "Lire les bobines",
  "TR_MODBUS_ACTION_DISCRETE_INPUTS": "Entrées discrètes",
  "TR_MODBUS_ACTION_DISCRETE_INPUTS_DESC": "Entrées discrètes",
  "TR_MODBUS_ACTION_EXCEPTION_STATUS": "Exception_Status",
  "TR_MODBUS_ACTION_EXCEPTION_STATUS_DESC": "Exception_Status",
  "TR_MODBUS_ACTION_HOLDING_REGISTERS": "Registres de stockage",
  "TR_MODBUS_ACTION_HOLDING_REGISTERS_DESC": "Registres de conservation",
  "TR_MODBUS_ACTION_INPUT_REGISTERS": "Registres d'entrée",
  "TR_MODBUS_ACTION_INPUT_REGISTERS_DESC": "Registres d'entrée",
  "TR_MODBUS_ACTION_PRD": "Periode (ms)",
  "TR_MODBUS_ACTION_PRD_DESC": "Période (ms)",
  "TR_MODBUS_HIGH_INDEX": "Erreur: l'index d'ordre élevé spécifié n'existe pas. Le Holding Holding MDO doit exister ",
  "TR_MODBUS_LOW_INDEX": "Erreur: l'index de poids faible spécifié n'existe pas. Le MDO du registre de stockage doit exister. ",
  "TR_MODBUS_MAP_EQUATION": "MDO: '{{arg1}}' est mappé sur des points esclaves ou est utilisé dans une équation, il ne peut pas être supprimé. Vous pouvez supprimer la dépendance et réessayer.",
  "TR_MODBUS_REG_EXISTS": "Erreur: Un registre double portant ce nom existe déjà. Veuillez fournir un nom unique. ",
  "TR_MODBUS_SAME_INDEX": "Erreur: les index d'ordre haut et bas ne peuvent pas être identiques",
  "TR_MODEL_FILE_NOT_FOUND": "Fichier de modèle introuvable: '{{arg1}}'",
  "TR_MODEM_BAUD": "Débit en bauds",
  "TR_MODEM_BAUD_DESC": "Définit le débit en bauds du modem correspondant",
  "TR_MODEM_COM_PORT": "Port com du modem",
  "TR_MODEM_COM_PORT_DESC": "Définit un port com du modem",
  "TR_MODEM_DATA_BITS": "Bits de données",
  "TR_MODEM_DATA_BITS_DESC": "Définit le nombre de bits de données pour le modem correspondant",
  "TR_MODEM_DIALING_MODE": "Mode de numérotation",
  "TR_MODEM_DIALING_MODE_DESC": "Définit le mode de numérotation du modem: 'pulse' - utilisez la numérotation par impulsions. 'Tone' - utilisez la numérotation par tonalités.",
  "TR_MODEM_ENABLE": "Activer le modem",
  "TR_MODEM_ENABLE_DESC": "Si la valeur est true, le modem sera activé et disponible.",
  "TR_MODEM_HANGUP_STRING": "Chaîne de suspension",
  "TR_MODEM_HANGUP_STRING_DESC": "Définit la chaîne de raccrochage du modem",
  "TR_MODEM_INIT_STRING": "Chaîne d'initialisation",
  "TR_MODEM_INIT_STRING_DESC": "Définit la chaîne d'initialisation du modem",
  "TR_MODEM_NAME": "Nom d'alias de modem",
  "TR_MODEM_NAME_DESC": "Définit un nom d'alias de modem",
  "TR_MODEM_NO_DIAL_OUT": "Réception d'appels téléphoniques uniquement",
  "TR_MODEM_NO_DIAL_OUT_DESC": "Si la valeur est true, le modem sera configuré pour recevoir uniquement les appels téléphoniques.",
  "TR_MODEM_PARITY": "Parité",
  "TR_MODEM_PARITY_DESC": "Définit la parité pour Modem ModemChannel correspondant",
  "TR_MODEM_POOL_NAME": "Nom du pool de modems",
  "TR_MODEM_POOL_NAME_DESC": "Définit un pool de modems contenant les modems d'un canal",
  "TR_MODEM_READ_CMD_TIMEOUT": "Délai de lecture de la commande (secondes)",
  "TR_MODEM_READ_CMD_TIMEOUT_DESC": "Spécifie le délai d'expiration en secondes pendant lequel le groupe de planification de développement attend qu'un modem réponde à une commande.",
  "TR_MODEM_RESP_TERMINATOR_CHAR": "Caractère de fin de réponse",
  "TR_MODEM_RESP_TERMINATOR_CHAR_DESC": "Définit le caractère utilisé pour mettre fin à une réponse du modem: 'aucun' - n'utilisez pas de caractère pour mettre fin à la réponse (ne suppose aucune réponse de commande). 'Saut de ligne' - utilisez un caractère saut de ligne pour mettre fin à la réponse. 'retour de chariot' - utilisez un caractère 'retour de chariot' pour mettre fin à la réponse.",
  "TR_MODEM_SERIAL_MODE": "Mode série",
  "TR_MODEM_SERIAL_MODE_DESC": "Définit le mode série du modem: matériel utilisez le contrôle de flux matériel (aucun et fenêtre ne fonctionnent pas pour les modems).",
  "TR_MODEM_SETUP": "Configuration du modem",
  "TR_MODEM_STOP_BITS": "Bits d'arrêt",
  "TR_MODEM_STOP_BITS_DESC": "Définit le nombre de bits d'arrêt pour le modem correspondant",
  "TR_MODEM_WRITE_CMD_TIMEOUT": "Délai d'écriture de la commande (secondes)",
  "TR_MODEM_WRITE_CMD_TIMEOUT_DESC": "Spécifie le délai d'expiration en secondes pendant lequel le SDG attend l'envoi d'une commande à un modem.",
  "TR_MONITOR": "Moniteur",
  "TR_MONITOR_ABBREVIATION": "M:",
  "TR_MONITOR_ENABLE_TRACE": "Surveiller l'activation de la trace",
  "TR_MONITOR_HOST_AND_HTTP_PORT": "Surveiller l'hôte et le port HTTP (s)",
  "TR_MONITOR_IS_RE_STARTING": "Le moniteur redémarre.",
  "TR_MONITOR_LOG": "Journal du moniteur",
  "TR_MONITOR_MANAGEMENT": "Gestion du moniteur",
  "TR_MONITOR_VIEW_1": "Vue du moniteur n ° 1",
  "TR_MONITOR_VIEW_2": "Vue du moniteur n ° 2",
  "TR_MONITOR_VIEW_3": "Vue du moniteur n ° 3",
  "TR_NAME": "Nom",
  "TR_NAME_DESC": "Spécifie le nom actuel.",
  "TR_NATIVE_DATA_TYPE": "Type de données natif",
  "TR_NATIVE_DATA_TYPE_DESC": "Définir le type de données natif",
  "TR_NEW_GATEWAY": "Nouvelle configuration de passerelle",
  "TR_NEW_PASSWORD": "Nouveau mot de passe",
  "TR_NEW_VALUE": "Nouvelle valeur",
  "TR_NO": "Non",
  "TR_NO_EDITOR": "Aucun éditeur disponible pour modifier la valeur",
  "TR_NO_FILE_TO_DOWNLOAD": "Aucun fichier à télécharger",
  "TR_NO_GOOSE": "Plus de GOOSE IEC 61850 disponibles",
  "TR_NO_LICENSE_INFORMATION_SAVED": "Aucune information de licence enregistrée",
  "TR_NO_POLLED": "Plus de PolledDataSets IEC 61850 disponibles",
  "TR_NO_RESULTS": "Aucun résultat",
  "TR_NO_SECTORS": "Plus de secteurs disponibles",
  "TR_NO_SESSIONS": "Plus de sessions disponibles",
  "TR_NODE_LIST": "Liste de nœuds",
  "TR_NODE_LIST_DESC": "Choisissez les nœuds à ajouter au nouveau jeu de données.",
  "TR_NODE_LIST_SDO_DESC": "Choisissez les nœuds à ajouter au nouveau SDO.",
  "TR_NODE_NAME": "Nom du nœud",
  "TR_NODE_NAME_DESC": "Définir le nom du nœud",
  "TR_NODE_SDO_LIST": "Liste de nœuds",
  "TR_NONE": "NONE",
  "TR_NONE_DESC": "Supprimer la sélection",
  "TR_NOT_TOPICAL": "NOT_TOPICAL",
  "TR_NOT_TOPICAL_DESC": "Pas d'actualité (hors ligne / non daté)",
  "TR_NUM_THREADS": "Nombre de personnes",
  "TR_OBJECT_NODENAME_DELETED": "L'objet {{NodeName}} a été supprimé",
  "TR_OBJECT_TAGNAME_ALREADY_EXISTS": "L'objet {{NomName}} existe déjà.",
  "TR_OBJECT_TAGNAME_CAN_T_BE_DELETED": "L'objet {{NomName}} ne peut pas être supprimé.",
  "TR_OBJECT_TAGNAME_CAN_T_BE_EDITED": "L'objet {{Nom du tag}} ne peut pas être modifié.",
  "TR_OBJECT_TAGNAME_CAN_T_BE_OPERATED_ON": "L'objet {{TagName}} ne peut pas être utilisé.",
  "TR_OBJECT_TAGNAME_DELETED": "L'objet {{tagName}} a été supprimé.",
  "TR_ODBC_CLIENT_DUPLICATE": "Impossible d'ajouter le client ODBC: '{{arg1}}'. Nom en double. ",
  "TR_ODBC_DELETE_EQUATION": "MDO: '{{}}' est associé à d'autres points ou est utilisé dans une équation, ne peut pas supprimer",
  "TR_ODBC_EXECUTE_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ExecuteSql :: Exception interceptée: {{arg3}}",
  "TR_ODBC_EXECUTE_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, Severe ExecuteSql :: Exception interceptée",
  "TR_ODBC_FAILED_TO_FIND_QUERY": "Echec de la recherche de la requête",
  "TR_ODBC_LIST_DSNS_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListDataSourceNames :: Exception interceptée: {{arg3}}",
  "TR_ODBC_LIST_DSNS_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, Severe ListDataSourceNames :: Exception interceptée",
  "TR_ODBC_LIST_TABLES_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ListTables :: Exception interceptée: {{arg3}}",
  "TR_ODBC_LIST_TABLES_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, Severe ListTables :: Exception interceptée",
  "TR_ODBC_MAP": "MDO: '{{arg1}}' est mappé sur d'autres points ou est utilisé dans une équation, ne peut pas supprimer",
  "TR_ODBC_MDO_DELETE": "Les MDO ODBC ne peuvent pas être supprimés",
  "TR_ODBC_NO_MORE": "Plus de clients ODBC disponibles",
  "TR_ODBC_NO_QUERIES": "Plus de requêtes disponibles",
  "TR_ODBC_OPENDB_FAILED": "Impossible d'ouvrir la base de données",
  "TR_ODBC_QUERY_CHANGE": "La requête SQL a été modifiée; par conséquent, les balises d'interrogation (MDO) peuvent être recréées et les correspondances peuvent être perdues",
  "TR_ODBC_ROOT_ODBC_SERVER_CONNECT": "Impossible de se connecter au serveur ODBC: OPCServerName [{{arg1}}] = '{{arg2}}' OPCServerProgID [{{arg3}}] = '{{arg4}}' OPCServerNode [{{arg5} }] = '{{arg6}}' ",
  "TR_ODBC_SHOW_TABLES_EXCEPTION_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ShowTable :: Exception interceptée: {{arg3}}",
  "TR_ODBC_SHOW_TABLES_SEVERE_FAILED": "ODBC: DB: {{arg1}}, Q: {{arg2}}, ShowTable :: Severe pris en défaut",
  "TR_ODBCALIAS_NAME": "Nom",
  "TR_ODBCALIAS_NAME_DESC": "Nom d'alias pour cette connexion ODBC",
  "TR_ODBCCONNECTION_STRING": "Chaîne de connexion ODBC",
  "TR_ODBCCONNECTION_STRING_DESC": "Spécifie la chaîne de connexion pour cette connexion ODBC",
  "TR_ODBCQUERY": "requête SQL",
  "TR_ODBCQUERY_ALIAS_NAME": "Nom de la requête",
  "TR_ODBCQUERY_ALIAS_NAME_DESC": "Le nom d'alias de la requête sur cette connexion ODBC.",
  "TR_ODBCQUERY_ALWAYS_REFRESH_RS": "Toujours actualiser le jeu d'enregistrements",
  "TR_ODBCQUERY_ALWAYS_REFRESH_RS_DESC": "La requête lira toujours les données de la base de données lorsque MDO GetNextRecord / MoveToRecord est modifié sur cette connexion ODBC lorsque ODBCQueryAlwaysRefreshRS est défini sur true. Si la valeur est false, les données sont lues lorsque ExecuteQuery mdo est modifié, mais pas lorsque GetNextRecord / MoveToRecord mdo est modifié. Notez que si la table change dans la base de données, une requête ExecuteQuery doit être émise pour actualiser le cache SDG local de la table / du jeu d’enregistrements ",
  "TR_ODBCQUERY_DESC": "La requête SQL à exécuter sur cette connexion ODBC.",
  "TR_OFFLINE_ACTIVATION": "Activation hors ligne",
  "TR_OFFLINE_ACTIVATION_NEW_LICENSE": "Activation hors ligne",
  "TR_OFFLINE_ACTIVATION_STEP1_TEXT": "Étape 1: Si cet ordinateur N'A PAS accès à Internet, cliquez sur ce bouton pour créer un fichier C2V pouvant être utilisé pour activer la clé. Transférez le fichier C2V sur un ordinateur avec un accès Internet et naviguez jusqu’à: ",
  "TR_OFFLINE_ACTIVATION_STEP2_TEXT": "Étape 2: sur le portail client, connectez-vous avec la clé de produit, puis sélectionnez l'activation hors ligne. Téléchargez le fichier C2V sur le portail. Cliquez sur le bouton Générer, puis téléchargez le fichier V2C. Un fichier V2C sera sauvegardé sur l'ordinateur connecté à Internet.  Transférez ce fichier sur cet ordinateur. Cliquez sur le bouton Installer V2C et naviguez jusqu'au fichier V2C. Voir le Guide des licences pour plus de détails. ",
  "TR_OK": "OK",
  "TR_OLD_VALUE": "Ancienne valeur",
  "TR_ONLINE_ACTIVATION": "Activation en ligne (nécessite un accès Internet)",
  "TR_ONLINE_ACTIVATION_TEXT": "Si cet ordinateur a un accès Internet, cliquez sur ce bouton pour activer la clé de produit en ligne.",
  "TR_ONLY_EXT_REF": "Afficher uniquement les références externes",
  "TR_ONLY_EXT_REF_DESC": "Spécifie si seul ExtRef doit être affiché",
  "TR_OPC": "OPC",
  "TR_OPC_61850_SERVER_CONTROL_IS_IN_MIDDLE_OPERATION_AND_CANNOT_BE_DELETED_NOW": "Le contrôle SDO '{{arg1}}' est en cours d'opération et ne peut pas être supprimé pour le moment.",
  "TR_OPC_ADD_CAUSE_POINT": "Ajouter un point de cause",
  "TR_OPC_ADD_CAUSE_POINT_DESC": "Ajouter un point de cause",
  "TR_OPC_AE_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Echec du chargement de l'élément d'espace adresse OPC AE. Erreur inconnue.",
  "TR_OPC_AE_ITEM_LIST_UNAVAILABLE": "Liste d'éléments non disponible",
  "TR_OPC_AE_SERVER_LIST_UNAVAILABLE": "Liste de serveurs indisponible",
  "TR_OPC_AESUBSCRIPTION_MASK": "Masque d'abonnement AE d'accès aux données OPC",
  "TR_OPC_AESUBSCRIPTION_MASK_DESC": "Chaque bit active (1) / désactive (0) la raison pour laquelle un abonnement d'élément via le serveur d'accès de données OPC SDG doit activer les notifications d'alarme et d'événement OPC pour l'élément. Notez que ce masque remplace tous les autres masques s'il est activé. Si 0, aucune notification d'alarme ou d'événement OPC ne sera générée à la suite d'un abonnement à un élément. ",
  "TR_OPC_CANCEL_REQUEST_POINT": "Annuler le point de demande",
  "TR_OPC_CANCEL_REQUEST_POINT_DESC": "Définir le point de demande d'annulation",
  "TR_OPC_CANCEL_RESPONSE_POINT": "Annuler le point de réponse",
  "TR_OPC_CANCEL_RESPONSE_POINT_DESC": "Définir le point d'annulation de réponse",
  "TR_OPC_CANT_ADD_MOD_SERVER_DUPLICATE": "Impossible d'ajouter {{arg1}} MDO sur le serveur OPC: {{arg2}}. (Dupliquer?)",
  "TR_OPC_CANT_ADD_SERVER_DUP": "Impossible d'ajouter {{arg1}} MDO sur le serveur OPC: {{arg2}}. (En double?)",
  "TR_OPC_CLIENT_CONNECT": "Impossible de se connecter au serveur OPC:\n  OPCServerName[{{arg1}}]='{{arg2}}'\n  OPCServerProgID[{{arg3}}]='{{arg4}}'\n OPCServerNode[{{arg5}}]='{{arg6}}'",
  "TR_OPC_CLIENT_IS_EMPTY": "Le client est vide",
  "TR_OPC_CLIENT_IS_NOT_CONNECTED_CANNOT_SELECT_POINTS_UNTIL_CONNECTED.": "Le client OPC {{arg1}} n'est pas connecté. Impossible de sélectionner des points jusqu'à ce qu'il soit connecté.",
  "TR_OPC_CLIENT_NAME": "Nom du client",
  "TR_OPC_COMMAND_TERM_POINT": "Point terme de la commande",
  "TR_OPC_COMMAND_TERM_POINT_DESC": "Définir le point de terme de la commande",
  "TR_OPC_DESC": "Enregistrer les messages de trace pour OPC",
  "TR_OPC_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Echec du chargement de l'élément d'espace adresse OPC. Erreur inconnue.",
  "TR_OPC_EXCEPTION_BROWSE_EVENT": "Une exception grave s'est produite dans BrowseEventSpace",
  "TR_OPC_ITEM_LIST_UNAVAILABLE": "Liste d'éléments non disponible",
  "TR_OPC_MDO_OPTIONS": "Options MDO",
  "TR_OPC_MDO_OPTIONS_DESC": "Définir les options MDO",
  "TR_OPC_MDO_OPTIONS_SET": "Impossible de définir les options MDO {{arg1}}",
  "TR_OPC_NO_MORE_CLIENTS": "Plus de clients opc disponibles",
  "TR_OPC_OPERATE_REQUEST_POINT": "Utiliser le point de demande",
  "TR_OPC_OPERATE_REQUEST_POINT_DESC": "Définir le point de demande d'opération",
  "TR_OPC_OPERATE_RESPONSE_POINT": "Utiliser le point de réponse",
  "TR_OPC_OPERATE_RESPONSE_POINT_DESC": "Définir le point de réponse à l'opération",
  "TR_OPC_SELECT_REQUEST_POINT": "Sélectionnez le point de demande",
  "TR_OPC_SELECT_REQUEST_POINT_DESC": "Définir le point de demande de sélection",
  "TR_OPC_SELECT_RESPONSE_POINT": "Sélectionner le point de réponse",
  "TR_OPC_SELECT_RESPONSE_POINT_DESC": "Définir le point de réponse sélectionné",
  "TR_OPC_SERVER_LIST_UNAVAILABLE": "Liste de serveurs indisponible",
  "TR_OPC_SERVER_PROG_ID_OR_XML_SERVER_PATH": "ID de programme du serveur ou chemin du serveur XML",
  "TR_OPC_SERVER_PROG_ID_OR_XML_SERVER_PATH_DESC": "Définir l'ID de programme du serveur ou le chemin du serveur XML",
  "TR_OPC_SET_MDO_OPTIONS": "Impossible de définir les options MDO {{arg1}}",
  "TR_OPC_STARTUP": "Démarrage OPC",
  "TR_OPC_STARTUP_DESC": "Enregistrer les messages de trace pour le démarrage de l'opc",
  "TR_OPC_STD_EXCEPTION_BROWSE_EVENT": "Une exception s'est produite dans BrowseEventSpace: {{arg1}}",
  "TR_OPC_UA_CLIENT_CERTIFICATE_FILE": "Fichier de certificat",
  "TR_OPC_UA_CLIENT_CERTIFICATE_FILE_DESC": "Spécifie le fichier de certificat Opc UA du client",
  "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES": "Supprimer les modifications les plus anciennes",
  "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES_DESC": "Lorsque plus de modifications de données surviennent que cet élément ne peut en stocker, la valeur la plus ancienne est ignorée.",
  "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH": "Notifications maximales par publication",
  "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH_DESC": "Nombre maximal d'éléments rapportés, si 0, le serveur en signalera autant que possible.",
  "TR_OPC_UA_CLIENT_NAME": "Nom de ce client UA OPC",
  "TR_OPC_UA_CLIENT_NAME_DESC": "Spécifie le nom de ce client UA OPC. Les noms doivent être uniques. Si non spécifié, il utilisera par défaut OpcUaClient-'index '",
  "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE": "Fichier de clé privée",
  "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE_DESC": "Spécifie le fichier de clé privée Opc UA du client",
  "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE": "Phrase de passe de clé privée",
  "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE_DESC": "Spécifie la phrase de passe de la clé privée Opc UA du client",
  "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE": "Taille de la file de publication",
  "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE_DESC": "Nombre de modifications pouvant être enregistrées pour cet élément sur le serveur jusqu'au prochain cycle de publication.",
  "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL": "Intervalle de publication (ms)",
  "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL_DESC": "Heure à laquelle le serveur enverra les modifications.",
  "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT": "Nombre de nouvelles tentatives",
  "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT_DESC": "Spécifie le décompte de nouvelles tentatives de reconnexion pour le client OPC UA (0 = tentative de reconnexion pour toujours). La définition du MDO d'actualisation du client entraîne la réinitialisation du compteur de limites interne sur 0resultant les tentatives de connexion au système OPC. Serveur UA. ",
  "TR_OPC_UA_CLIENT_RECONNECT_TIME": "Délai de reconnexion (ms)",
  "TR_OPC_UA_CLIENT_RECONNECT_TIME_DESC": "Spécifie le délai de reconnexion pour le client OPC UA (0 = pas de reconnexion)",
  "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL": "Intervalle d'échantillonnage (ms)",
  "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL_DESC": "Intervalle pendant lequel l'élément stocke les modifications, la valeur par défaut est de 5 par seconde.",
  "TR_OPC_UA_CLIENT_SECURITY_MODE": "Mode de sécurité",
  "TR_OPC_UA_CLIENT_SECURITY_MODE_DESC": "Sélectionne le mode de sécurité pour une connexion client OPC UA.",
  "TR_OPC_UA_CLIENT_SECURITY_POLICY": "Politique de sécurité",
  "TR_OPC_UA_CLIENT_SECURITY_POLICY_DESC": "Sélectionne la politique de sécurité pour une connexion client OPC UA.",
  "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE": "Type de jeton",
  "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE_DESC": "Sélectionne le type de jeton de sécurité pour une connexion client OPC UA.",
  "TR_OPC_UA_CLIENT_SERVER_URL": "URL du serveur UA OPC auquel se connecter",
  "TR_OPC_UA_CLIENT_SERVER_URL_DESC": "Spécifie l'URL du serveur UA OPC auquel se connecter.",
  "TR_OPC_UA_CLIENT_SESSION_TIMEOUT": "Délai de session (ms)",
  "TR_OPC_UA_CLIENT_SESSION_TIMEOUT_DESC": "Délai jusqu'à l'expiration de la session s'il n'y a pas de communication.",
  "TR_OPC_UA_CONNECTION_SETTINGS": "Paramètres de connexion OPC UA",
  "TR_OPC_UA_SECURITY_SETTINGS": "Paramètres de sécurité OPC UA",
  "TR_OPC_UA_SERVER_LIST_UNAVAILABLE": "Liste de serveurs indisponible",
  "TR_OPCAE_CLIENT_CONNECT": "Impossible de se connecter au serveur OPC:\n  OPCServerName[{{arg1}}]='{{arg2}}'\n  OPCServerProgID[{{arg3}}]='{{arg4}}'\n OPCServerNode[{{arg5}}]='{{arg6}}'",
  "TR_OPCAE_CLIENT_DUPLICATE": "Impossible d'ajouter le client OPC AE: '{{arg1}}'. Nom en double. ",
  "TR_OPCAE_NO_MORE": "Plus de clients opc AE disponibles",
  "TR_OPCAESERVER_BUFFER_TIME": "Durée de la mémoire tampon",
  "TR_OPCAESERVER_BUFFER_TIME_DESC": "La durée du tampon, spécifiée en millisecondes, indique le nombre de fois que les notifications d'événement peuvent être envoyées à l'objet d'abonnement. Ce paramètre est l'intervalle de temps minimum entre deux notifications d'événement successives. La valeur 0 signifie que toutes les notifications d'événement doivent être envoyées immédiatement du serveur. Si le paramètre MaxSize est supérieur à 0, il indique au serveur d’envoyer les notifications d’événement plus rapidement afin de conserver la taille de la mémoire tampon dans MaxSizeSpecifie la durée de la mémoire tampon pour l’abonnement OPC AE Server ",
  "TR_OPCAESERVER_MAX_SIZE": "Taille maximale",
  "TR_OPCAESERVER_MAX_SIZE_DESC": "Ce paramètre correspond au nombre maximal d'événements pouvant être spécifiés dans un appel. La valeur 0 signifie aucune restriction pour le nombre d'événements. Veuillez noter que si la valeur MaxSize est supérieure à 0, les événements peuvent être envoyés plus rapidement du serveur que via le paramètre BufferTime. Spécifie la taille maximale de l'abonnement OPC AE Server ",
  "TR_OPCAESERVER_NAME": "Nom du serveur",
  "TR_OPCAESERVER_NAME_DESC": "Le nom facultatif du serveur OPC AE auquel se connecter, sans utiliser la valeur de OPCAEserverProgID, est spécifié. Il est vivement recommandé de définir ce paramètre car un client OPC AE externe peut ne pas être en mesure de rechercher des balises dans SDG. Serveur OPC AE si le nom du serveur OPC AE contient un ou plusieurs caractères de période ('.'). Pour contourner ce problème, définissez ce nom d'alias (sans période pour le serveur externe OPC AE, et faites référence au serveur par son alias. ",
  "TR_OPCAESERVER_NODE": "Nom du nœud du serveur OPC AE",
  "TR_OPCAESERVER_NODE_DESC": "Spécifie le nom de noeud du serveur OPC AE auquel se connecter",
  "TR_OPCAESERVER_PROG_ID": "Prog ID",
  "TR_OPCAESERVER_PROG_ID_DESC": "Spécifie l'ID PROG du serveur OPC AE auquel se connecter",
  "TR_OPCAESERVER_RECONNECT_RETRY_COUNT": "Nombre de nouvelles tentatives de reconnexion",
  "TR_OPCAESERVER_RECONNECT_RETRY_COUNT_DESC": "Spécifie le nombre de nouvelles tentatives de reconnexion pour le serveur OPC AE externe (0 = tentative de reconnexion pour toujours). La définition du MDO d'actualisation du client entraîne la réinitialisation du compteur de limite interne sur 0 Serveur OPC AE. ",
  "TR_OPCAESERVER_RECONNECT_TIME": "Délai de reconnexion (ms)",
  "TR_OPCAESERVER_RECONNECT_TIME_DESC": "Spécifie le délai de reconnexion pour le serveur OPC AE (0 = pas de reconnexion)",
  "TR_OPCAETIME_SOURCE": "Source de temps d'événement OPC",
  "TR_OPCAETIME_SOURCE_DESC": "Spécifie la source de l'horodatage pour les points de données d'alarme et d'événement OPC. Les valeurs possibles sont Update ou Reported, où Update correspond à l'heure, par rapport à l'horloge système SDG, à laquelle le point de données a été mis à jour pour la dernière fois. Reported spécifie l'heure de l'événement à l'origine du changement des données. L'heure rapportée sera relative à l'horloge système de l'esclave distant, sauf lors de l'initialisation, où l'horloge système du SDG est utilisée jusqu'à la réception du premier événement avec l'heure. Il est important de noter que l'interrogation de données statiques ou les événements reçus qui ne spécifient pas une heure rapportée peuvent entraîner la modification de la valeur d'un point de données spécifique sans qu'aucun événement ne soit généré. Par conséquent, l'heure de l'événement ne changera pas.  Remarque: ce paramètre spécifie l'heure à laquelle le serveur SDG OPC AE sera fourni ",
  "TR_OPCAEUSE_SIMPLE_EVENTS": "Utiliser l'alarme OPC et les événements simples",
  "TR_OPCAEUSE_SIMPLE_EVENTS_DESC": "Si la valeur est true, tous les événements OPC AE sont signalés sous forme d'événements simples.",
  "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE": "Taux de mise à jour de l'état du client OPC (ms)",
  "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE_DESC": "Intervalle auquel un client OPC SDG demande des informations d'état à son serveur.  Si le client ne souhaite pas demander de mises à jour de statut, définissez cette valeur sur 0. ",
  "TR_OPCDA_CLIENT_DUPLICATE": "Impossible d'ajouter le client OPC DA: '{{arg1}}'. Nom en double. ",
  "TR_OPCSERVER_NAME": "Nom",
  "TR_OPCSERVER_NAME_DESC": "Le nom facultatif du serveur OPC auquel se connecter, sans utiliser la valeur de OPCserverProgID, doit être défini. Il est vivement recommandé de définir ce paramètre car un client OPC externe peut ne pas être en mesure de rechercher des balises sur le serveur OPC SDG. si le nom du serveur OPC contient un ou plusieurs caractères de période ('.'). Pour contourner ce problème, définissez ce nom d'alias (sans période pour le serveur OPC externe, et faites référence au serveur par son alias. ",
  "TR_OPCSERVER_NODE": "Nom du nœud",
  "TR_OPCSERVER_NODE_DESC": "Spécifie le nom de noeud du serveur OPC auquel se connecter",
  "TR_OPCSERVER_READ_PROPERTIES_TIME": "Période de lecture des propriétés (ms)",
  "TR_OPCSERVER_READ_PROPERTIES_TIME_DESC": "Spécifie la période de lecture des propriétés (0 = ne pas lire)",
  "TR_OPCSERVER_RECONNECT_DELAY": "Délai de reconnexion (ms)",
  "TR_OPCSERVER_RECONNECT_DELAY_DESC": "S'il est différent de zéro, cela indique le temps (en ms) à attendre pour réinitialiser le nombre de tentatives et continuer à essayer de vous reconnecter. Si la valeur est zéro, il est ignoré et les tentatives ne se poursuivront pas après la valeur du nombre de tentatives. ",
  "TR_OPCSERVER_RECONNECT_RETRY_COUNT": "Nombre de nouvelles tentatives",
  "TR_OPCSERVER_RECONNECT_RETRY_COUNT_DESC": "Spécifie le nombre de tentatives de reconnexion pour le serveur OPC externe (0 = tentative de reconnexion pour toujours). La définition du MDO d'actualisation du client entraîne la réinitialisation du compteur de limite interne sur 0, ce qui entraîne la poursuite des tentatives de connexion à l'OPC. serveur.",
  "TR_OPCSERVER_RECONNECT_TIME": "Temps de nouvelle connexion (ms)",
  "TR_OPCSERVER_RECONNECT_TIME_DESC": "Spécifie le délai de nouvelle tentative de reconnexion du client OPC pour tenter de se reconnecter au serveur (0 = pas de reconnexion)",
  "TR_OPCSERVER_UPDATE_RATE": "Taux de mise à jour (ms)",
  "TR_OPCSERVER_UPDATE_RATE_DESC": "Spécifie le taux de mise à jour du serveur OPC",
  "TR_OPCTIME_SOURCE": "Source temps OPC",
  "TR_OPCTIME_SOURCE_DESC": "Spécifie la source de l'étiquette de temps pour les points de données OPC. Les valeurs possibles sont Update ou Reported, où Update correspond à l'heure, par rapport à l'horloge système SDG, à laquelle le point de données a été mis à jour pour la dernière fois. Reported spécifie l'heure indiquée par le dernier événement ayant entraîné la modification des données. L'heure rapportée sera relative à l'horloge système de l'esclave distant, sauf lors de l'initialisation, où l'horloge système du SDG est utilisée jusqu'à la réception du premier événement avec l'heure. Il est important de noter que l'interrogation de données statiques ou les événements reçus qui ne spécifient pas une heure rapportée peuvent entraîner la modification de la valeur d'un point de données spécifique sans qu'aucun événement ne soit généré. Par conséquent, l'heure de l'événement ne changera pas.  Remarque: ce paramètre spécifie l'heure à laquelle le serveur SDG OPC DA sera fourni ",
  "TR_OPCUA_CLIENT_DUPLICATE": "Impossible d'ajouter le client UA OPC: '{{arg1}}'. Nom en double. ",
  "TR_OPCUA_NO_MORE": "Plus de clients UA OPC UA disponibles",
  "TR_OPCXML_DA_SERVER_NAME": "Nom du serveur OPC Xml Da",
  "TR_OPCXML_DA_SERVER_NAME_DESC": "Spécifie le nom utilisé par un client OPC XML DA pour se connecter au SDG.  Avec le fichier INI par défaut, un client opc xml da local peut se connecter au SDG comme suit: http: // localhost: 8081 / SDG ",
  "TR_OPCXML_DA_SERVER_PORT": "Port du serveur OPC XML DA",
  "TR_OPCXML_DA_SERVER_PORT_DESC": "Le port TCP / IP du serveur OPC XML DA.  Avec le fichier INI par défaut, un client opc xml da local peut se connecter au SDG comme suit: http: // localhost: 8081 / SDG ",
  "TR_OPEN_FILE": "{{arg1}} ({{arg2}}) enregistre trop int (plus de {{arg3}} octets)",
  "TR_OPERATION_DESCRIPTION": "Spécifiez l'opération",
  "TR_OPERATION_HELP": "Aide à l'opération",
  "TR_OPERATION_LIST": "Opérations",
  "TR_OPERATION_LIST_DESC": "Liste des opérations possibles",
  "TR_OPTION": "Option",
  "TR_OPTIONAL_ITEMS_TO_INCUDE_IN_REPORT": "Eléments d'opération à inclure dans le rapport",
  "TR_OPTIONS": "Options",
  "TR_OPTIONS_EDITOR": "Editeur d'options",
  "TR_OVERALL": "Overall",
  "TR_OVERFLOW": "OVERFLOW",
  "TR_OVERFLOW_DESC": "Débordement / roulement",
  "TR_PARAMETERS": "Paramètres",
  "TR_PARSE_OPTION_VALIDATION_FAILED": "Echec de la validation du nom de la catégorie / condition / sous-condition '{{arg1}}'",
  "TR_PARSE_OPTION_VALIDATION_FAILED2": "Echec de la validation du nom de la catégorie / condition / sous-condition '{{arg1}}'",
  "TR_PASSWORD": "Mot de passe",
  "TR_PASSWORD_IS_REQUIRED": "Un mot de passe est requis.",
  "TR_PASSWORD_IS_REQUIRED_CHARACTERS_MINIMUM": "Un mot de passe est requis (6 caractères minimum).",
  "TR_PAUSE": "Pause",
  "TR_PERFORMANCE_DESC": "Performance",
  "TR_PERFORMANCE_VIEW": "Vue de performance",
  "TR_PHYS_COM_BAUD": "Débit en bauds",
  "TR_PHYS_COM_BAUD_DESC": "Définit le débit en bauds du PhysComChannel série correspondant",
  "TR_PHYS_COM_CHANNEL": "Canal physique, c.-à-d. Port COM, nom IP / nœud distant",
  "TR_PHYS_COM_CHANNEL_DESC": "Définit le canal de communication.\nExemples: 'COM1', '************' ou 'ROCKYHILLSUBSTATION' (pour un canal TCP / IP)\n\nOn Port série -\n il s’agit du port com nom à utiliser\nSe client TCP -\n il s’agit du nom d’hôte ou de l’adresse IP pour configurer la connexion TCP avec serveur TCP -\n il s’agit du nom d’hôte ou de l’adresse IP à partir duquel la connexion TCP doit être acceptée  *. *. *. * indiquant accepter une connexion de n’importe quel client\n peut également être une liste de ';' noms d’hôte ou adresses IP séparés\n pour autoriser les connexions depuis.\nOn Périphérique TCP à point final double -\n il s’agit des noms d’hôte ou de l’adresse IP avec lesquels accepter la connexion TCP ou se connecter.\n Peut-être aussi une liste de ' ; ' ou ',' noms d'hôtes ou adresses IP séparés\n pour autoriser les connexions à partir de, essaiera uniquement de se connecter au premier de la liste.\n ",
  "TR_PHYS_COM_CHNL_NAME": "Nom du canal",
  "TR_PHYS_COM_CHNL_NAME_DESC": "Nom d'alias pour le canal de communication. Doit être spécifié et être unique. ",
  "TR_PHYS_COM_DATA_BITS": "Bits de données",
  "TR_PHYS_COM_DATA_BITS_DESC": "Définit le nombre de bits de données pour le PhysComChannel série correspondant",
  "TR_PHYS_COM_DEST_UDPPORT": "Port UDP de destination",
  "TR_PHYS_COM_DEST_UDPPORT_DESC": "Sur maître - si TCP et UDP sont configurés, cela spécifie le port UDP / IP\n  de destination auquel envoyer les demandes de diffusion dans les datagrammes UDP.\n  si UDP ONLY est configuré, cela spécifie le port UDP / IP\n  de destination. pour envoyer toutes les demandes dans les datagrammes UDP à.\n  Ceci doit correspondre au 'localUDPPort', de l'esclave.\n On Esclave - si TCP et UDP, cela n'est pas utilisé.\n  si UDP ONLY est configuré, cela spécifie la destination UDP / Port IP\n  auquel les réponses doivent être envoyées.\n  Peut être WINTCP_UDP_PORT_SRC = 2 et indiquer que le port src est utilisé à partir d'une demande\n  UDP reçue du maître.",
  "TR_PHYS_COM_DIAL_OUT": "Activer la numérotation",
  "TR_PHYS_COM_DIAL_OUT_DESC": "Si la valeur est true, le modem sera configuré pour la composition et la réponse aux appels téléphoniques. Si la valeur est false, le modem ne sera configuré que pour répondre aux appels téléphoniques. ",
  "TR_PHYS_COM_DUAL_END_POINT_IP_PORT": "Numéro de port IP du noeud final double",
  "TR_PHYS_COM_DUAL_END_POINT_IP_PORT_DESC": "Si le système d'extrémité double est pris en charge, une écoute est effectuée sur le port PhysComIpPort\n et une demande de connexion est envoyée à ce numéro de port si nécessaire.\n Cela doit correspondre à ipPort sur un périphérique distant.\n L'état normal est en écoute, la connexion sera faite quand il y a des données à envoyer. ",
  "TR_PHYS_COM_INIT_UNSOL_UDPPORT": "Port UDP non sollicité",
  "TR_PHYS_COM_INIT_UNSOL_UDPPORT_DESC": "Sur maître - Inutilisé.\n On Esclave - si TCP et UDP non utilisés.\n  Si UDP UNIQUEMENT est configuré, spécifie le port UDP / IP de destination auquel envoyer la réponse Null non sollicitée initiale.\n  Après avoir reçu une demande UDP du maître, destUDPPort (lequel\n  peut indiquer que le port src est utilisé) sera utilisé pour toutes les réponses.\n  Ce ne doit pas être WINTCP_UDP_PORT_NONE (0), WINTCP_UDP_PORT_ANY (1) ou\n  WINTCP_UDP_PORT_SRC (2). pour un esclave qui supporte UDP. ",
  "TR_PHYS_COM_IP_CONNECT_TIMEOUT": "Délai de connexion (ms)",
  "TR_PHYS_COM_IP_CONNECT_TIMEOUT_DESC": "Définit le délai d'attente de connexion TCP / IP à utiliser si PhysComChannel spécifie une adresse IP. Pour CEI 60870-5-104, il s'agit du paramètre T0. Notez que ce paramètre doit être défini sur la valeur la plus basse qui fonctionne de manière fiable.  Dans les cas où une connexion ne peut pas être établie, le processus bloquera pour la période spécifiée. ",
  "TR_PHYS_COM_IP_MODE": "Mode IP",
  "TR_PHYS_COM_IP_MODE_DESC": "Définit le mode de connexion à utiliser si PhysComChannel spécifie une adresse IP",
  "TR_PHYS_COM_IP_PORT": "Numéro de port IP",
  "TR_PHYS_COM_IP_PORT_DESC": "Définit le numéro de port TCP / IP à utiliser si PhysComChannel spécifie une adresse IP",
  "TR_PHYS_COM_LOCAL_IP_ADDRESS": "Adresse IP locale",
  "TR_PHYS_COM_LOCAL_IP_ADDRESS_DESC": "Sur le client -\n  Adresse à laquelle lier le socket. Ceci vous permet de spécifier quelle adresse IP\n  envoyer comme adresse source dans les messages TCP s'il y a plusieurs adresses IP,\n  par exemple s'il y a plusieurs cartes d'interface réseau (NIC).\n  Si '0.0.0.0' est utilisé, la pile TCP choisira l’adresse IP à utiliser.\n  Si une adresse non présente est spécifiée, la liaison échouera et\n  la pile TCP choisira quelle adresse.\n  Cette adresse est également utilisée pour le maître DNP lors de l’envoi de datagrammes UDP.\n La liaison de cette adresse ne garantit pas l’envoi sur une carte réseau particulière.\n  Ceci est déterminé par la table de routage IP en fonction de la\n adresse IP de destination. Vous pouvez afficher cette table en entrant route print dans une fenêtre de commande. Il est possible d’ajouter des routes manuelles pour provoquer l’utilisation d’une carte réseau particulière. route, ajouter la passerelle dest IPAddress.\n Entrez route? pour plus de détails.\n  Sur le serveur -\n  n’est pas utilisé actuellement pour les écouteurs.\n  (Remarque: cette adresse est utilisée pour la station de sortie DNP si elle est configurée pour UDP UNIQUEMENT)\n ",
  "TR_PHYS_COM_LOCAL_UDPPORT": "Port UDP local",
  "TR_PHYS_COM_LOCAL_UDPPORT_DESC": "Port local pour l'envoi et la réception de datagrammes UDP sur.\n Si ce paramètre est défini sur WINTCP_UDP_PORT_NONE = 0, le protocole UDP ne sera pas activé.\n Pour le réseau DNP, le protocole UDP ne devrait pas être utilisé.\n Il n'est pas nécessaire pour le réseau en cours.'Protocoles CEI ou modbus.\n Son maître - Si ce paramètre est défini sur WINTCP_UDP_PORT_ANY = 1, un port disponible non spécifié sera utilisé.\n On Esclave: cette option doit être choisie pour correspondre au port UDP que le maître utilise\n  pour envoyer le datagramme. messages à.\n  Ce ne doit pas être WINTCP_UDP_PORT_ANY = 1 ou WINTCP_UDP_PORT_SRC = 2. ",
  "TR_PHYS_COM_MBPCARD_NUMBER": "Numéro de carte Channel MBP",
  "TR_PHYS_COM_MBPCARD_NUMBER_DESC": "Définit le numéro de la carte Modbus Plus à utiliser si PhysComType spécifie un canal Modbus Plus.",
  "TR_PHYS_COM_MBPRECIEVE_TIMEOUT": "Délai d'attente réception Modbus Plus",
  "TR_PHYS_COM_MBPRECIEVE_TIMEOUT_DESC": "Définit le délai de réception Modbus Plus à utiliser si PhysComType spécifie un canal Modbus Plus.",
  "TR_PHYS_COM_MBPROUTE_PATH": "Chemin de la route Modbus Plus",
  "TR_PHYS_COM_MBPROUTE_PATH_DESC": "Spécifie le chemin de la route Modbus Plus à utiliser si PhysComType spécifie un canal Modbus Plus. Ce paramètre s’applique uniquement aux maîtres Modbus plus. ",
  "TR_PHYS_COM_MBPSLAVE_PATH": "Chemin d'esclave Modbus Plus",
  "TR_PHYS_COM_MBPSLAVE_PATH_DESC": "Définit le chemin d'accès de l'esclave Modbus Plus à utiliser si PhysComType spécifie un canal Modbus Plus.  Ce paramètre s’applique uniquement aux esclaves Modbus plus. ",
  "TR_PHYS_COM_MODBUS_RTU": "Activer Modbus RTU",
  "TR_PHYS_COM_MODBUS_RTU_DESC": "Si la valeur est true, le canal sera configuré pour les communications série Modbus RTU.",
  "TR_PHYS_COM_MODEM_ANSWER_TIME": "Temps de réponse (secondes)",
  "TR_PHYS_COM_MODEM_ANSWER_TIME_DESC": "Définit la durée pendant laquelle le modem attend une réponse après la numérotation (en secondes)",
  "TR_PHYS_COM_MODEM_IDLE_TIME": "Temps d'attente pour raccrocher après un temps d'inactivité (secondes)",
  "TR_PHYS_COM_MODEM_IDLE_TIME_DESC": "Définit la durée pendant laquelle le modem attend de raccrocher après que le canal soit inactif (en secondes)",
  "TR_PHYS_COM_MODEM_POOL": "Pool de modems",
  "TR_PHYS_COM_MODEM_POOL_DESC": "Le pool de modems utilisé par ce canal",
  "TR_PHYS_COM_MODEM_REDIAL_LIMIT": "Heures de recomposition",
  "TR_PHYS_COM_MODEM_REDIAL_LIMIT_DESC": "Définit le nombre de fois que le modem composera un numéro avant de conclure que la communication a échoué. Si la composition a échoué (c’est-à-dire que la limite est atteinte), le canal du modem MDO ChannelRedialLimitControl sera VRAI, définissez ce MDO (ChannelRedialLimitControl) sur false pour reprendre la numérotation. Si la valeur est 0, le modem tentera de composer pour toujours. Notez que si vous définissez cette valeur sur 0, le modem sera définitivement bloqué si aucune connexion n’est établie. ",
  "TR_PHYS_COM_PARITY": "Parité",
  "TR_PHYS_COM_PARITY_DESC": "Définit la parité pour le PhysComChannel série correspondant",
  "TR_PHYS_COM_PHONE_NUMBER": "Numéro de téléphone",
  "TR_PHYS_COM_PHONE_NUMBER_DESC": "Numéro de téléphone à composer avec un modem",
  "TR_PHYS_COM_PROTOCOL": "Type de protocole de canal",
  "TR_PHYS_COM_PROTOCOL_DESC": "Définit le protocole du canal S'applique à tous les types de canaux physiques",
  "TR_PHYS_COM_SERIAL_MODE": "Contrôle de flux",
  "TR_PHYS_COM_SERIAL_MODE_DESC": "Définit le mode du canal série:'aucun'- ne pas utiliser de contrôle de flux.' Matériel'- utiliser un contrôle de flux matériel.'Windows'- utiliser un contrôle de flux et les paramètres série (débit en bauds, parité, etc.) comme spécifié avec la commande MODE. ",
  "TR_PHYS_COM_STOP_BITS": "Bits d'arrêt",
  "TR_PHYS_COM_STOP_BITS_DESC": "Définit le nombre de bits d'arrêt pour le PhysComChannel série correspondant",
  "TR_PHYS_COM_VALIDATE_UDPADDRESS": "Valider l'adresse UDP",
  "TR_PHYS_COM_VALIDATE_UDPADDRESS_DESC": "Valider ou non l'adresse source du datagramme UDP reçu.",
  "TR_PHYS_OFFLINE_POLL_PERIOD": "Période d'interrogation hors ligne (ms)",
  "TR_PHYS_OFFLINE_POLL_PERIOD_DESC": "Période à laquelle les sessions du canal sont interrogées si elles sont hors ligne.  Notez que ce paramètre s'applique uniquement aux sessions maîtres DNP et Modbus série sur ce canal. Une valeur de zéro (0) désactivera cette fonctionnalité. ",
  "TR_PLEASE_SELECT_A_FILE": "Veuillez sélectionner un fichier",
  "TR_PLEASE_SELECT_A_PROPERTY": "Veuillez sélectionner une propriété",
  "TR_PLEASE_SELECT_AT_LEAST_ONE_ROLE": "Veuillez sélectionner au moins un rôle.",
  "TR_POINT_IN_USE_CANT_DELETE": "MDO: '{{arg1}}' est mappé sur des points esclaves ou maîtres ou est utilisé dans une équation, ne peut pas supprimer",
  "TR_POINT_MAP": "Carte de points",
  "TR_POINT_MAP_FILE": "Fichier de carte de points",
  "TR_POINT_MAP_FILE_DESC": "Spécifie le fichier de mappage de données de points.  Cette valeur peut avoir le chemin complet du fichier ou juste le nom du fichier.  Si seul le nom du fichier est spécifié, il doit se trouver dans le même répertoire que le fichier INI. ",
  "TR_POINT_TYPE": "Type de point",
  "TR_POINT_TYPE_DESC": "Type de point",
  "TR_POLLED_DATA_SET_NAME": "Nom du jeu de données interrogé",
  "TR_POLLED_DS_DS_ID": "Nom du fichier",
  "TR_POLLED_DS_DS_ID_DESC": "Spécifiez le nom du fichier",
  "TR_POLLED_DS_ID": "Nom du fichier interrogé",
  "TR_POLLED_DS_ID_DESC": "Spécifiez le nom du fichier interrogé",
  "TR_POLLED_DS_PERIOD": "Période interrogée",
  "TR_POLLED_DS_PERIOD_DESC": "Spécifie la période interrogée.",
  "TR_POLLED_DS_SET_PERIOD": "Nombre de jeux de transferts DS",
  "TR_POLLED_DS_SET_PERIOD_DESC": "Spécifiez le nombre d'ensembles de transfert DS",
  "TR_POLLED_POINT_SET_DESC": "Spécifiez la période de sondage",
  "TR_POLLED_POINT_SET_ID": "Nom du groupe de points interrogés",
  "TR_POLLED_POINT_SET_ID_DESC": "Spécifiez le nom d'ensemble du point interrogé",
  "TR_POLLED_POINT_SET_PERIOD": "Période de sondage",
  "TR_POLLED_POINT_SET_PERIOD_DESC": "Spécifiez la période de jeu de points",
  "TR_PRODUCT_KEY_EXHAUSTED": "Clé de produit épuisée",
  "TR_PRODUCT_KEY_FOR_NEW_LICENSE": "Cette clé de produit concerne une nouvelle licence",
  "TR_PRODUCT_KEY_FOR_UPDATE_LICENSE": "Cette clé de produit mettra à jour la licence actuelle (mise à jour du plan de maintenance)",
  "TR_PROG_ID": "Prog ID",
  "TR_PROPERTIES": "Propriétés",
  "TR_PROPERTY_DESC": "Définir la propriété",
  "TR_PROTO_ANALYZER": "Analyseur de protocole",
  "TR_PROTO_ANALYZER_DESC": "Enregistrer les messages de trace pour l'analyseur de protocole",
  "TR_PROTOCOL_ANALYSER": "Analyseur de protocole",
  "TR_PROTOCOL_ANALYSER_PARAMETERS": "Paramètres de l'analyseur de protocole",
  "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT": "Purger avant d'activer à la reconnexion",
  "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Spécifie si la purge avant l'activation à la reconnexion est active.",
  "TR_QUAL_CHNG_MON": "Changement de qualité",
  "TR_QUALITY": "Qualité",
  "TR_QUALITY_CHANGE": "Changement de qualité",
  "TR_QUALITY_CHANGE_DESC": "Spécifie si le changement de qualité est actif.",
  "TR_QUALITY_ENUM_BLOCKED_DESC": "Bloquer",
  "TR_QUALITY_ENUM_GOOD_DESC": "Bien",
  "TR_QUALITY_ENUM_IN_TRANSIT_DESC": "en transit",
  "TR_QUALITY_ENUM_INVALID_DESC": "invalide",
  "TR_QUALITY_ENUM_INVALID_TIME_DESC": "Heure invalide",
  "TR_QUALITY_ENUM_NOT_TOPICAL_DESC": "Pas d'actualité",
  "TR_QUALITY_ENUM_OVERFLOW_DESC": "Débordement",
  "TR_QUALITY_ENUM_REF_ERROR_DESC": "Erreur de référence",
  "TR_QUALITY_ENUM_SUBSTITUTED_DESC": "Remplacé",
  "TR_QUALITY_ENUM_TEST_DESC": "Test",
  "TR_QUALITY_ENUM_UNINITIALIZED_DESC": "Non initialisé",
  "TR_QUERY_RESULT_DESC": "Spécifie les résultats de la requête",
  "TR_QUERY_RESULTS": "Résultats de la requête",
  "TR_RCB_DATASET_NAME": "Nom du fichier",
  "TR_RCB_DATASET_NAME_DESC": "Spécifiez le nom du fichier",
  "TR_RCB_LIST": "Liste de rapports",
  "TR_RCB_LIST_DESC": "Spécifie la liste de rapports en cours.",
  "TR_RCB_NAME": "Nom du rapport",
  "TR_RCB_NAME_DESC": "Spécifiez le nom du rapport",
  "TR_RE_START_MONITOR": "Redémarrer le moniteur",
  "TR_READ": "Lire",
  "TR_READ_CONFIG_FROM_SERVER": "Lecture de la configuration à partir du serveur",
  "TR_READ_CONFIG_FROM_SERVER_DESC": "Lire la configuration à partir du serveur.",
  "TR_READ_DESC": "Lire",
  "TR_READ_GOOSE": "Avertissement: impossible de lire le rapport d'oie '{{arg1}}', erreur = {{arg2}}",
  "TR_READ_POINT_MAP_AT_STARTUP": "Lire la mappe de points au démarrage",
  "TR_READ_POINT_MAP_AT_STARTUP_DESC": "si la valeur est true, le fichier de mappage de points sera lu au démarrage",
  "TR_REASON_FOR_INCLUSION": "Motif de l'inclusion",
  "TR_REASON_FOR_INCLUSION_DESC": "Spécifie si le motif de l'inclusion est actif.",
  "TR_RECONNECT_RETRY_COUNT": "Nombre de nouvelles tentatives de reconnexion",
  "TR_RECONNECT_TIME": "Temps de reconnexion (ms)",
  "TR_RECONNECTION_SETTINGS": "Paramètres de reconnexion",
  "TR_REF_ERROR": "REF_ERROR",
  "TR_REF_ERROR_DESC": "Erreur de référence (ADC)",
  "TR_REFRESH": "REFRESH",
  "TR_REFRESH_AREA_SPACE": "Actualiser l'espace de la zone",
  "TR_REFRESH_AREA_SPACE_DESC": "Actualiser l'espace de la zone",
  "TR_REFRESH_DESC": "Les données sont mises à jour par la source de données sans demande directe.  Aucun changement n'est nécessairement indiqué. ",
  "TR_REFRESH_ITEM_PARENT": "Actualiser l'élément",
  "TR_REFRESH_ITEM_PARENT_DESC": "Actualiser l'élément",
  "TR_REFRESH_LIST_DESC": "Liste de rafraîchissement",
  "TR_REFRESH_PROPERTIES": "Actualiser les propriétés",
  "TR_REFRESH_PROPERTIES_DESC": "Actualiser les propriétés",
  "TR_REFRESH_SERVER_LIST": "Actualiser la liste des serveurs",
  "TR_REGISTER": "Enregistrer",
  "TR_REMEMBER_ME": "Mémoriser mes informations",
  "TR_REMOTE_IP_NODE_NAME": "Nom d'IP / de nœud distant",
  "TR_REMOVE_SUBSCRIPTION": "Supprimer l'abonnement",
  "TR_REMOVE_SUBSCRIPTION_DESC": "Supprimer l'abonnement",
  "TR_REPORT_BY_EXCEPTION": "Rapport par exception",
  "TR_REPORT_BY_EXCEPTION_DESC": "Spécifie le rapport actuel par exception.",
  "TR_REPORT_NAME": "Nom du rapport",
  "TR_REPORT_NONE_LEFT": "Plus de rapports CEI 61850 disponibles",
  "TR_REQUEST_DATA_TYPE": "Type de données de la requête",
  "TR_REQUEST_DATA_TYPE_DESC": "Définir le type de données de la requête",
  "TR_REQUESTED": "REQUESTED",
  "TR_REQUESTED_DESC": "Les données sont en cours de mise à jour car elles ont été demandées.",
  "TR_REQUIRES_RESTART": "Redémarrage requis",
  "TR_RESUMED": "repris",
  "TR_RETRY_ENABLE_COUNT": "Nombre de tentatives d'activation (-1 = pour toujours, 0 = jamais)",
  "TR_RETRY_ENABLE_COUNT_DESC": "Spécifie le nombre actuel de tentatives d'activation.",
  "TR_RETRY_ENABLE_PERIOD": "Période d'activation de la nouvelle tentative (ms)",
  "TR_RETRY_ENABLE_PERIOD_DESC": "Spécifie la période d'activation de la tentative en cours.",
  "TR_RETRY_FAILED_TRANSACTION": "Nouvelle tentative de transaction échouée",
  "TR_ROLE": "Role",
  "TR_RR_COILS_END_INDEX": "Index final",
  "TR_RR_COILS_END_INDEX_DESC": "Index final",
  "TR_RR_COILS_START_INDEX": "Index de démarrage",
  "TR_RR_COILS_START_INDEX_DESC": "Démarrer l'index",
  "TR_RR_DISCRETE_INPUTS_END_INDEX": "Index final",
  "TR_RR_DISCRETE_INPUTS_END_INDEX_DESC": "Index final",
  "TR_RR_DISCRETE_INPUTS_START_INDEX": "Index de démarrage",
  "TR_RR_DISCRETE_INPUTS_START_INDEX_DESC": "Index de démarrage",
  "TR_RR_HOLDING_REGISTERS_END_INDEX": "Index final",
  "TR_RR_HOLDING_REGISTERS_END_INDEX_DESC": "Index final",
  "TR_RR_HOLDING_REGISTERS_START_INDEX": "Démarrer l'index",
  "TR_RR_HOLDING_REGISTERS_START_INDEX_DESC": "Index de démarrage",
  "TR_RR_INPUT_REGISTERS_END_INDEX": "Index final",
  "TR_RR_INPUT_REGISTERS_END_INDEX_DESC": "Index final",
  "TR_RR_INPUT_REGISTERS_START_INDEX": "Démarrer l'index",
  "TR_RR_INPUT_REGISTERS_START_INDEX_DESC": "Index de démarrage",
  "TR_RSA": "TLS RSA",
  "TR_RSA_PRIVATE_KEY_FILE": "Fichier de clé privée RSA",
  "TR_RSA_PRIVATE_KEY_PASS_PHRASE": "RSA Private PassPhrase",
  "TR_RSA_PUBLIC_CERT_FILE": "Fichier de certificat public RSA",
  "TR_RUN_SELECTED_INI_FILE": "Exécuter le fichier INI sélectionné",
  "TR_RUNTIME_PARAMETERS": "Paramètres d'exécution",
  "TR_SAVE": "Enregistrer",
  "TR_SAVE_GATEWAY": "Enregistrer le fichier INI / CSV actuel",
  "TR_SAVE_INI / CSV": "Enregistrer le fichier INI / CSV actuel",
  "TR_SAVE_UNMAPPED_POINTS": "Enregistrer les points non mappés",
  "TR_SAVE_UNMAPPED_POINTS_DESC": "si la valeur est true, les balises non mappées seront enregistrées dans le fichier de mappage de points",
  "TR_SBO": "SBO",
  "TR_SCL_CATEGORY _...PHYSIQUE ": " ...PHYSIQUE",
  "TR_SCL_CATEGORY_ ~~~ TRANSPORT": "~~~ TRANSPORT",
  "TR_SCL_CATEGORY _ +++ USER": "+++ USER",
  "TR_SCL_CATEGORY _ === APPLICATION": "=== APPLICATION",
  "TR_SCL_CATEGORY_CYCLIC_DATA": "DONNEES CYCLIQUES",
  "TR_SCL_CATEGORY_CYCLIC_HDRS": "CYCLIC HDRS",
  "TR_SCL_CATEGORY _--- DATA_LINK": "--- DATA_LINK",
  "TR_SCL_CATEGORY_EVENT_DATA": "DONNÉES D'ÉVÉNEMENT",
  "TR_SCL_CATEGORY_EVENT_HDRS": "EVENT HDRS",
  "TR_SCL_CATEGORY_MMI": "MMI",
  "TR_SCL_CATEGORY_SECURITY_DATA": "DONNÉES DE SÉCURITÉ",
  "TR_SCL_CATEGORY_SECURITY_HDRS": "SECURITY HDRS",
  "TR_SCL_CATEGORY_STATIC_DATA": "DONNÉES STATIQUES",
  "TR_SCL_CATEGORY_STATIC_HDRS": "STATIC HDRS",
  "TR_SCL_CATEGORY_TARGET": "CIBLE",
  "TR_SCL_DATABASE": "Base de données",
  "TR_SCL_FILE_NAME": "Fichier SCL",
  "TR_SCL_FILTER": "Filtre SCL",
  "TR_SCL_PROTOCOL_LAYER": "Couche de protocole",
  "TR_SDG_CATEGORY_61850": "61850",
  "TR_SDG_CATEGORY_870": "870",
  "TR_SDG_CATEGORY_DNP": "DNP",
  "TR_SDG_CATEGORY_EQUATION": "EQUATION",
  "TR_SDG_CATEGORY_MODBUS": "MODBUS",
  "TR_SDG_CATEGORY_ODBC": "ODBC",
  "TR_SDG_CATEGORY_OPC": "OPC",
  "TR_SDG_CATEGORY_OPC_DEEP": "OPC DEEP",
  "TR_SDG_CATEGORY_OPC_SU": "OPC SU",
  "TR_SDG_CATEGORY_OPC_UA": "UA OPC",
  "TR_SDG_CATEGORY_TASE2": "ICCP",
  "TR_SDG_FILTER": "Filtre SDG",
  "TR_SDG_MMS": "MMS",
  "TR_SDG_OPC": "OPC",
  "TR_SDG_OTHER": "Autre",
  "TR_SDG_SCL": "SCL",
  "TR_SDO_DESC": "Nom du SDO",
  "TR_SDO_NAME": "Nom SDO",
  "TR_SDO_NAME_DESC": "Le nom du SDO",
  "TR_SDO_OPTIONS": "Options SDO",
  "TR_SDO_OPTIONS_DESC": "Les options du SDO",
  "TR_SEARCH": "Recherche",
  "TR_SEARCH_CRITERIA": "Critères de recherche",
  "TR_SECTOR_DUPLICATE": "Impossible d'ajouter le secteur. Le secteur à l'adresse {{arg1}} existe déjà. ",
  "TR_SECURITY": "Sécurité",
  "TR_SECURITY_PARAMETERS": "Paramètres de sécurité",
  "TR_SECURITY_SETUP": "Configuration de la sécurité",
  "TR_SELECT_ALL": "Tout sélectionner",
  "TR_SELECT_LANGUAGE": "Changer de langue",
  "TR_SELECT_ONE": "Sélectionnez un:",
  "TR_SEQUENCE_NUMBER": "Numéro de séquence",
  "TR_SEQUENCE_NUMBER_DESC": "Spécifie si le numéro de séquence est actif.",
  "TR_SERIAL_PORT_SETUP": "Configuration du port série",
  "TR_SERVER": "Serveur",
  "TR_SERVER_AE_INVOKE_ID": "ID d'invocation AE",
  "TR_SERVER_AE_QUALIFIER": "Qualificateur AE",
  "TR_SERVER_AP_INVOKE_ID": "ID d'invocation AP",
  "TR_SERVER_APP_ID": "ID de l'application",
  "TR_SERVER_IP_ADDRESS": "Adresse IP du serveur",
  "TR_SERVER_IP_PORT": "Port TCP du serveur",
  "TR_SERVER_LIST": "Liste de serveurs",
  "TR_SERVER_LIST_DESC": "Définir la liste de serveurs",
  "TR_SERVER_NAME": "Nom du serveur",
  "TR_SERVER_NAME_DESC": "Définir le nom du serveur",
  "TR_SERVER_NODE": "Nœud de serveur",
  "TR_SERVER_NODE_DESC": "Définir le nœud du serveur",
  "TR_SERVER_PRESENTATION_ADDRESS": "Sélecteur de présentation",
  "TR_SERVER_SESSION_ADDRESS": "Sélecteur de session",
  "TR_SERVER_SUPPORTED_FEATURES": "Fonctionnalités prises en charge par le serveur",
  "TR_SERVER_TRANSPORT_ADDRESS": "Sélecteur de transport",
  "TR_SERVICE_INI_FILE_NAME": "Nom du fichier INI du service",
  "TR_SERVICE_PARAMETERS": "Paramètres de service",
  "TR_SESSION_CONFIG": "Configuration de session",
  "TR_SESSION_DUPLICATE": "Impossible d'ajouter une session. La session {{arg1}} existe déjà. ",
  "TR_SESSION_LINK_ADDRESS": "Adresse du lien",
  "TR_SESSION_LINK_ADDRESS_DESC": "Adresse de liaison de données du composant esclave ou de l'équipement distant. Chaque index identifie une session unique, qui est une connexion de couche liaison entre un périphérique maître et un périphérique esclave. Définissez sur 0xffff (65535) pour que la session soit une 'session de diffusion.'.",
  "TR_SESSION_LOCAL_ADDRESS": "Adresse source / locale",
  "TR_SESSION_LOCAL_ADDRESS_DESC": "Adresse de liaison de données du périphérique local. Ce paramètre n’est utilisé que pour les sessions maître ou esclave utilisant le protocole DNP3. ",
  "TR_SESSION_NAME": "Nom de session",
  "TR_SESSION_NAME_DESC": "Nom de la session",
  "TR_SET_MDO_OPTIONS_FAILED": "Impossible de définir les options MDO {{arg1}}",
  "TR_SET_TAGNAME_FAILED": "Impossible de définir le nom de la balise utilisateur sur {{arg1}} (peut être un doublon)",
  "TR_SETTINGS": "Paramètres",
  "TR_SEVERITY": "Gravité",
  "TR_SHOW_ALL_FCS": "Afficher toutes les contraintes fonctionnelles",
  "TR_SHOW_ALL_FCS_DESC": "Modifier l'affichage des contraintes fonctionnelles.",
  "TR_SHOW_ONLY_ST_MX": "Afficher uniquement les formats ST / MX",
  "TR_SISCO_COMPATABILITY": "Compatibilité SISCO",
  "TR_SLAVE_MASTER_DATA_OBJECT": "Objet de données esclave / maître",
  "TR_SLCT_CONFIRM": "SLCT_CONFIRM",
  "TR_SLCT_CONFIRM_DESC": "Un premier passage dans une opération de contrôle à 2 passes a été confirmé par un périphérique distant.",
  "TR_SLCT_PENDING": "SLCT_PENDING",
  "TR_SLCT_PENDING_DESC": "Un premier passage dans une opération de contrôle à deux passes a été transmis à un périphérique distant.",
  "TR_SOE_LOG": "Journal de séquence d'événements",
  "TR_SOEQ": "SOEQ",
  "TR_SOEQFILE_NAME": "Nom du fichier de file d'attente SOE",
  "TR_SOEQFILE_NAME_DESC": "Nom et chemin du fichier de file d'attente d'événements.",
  "TR_SOURCE": "Source",
  "TR_START_DATE": "Date de début",
  "TR_START_GATEWAY": "Démarrer la passerelle",
  "TR_STATUS": "STATUS",
  "TR_STATUS_CODE": "Code d'état",
  "TR_STATUS_CODE_DESC": "Définir le code d'état",
  "TR_STOP_GATEWAY": "Stop Gateway",
  "TR_SUBMIT_SUPPORT_REQUEST": "Soumettre une demande d'assistance",
  "TR_SUBSCRIBED_STREAM": "Flux souscrit",
  "TR_SUBSCRIBED_STREAM_DESC": "Flux souscrit",
  "TR_SUBSTITUTED": "SUBSTITUTED",
  "TR_SUBSTITUTED_DESC": "Remplacé (annulé ou forcé)",
  "TR_SUCCES_ARG1": "Succès: {{arg1}}",
  "TR_SUCCESS": "Succès",
  "TR_SUPER_USERS": "Super utilisateurs",
  "TR_SYSTEM_LOGS": "Journaux système",
  "TR_SYSTEM_SETTINGS": "Paramètres système",
  "TR_SYSTEM_TRACE": "Trace système",
  "TR_SYSTEMS_LOGS": "Journaux système",
  "TR_SYSTEMS_LOGS_DESC": "Journaux système",
  "TR_TABLE_INFO": "Informations sur la table",
  "TR_TABLE_INFO_DESC": "Spécifie les informations de la table",
  "TR_TABLE_LIST": "Liste de tables",
  "TR_TABLE_LIST_DESC": "Spécifie la liste des tables",
  "TR_TAG_DESCRIPTION": "Description de la balise:",
  "TR_TAG_DESCRIPTION_DESC": "Spécifiez la description de la balise",
  "TR_TAG_EDIT_": "Modifier",
  "TR_TAG_NAME": "Nom du tag:",
  "TR_TAG_NAME_DESC": "Spécifiez le nom de la balise",
  "TR_TAG_OPTIONS": "Options de balise:",
  "TR_TAG_OPTIONS_DESC": "Spécifiez les options de la balise",
  "TR_TAG_QUALITY": "Qualité",
  "TR_TAG_QUALITY_DESC": "Définir la qualité pour le MDO",
  "TR_TAG_TIME": "Heure du tag",
  "TR_TAG_TIME_DESC": "Définir l'heure du MDO",
  "TR_TAG_VALUE_TYPE": "Type de valeur de balise:",
  "TR_TAG_VALUE_TYPE_DESC": "Le type de la valeur de la balise",
  "TR_TARGET_LAYER": "Couche cible",
  "TR_TARGET_LAYER_DESC": "Enregistrer les messages de trace pour la couche cible",
  "TR_TASE2_ADD_DS_TRANSFERSET": "Impossible d'ajouter {{arg1}} ensemble de transfert DS sur le serveur ICCP à {{arg2}}. Assurez-vous que le jeu de données sélectionné existe sur le serveur et que plusieurs jeux de transfert sont disponibles. ",
  "TR_TASE2_ADD_MDO": "Impossible d'ajouter {{arg1}} MDO sur le serveur 61850: {{arg2}}. (En double?)",
  "TR_TASE2_ADD_MDO_DUPLICATE": "Impossible d'ajouter {{arg1}} MDO sur le serveur ICCP: {{arg2}}. (En double?)",
  "TR_TASE2_CLIENT_DELETE": "Le client '{{arg1}}' a des enfants MDO qui doivent être supprimés avant que le modèle ne puisse être chargé.",
  "TR_TASE2_CLIENT_DELETE_CLEAR_MODEL": "Le client '{{arg1}}' a des enfants MDO qui doivent être supprimés avant que le modèle puisse être effacé.",
  "TR_TASE2_CLIENT_DUP_LIC": "Échec de l'ajout du client ICCP (peut être dupliqué ou sans licence)",
  "TR_TASE2_CLIENT_DUPLICATE": "Impossible d'ajouter un client ICCP: '{{arg1}}'. Nom en double. ",
  "TR_TASE2_CLIENT_EXISTS": "Erreur: un client portant ce nom existe déjà.",
  "TR_TASE2_CLIENT_NO_MORE": "Plus de clients ICCP disponibles",
  "TR_TASE2_CREAT_MDO_NAME": "MDO doit avoir un nom, ne peut pas créer",
  "TR_TASE2_CREATE_DS": "Echec de la création du DataSet: {{arg1}} sur le serveur.Voir erreur dans l'analyseur de protocole pour plus d'informations. ",
  "TR_TASE2_CREATE_MDO_INVALID_TAG": "Impossible de créer {{arg1}} MDO.  Nom de balise invalide?",
  "TR_TASE2_DELETE_DS_NOT_FOUND": "Jeu de données {{arg1}} non trouvé.",
  "TR_TASE2_DELETE_IN_USE": "MDO: '{{arg1}}' est mappé sur des points esclaves ou est utilisé dans une équation, ne peut pas supprimer",
  "TR_TASE2_DELETE_DS": "Le DataSet ne peut pas être supprimé",
  "TR_TASE2_DUPLICATE_DOMAIN": "Impossible d'avoir des noms de domaine en double. Veuillez entrer un nom unique. ",
  "TR_TASE2_MDO_ALREADY_DEFINED": "MDO déjà défini, impossible de créer",
  "TR_TASE2_MDO_DELETE": "Les MDO ICCP ne peuvent pas être supprimés",
  "TR_TASE2_MDO_DUP_TAG": "Impossible de définir le nom de balise utilisateur MDO {{arg1}} (dupliquer?)",
  "TR_TASE2_NO_CONTROL_BLOCK": "Bloc de contrôle non valide",
  "TR_TASE2_NO_EDIT_CONN": "Impossible de modifier le serveur lorsque la connexion est en cours",
  "TR_TASE2_NO_EDIT_WITH_CLIENTS_CON": "Impossible de modifier le serveur lorsque les clients sont connectés",
  "TR_TASE2_NO_MORE": "Plus de clients ICCP disponibles",
  "TR_TASE2_NO_MORE_DS_TRANSFER": "Plus d'ensembles de transfert DS disponibles",
  "TR_TASE2_NO_MORE_LD": "Plus de périphériques logiques disponibles",
  "TR_TASE2_NO_MORE_POLLED": "Plus de ICCP PolledPointSets disponibles",
  "TR_TASE2_NO_MORE_POLLED_DS": "Impossible d'ajouter {{arg1}} PolledPointSet sur le serveur ICCP: {{arg2}}.",
  "TR_TASE2_NO_POINTS": "Aucun point ICCP disponible",
  "TR_TASE2_NO_POLLED": "Aucun élément de fichier interrogé disponible",
  "TR_TASE2_NONE_LEFT": "Plus de clients ICCP disponibles",
  "TR_TASE2_OPT_MDO": "Impossible de définir les options MDO {{arg1}}",
  "TR_TASE2_SELECT": "Échec de la sélection du contrôle - rien n'a été fait.",
  "TR_TASE2_SELECT_INTEGER": "La valeur en écriture n'est pas valide - doit être un entier",
  "TR_TASE2_SELECT_WRONG_VALUE": "La valeur en écriture n'est pas valide - doit être un nombre décimal",
  "TR_TASE2_SERVER_ADD_POLLED_DS": "Impossible d'ajouter {{arg1}} PolledDataSet sur le serveur ICCP: {{arg2}}.",
  "TR_TASE2_SERVER_DUP_LIC": "Échec de l'ajout du serveur ICCP (peut être dupliqué ou sans licence)",
  "TR_TASE2_SERVER_EXISTS": "Erreur: le serveur portant ce nom existe déjà.",
  "TR_TASE2_SET_MDO": "Impossible de définir les options MDO {{arg1}}",
  "TR_TASE2CLIENT_AEINVOKE_ID": "ID d'invocation AE",
  "TR_TASE2CLIENT_AEINVOKE_ID_DESC": "Spécifie l'ID d'invocation AE du client ICCP",
  "TR_TASE2CLIENT_AEQUALIFIER": "Qualificateur AE",
  "TR_TASE2CLIENT_AEQUALIFIER_DESC": "Spécifie le qualificateur AE du client ICCP",
  "TR_TASE2CLIENT_APINVOKE_ID": "ID d'invocation AP",
  "TR_TASE2CLIENT_APINVOKE_ID_DESC": "Spécifie l'ID d'appel AP du client ICCP",
  "TR_TASE2CLIENT_APP_ID": "ID de l'application",
  "TR_TASE2CLIENT_APP_ID_DESC": "Spécifie l'identifiant d'application du client ICCP",
  "TR_TASE2CLIENT_CONNECT_TIMEOUT": "Délai d'attente MMS (ms)",
  "TR_TASE2CLIENT_CONNECT_TIMEOUT_DESC": "Spécifie le délai d'expiration de la connexion MMS pour le client ICCP.  Après avoir démarré une tentative de connexion, voici comment int attendre le succès. ",
  "TR_TASE2CLIENT_CONNECTING_PORT": "Port IP",
  "TR_TASE2CLIENT_CONNECTING_PORT_DESC": "Spécifie le port TCP / IP auquel le client tentera de se connecter sur le serveur",
  "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME": "DataSet Transfer Set buffer time (secs)",
  "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME_DESC": "Spécifie l'attribut Buffer Time pour un ensemble de transfert DataSet",
  "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL": "Intervalle de définition du transfert de DataSet (secondes)",
  "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL_DESC": "Spécifie l'attribut Intervalle d'un ensemble de transfert de DataSet",
  "TR_TASE2CLIENT_DSTRANSFER_SET_RBE": "Rapport d'ensemble de transferts DataSet par exception",
  "TR_TASE2CLIENT_DSTRANSFER_SET_RBE_DESC": "Spécifie l'attribut Rapport par exception d'un ensemble de transferts DataSet",
  "TR_TASE2CLIENT_INITIATE": "Établir une connexion",
  "TR_TASE2CLIENT_INITIATE_DESC": "Le client doit-il établir une connexion",
  "TR_TASE2CLIENT_PRESENTATION_ADDRESS": "Adresse de présentation",
  "TR_TASE2CLIENT_PRESENTATION_ADDRESS_DESC": "Spécifie l'adresse de présentation du client ICCP",
  "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT": "Reconnecter le nombre de tentatives répétées",
  "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT_DESC": "Spécifie le décompte de nouvelles tentatives de reconnexion pour le client ICCP (0 = tentative de reconnexion permanente). Une connexion réussie entraîne la réinitialisation du compteur de limite interne sur 0 après la tentative de connexion au serveur ICCP.",
  "TR_TASE2CLIENT_RECONNECT_TIME": "Délai de reconnexion (ms)",
  "TR_TASE2CLIENT_RECONNECT_TIME_DESC": "Spécifie le délai de reconnexion pour le client ICCP (0 = pas de reconnexion)",
  "TR_TASE2CLIENT_RFC_IP_ADDR": "Adresse IP RFC du client ICCP",
  "TR_TASE2CLIENT_RFC_IP_ADDR_DESC": "Spécifie l'adresse IP RFC du client ICCP",
  "TR_TASE2CLIENT_SESSION_ADDRESS": "Adresse de session",
  "TR_TASE2CLIENT_SESSION_ADDRESS_DESC": "Spécifie l'adresse de session du client ICCP",
  "TR_TASE2CLIENT_TRANSPORT_ADDRESS": "Adresse de transport",
  "TR_TASE2CLIENT_TRANSPORT_ADDRESS_DESC": "Spécifie l'adresse de transport du client ICCP",
  "TR_TASE2CLT_POLLED_POINT_SET_NAME": "Nom de l'ensemble de points interrogés",
  "TR_TASE2CLT_POLLED_POINT_SET_NAME_DESC": "Spécifie le nom de l'ensemble de points interrogés",
  "TR_TASE2CLT_POLLED_POINT_SET_PERIOD": "Période de définition du point d'interrogation (ms)",
  "TR_TASE2CLT_POLLED_POINT_SET_PERIOD_DESC": "Spécifie la période pour lire l'ensemble de points interrogés",
  "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME": "Domaine ICCP de l'ensemble de données du jeu de données signalé",
  "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME_DESC": "Spécifie le domaine d'un ensemble de données enregistré sur un serveur ICCP",
  "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD": "Période d'ensemble de transfert de DataSet (secondes)",
  "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD_DESC": "Spécifie la période de mise à jour de l'intégrité d'un ensemble de transfert de DataSet",
  "TR_TASE2CLT_REPORTED_DSNAME": "Nom du fichier signalé",
  "TR_TASE2CLT_REPORTED_DSNAME_DESC": "Spécifie le nom d'un ensemble de données signalé sur un serveur ICCP",
  "TR_TASE2SECURITY_ON": "Activer la sécurité",
  "TR_TASE2SECURITY_ON_DESC": "La sécurité doit-elle être activée",
  "TR_TASE2SERVER_AEINVOKE_ID": "ID d'invocation AE",
  "TR_TASE2SERVER_AEINVOKE_ID_DESC": "Spécifie l'ID d'invocation AE du serveur ICCP",
  "TR_TASE2SERVER_AEQUALIFIER": "Qualificateur AE",
  "TR_TASE2SERVER_AEQUALIFIER_DESC": "Spécifie le qualificateur AE du serveur ICCP",
  "TR_TASE2SERVER_APINVOKE_ID": "ID d'invocation AP",
  "TR_TASE2SERVER_APINVOKE_ID_DESC": "Spécifie l'ID d'appel AP du serveur ICCP",
  "TR_TASE2SERVER_APP_ID": "ID de l'application",
  "TR_TASE2SERVER_APP_ID_DESC": "Spécifie l'ID de l'application du serveur ICCP",
  "TR_TASE2SERVER_IPADDRESS": "Adresse IP du serveur ICCP",
  "TR_TASE2SERVER_IPADDRESS_DESC": "Spécifie l'adresse IP du serveur ICCP auquel le client tentera de se connecter",
  "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID": "ID de table bilatérale",
  "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID_DESC": "Spécifie l'ID de la table bilatérale",
  "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT": "Nombre d'ensembles de transfert DS",
  "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT_DESC": "Spécifie le nombre d'ensembles de transferts DS",
  "TR_TASE2SERVER_LOGICAL_DEVICE_NAME": "Nom de périphérique logique",
  "TR_TASE2SERVER_LOGICAL_DEVICE_NAME_DESC": "Spécifie le nom d'un périphérique logique sur un serveur ICCP",
  "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED": "Nombre maximum de clients",
  "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED_DESC": "Spécifie le nombre maximal de clients autorisés à se connecter à ce serveur. Unspecified signifie no max. ",
  "TR_TASE2SERVER_PRESENTATION_ADDRESS": "Adresse de présentation",
  "TR_TASE2SERVER_PRESENTATION_ADDRESS_DESC": "Spécifie l'adresse de présentation du serveur ICCP",
  "TR_TASE2SERVER_RFC_IP_ADDR": "Adresse IP RFC du serveur ICCP",
  "TR_TASE2SERVER_RFC_IP_ADDR_DESC": "Spécifie l'adresse IP RFC du serveur ICCP",
  "TR_TASE2SERVER_SESSION_ADDRESS": "Adresse de session",
  "TR_TASE2SERVER_SESSION_ADDRESS_DESC": "Spécifie l'adresse de session du serveur ICCP",
  "TR_TASE2SERVER_SUPPORTED_FEATURES": "Fonctionnalités prises en charge par le serveur ICCP",
  "TR_TASE2SERVER_SUPPORTED_FEATURES_DESC": "Spécifie les blocs ICCP signalés aux clients qui se connectent en tant que Supported_Features",
  "TR_TASE2SERVER_TRANSPORT_ADDRESS": "Adresse de transport",
  "TR_TASE2SERVER_TRANSPORT_ADDRESS_DESC": "Spécifie l'adresse de transport du serveur ICCP",
  "TR_TASE2SERVICE_ROLE": "Rôle de service",
  "TR_TASE2SERVICE_ROLE_DESC": "Spécifie le rôle de service Tase2",
  "TR_TASE2SYNC_DATA_SETS": "Ensembles de données de synchronisation",
  "TR_TASE2SYNC_DATA_SETS_DESC": "Le client doit-il essayer de synchroniser les ensembles de données sur la connexion",
  "TR_TASE2VERSION": "Version d'application ICCP",
  "TR_TASE2VERSION_DESC": "Spécifie la version de l'application ICCP",
  "TR_TEST": "TEST",
  "TR_TEST_DESC": "Test",
  "TR_TEST_MODE": "Mode test",
  "TR_TEST_MODE_DESC": "Le point de données ou le périphérique distant fonctionne en mode test.",
  "TR_THE_CURRENT_PASSWORD_IS_REQUIRED": "Le mot de passe actuel est requis.",
  "TR_THE_NEW_PASSWORD_MUST_BE_AT_LEAST_CHARACTERS_LONG": "Le nouveau mot de passe doit comporter au moins 6 caractères.",
  "TR_THE_TWO_PASSWORD_FIELDS_DIDN_T_MATCH": "Les deux champs de mot de passe ne correspondaient pas.",
  "TR_THE_USER_ADMIN_CAN_NOT_BE_DELETED": "L'utilisateur Admin ne peut pas être supprimé.",
  "TR_THE_USERNAME_X_IS_ALREADY_USED": "Le nom d'utilisateur {{nom d'utilisateur}} est déjà utilisé.",
  "TR_THIS_SYSTEM": "Ce système",
  "TR_THRESHOLD": "Theshold",
  "TR_TIME": "Heure",
  "TR_TIME_AND_TIMING": "Heure et réglage",
  "TR_TIME_STAMP": "Horodatage",
  "TR_TIME_STAMP_DESC": "Spécifie si l'horodatage est actif.",
  "TR_TIME_ZONE_BIAS": "Biais de fuseau horaire",
  "TR_TIME_ZONE_NAME": "Fuseau horaire",
  "TR_TIME_ZONE_NAME_DESC": "Le nom du fuseau horaire à utiliser pour l'heure d'affichage du SDG (chaîne vide / le paramètre par défaut sera défini sur UTC).  Remarque: SDG utilise UTC pour les heures internes.  UseTimeZoneClock doit être vrai. ",
  "TR_TLS": "TLS",
  "TR_TLS_COMMON_NAME": "Nom commun",
  "TR_TLS_CONFIG": "62351-3 Configuration de la sécurité",
  "TR_TLS_HANDSHAKE_TIMEOUT": "Délai de prise de contact TLS",
  "TR_TLS_MAX_PDUS": "Nombre maximal de PDU avant de forcer la renégociation de chiffrement",
  "TR_TLS_MAX_RENEG_WAIT_TIME": "Temps d'attente maximal pour la renégociation (ms)",
  "TR_TLS_RENEGOTIATION": "Renégociation (sec)",
  "TR_TLS_RENEGOTIATION_COUNT": "Nombre de renégociations",
  "TR_TLS_RENEGOTIATION_SECONDS": "Renégociation (sec)",
  "TR_TRIGGER_OPTIONS": "Options de déclenchement",
  "TR_TS_CONDITIONS_DETECTED": "Inclure les conditions TS détectées",
  "TR_TS_CONDITIONS_DETECTED_DESC": "Inclure les conditions TS détectées",
  "TR_TS_SET_NAME": "Inclure le nom de l'ensemble de transfert",
  "TR_TS_SET_NAME_DESC": "Inclure le nom de l'ensemble de transfert",
  "TR_TS_SET_TIMESTAMP": "Inclure l'horodatage de l'ensemble de transfert",
  "TR_TS_SET_TIMESTAMP_DESC": "Inclure l'horodatage de l'ensemble de transfert",
  "TR_TYPE": "Type",
  "TR_UNAUTHORIZED": "Non autorisé",
  "TR_UNINITIALIZED": "Uninitialized",
  "TR_UNINITIALIZED_DESC": "Non défini depuis le démarrage",
  "TR_UNKNOWN": "Inconnu",
  "TR_UNKNOWN_DESC": "Les données sont en cours de mise à jour, mais le motif de la mise à jour est inconnu.",
  "TR_UNLOCK_SCROLL": "Déverrouiller le défilement",
  "TR_UNSELECT_ALL": "Désélectionner tout",
  "TR_UNSOLICITED_UDP_PORT": "Port UDP non sollicité",
  "TR_UNSPECIFIED_ERROR": "Erreur non spécifiée",
  "TR_UPLOAD_FILE": "Télécharger le fichier",
  "TR_UPLOAD_NEW_CONFIGURATION_FILE": "Télécharger le nouveau fichier de configuration",
  "TR_UPLOAD_NEW_CSV_FILE": "Télécharger un nouveau fichier CSV",
  "TR_UPLOAD_NEW_INI_FILE": "Télécharger le nouveau fichier INI",
  "TR_USE": "Utiliser",
  "TR_USE_DEFLATE": "Utiliser la compression gzip",
  "TR_USE_REPORTED_TIME": "Utiliser l'heure reportée pour les MDO",
  "TR_USE_REPORTED_TIME_DESC": "si la valeur est true, ne mettez pas à jour l'heure sur les mises à jour statiques, cela s'applique aux objets de MDO pouvant être signalés en tant qu'événements",
  "TR_USE_SCL_FILE": "Utiliser le fichier SCL",
  "TR_USE_SYSTEM_TIME": "Utiliser l'horloge système",
  "TR_USE_SYSTEM_TIME_DESC": "Si la valeur est true, lisez toujours la date et l'heure à partir de l'horloge système Windows, par opposition à une horloge maintenue en interne. L'horloge interne est initialisée sur l'horloge système Windows au démarrage, mais elle est ajustée chaque fois qu'une synchronisation d'horloge est reçue d'un maître externe. Vous devez généralement définir UseSystemTime sur true si vous disposez d'un mécanisme de synchronisation d'horloge externe qui synchronise l'horloge système Windows en dehors de la passerelle de données SCADA. Dans ce cas, il est conseillé de définir la valeur false sur AcceptClockSync. ",
  "TR_USE_TIME_ZONE_CLOCK": "Utiliser l'horloge du fuseau horaire",
  "TR_USE_TIME_ZONE_CLOCK_DESC": "Si true, affiche la date et l'heure du SDG dans le TimeZoneName spécifié",
  "TR_USE_WEB_SSL": "Utiliser Web SSL / HTTPS",
  "TR_USER_TAG_NAME": "Nom de balise utilisateur",
  "TR_USER_TAG_NAME_DESC": "Le nom de la balise utilisateur du MDO",
  "TR_USER_X_DELETED": "Utilisateur {{nom d'utilisateur}} supprimé.",
  "TR_USERNAME": "Nom d'utilisateur",
  "TR_USERNAME_IS_REQUIRED": "Le nom d'utilisateur est requis.",
  "TR_USERNAME_IS_REQUIRED_CHARACTERS_MINIMUM": "Un nom d'utilisateur est requis (minimum 4 caractères).",
  "TR_USERS": "Utilisateurs",
  "TR_USERS_MANAGEMENT": "Gestion des utilisateurs",
  "TR_VALID_EQUATION_SUCCESS": "Succès: équation valide",
  "TR_VALIDATE_EQUATION": "Valider l'équation",
  "TR_VALIDATE_UDP_ADDRESS": "Valider l'adresse UDP",
  "TR_VALUE": "Valeur",
  "TR_VERBOSE": "Vebose",
  "TR_VERIFY_CERTIFICATE": "Vérifier le certificat HTTPS",
  "TR_VERSION": "Version",
  "TR_VIEW": "Voir",
  "TR_VIEWS": "Vues spéciales",
  "TR_WARNING": "Attention",
  "TR_WARNING_DESC": "Attention",
  "TR_WARNING_VIEW": "Vue d'avertissement",
  "TR_WEB_SERVER": "Serveur Web",
  "TR_WEB_SERVER_DESC": "Enregistrer les messages de trace pour le serveur Web",
  "TR_WEBSOCKET_CLOSE": "Websocket {{websocketName}} fermer: {{raison}}",
  "TR_WEBSOCKET_OPEN": "Websocket {{websocketName}} ouvert",
  "TR_WEBSOCKET_UPDATE_PARAMETERS": "Paramètres de mise à jour Websocket",
  "TR_WHAT_NAME_DO_YOU_TO_USE_FOR_YOUR_NEW_INI_CSV_FILE": "Quel nom voulez-vous utiliser pour votre nouveau fichier INI",
  "TR_WIN_TIMER_FAILED": "Une erreur fatale s'est produite: {{arg1}}. Le SDG va maintenant essayer de sortir. Veuillez consulter les journaux pour plus de détails. ",
  "TR_YES": "Oui",
  "TR_YOUR_BROWSER_WILL_REFRESH_IN_X_SECONDS": "Votre navigateur sera actualisé dans {{valeur}} secondes.",
  "TR_ARE_YOU_SURE_TO_DELETE_USER_": "Are you sure to delete user {{username}}?"
}