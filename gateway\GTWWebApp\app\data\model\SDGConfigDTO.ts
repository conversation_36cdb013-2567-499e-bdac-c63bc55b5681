/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export interface SDGConfigDTO {
//  gtwExeName?: string;

  gtwDoValidateConfig?: boolean;

  gtwHttpPort?: number;

  gtwHost?: string;

  monHttpPort?: number;

  monHost?: string;

  gtwWebDir?: string;

  gtwTzPath?: string;

  gtwAllowedIPs?: string;

  //httpsPrivateKeyFile?: string;

  //httpsPrivateKeyPassPhrase?: string;

  //httpsCertificateFile?: string;

  gtwDoAuth?: boolean;

  gtwDoAudit?: boolean;

  gtwUseWebSSL?: boolean;

  gtwHttpsCertIsTmwSigned?: boolean;

  gtwUseLocalHostForEngineAndMonitorComms?: boolean;

  gtwEnableHttpDeflate?: boolean;

  gtwAuthExpVIEWER_ROLE?: number;

  gtwAuthExpOPERATOR_ROLE?: number;

  gtwAuthExpCONFIGURATOR_ROLE?: number;

  gtwAuthExpSU_ROLE?: number;

  //monTraceEnable?: boolean;

  //monTraceMask?: number;

  //sdgTraceEnable?: boolean;

  //sdgTraceMask?: number;

  //pdoDiagMask?: number;

  gtwWsUpdateRate?: number;

  gtwLicGracePeriodInMinutes?: number;

  gtwWsUpdateBlockSize?: number;

  gtwHttpPageBlockSize?: number;

  currentWorkSpaceName?: string;

  enableIECFullStackAddressing?: boolean;

  gtwMaxLogFiles?: number;

  gtwMaxLogBufferEntries?: number;

  fullLogOnRestart?: boolean;

  mirrorAllToLog?: boolean;
}
