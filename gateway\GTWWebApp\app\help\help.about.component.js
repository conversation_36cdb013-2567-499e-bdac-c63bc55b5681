System.register(["@angular/core", "../data/api/api", "../modules/alert/alert.service", "../authentication/authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, api_1, alert_service_1, authentication_service_1, HelpAboutComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            HelpAboutComponent = (function () {
                function HelpAboutComponent(miscService, alertService, authenticationService) {
                    this.miscService = miscService;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.aboutContent = "";
                }
                HelpAboutComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    this.miscService.getAbout().subscribe(function (data) {
                        var aboutDTO;
                        aboutDTO = data;
                        _this.aboutContent = aboutDTO.aboutContent;
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_CAN_T_READ_ABOUT_CONTENT");
                        }
                    });
                };
                HelpAboutComponent = __decorate([
                    core_1.Component({
                        selector: "helpAboutComponent",
                        styles: ["\n   .center{\n      text-align: center\n    }\n\t"],
                        template: "\n\t\t<div>\n\t\t  <div class=\"panel panel-default panel-main\" style=\"width:99%; margin-left:6px; margin-top:60px\">\n        <div class=\"panel-heading\"><img src=\"../../images/about.svg\" class=\"module-icon\" />{{'TR_ABOUT' | translate}}</div>\n\t      <div class=\"panel-body center\" [innerHTML]=\"aboutContent\"></div>\n      </div>\n\t\t</div>"
                    }),
                    __metadata("design:paramtypes", [api_1.MiscService, alert_service_1.AlertService, authentication_service_1.AuthenticationService])
                ], HelpAboutComponent);
                return HelpAboutComponent;
            }());
            exports_1("HelpAboutComponent", HelpAboutComponent);
        }
    };
});
//# sourceMappingURL=help.about.component.js.map