/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

/**
 * Sent by /getLogEnties web socket endpoint
 */
export interface LogEntryDTO {
    source?: string;

    name?: string;

    id?: number;

    category?: string;

    severity?: string;

    timeStamp?: string;

    message?: string;

}
