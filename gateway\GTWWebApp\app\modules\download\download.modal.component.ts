import { Component } from "@angular/core";
import { AlertService } from "../alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { FileService } from "../../data/api/api";

@Component({
  selector: "downloadModalComponent",
  template: ``
})

export class DownloadModalComponent {

  constructor(private alertService: AlertService, private authenticationService: AuthenticationService) {
  }

  public downloadClick(fileName: string, fileType: string, fileService: FileService = null ): void {
    fileService.fileGet(fileName, fileType).subscribe(
      (data: any) => {
        this.saveData(data, fileName);
        this.alertService.success("TR_FILE_DOWNLOADED");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED"); }
      }
    );
  }

  public saveData(blob, fileName): void {
    let a = document.createElement("a");
    document.body.appendChild(a);
    let url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
}