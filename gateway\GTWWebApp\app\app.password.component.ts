import { Component } from "@angular/core"
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { AuthenticationPasswordModal } from "./authentication/authentication.password.modal";
import { AuthenticationService } from "./authentication/authentication.service";

@Component({
	selector: "appPasswordComponent",
	template: `
			<div (click)="onChangePassword()">{{'TR_CHANGE_PASSWORD' | translate}}</div>`
})

export class AppPasswordComponent {

  constructor(private modal: Modal, private authenticationService: AuthenticationService) { }

	private onChangePassword(): void {
    this.modal.open(AuthenticationPasswordModal, overlayConfigFactory({ authenticationService: this.authenticationService, username: this.authenticationService.userApiConfig.username }, BSModalContext));
	}
}
