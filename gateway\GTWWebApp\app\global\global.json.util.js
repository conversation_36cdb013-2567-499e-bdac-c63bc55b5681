System.register([], function (exports_1, context_1) {
    "use strict";
    var GlobalJsonUtil;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            GlobalJsonUtil = (function () {
                function GlobalJsonUtil() {
                }
                GlobalJsonUtil.sortByProperty = function (jsonObjects, property, direction) {
                    if (direction === void 0) { direction = 1; }
                    var clone = jsonObjects;
                    var propPath = (property.constructor === Array) ? property : property.split(".");
                    if (typeof clone === 'undefined')
                        return null;
                    clone.sort(function (a, b) {
                        for (var p in propPath) {
                            if (a[propPath[p]] && b[propPath[p]]) {
                                a = a[propPath[p]];
                                b = b[propPath[p]];
                            }
                        }
                        var reA = /[^a-zA-Z]/g;
                        var reN = /[^0-9]/g;
                        var aA = a.replace(reA, "");
                        var bA = b.replace(reA, "");
                        if (aA === bA) {
                            var aN = parseInt(a.replace(reN, ""), 10);
                            var bN = parseInt(b.replace(reN, ""), 10);
                            return ((aN < bN) ? -1 * direction : ((aN > bN) ? 1 * direction : 0));
                        }
                        else {
                            return ((aA < bA) ? -1 * direction : ((aA > bA) ? 1 * direction : 0));
                        }
                    });
                    return clone;
                };
                return GlobalJsonUtil;
            }());
            exports_1("GlobalJsonUtil", GlobalJsonUtil);
        }
    };
});
//# sourceMappingURL=global.json.util.js.map