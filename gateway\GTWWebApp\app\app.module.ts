﻿import { NgModule } from '@angular/core';
import { HttpClient, HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LocationStrategy, HashLocationStrategy } from '@angular/common';
import { ModalModule } from 'ngx-modialog-7';
import { BootstrapModalModule } from 'ngx-modialog-7/plugins/bootstrap';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { AngularSplitModule } from "./modules/angular-split/index";
import { PanelComponent } from "./modules/panel/panel.component";
import { PanelService } from "./modules/panel/panel.service";
import { BitmaskComponent } from "./modules/bitmask/bitmask.component";
import { BitmaskEditorComponent } from "./modules/bitmask/bitmask.editor.component";
import { BitmaskAdvanceEditorComponent } from "./modules/bitmask/bitmask.advance-editor.component";
import { DraggableDirective } from "./modules/dragndrop/draggable.directive";
import { DropTargetDirective } from "./modules/dragndrop/drop-target.directive";
import { CollapsiblePanel } from "./modules/collapsible-panel/collapsible-panel";
import { AlertService } from "./modules/alert/alert.service";
import { AlertStatusBarComponent } from "./modules/alert/alert.status-bar.component";
import { AlertLogModal } from "./modules/alert/alert.log.modal";
import { ContextMenuService } from "./modules/context-menu/context-menu.service";
import { ContextMenuComponent } from "./modules/context-menu/context-menu.component";
import { ContextMenuDirective } from "./modules/context-menu/context-menu.directive";
import { DropdownDirective } from "./modules/dropdown/dropdown.directive";
import { UppercaseDirective } from "./modules/uppercase/uppercase.directive";
import { DownloadComponent } from "./modules/download/download.component";
import { DownloadModalComponent } from "./modules/download/download.modal.component";
import { ShowPassword } from "./modules/show-password/show-password.directive";
import { TooltipService } from "./modules/angular-tooltip/tooltip.service";
import { TooltipComponent } from "./modules/angular-tooltip/tooltip.component";
import { TooltipDirective } from "./modules/angular-tooltip/tooltip.directive";
import { TabComponent } from "./modules/tab/tab.component";
import { TabsetComponent } from "./modules/tab/tabset.component";
import { LoaderComponent } from "./modules/loader/loader.component";
import { LoaderService } from "./modules/loader/loader.service";
import { LoaderInterceptorService } from "./modules/loader/loader-interceptor.service";
import { ResizableComponent } from './modules/resizable/resizable.component';
import { ResizableDirective } from './modules/resizable/resizable.directive';
import { ComboboxEditorMultiselectComponent } from './modules/combobox/combobox.editor.multiselect.component';

import { Routing } from "./app.routing";
import { AppComponent } from "./app.component";
import { AppHealthComponent } from "./app.health.component";
import { AppBlankComponent } from './app.blank.component';
import { AppInfoComponent } from './app.info.component';

import { DashboardComponent } from "./dashboard/dashboard.component";
import { DashboardManagerComponent } from "./dashboard/dashboard.manager.component";
import { SettingsComponent } from "./settings/settings.component";
import { SettingsFormComponent } from "./settings/settings.form.component";

import { GlobalDataService } from "./global/global.data.service";
import { KeysPipe } from "./global/keys.pipe";
import { SafeHtmlPipe } from "./global/safeHtml.pipe";
import { TranslateKeyPipe } from "./global/translateKey.pipe";
import { GTWImagePipe } from "./global/gtwImage.pipe";
import { LogEntryContainPipe } from "./global/logEntryContain.pipe";
import { GridContain } from "./global/gridContain.pipe";

import { AuthenticationService } from "./authentication/authentication.service";
import { AuthenticationLoginModal } from "./authentication/authentication.login.modal";
import { AuthenticationPasswordModal } from "./authentication/authentication.password.modal";
import { CheckRolePipe } from "./authentication/check.role.pipe";
import { AuthenticationInterceptorService } from "./authentication/authentication-interceptor.service";

import { AppPasswordComponent } from "./app.password.component";

import { LicenseComponent } from './license/license.component';

import { UserGridComponent } from './user/user.grid.component';
import { UserModal } from "./user/user.modal";
import { UserResetPasswordModal } from "./user/user.reset.password.modal";

import { DashboardConfigComponent } from "./dashboard/config/dashboard.config.component";
import { DashboardConfigDevicesComponent } from "./dashboard/config/dashboard.config.devices.component";
import { DashboardConfigDevicesSearchComponent } from "./dashboard/config/dashboard.config.devices.search.component";
import { DashboardConfigDevicesTreeViewComponent } from "./dashboard/config/dashboard.config.devices.treeview.component";
import { DashboardConfigContextMenuDirective } from "./dashboard/config/dashboard.config.context-menu.directive";
import { DashboardConfigTagsGridComponent } from "./dashboard/config/dashboard.config.tags.grid.component";
import { DashboardConfigTagEditorModal } from "./dashboard/config/dashboard.config.tag.editor.modal";
import { DashboardConfigTagEditorComponent } from "./dashboard/config/dashboard.config.tag.editor.component";
import { DashboardConfigTagEditorAdvanceComponent } from "./dashboard/config/dashboard.config.tag.editor.advance.component";
import { DashboardConfigTagEditorLogic } from "./dashboard/config/dashboard.config.tag.editor.logic";
import { DashboardConfigTagOptionsModal } from "./dashboard/config/dashboard.config.tag.options.modal";
import { DashboardConfigImportExportModal } from "./dashboard/config/dashboard.config.import-export.modal";
import { DashboardConfigTagValueQualityModal } from "./dashboard/config/dashboard.config.tag.value.quality.modal";
import { DashboardConfigTagEditorGroupPipe } from "./dashboard/config/dashboard.config.tag.editor.group.pipe";
import { DashboardConfigGridComponent } from "./dashboard/config/dashboard.config.grid.component";
import { DashboardConfigTagsGridSearchComponent } from "./dashboard/config/dashboard.config.tags.grid.search.component";
import { DashboardConfigTagTooltipDirective } from "./dashboard/config/dashboard.config.tag.tooltip.directive";
import { DashboardConfigDeviceVirtualNodeComponent } from "./dashboard/config/dashboard.config.device.virtual-node.component";

import { DashboardLogComponent } from "./dashboard/log/dashboard.log.component";
import { DashboardLogGridComponent } from "./dashboard/log/dashboard.log.grid.component";
import { DashboardLogExtraComponent } from "./dashboard/log/dashboard.log.extra.component";

import { LogAuditGridComponent } from './log/log.audit.grid.component';
import { LogMonitorComponent } from './log/log.monitor.component';
import { LogMonitorGridComponent } from './log/log.monitor.grid.component';
import { LogSoeComponent } from './log/log.soe.component';

import { HelpComponent } from "./help/help.component";
import { HelpAboutComponent } from './help/help.about.component';
import { HelpUpdateComponent } from './help/help.update.component';
import { HelpQuickStartComponent } from "./help/help.quick-start.component";
import { HelpSupportComponent } from "./help/help.support.component";
import { HelpIniComponent } from "./help/help.ini.component";

import { BASE_PATH as ApiUrl } from "./data/variables";

import { GridComponent } from "./modules/grid/grid.component";
import { GridSearchComponent } from "./modules/grid/grid.search.component";
import { GridPaginationComponent } from "./modules/grid/grid.pagination.component";
import { TreeviewComponent } from "./modules/treeview/treeview.component";
import { DynamicTreeviewComponent } from "./modules/treeview/dynamic-treeview.component";
import { DynamicTreeviewSelectComponent } from "./modules/treeview-select/dynamic-treeview-select.component";
import { CheckboxIndeterminateDirective } from "./modules/checkbox-indeterminated/checkbox-indeterminated.directive";

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient, "i18n/", ".json");
}

@NgModule({
  imports: [
    HttpClientModule,
    Routing,
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    BootstrapModalModule,
    ModalModule.forRoot(),
    AngularSplitModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    })],
  declarations: [
    AppComponent,
    DashboardComponent,
    DashboardManagerComponent,
    SettingsComponent,
    SettingsFormComponent,
    DashboardConfigComponent,
    DashboardConfigDevicesComponent,
    DashboardConfigDevicesSearchComponent,
    DashboardConfigDevicesTreeViewComponent,
    DashboardConfigContextMenuDirective,
    DashboardConfigTagsGridComponent,
    DashboardConfigTagEditorModal,
    DashboardConfigTagEditorGroupPipe,
    DashboardConfigTagEditorComponent,
    DashboardConfigTagEditorAdvanceComponent,
    DashboardConfigTagOptionsModal,
    DashboardConfigImportExportModal,
    DashboardConfigTagValueQualityModal,
    DashboardConfigGridComponent,
    DashboardConfigTagsGridSearchComponent,
    DashboardConfigTagTooltipDirective,
    DashboardConfigDeviceVirtualNodeComponent,
    DashboardLogComponent,
    DashboardLogGridComponent,
    DashboardLogExtraComponent,
    UserGridComponent,
    UserModal,
    UserResetPasswordModal,
    AppHealthComponent,
    AppBlankComponent,
    AppInfoComponent,
    LicenseComponent,
    AuthenticationLoginModal,
    AuthenticationPasswordModal,
    CheckRolePipe,
    AppPasswordComponent,
    HelpComponent,
    HelpAboutComponent,
    HelpUpdateComponent,
    HelpQuickStartComponent,
    HelpSupportComponent,
    HelpIniComponent,
    LogAuditGridComponent,
    LogMonitorComponent,
    LogMonitorGridComponent,
    LogSoeComponent,
    AlertStatusBarComponent,
    AlertLogModal,
    GridComponent,
    GridSearchComponent,
    GridPaginationComponent,
    TreeviewComponent,
    DynamicTreeviewComponent,
    DynamicTreeviewSelectComponent,
    CheckboxIndeterminateDirective,
    PanelComponent,
    BitmaskComponent,
    BitmaskEditorComponent,
    BitmaskAdvanceEditorComponent,
    ContextMenuComponent,
    DownloadComponent,
    DownloadModalComponent,
    ComboboxEditorMultiselectComponent,
    KeysPipe,
    SafeHtmlPipe,
    TranslateKeyPipe,
    GTWImagePipe,
    LogEntryContainPipe,
    GridContain,
    ResizableComponent,
    ResizableDirective,
    DraggableDirective,
    DropTargetDirective,
    ContextMenuDirective,
    TooltipDirective,
    TooltipComponent,
    TabComponent,
    TabsetComponent,
    LoaderComponent,
    DropdownDirective,
    UppercaseDirective,
    ShowPassword,
    CollapsiblePanel
  ],
  providers: [
    HttpClientModule,
    AlertService,
    PanelService,
    AuthenticationService,
    GlobalDataService,
    TooltipService,
    ContextMenuService,
    LoaderService,
    DashboardConfigTagEditorLogic,
    CheckRolePipe,
    LogEntryContainPipe,
    { provide: LocationStrategy, useClass: HashLocationStrategy },
    { provide: ApiUrl, useValue: "rest" },
    { provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptorService, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: AuthenticationInterceptorService, multi: true }
  ],
  bootstrap: [AppComponent],
  entryComponents: [UserModal, UserResetPasswordModal, AuthenticationLoginModal, AuthenticationPasswordModal, DashboardConfigTagEditorModal, DashboardConfigTagOptionsModal, DashboardConfigImportExportModal, DashboardConfigTagValueQualityModal, DownloadComponent, DownloadModalComponent, AlertLogModal]

})

export class AppModule {}
