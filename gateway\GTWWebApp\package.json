{"name": "tmw-gateway", "repository": "N/A", "license": "See License in Application", "version": "1.0.0", "description": "TMW SCADA Data Gateway Web UI", "devDependencies": {"del": "^6.0.0", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "systemjs-builder": "^0.16.13", "path": "^0.12.7", "gulp-rename": "^2.0.0", "gulp-typescript": "^5.0.1", "@types/node": "16.7.1", "typescript": "4.3.2"}, "dependencies": {"@angular/animations": "^12.2.1", "@angular/common": "^12.2.1", "@angular/compiler": "^12.2.1", "@angular/compiler-cli": "^12.2.1", "@angular/core": "^12.2.1", "@angular/forms": "^12.2.1", "@angular/platform-browser": "^12.2.1", "@angular/platform-browser-dynamic": "^12.2.1", "@angular/router": "^12.2.1", "@angular/upgrade": "^12.2.1", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "bootstrap": "^3.4.1", "core-js": "^3.17.0", "ngx-modialog-7": "^9.0.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.3.0", "systemjs": "0.21.6", "zone.js": "^0.11.4"}, "scripts": {"cmd": "npm typescript", "getNpmVersion": "npm -v", "getNodeVersion": "node -v", "updateNpm": "npm install npm@latest", "npm-audit": "npm audit", "npm-audit fix": "npm audit fix", "install-node": "npm install", "re-install-node": "npm rm -rf node_modules"}}