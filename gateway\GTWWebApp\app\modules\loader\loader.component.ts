import { Component, <PERSON><PERSON><PERSON><PERSON>} from "@angular/core";
import { LoaderService } from "./loader.service";
import { Subscription } from 'rxjs';

@Component({
  selector: "loaderComponent",
  template:`
    <div *ngIf="this.isLoading" class="block-click">
      <div class="center-loader">
        <img src="./images/GTWLogo.png" class="logo-loader">
        <div class="loader"></div>
      </div>
    </div>
  `
})

export class LoaderComponent implements OnDestroy{
  private isLoading: boolean = false;
  private isLoadingChangeSubscriptionShow: Subscription

  constructor(private loaderService: LoaderService) {
    this.isLoadingChangeSubscriptionShow = this.loaderService.isLoadingChange.subscribe(value => {this.isLoading = value});
  }

  public ngOnDestroy(): void {
    this.isLoadingChangeSubscriptionShow.unsubscribe();
  }
}