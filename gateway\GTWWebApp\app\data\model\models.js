System.register(["./BoundObjectDTO", "./BroadcastEventDTO", "./BroadcastEventTypeEnumDTO", "./ClassNodeDTO", "./EdgePairDTO", "./EditorCommandDTO", "./EditorCommandsDTO", "./EditorContextMenuDTO", "./EditorFieldObjectDTO", "./EditorSpecificationObjectDTO", "./HealthObjectDTO", "./LogEntryDTO", "./LogConfigMaskDTO", "./MappingObjectDTO", "./ProtocolTypesEnumDTO", "./SDGCheckAuthDTO", "./SDGConfigDTO", "./SDGHealthObjectDTO", "./EngineExitFailStateEnumDTO", "./TagCollectionObjectDTO", "./TagObjectDTO", "./TagValuesDTO", "./TagValueDTO", "./TreeNodeDTO", "./TargetTCPModeEnumDTO", "./TargetTypeEnumDTO", "./TreeNodePageInfoDTO", "./TreeNodeCollectionObjectDTO", "./UserObjectDTO", "./TagPurposeFilterEnumDTO", "./SDGAboutDTO", "./AuditLogEntryDTO", "./EngineStateEnumDTO", "./INIParamNodeDTO", "./LogDeviceDTO", "./logDeviceGetDTO"], function (exports_1, context_1) {
    "use strict";
    var __moduleName = context_1 && context_1.id;
    function exportStar_1(m) {
        var exports = {};
        for (var n in m) {
            if (n !== "default") exports[n] = m[n];
        }
        exports_1(exports);
    }
    return {
        setters: [
            function (BoundObjectDTO_1_1) {
                exportStar_1(BoundObjectDTO_1_1);
            },
            function (BroadcastEventDTO_1_1) {
                exportStar_1(BroadcastEventDTO_1_1);
            },
            function (BroadcastEventTypeEnumDTO_1_1) {
                exportStar_1(BroadcastEventTypeEnumDTO_1_1);
            },
            function (ClassNodeDTO_1_1) {
                exportStar_1(ClassNodeDTO_1_1);
            },
            function (EdgePairDTO_1_1) {
                exportStar_1(EdgePairDTO_1_1);
            },
            function (EditorCommandDTO_1_1) {
                exportStar_1(EditorCommandDTO_1_1);
            },
            function (EditorCommandsDTO_1_1) {
                exportStar_1(EditorCommandsDTO_1_1);
            },
            function (EditorContextMenuDTO_1_1) {
                exportStar_1(EditorContextMenuDTO_1_1);
            },
            function (EditorFieldObjectDTO_1_1) {
                exportStar_1(EditorFieldObjectDTO_1_1);
            },
            function (EditorSpecificationObjectDTO_1_1) {
                exportStar_1(EditorSpecificationObjectDTO_1_1);
            },
            function (HealthObjectDTO_1_1) {
                exportStar_1(HealthObjectDTO_1_1);
            },
            function (LogEntryDTO_1_1) {
                exportStar_1(LogEntryDTO_1_1);
            },
            function (LogConfigMaskDTO_1_1) {
                exportStar_1(LogConfigMaskDTO_1_1);
            },
            function (MappingObjectDTO_1_1) {
                exportStar_1(MappingObjectDTO_1_1);
            },
            function (ProtocolTypesEnumDTO_1_1) {
                exportStar_1(ProtocolTypesEnumDTO_1_1);
            },
            function (SDGCheckAuthDTO_1_1) {
                exportStar_1(SDGCheckAuthDTO_1_1);
            },
            function (SDGConfigDTO_1_1) {
                exportStar_1(SDGConfigDTO_1_1);
            },
            function (SDGHealthObjectDTO_1_1) {
                exportStar_1(SDGHealthObjectDTO_1_1);
            },
            function (EngineExitFailStateEnumDTO_1_1) {
                exportStar_1(EngineExitFailStateEnumDTO_1_1);
            },
            function (TagCollectionObjectDTO_1_1) {
                exportStar_1(TagCollectionObjectDTO_1_1);
            },
            function (TagObjectDTO_1_1) {
                exportStar_1(TagObjectDTO_1_1);
            },
            function (TagValuesDTO_1_1) {
                exportStar_1(TagValuesDTO_1_1);
            },
            function (TagValueDTO_1_1) {
                exportStar_1(TagValueDTO_1_1);
            },
            function (TreeNodeDTO_1_1) {
                exportStar_1(TreeNodeDTO_1_1);
            },
            function (TargetTCPModeEnumDTO_1_1) {
                exportStar_1(TargetTCPModeEnumDTO_1_1);
            },
            function (TargetTypeEnumDTO_1_1) {
                exportStar_1(TargetTypeEnumDTO_1_1);
            },
            function (TreeNodePageInfoDTO_1_1) {
                exportStar_1(TreeNodePageInfoDTO_1_1);
            },
            function (TreeNodeCollectionObjectDTO_1_1) {
                exportStar_1(TreeNodeCollectionObjectDTO_1_1);
            },
            function (UserObjectDTO_1_1) {
                exportStar_1(UserObjectDTO_1_1);
            },
            function (TagPurposeFilterEnumDTO_1_1) {
                exportStar_1(TagPurposeFilterEnumDTO_1_1);
            },
            function (SDGAboutDTO_1_1) {
                exportStar_1(SDGAboutDTO_1_1);
            },
            function (AuditLogEntryDTO_1_1) {
                exportStar_1(AuditLogEntryDTO_1_1);
            },
            function (EngineStateEnumDTO_1_1) {
                exportStar_1(EngineStateEnumDTO_1_1);
            },
            function (INIParamNodeDTO_1_1) {
                exportStar_1(INIParamNodeDTO_1_1);
            },
            function (LogDeviceDTO_1_1) {
                exportStar_1(LogDeviceDTO_1_1);
            },
            function (logDeviceGetDTO_1_1) {
                exportStar_1(logDeviceGetDTO_1_1);
            }
        ],
        execute: function () {
        }
    };
});
//# sourceMappingURL=models.js.map