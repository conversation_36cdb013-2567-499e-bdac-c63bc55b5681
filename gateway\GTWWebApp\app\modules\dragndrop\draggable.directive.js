System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, DraggableDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            DraggableDirective = (function () {
                function DraggableDirective() {
                    this.options = {};
                }
                Object.defineProperty(DraggableDirective.prototype, "draggable", {
                    get: function () {
                        return true;
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(DraggableDirective.prototype, "myDraggable", {
                    set: function (options) {
                        if (options) {
                            this.options = options;
                        }
                    },
                    enumerable: false,
                    configurable: true
                });
                DraggableDirective.prototype.onDragStart = function (event) {
                    var _a = this.options.data, data = _a === void 0 ? {} : _a;
                    if (typeof data === 'string')
                        event.dataTransfer.setData('text', data);
                    else
                        event.dataTransfer.setData('text', JSON.stringify(data));
                };
                __decorate([
                    core_1.HostBinding('draggable'),
                    __metadata("design:type", Object),
                    __metadata("design:paramtypes", [])
                ], DraggableDirective.prototype, "draggable", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object),
                    __metadata("design:paramtypes", [Object])
                ], DraggableDirective.prototype, "myDraggable", null);
                __decorate([
                    core_1.HostListener('dragstart', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [Object]),
                    __metadata("design:returntype", void 0)
                ], DraggableDirective.prototype, "onDragStart", null);
                DraggableDirective = __decorate([
                    core_1.Directive({
                        selector: '[myDraggable]'
                    }),
                    __metadata("design:paramtypes", [])
                ], DraggableDirective);
                return DraggableDirective;
            }());
            exports_1("DraggableDirective", DraggableDirective);
        }
    };
});
//# sourceMappingURL=draggable.directive.js.map