import { Component, OnInit } from "@angular/core";
import { AuthService } from "../data/api/api";
import { UserObjectDTO } from "../data/model/models";
import { AlertService } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";
import { Column } from "../modules/grid/column";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { UserModal } from "./user.modal";
import { UserResetPasswordModal } from "./user.reset.password.modal";
import { TranslateService } from "@ngx-translate/core";
import { UserRoleEnum } from '../authentication/authentication.service';
import { GlobalDataService } from "../global/global.data.service";

@Component({
  selector: "userGridComponent",
  styles: [`
		.button-add {
		  padding: 2px;
		  position: absolute;
		  top: 60px;
		  right: 30px;
		}
	`],
  template: `
		<div>
		  <div class="panel panel-default panel-main" style="width:99%; margin-left:6px; margin-top:60px">
        <div class="panel-heading"><img src="../../images/users.svg" class="module-icon" />{{'TR_USERS_MANAGEMENT' | translate}}
          <div class="button-add">
		        <button (click)="onNewUser()" class="btn btn-default"><div style="color:green;font-size: 12px;" class="glyph-button glyphicon glyphicon-plus" title="{{'TR_ADD_NEW_USER' | translate}}" ></div>&nbsp;{{'TR_ADD_NEW_USER' | translate}}</button>
          </div>
        </div>
	      <div class="panel-body">	
		      <gridComponent [rows]='users' [columns]='columns' (onClickActionGrid)='clickActionGrid($event)'></gridComponent>
	      </div>
      </div>
		</div>`
})

export class UserGridComponent implements OnInit {
  private users: Array<UserObjectDTO> = [];
  private columns: Array<Column>;
  private autenticatedUserNames: Array<string>;

  constructor(private modal: Modal, private authService: AuthService, private alertService: AlertService, private authenticationService: AuthenticationService, private globalDataService: GlobalDataService, private translate: TranslateService) {
    this.columns = this.getColumns();
  }

  public ngOnInit(): void {
    setInterval(() => {
      if (this.globalDataService.healthData && this.globalDataService.healthData.activeUsers && this.globalDataService.healthData.activeUsers.length > 0) {
        this.autenticatedUserNames = this.globalDataService.healthData.activeUsers;
        this.users.forEach((user) => {
          if (this.autenticatedUserNames.includes(user.username))
            user["authenticated"] = true;
          else
            user["authenticated"] = false;
        });
      }
    }, 5000);
    this.getUserList();
  }

  private getUserList(): void {
    this.authService.getUsers().subscribe(
      data => {
        this.users = data;
        this.users.forEach((user) => {
          this.translate.get("TR_" + UserRoleEnum[user.role]).subscribe(res => {
            user["roleName"] = res;
          });
        });
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_READ_USERS_DATABASE"); }
      }
    );
  }

  private onNewUser(): void {
    let newUser: UserObjectDTO = null;
    const userModalRef = this.modal.open(UserModal, overlayConfigFactory({ currentUser: newUser, users: this.users }, BSModalContext));
    userModalRef.result.then(
      (result) => {
        if (result != newUser) {
          this.authService.addUser(result.username, result.password, result.isactive, result.role, result.email).subscribe(
            data => {
              this.getUserList();
              this.alertService.success("TR_DATA_SAVED");
            },
            error => {
              if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_DATA_NOT_SAVED"); }
            }
          );
        }
      },
      (error) => { this.alertService.debug(error.toString()); }
    );
  }

  private clickActionGrid(actionObject:any): void {
    if (actionObject.action == "action$edit") {
      const userModalRef = this.modal.open(UserModal, overlayConfigFactory({ currentUser: actionObject.item, users: this.users }, BSModalContext));
      userModalRef.result.then(
        (result) => {
          if (result != actionObject.item) {
            this.authService.updateUser(result.username, result.isactive, result.role, result.email).subscribe(
              data => {
                this.getUserList();
                this.alertService.success("TR_DATA_SAVED");
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_DATA_NOT_SAVED"); }
              }
            );
          }
        },
        (error) => { this.alertService.debug(error.toString()); }
      );
    }
    else if (actionObject.column != null && actionObject.column.arg === "resetPassword") {
      const userModalRef = this.modal.open(UserResetPasswordModal, overlayConfigFactory({ currentUser: actionObject.item }, BSModalContext));
      userModalRef.result.then(
        (result) => {
          if (result != actionObject.item) {
            this.authService.resetUserPassword(result.username, result.password).subscribe(
              data => {
                this.alertService.success("TR_DATA_SAVED");
              },
              error => {
                if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_DATA_NOT_SAVED"); }
              }
            );
          }
        },
        (error) => { this.alertService.debug(error.toString()); }
      );
    }
    else if (actionObject.column != null && actionObject.column.arg === "logoffRemoteUser") {
      this.authService.logoffRemoteUser(actionObject.item.username).subscribe(
        (data: any) => {
          if (data && data.result == "true") {
            this.alertService.success("TR_USER_LOGGED_OUT_SUCCEFULLY");
          }
          else {
            this.alertService.error("TR_ERROR_USER_IS_NOT_LOGGED_IN");
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_LOGOFF_USER"); }
        }
      );
    }
  }

  private getColumns(): Array<Column> {
    return [
      new Column("username", "TR_USERNAME", "", ""),
      new Column("isactive", "TR_IS_ACTIVE", "", ""),
      new Column("authenticated", "TR_IS_LOGGED_IN", "", ""),
      new Column("email", "TR_EMAIL", "", ""),
      new Column("roleName", "TR_ROLE", "", ""),
      new Column("", "", "action$edit", "TR_EDIT"),
      new Column("", "", "action$image-click", "TR_RESET_PASSWORD", "resetPassword", "resetPassword"),
      new Column("", "", "action$image-click", "TR_LOGOFF_REMOTE_USER", "logoffRemoteUser", "logoff")
    ];
  }
}