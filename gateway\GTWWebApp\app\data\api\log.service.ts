/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import {
  HttpClient, HttpHeaders, HttpParams,
  HttpResponse, HttpEvent
} from '@angular/common/http';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { Observable } from 'rxjs';

import { LogConfigMaskDTO } from '../model/logConfigMaskDTO';
import { LogDeviceDTO } from '../model/logDeviceDTO';
import { LogDeviceGetDTO } from '../model/logDeviceGetDTO';
import { LogEntryDTO } from '../model/logEntryDTO';

import { BASE_PATH, COLLECTION_FORMATS } from '../variables';
import { Configuration } from '../configuration';


@Injectable()
export class LogService {

  public basePath = 'http://localhost/rest';
  public defaultHeaders = new HttpHeaders();
  public configuration = new Configuration();

  constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
    if (basePath) {
      this.basePath = basePath;
    }
    if (configuration) {
      this.configuration = configuration;
      this.basePath = basePath || configuration.basePath || this.basePath;
    }
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    for (const consume of consumes) {
      if (form === consume) {
        return true;
      }
    }
    return false;
  }


  /**
   * Retrieve last log entry id
   * Get last log entry id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getLastLogEntryID(observe?: 'body', reportProgress?: boolean): Observable<number>;
  public getLastLogEntryID(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<number>>;
  public getLastLogEntryID(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<number>>;
  public getLastLogEntryID(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<number>(`${this.basePath}/get_last_log_entry_id`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Get log items.
   * Get protocol data object items as array
   * @param startEntryID Retreive log entries starting with startEntryID
   * @param endEntryID Retreive log entries ending with endEntryID
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getLogEntriesRange(startEntryID?: number, endEntryID?: number, observe?: 'body', reportProgress?: boolean): Observable<Array<LogEntryDTO>>;
  public getLogEntriesRange(startEntryID?: number, endEntryID?: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<LogEntryDTO>>>;
  public getLogEntriesRange(startEntryID?: number, endEntryID?: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<LogEntryDTO>>>;
  public getLogEntriesRange(startEntryID?: number, endEntryID?: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {



    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    if (startEntryID !== undefined && startEntryID !== null) {
      queryParameters = queryParameters.set('startEntryID', <any>startEntryID);
    }
    if (endEntryID !== undefined && endEntryID !== null) {
      queryParameters = queryParameters.set('endEntryID', <any>endEntryID);
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<Array<LogEntryDTO>>(`${this.basePath}/get_log_entries_range`,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Retrieve severity and category masks for each of source: SDG&#x3D;0, SCL&#x3D;1, 6T&#x3D;2
   * Retrieve log filter masks
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getLogFilterConfig(observe?: 'body', reportProgress?: boolean): Observable<Array<LogConfigMaskDTO>>;
  public getLogFilterConfig(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<LogConfigMaskDTO>>>;
  public getLogFilterConfig(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<LogConfigMaskDTO>>>;
  public getLogFilterConfig(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<Array<LogConfigMaskDTO>>(`${this.basePath}/get_log_filter_config`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Retrieve filtered devices
   * Retrieve filtered devices
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getLogFilterDevice(observe?: 'body', reportProgress?: boolean): Observable<Array<LogDeviceGetDTO>>;
  public getLogFilterDevice(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<LogDeviceGetDTO>>>;
  public getLogFilterDevice(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<LogDeviceGetDTO>>>;
  public getLogFilterDevice(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<Array<LogDeviceGetDTO>>(`${this.basePath}/get_log_filter_device`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Retrieve max log entries to store on server
   * Get max log entries
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getMaxLogEntries(observe?: 'body', reportProgress?: boolean): Observable<number>;
  public getMaxLogEntries(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<number>>;
  public getMaxLogEntries(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<number>>;
  public getMaxLogEntries(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<number>(`${this.basePath}/get_max_log_entries`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Retrieve MirrorAllToLogFile setting for logger
   * Get MirrorAllToLogFile setting
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getMirrorAllToLogFile(observe?: 'body', reportProgress?: boolean): Observable<any>;
  public getMirrorAllToLogFile(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public getMirrorAllToLogFile(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public getMirrorAllToLogFile(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<any>(`${this.basePath}/get_mirror_all_to_log_file`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Retrieve number of lines in protocol buffer
   * Get number of lines
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getNumLogEntries(observe?: 'body', reportProgress?: boolean): Observable<number>;
  public getNumLogEntries(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<number>>;
  public getNumLogEntries(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<number>>;
  public getNumLogEntries(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<number>(`${this.basePath}/get_num_log_entries`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Set severity mask
   * Set severity mask
   * @param logconfigmasks Severity and category mask for each source
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public putLogFilterConfig(logconfigmasks: Array<LogConfigMaskDTO>, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public putLogFilterConfig(logconfigmasks: Array<LogConfigMaskDTO>, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public putLogFilterConfig(logconfigmasks: Array<LogConfigMaskDTO>, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public putLogFilterConfig(logconfigmasks: Array<LogConfigMaskDTO>, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (logconfigmasks === null || logconfigmasks === undefined) {
      throw new Error('Required parameter logconfigmasks was null or undefined when calling putLogFilterConfig.');
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.post<any>(`${this.basePath}/set_log_filter_config`,
      logconfigmasks,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Set device filter
   * Set device filter
   * @param logdevices Device name and action
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public putLogFilterDevice(logdevices: Array<LogDeviceDTO>, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public putLogFilterDevice(logdevices: Array<LogDeviceDTO>, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public putLogFilterDevice(logdevices: Array<LogDeviceDTO>, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public putLogFilterDevice(logdevices: Array<LogDeviceDTO>, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (logdevices === null || logdevices === undefined) {
      throw new Error('Required parameter logdevices was null or undefined when calling putLogFilterDevice.');
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.post<any>(`${this.basePath}/set_log_filter_device`,
      logdevices,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Set max log entries
   * Set max log entries
   * @param maxLogEntries Max log entries to store on server
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public putMaxLogEntries(maxLogEntries: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public putMaxLogEntries(maxLogEntries: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public putMaxLogEntries(maxLogEntries: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public putMaxLogEntries(maxLogEntries: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (maxLogEntries === null || maxLogEntries === undefined) {
      throw new Error('Required parameter maxLogEntries was null or undefined when calling putMaxLogEntries.');
    }

    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    if (maxLogEntries !== undefined && maxLogEntries !== null) {
      queryParameters = queryParameters.set('maxLogEntries', <any>maxLogEntries);
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/x-www-form-urlencoded'
    ];

    return this.httpClient.post<any>(`${this.basePath}/set_max_log_entries`,
      null,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * Set MirrorAllToLogFile setting for logger
   * Set MirrorAllToLogFile setting
   * @param mirrorAllToLogFile True to MirrorAllToLogFile
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public putMirrorAllToLogFile(mirrorAllToLogFile: boolean, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public putMirrorAllToLogFile(mirrorAllToLogFile: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public putMirrorAllToLogFile(mirrorAllToLogFile: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public putMirrorAllToLogFile(mirrorAllToLogFile: boolean, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (mirrorAllToLogFile === null || mirrorAllToLogFile === undefined) {
      throw new Error('Required parameter mirrorAllToLogFile was null or undefined when calling putMirrorAllToLogFile.');
    }

    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    if (mirrorAllToLogFile !== undefined && mirrorAllToLogFile !== null) {
      queryParameters = queryParameters.set('mirrorAllToLogFile', <any>mirrorAllToLogFile);
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/x-www-form-urlencoded'
    ];

    return this.httpClient.post<any>(`${this.basePath}/set_mirror_all_to_log_file`,
      null,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

}
