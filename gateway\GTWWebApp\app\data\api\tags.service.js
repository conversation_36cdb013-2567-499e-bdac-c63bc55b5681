System.register(["@angular/core", "@angular/common/http", "../encoder", "../variables", "../configuration"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, http_1, encoder_1, variables_1, configuration_1, TagsService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (encoder_1_1) {
                encoder_1 = encoder_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            }
        ],
        execute: function () {
            TagsService = (function () {
                function TagsService(httpClient, basePath, configuration) {
                    this.httpClient = httpClient;
                    this.basePath = 'http://localhost/rest';
                    this.defaultHeaders = new http_1.HttpHeaders();
                    this.configuration = new configuration_1.Configuration();
                    if (basePath) {
                        this.basePath = basePath;
                    }
                    if (configuration) {
                        this.configuration = configuration;
                        this.basePath = basePath || configuration.basePath || this.basePath;
                    }
                }
                TagsService.prototype.canConsumeForm = function (consumes) {
                    var form = 'multipart/form-data';
                    for (var _i = 0, consumes_1 = consumes; _i < consumes_1.length; _i++) {
                        var consume = consumes_1[_i];
                        if (form === consume) {
                            return true;
                        }
                    }
                    return false;
                };
                TagsService.prototype.deleteTags = function (body, parentObjectName, objectCollectionKind, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (body === null || body === undefined) {
                        throw new Error('Required parameter body was null or undefined when calling deleteTags.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (parentObjectName !== undefined && parentObjectName !== null) {
                        queryParameters = queryParameters.set('parentObjectName', parentObjectName);
                    }
                    if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
                        queryParameters = queryParameters.set('objectCollectionKind', objectCollectionKind);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    var httpContentTypeSelected = this.configuration.selectHeaderContentType(consumes);
                    if (httpContentTypeSelected != undefined) {
                        headers = headers.set('Content-Type', httpContentTypeSelected);
                    }
                    return this.httpClient.delete(this.basePath + "/tags", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress,
                        body: body
                    });
                };
                TagsService.prototype.getTag = function (tagName, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (tagName === null || tagName === undefined) {
                        throw new Error('Required parameter tagName was null or undefined when calling getTag.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (tagName !== undefined && tagName !== null) {
                        queryParameters = queryParameters.set('tagName', tagName);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/gettag", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                TagsService.prototype.getTags = function (nodeFullName, sortColumn, sortColumnDirection, valueTypeFilter, tagNameFilter, tagDescriptionFilter, tagAliasFilter, tagCollectionKindFilter, tagPurposeFilter, recursive, startIndex, endIndex, fieldSelectionMask, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (nodeFullName !== undefined && nodeFullName !== null) {
                        queryParameters = queryParameters.set('nodeFullName', nodeFullName);
                    }
                    if (sortColumn !== undefined && sortColumn !== null) {
                        queryParameters = queryParameters.set('sortColumn', sortColumn);
                    }
                    if (sortColumnDirection !== undefined && sortColumnDirection !== null) {
                        queryParameters = queryParameters.set('sortColumnDirection', sortColumnDirection);
                    }
                    if (valueTypeFilter !== undefined && valueTypeFilter !== null) {
                        queryParameters = queryParameters.set('valueTypeFilter', valueTypeFilter);
                    }
                    if (tagNameFilter !== undefined && tagNameFilter !== null) {
                        queryParameters = queryParameters.set('tagNameFilter', tagNameFilter);
                    }
                    if (tagAliasFilter !== undefined && tagAliasFilter !== null) {
                        queryParameters = queryParameters.set('tagAliasFilter', tagAliasFilter);
                    }
                    if (tagDescriptionFilter !== undefined && tagDescriptionFilter !== null) {
                        queryParameters = queryParameters.set('tagDescriptionFilter', tagDescriptionFilter);
                    }
                    if (tagPurposeFilter !== undefined && tagPurposeFilter !== null) {
                        queryParameters = queryParameters.set('tagPurposeFilter', tagPurposeFilter);
                    }
                    if (tagCollectionKindFilter !== undefined && tagCollectionKindFilter !== null) {
                        queryParameters = queryParameters.set('tagCollectionKindFilter', tagCollectionKindFilter);
                    }
                    if (tagPurposeFilter !== undefined && tagPurposeFilter !== null) {
                        queryParameters = queryParameters.set('tagPurposeFilter', tagPurposeFilter);
                    }
                    if (recursive !== undefined && recursive !== null) {
                        queryParameters = queryParameters.set('recursive', recursive);
                    }
                    if (startIndex !== undefined && startIndex !== null) {
                        queryParameters = queryParameters.set('startIndex', startIndex);
                    }
                    if (endIndex !== undefined && endIndex !== null) {
                        queryParameters = queryParameters.set('endIndex', endIndex);
                    }
                    if (fieldSelectionMask !== undefined && fieldSelectionMask !== null) {
                        queryParameters = queryParameters.set('fieldSelectionMask', fieldSelectionMask);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/tags", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                TagsService.prototype.postPoints = function (file, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (file === null || file === undefined) {
                        throw new Error('Required parameter file was null or undefined when calling postPoints.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'multipart/form-data'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    useForm = canConsumeForm;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (file !== undefined) {
                        formParams = formParams.append('file', file) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/tags", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                TagsService.prototype.setTagForm = function (tagName, tagValue, tagQuality, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (tagName === null || tagName === undefined) {
                        throw new Error('Required parameter tagName was null or undefined when calling setTagForm.');
                    }
                    if (tagValue === null || tagValue === undefined) {
                        throw new Error('Required parameter tagValue was null or undefined when calling setTagForm.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (tagName !== undefined) {
                        formParams = formParams.append('tagName', tagName) || formParams;
                    }
                    if (tagValue !== undefined) {
                        formParams = formParams.append('tagValue', tagValue) || formParams;
                    }
                    if (tagQuality !== undefined) {
                        formParams = formParams.append('tagQuality', tagQuality) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/settag", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                TagsService.prototype.setTags = function (body, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (body === null || body === undefined) {
                        throw new Error('Required parameter body was null or undefined when calling setTags.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    var httpContentTypeSelected = this.configuration.selectHeaderContentType(consumes);
                    if (httpContentTypeSelected != undefined) {
                        headers = headers.set('Content-Type', httpContentTypeSelected);
                    }
                    return this.httpClient.post(this.basePath + "/settags", body, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                TagsService.prototype.setTagsFiltered = function (valueTypeFilter, tagValue, nodeFullName, tagNameFilter, tagDescriptionFilter, tagAliasFilter, tagPurposeFilter, recursive, tagQuality, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (valueTypeFilter === null || valueTypeFilter === undefined) {
                        throw new Error('Required parameter valueTypeFilter was null or undefined when calling setTagsFiltered.');
                    }
                    if (tagValue === null || tagValue === undefined) {
                        throw new Error('Required parameter tagValue was null or undefined when calling setTagsFiltered.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/x-www-form-urlencoded'
                    ];
                    var canConsumeForm = this.canConsumeForm(consumes);
                    var formParams;
                    var useForm = false;
                    var convertFormParamsToString = false;
                    if (useForm) {
                        formParams = new FormData();
                    }
                    else {
                        formParams = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    }
                    if (nodeFullName !== undefined) {
                        formParams = formParams.append('nodeFullName', nodeFullName) || formParams;
                    }
                    if (valueTypeFilter !== undefined) {
                        formParams = formParams.append('valueTypeFilter', valueTypeFilter) || formParams;
                    }
                    if (tagNameFilter !== undefined) {
                        formParams = formParams.append('tagNameFilter', tagNameFilter) || formParams;
                    }
                    if (tagAliasFilter !== undefined) {
                        formParams = formParams.append('tagAliasFilter', tagAliasFilter) || formParams;
                    }
                    if (tagNameFilter !== undefined) {
                        formParams = formParams.append('tagPurposeFilter', tagPurposeFilter) || formParams;
                    }
                    if (tagDescriptionFilter !== undefined) {
                        formParams = formParams.append('tagDescriptionFilter', tagDescriptionFilter) || formParams;
                    }
                    if (recursive !== undefined) {
                        formParams = formParams.append('recursive', recursive) || formParams;
                    }
                    if (tagValue !== undefined) {
                        formParams = formParams.append('tagValue', tagValue) || formParams;
                    }
                    if (tagQuality !== undefined) {
                        formParams = formParams.append('tagQuality', tagQuality) || formParams;
                    }
                    return this.httpClient.post(this.basePath + "/set_tags_filtered", convertFormParamsToString ? formParams.toString() : formParams, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                TagsService = __decorate([
                    core_1.Injectable(),
                    __param(1, core_1.Optional()),
                    __param(1, core_1.Inject(variables_1.BASE_PATH)),
                    __param(2, core_1.Optional()),
                    __metadata("design:paramtypes", [http_1.HttpClient, String, configuration_1.Configuration])
                ], TagsService);
                return TagsService;
            }());
            exports_1("TagsService", TagsService);
        }
    };
});
//# sourceMappingURL=tags.service.js.map