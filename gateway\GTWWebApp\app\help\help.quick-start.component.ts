﻿import { Component, OnInit, Input } from "@angular/core";
import { GlobalDataService } from "../global/global.data.service";

@Component({
  selector: "helpQuickStartComponent",
  styles: [`
      .panel {
        position: absolute;
        z-index: 999;
        max-width: 500px;
        margin: auto;
        width: 50%;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
      }
      .panel-heading{
        padding: 4px;
      }
      .panel-body{
        padding: 8px;
      }
      .div-image{
        max-width: 220px;
        display:inline-block;
        text-align: center;
      }
      .image-gif{
        max-width: 50%;
        max-height: 50%;
        border: solid 1px gray;
        display: block;
        margin-right: auto;
        margin-left: auto;
      }
      .context-menu-image-gif:hover{
        background: url('../images/help/contextMenuPlaying.gif');
        background-size: auto;
        background-repeat: no-repeat;
        padding: 440px 470px 0px 0px;
        position: absolute;
      }
      .mapping-image-gif:hover{
        background: url(../images/help/mappingPlaying.gif);
        background-size: auto;
        background-repeat: no-repeat;
        padding: 440px 470px 0px 0px;
        position: absolute;
      }
      .dashboard-image-gif:hover{
        background: url(../images/help/dashboardPlaying.gif);
        background-size: 100%;
        background-repeat: no-repeat;
        padding: 600px 787px 0px 0px;
        margin:-200px;
        position: absolute;
      }
      .search-tags-image-gif:hover{
        background: url(../images/help/searchTagsPlaying.gif);
        background-size: 100%;
        background-repeat: no-repeat;
        padding: 600px 787px 0px 0px;
        margin:-200px;
        position: absolute;
      }
      .button-close {
        position: absolute;
        top: 6px;
        right: 6px;
      }
      `
    ],
    template: `
      <div class="panel-main panel panel-default" *ngIf="isQuickStartVisible" style="min-width:400px">
        <div class="panel-heading">
          {{'TR_QUICK_START' | translate}}
          <div class="button-close round-button" title="{{'TR_CLOSE' | translate}}" (click)="close()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
        </div>
        <div class="panel-body">
          <h4>{{'TR_QUICK_START_TOPICS' | translate}}</h4>
          <ul>
            <li><a href="../../help/Content/QUICK-START/Dashboard-Management.htm" target="_blank">{{'TR_QUICK_START_DASHBOARD_MANAGEMENT' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/Default-Dashboard.htm" target="_blank">{{'TR_QUICK_START_DEFAULT_DASHBOARD' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/Device-Tree-View.htm" target="_blank">{{'TR_QUICK_START_DEVICE_TREE_VIEW' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/Tags-Grid.htm" target="_blank">{{'TR_QUICK_START_TAGS_GRIDS' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/Mapping.htm" target="_blank">{{'TR_QUICK_START_MAPPING' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/Editor.htm" target="_blank">{{'TR_QUICK_START_EDITOR' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/SDG-Workspaces.htm" target="_blank">{{'TR_QUICK_START_WORKSPACES' | translate}}</a></li>
            <li><a href="../../help/Content/QUICK-START/SDG-Settings.htm" target="_blank">{{'TR_QUICK_START_SYSTEM_SETTINGS' | translate}}</a></li>
            <li><a href="../../help/Content/USERS/Enable-Users.htm" target="_blank">{{'TR_QUICK_START_USERS_MANAGEMENT' | translate}}</a></li>
            <li><a href="../../help/Content/LICENSING/Trial-License.htm" target="_blank">{{'TR_QUICK_START_LICENSES_MANAGEMENT' | translate}}</a></li>
          </ul>
          <div class="div-image">
            <h4>{{'TR_HOW_TO_ADD_A_DEVICE' | translate}}</h4>
            <img class="image-gif context-menu-image-gif" src="../images/help/contextMenuPaused.gif">
          </div>
          <div class="div-image">
            <h4>{{'TR_HOW_TO_MAP_POINTS' | translate}}</h4>
            <img class="image-gif mapping-image-gif" src="../images/help/mappingPaused.gif">
          </div>
          <div class="div-image">
            <h4>{{'TR_HOW_ADD_VIEWS_TO_DASHBOARD' | translate}}</h4>
            <img class="image-gif dashboard-image-gif" src="../images/help/dashboardPaused.gif">
          </div>
          <div class="div-image">
            <h4>{{'TR_HOW_SEARCH_TAGS' | translate}}</h4>
            <img class="image-gif search-tags-image-gif" src="../images/help/searchTagsPaused.gif">
          </div>
          <br>
          <br>
          <label class="form-check-label">
            <input type="checkbox" class="form-check" [checked]="this.globalDataService.isQuickStartVisible" (change)="quickStartShowHideOnChange()" />
            {{'TR_SHOW_QUICK_START_AT_STARTUP' | translate}}
          </label>
        </div>
      </div>`
})

export class HelpQuickStartComponent implements OnInit{
  private isQuickStartVisible: boolean = false;
  constructor(private globalDataService: GlobalDataService) { }

  public ngOnInit(): void {
    if (localStorage.getItem("SDGIsQuickStartVisible") === "false") {
      this.globalDataService.isQuickStartVisible = false;
    }
    else {
      this.globalDataService.isQuickStartVisible = true;
      this.isQuickStartVisible = true;
    }
  }

  public show() {
    this.isQuickStartVisible = true;
  }

  private close() {
    this.isQuickStartVisible = false;
  }

  private quickStartShowHideOnChange(): void {
    this.globalDataService.isQuickStartVisible = !this.globalDataService.isQuickStartVisible;
    if (!this.globalDataService.isQuickStartVisible)
      this.isQuickStartVisible = false;
  }
}