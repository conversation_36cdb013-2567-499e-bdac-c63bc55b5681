{"name": "swagger-editor", "description": "Swagger Editor", "version": "3.0.11", "main": "dist/swagger-editor.js", "repository": "**************:swagger-api/swagger-editor.git", "license": "Apache-2.0", "contributors": ["(in alphabetical order)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "scripts": {"build": "npm run build-core && npm run build-bundle && npm run build-standalone", "build-bundle": "webpack --config webpack-dist-bundle.config.js --colors", "build-core": "webpack --config webpack-dist.config.js --colors", "build-standalone": "webpack --config webpack-standalone.config.js --colors", "deps-check": "npm run deps-license && npm run deps-size", "deps-license": "license-checker --production --csv --out $npm_package_config_deps_check_dir/licenses.csv && license-checker --development --csv --out $npm_package_config_deps_check_dir/licenses-dev.csv", "deps-size": "webpack -p --config webpack.check.js --json | webpack-bundle-size-analyzer >| $npm_package_config_deps_check_dir/sizes.txt", "predev": "npm install", "dev": "npm-run-all --parallel hot-server open-dev watch", "hot-server": "webpack-dev-server --host 0.0.0.0 --config webpack-hot-dev-server.config.js --inline --hot --progress", "open-dev": "sleep 3 && node -e 'require(\"open\")(\"http://localhost:3200\")'", "open-static": "node -e 'require(\"open\")(\"http://localhost:3001\")'", "lint": "eslint --cache --ext '.js,.jsx' src test", "lint-errors": "eslint --cache --quiet --ext '.js,.jsx' src test", "lint-fix": "eslint --cache --ext '.js,.jsx' src test --fix", "test": "npm run lint-errors && npm run just-test-in-node", "test-in-node": "npm run lint-errors && npm run just-test-in-node", "just-test": "karma start --config karma.conf.js", "just-test-in-node": "mocha --recursive --compilers js:babel-core/register test/plugins", "serve-static": "http-server -i -a 0.0.0.0 -p 3001", "prestart": "npm install", "start": "npm-run-all --parallel serve-static open-static", "watch": "webpack --config webpack.config.js --watch --progress"}, "dependencies": {"boron": "^0.2.3", "classnames": "^2.1.3", "immutable": "^3.x.x", "js-yaml": "^3.5.5", "json-beautify": "^1.0.1", "jsonschema": "^1.1.0", "react": "^15.x", "react-addons-css-transition-group": "^15.4.2", "react-dd-menu": "^2.0.0", "react-dom": "^15.x", "react-file-download": "^0.3.2", "react-redux": "^4.x.x", "react-transition-group": "^1.1.1", "redux": "^3.x.x", "swagger-client": "~3.0.10", "swagger-ui": "^3.0.9", "whatwg-fetch": "^2.0.3"}, "devDependencies": {"autoprefixer": "6.6.1", "babel-core": "^6.23.1", "babel-eslint": "^6.1.2", "babel-loader": "^6.3.2", "babel-plugin-module-alias": "^1.6.0", "babel-preset-es2015": "^6.22.0", "babel-preset-es2015-ie": "^6.6.2", "babel-preset-react": "^6.23.0", "babel-preset-stage-0": "^6.22.0", "babel-runtime": "^6.23.0", "brace": "^0.10.0", "css-loader": "0.22.0", "deep-extend": "^0.4.1", "deepmerge": "^1.3.2", "eslint": "^2.13.1", "eslint-plugin-react": "^4.3.0", "expose-loader": "^0.7.3", "extract-text-webpack-plugin": "0.8.2", "file-loader": "0.8.4", "git-describe": "^4.0.1", "html-webpack-plugin": "^2.28.0", "http-server": "^0.9.0", "imports-loader": "0.6.5", "json-loader": "0.5.3", "karma": "^0.13.22", "karma-chrome-launcher": "^0.2.3", "karma-mocha": "^0.2.2", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "1.8.0", "less": "2.5.3", "less-loader": "2.2.1", "license-checker": "^8.0.4", "lodash": "^4.17.4", "matcher": "^0.1.2", "mocha": "^3.2.0", "npm-run-all": "3.1.1", "null-loader": "0.1.1", "open": "0.0.5", "postcss-loader": "0.7.0", "promise-worker": "^1.1.1", "raw-loader": "0.5.1", "react-ace": "^4.1.6", "react-hot-loader": "^1.3.1", "react-immutable-proptypes": "^2.1.0", "reselect": "^2.5.4", "rimraf": "^2.6.0", "standard": "^8.6.0", "standard-loader": "^5.0.0", "style-loader": "0.13.0", "url-loader": "0.5.6", "webpack": "^1.14.0", "webpack-bundle-size-analyzer": "^2.5.0", "worker-loader": "^0.7.1", "yaml-js": "^0.1.4"}, "config": {"deps_check_dir": ".deps_check"}, "browserslist": ["> 1%", "last 2 versions", "IE 10"], "optionalDependencies": {"webpack-dev-server": "1.14.0"}}