﻿import { Injectable } from '@angular/core';
import { Panel } from './panel';

@Injectable()
export class PanelService {
  public panelList: Panel[] = [];
  public panelLayoutDirection: TMW_LAYOUT_DASHBOARD_DIRECTION;

  constructor() { }

  public panelInit(): void {
    try {
      if (this.panelLayoutDirection == null) {
        if (localStorage.getItem("SDGDashboardLayoutDirection") != null)
          this.panelLayoutDirection = Number(localStorage.getItem("SDGDashboardLayoutDirection"));
        else
          this.panelLayoutDirection = TMW_LAYOUT_DASHBOARD_DIRECTION.HORIZONTAL;
      }
    }
    catch (err) {
      this.panelLayoutDirection = TMW_LAYOUT_DASHBOARD_DIRECTION.HORIZONTAL;
    }

    if (this.panelList.length == 0) {
      this.panelList.push(new Panel("SDGConfigPanel_1", "configPanel.svg", "TR_CONFIG_VIEW_1", "", 0, 0, 0, 0, 0, "dashboardConfigComponent", true, "", "panel-heading-config-bg"));
      this.panelList.push(new Panel("SDGConfigPanel_2", "configPanel.svg", "TR_CONFIG_VIEW_2", "", 0, 0, 0, 0, 0, "dashboardConfigComponent", false, "", "panel-heading-config-bg"));
      this.panelList.push(new Panel("SDGConfigPanel_3", "configPanel.svg", "TR_CONFIG_VIEW_3", "", 0, 0, 0, 0, 0, "dashboardConfigComponent", false, "", "panel-heading-config-bg"));
      this.panelList.push(new Panel("SDGLogPanel", "dataLog.svg", "TR_LOG", "", 0, 0, 0, 0, 0, "dashboardLogComponent", true, ""));
      this.toggleLayoutDasboard(this.panelLayoutDirection);
      try {
        let lsDashboardLayout = localStorage.getItem("SDGDashboardLayout");
        if (lsDashboardLayout != null) {
          let lsPanelList = JSON.parse(lsDashboardLayout);
          this.panelList.forEach((panel, index) => {
            lsPanelList.forEach((lsPanel, index) => {
              if (panel.lsName === lsPanel.lsName) {
                panel.width = lsPanel.width;
                panel.height = lsPanel.height;
                panel.x = lsPanel.x;
                panel.y = lsPanel.y;
                panel.zindex = lsPanel.zindex;
                panel.visible = lsPanel.visible;
                panel.parameters = lsPanel.parameters;
              }
            })
          })
        }
      }
      catch (err) {
        localStorage.removeItem("SDGDashboardLayout");
      }
    }
  }


  public toggleLayoutDasboard(layoutDirection: TMW_LAYOUT_DASHBOARD_DIRECTION): void {
    this.panelLayoutDirection = layoutDirection;

    let panelCount = this.panelList.filter(panel => panel.visible).length;
    let topMargin = 60;
    let rightMargin = 5;
    let leftMargin = 5;
    let bottomMargin = 10;
    let gutter = 10;

    if (layoutDirection == TMW_LAYOUT_DASHBOARD_DIRECTION.VERTICAL) {
      this.panelList.filter(panel => panel.visible).forEach((panel, index) => {
        panel.width = (window.innerWidth / panelCount) - ((rightMargin + leftMargin) / panelCount) - gutter;
        panel.height = window.innerHeight - topMargin - (gutter * 2) - bottomMargin;
        panel.x = (panel.width * index) + rightMargin + (gutter * index);
        panel.y = topMargin;
      });
    }
    else {
      this.panelList.filter(panel => panel.visible).forEach((panel, index) => {
        panel.width = window.innerWidth - rightMargin - leftMargin - gutter;
        panel.height = ((window.innerHeight - topMargin - bottomMargin - gutter) / panelCount) - (gutter);
        panel.x = rightMargin;
        panel.y = (panel.height * index) + topMargin + (gutter * index);
      });
    }
  }

  public toggleLayoutDasboardCustom(): void {
    try {
      let lsDashboardLayout = localStorage.getItem("SDGDashboardLayout");
      if (lsDashboardLayout != null) {
        let lsPanelList = JSON.parse(lsDashboardLayout);
        this.panelList.forEach((panel, index) => {
          lsPanelList.forEach((lsPanel, index) => {
            if (panel.lsName === lsPanel.lsName) {
              panel.width = lsPanel.width;
              panel.height = lsPanel.height;
              panel.x = lsPanel.x;
              panel.y = lsPanel.y;
              panel.zindex = lsPanel.zindex;
              panel.visible = lsPanel.visible;
              panel.parameters = lsPanel.parameters;
            }
          })
        })
        this.panelLayoutDirection = TMW_LAYOUT_DASHBOARD_DIRECTION.CUSTOM;
      }
    } catch (err) {
      localStorage.removeItem("SDGDashboardLayout");
    }
  }
}
export const enum TMW_LAYOUT_DASHBOARD_DIRECTION {
  CUSTOM = 0,
  HORIZONTAL = 1,
  VERTICAL = 2
}