import { Input, HostListener, Directive, HostBinding } from '@angular/core';

export interface DraggableOptions {
  data?: any;
}

@Directive({
  selector: '[myDraggable]'
})
export class DraggableDirective {
  constructor() {
    
  }
  
  @HostBinding('draggable')
  get draggable() {
    return true;
  }
  
  @Input()
  set myDraggable(options: DraggableOptions) {
    if (options) {
      this.options = options;
    }
  }
  
  private options: DraggableOptions = {};
  
  @HostListener('dragstart', ['$event'])
  onDragStart(event) {
    const { data = {} } = this.options;

    if (typeof data === 'string')
      event.dataTransfer.setData('text', data);
    else
      event.dataTransfer.setData('text', JSON.stringify(data));
  }
}