﻿import { Component, OnInit, HostListener } from "@angular/core";
import { ConfigService } from "../data/api/api";
import { AuthenticationService } from "../authentication/authentication.service";
import { PanelService, TMW_LAYOUT_DASHBOARD_DIRECTION } from '../modules/panel/panel.service';
import { Panel } from '../modules/panel/panel';

@Component({
  selector: 'dashboardComponent',
  host: { "style": "height: 100%" },
  styles: [`
      .unselectable {
			  -moz-user-select: -moz-none;
			  -khtml-user-select: none;
			  -webkit-user-select: none;
			  -ms-user-select: none;
			  user-select: none;
			}
      `
  ],
  template: `
		<div *ngFor="let panel of this.panelService.panelList" class="unselectable">
			<panelComponent class="dashboard-panel" [panel]="panel" *ngIf="(this.authenticationService.role | checkRole:'VIEWER_ROLE') && (panel.visible === true)" (onPanelCloseParent)=this.panelService.toggleLayoutDasboard(this.panelLayoutDirection)></panelComponent>
		</div>
		`
})

export class DashboardComponent implements OnInit {

  constructor(private panelService: PanelService, private authenticationService: AuthenticationService, private configService: ConfigService) {}

  public ngOnInit(): void {

    this.panelService.panelInit();

    this.configService.getConfig().subscribe(
      data => {
        let currentWorkSpaceName = data.currentWorkSpaceName;
        this.panelService.panelList.forEach((panel, index) => {
          panel.titleToolTip = currentWorkSpaceName;
        });
        if (this.panelService.panelLayoutDirection != TMW_LAYOUT_DASHBOARD_DIRECTION.CUSTOM)
          this.panelService.toggleLayoutDasboard(this.panelService.panelLayoutDirection);
      }
    );
  }

  @HostListener('window:beforeunload', ['$event'])
  beforeunloadHandler(event) {
    localStorage.setItem("SDGDashboardLayout", JSON.stringify(this.panelService.panelList));
    localStorage.setItem("SDGDashboardLayoutDirection", String(this.panelService.panelLayoutDirection));
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (this.panelService.panelLayoutDirection != TMW_LAYOUT_DASHBOARD_DIRECTION.CUSTOM) 
      this.panelService.toggleLayoutDasboard(this.panelService.panelLayoutDirection);
  }
}

