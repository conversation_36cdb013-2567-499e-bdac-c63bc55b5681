{"version": 3, "file": "HealthWSApi.js", "sourceRoot": "", "sources": ["HealthWSApi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAmBE,qBAAoB,YAA0B,EAAU,SAA2B;oBAA/D,iBAAY,GAAZ,YAAY,CAAc;oBAAU,cAAS,GAAT,SAAS,CAAkB;oBAT5E,aAAQ,GAAW,qBAAqB,CAAC;oBACzC,kBAAa,GAAkB,IAAI,6BAAa,EAAE,CAAC;oBAGlD,sBAAiB,GAAG,KAAK,CAAC;oBAC1B,sBAAiB,GAAG,CAAC,CAAC;oBACtB,yBAAoB,GAAG,CAAC,CAAC;oBACzB,sBAAiB,GAAG,IAAI,CAAC;gBAEsD,CAAC;gBAEjF,mCAAa,GAApB;oBACE,IAAI,CAAC,IAAI,CAAC,OAAO;wBACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC;gBACtB,CAAC;gBAEM,2BAAK,GAAZ;oBACE,IAAI,IAAI,CAAC,SAAS,EAAE;wBAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;wBACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;qBACrB;gBACH,CAAC;gBAEO,4BAAM,GAAd;oBAAA,iBAcC;oBAbC,IAAM,UAAU,GAAG,iBAAU,CAAC,MAAM,CAClC,UAAC,GAAkB;wBACjB,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAK,EAAE,CAAC,CAAC;oBAEnB,IAAM,QAAQ,GAAG;wBACf,IAAI,EAAE,UAAC,IAAY;4BACjB,IAAI,KAAI,CAAC,SAAS,IAAI,KAAI,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;gCAClE,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;6BAC3C;wBACH,CAAC;qBACF,CAAC;oBACF,OAAO,cAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC9C,CAAC;gBAEO,sCAAgB,GAAxB,UAAyB,GAAkB;oBAA3C,iBAsCC;oBArCC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;wBAC9B,IAAI,KAAK,GAAG,EAAE,CAAC;wBACf,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ;4BAC/B,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;;4BAE3G,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;wBAE9G,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;wBAEtC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAC,SAAS;4BAEhC,KAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;4BAE3B,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCACnF,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC;wBAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAI9C,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAC,KAAK,IAAO,CAAC,CAAC;wBAExC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAC,GAAG;4BAE3B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;gCAC5B,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;oCACtG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oCAC7B,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gCACvB,CAAC,CAAC,CAAC;6BACJ;iCACI;gCACH,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;6BACvB;wBACH,CAAC,CAAC;qBACH;gBACH,CAAC;gBAEO,iCAAW,GAAnB,UAAoB,GAAkB;oBAAtC,iBAoBC;oBAlBC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAEvD,UAAU,CAAC;4BACT,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,KAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCAClI,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;4BACH,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;wBAC7B,CAAC,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;qBACxE;yBACI;wBAGH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;4BAC3G,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,KAAK,EAAE,CAAC;qBACd;gBACH,CAAC;gBArGU,WAAW;oBADvB,iBAAU,EAAE;qDAWuB,4BAAY,EAAqB,uBAAgB;mBAVxE,WAAW,CAsGvB;gBAAD,kBAAC;aAAA,AAtGD;;QAsGC,CAAC"}