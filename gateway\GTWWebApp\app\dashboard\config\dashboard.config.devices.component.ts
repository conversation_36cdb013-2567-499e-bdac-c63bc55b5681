﻿import { Component, AfterViewInit, Output, Input, EventEmitter, OnDestroy, ViewChild, ViewContainerRef } from "@angular/core";
import { Router } from "@angular/router";
import { Subscription, Observable } from "rxjs"
import { TreeNodeDTO, BroadcastEventTypeEnumDTO, SDGHealthObjectDTO, LogDeviceDTO } from "../../data/model/models";
import { AlertService, messageLogMask } from "../../modules/alert/alert.service";
import { AuthenticationService } from "../../authentication/authentication.service";
import { VirtualNode, DashboardConfigDeviceVirtualNodeComponent } from "./dashboard.config.device.virtual-node.component";
import { NodesService, ManageService, TagsService, MappingsService, FileService, LogService } from "../../data/api/api";
import { NodesWSApi, BroadcastEventWSApi } from "../../data/wsApi/wsApi";
import { TranslateService } from "@ngx-translate/core";
import { Panel } from '../../modules/panel/panel';
import { GlobalJsonUtil } from '../../global/global.json.util';
import { LoaderService } from "../../modules/loader/loader.service";
import { DashboardConfigImportExportModal } from "./dashboard.config.import-export.modal";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { map } from "rxjs/operators";

@Component({
  selector: "dashboardConfigDevicesComponent",
  styles: [`
      .title {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        vertical-align: middle;
        display: table-cell;
        width: 100%;
      }
      .panel-content {
        padding: 6px
      }
      .panel-heading {
        padding: 2px 6px;
        width: 100%;
        border: 1px solid #f5f5f5;
        border-radius: 3px;
      }
      .cell{
        display: table-cell;
        padding: 2px;
      }
      `
  ],
  template: `
      <div>
        <div class="panel-heading shadow-bg">
          <div *ngIf="isActive" class="glyphicon glyphicon-chevron-down glyphicon-size cell" title="{{'TR_COLLAPSE' | translate}}" (click)="toggle()"></div>
          <div *ngIf="!isActive" class="glyphicon glyphicon-chevron-right glyphicon-size cell" title="{{'TR_EXPAND' | translate}}" (click)="toggle()"></div>
          <div class="title">{{'TR_VIEWS' | translate}}</div>
        </div>
        <div class="panel-content shadow-bg" [ngClass]="{hide: !isActive}">
          <dashboardConfigDeviceVirtualNodeComponent #dashboardConfigDeviceVirtualNodeComponent [isDevicesTreeViewSelected]="isDevicesTreeViewSelected" [virtualNodeList]="virtualNodeList" (onSelectedChanged)="onSelectedVirtualNode($event)"></dashboardConfigDeviceVirtualNodeComponent>
        </div>
        <dashboardConfigDevicesSearchComponent (onSearchChanged)="onSearchChanged($event)"></dashboardConfigDevicesSearchComponent>
        <dashboardConfigDevicesTreeViewComponent [deviceNode]="deviceRootNode" [deviceRootNode]=deviceRootNode [isDevicesTreeViewSelected]="isDevicesTreeViewSelected" [selectedNode]="selectedNode" [isNodeTreeFromSearch]="isNodeTreeFromSearch" (onSelectedChanged)="onSelectNode($event)" (onDropNode)="onDrop($event)" (onToggleNode)="onToggleNode($event)"></dashboardConfigDevicesTreeViewComponent>
      </div>
      <ng-container #container></ng-container>
  `
})

export class DashboardConfigDevicesComponent implements AfterViewInit, OnDestroy {
  @ViewChild("dashboardConfigDeviceVirtualNodeComponent", { static: false }) dashboardConfigDeviceVirtualNodeComponent: DashboardConfigDeviceVirtualNodeComponent;
  @Input("panel") panel: Panel;
  @Output() onSelectedChanged: EventEmitter<object> = new EventEmitter();
  @Output() onDropNode: EventEmitter<any> = new EventEmitter();
  @ViewChild("container", { read: ViewContainerRef, static: true }) container!: ViewContainerRef;

  private globalBroadcastServiceSubscription: Subscription;
  private nodeServiceSubscription: Subscription;
  private selectedNode: TreeNodeDTO;
  private deviceRootNode: TreeNodeDTO;
  private isNodeTreeFromSearch: boolean = false;
  private isDevicesTreeViewSelected: boolean = true;
  private virtualNodeList: Array<VirtualNode> = [];
  private nodesWebsocket: WebSocket;
  private isActive: boolean;
  private nodesWSReconnectAttempts: number = 0;

  constructor(private alertService: AlertService, private manageService: ManageService, private nodesService: NodesService,
    private nodesWSApi: NodesWSApi, private broadcastEventWSApi: BroadcastEventWSApi,
    private authenticationService: AuthenticationService, private translate: TranslateService,
    private loaderService: LoaderService, private fileService: FileService,
    private tagsService: TagsService, private mappingsService: MappingsService,
    private logService: LogService, private modal: Modal, private router: Router) { }

  public ngAfterViewInit(): void {
    this.manageService.healthGET().subscribe(
      data => {
        if (data) {
          let healthObject: SDGHealthObjectDTO = data;
          if (healthObject.isIniCsvDirty)
            this.panel.panelHeadingCSS = "panel-heading-config-dirty-bg";
          else
            this.panel.panelHeadingCSS = "panel-heading-config-bg";
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_SAVED"); }
      }
    );
    this.openGlobalBroadcastServiceSubscription();
    this.nodesWSApi.openWebsocket().subscribe(
      data => {
        this.nodesWebsocket = data;
        this.loadNodesTreeviewInit();
        this.wsGetNodeData();
      },
      error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getNodes", reason: "Open fail" }); }
    );

    this.virtualNodeList.push({ name: "warning", label: "TR_WARNING_VIEW", description: "TR_WARNING_DESC", isHealthy: true, icon: "warningView.svg" });
    this.virtualNodeList.push({ name: "health", label: "TR_HEALTH_VIEW", description: "TR_HEALTH_DESC", isHealthy: true, icon: "healthView.svg" });
    this.virtualNodeList.push({ name: "performance", label: "TR_PERFORMANCE_VIEW", description: "TR_PERFORMANCE_DESC", isHealthy: true, icon: "performanceView.svg" });
  }

  public ngOnDestroy(): void {
    if (this.nodesWebsocket != null && this.nodesWebsocket.readyState === WebSocket.OPEN)
      this.nodesWebsocket.close(3000, "panelClose");
    if (this.nodeServiceSubscription != null)
      this.nodeServiceSubscription.unsubscribe();
    if (this.globalBroadcastServiceSubscription != null)
      this.globalBroadcastServiceSubscription.unsubscribe();
  }

  public saveConfig() {
    this.loaderService.toggleIsLoading(true);
    this.manageService.saveFile().subscribe(
      data => {
        this.alertService.success("TR_FILE_SAVED");
        this.loaderService.toggleIsLoading(false);
      },
      error => {
        if (error.status == 401) {
          this.authenticationService.onLoginFailed("/");
        }
        else {
          this.alertService.show({ messageKey: "TR_THERE_WERE_PROBLEMS_SAVING_THE_INI_CSV_FILES", messageLogMask: messageLogMask.alertPopup, messageType: BroadcastEventTypeEnumDTO.MessageError }, false);
          this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
        }
        this.loaderService.toggleIsLoading(false);
      },
      () => this.loaderService.toggleIsLoading(false)
    );
  }

  public onWarningOnRootActiveChange(node: TreeNodeDTO): void {
    this.deviceRootNode = node;
    node.isWarningActive = !node.isWarningActive
    localStorage.setItem("SDGIsWarningOnRootActive", node.isWarningActive.toString());
  }

  public expandChild(node: TreeNodeDTO, rootNode: TreeNodeDTO): void {
    this.deviceRootNode = rootNode;
    node.isExpanded = true;
    if (node.hasChildren) {
      this.nodesService.getNodes(node.nodeFullName, null, false, false, <any>node.nodeCollectionKind.toString())
      .subscribe(
        data => {
          if (node.hasChildren) {
            node.hasChildren = data.hasChildren;
            node.isExpanded = true;
            node.children = data.children;
            this.expandCollapseChildRecursively(node, true);
          }
          this.sendCurrentNodesToWS()
        }
      );
    }
  }

  public collapseChild(node: TreeNodeDTO): void {
    this.expandCollapseChildRecursively(node, false);
  }

  public importExportCSV(nodeFullName: string): void {
    const dashboardConfigImportExportModalRef = this.modal.open(DashboardConfigImportExportModal, overlayConfigFactory({ nodeFullName: nodeFullName, tagsService: this.tagsService, mappingsService: this.mappingsService, fileService: this.fileService}, BSModalContext));
    dashboardConfigImportExportModalRef.result.then(
      (result) => {},
      (error) => { this.alertService.debug(error.toString()); }
    );
  }

  public addNodeToLogFilter(nodeFullName: string): void {
    let logDevices: Array<LogDeviceDTO> = [];
    logDevices.push({ name: nodeFullName, add: true });
    this.logService.putLogFilterDevice(logDevices)
    .subscribe(
      data => {
        this.alertService.success("TR_NODE_ADDED");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR"); }
      }
    );
  }

  private openGlobalBroadcastServiceSubscription(): void {
    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(
      event => {
        if (event.type === 'message') {
          let broadcastEventData = JSON.parse(event.data);
          if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.RefreshUI.toString() && broadcastEventData.parameters != null && broadcastEventData.parameters.refreshWebBrowser == null) {
            let objectName = null;
            let objectCollectionKind = null;
            if (broadcastEventData.parameters != null) {
              if (broadcastEventData.parameters.objectName != null)
                objectName = broadcastEventData.parameters.objectName;
              if (broadcastEventData.parameters.objectCollectionKind)
                objectCollectionKind = broadcastEventData.parameters.objectCollectionKind;
            }
            this.panel.panelHeadingCSS = "panel-heading-config-dirty-bg";
            this.loadNodeTreeview(objectName, objectCollectionKind, false).subscribe(value => {
              this.sendCurrentNodesToWS();
            });
          }
          else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.RefreshDirtyFlag.toString()) {
            if (broadcastEventData.parameters != null && broadcastEventData.parameters.flag == false)
              this.panel.panelHeadingCSS = "panel-heading-config-bg";
            else if (broadcastEventData.parameters != null && broadcastEventData.parameters.flag == true)
              this.panel.panelHeadingCSS = "panel-heading-config-dirty-bg";
          }
        }
      }
    );
  }

  private toggle(): void {
    this.isActive = !this.isActive;
  }

  private expandCollapseChildRecursively(node: TreeNodeDTO, isExpanded: boolean): void {
    node.isExpanded = isExpanded;
    if (node.hasChildren && node.children != null) {
      for (var i = 0; i < node.children.length; i++) {
        node.children[i].isExpanded = isExpanded;
        if (node.children[i].hasChildren && node.children != null)
          this.expandCollapseChildRecursively(node.children[i], isExpanded);
      }
    }
  }

  private onSearchChanged(searchResult: any): void {
    let node: TreeNodeDTO = searchResult.data;
    let isNodeTreeFromSearch: boolean = searchResult.isNodeTreeFromSearch
    this.deviceRootNode = node;
    this.isNodeTreeFromSearch = isNodeTreeFromSearch;
    if (node)
      this.onSelectNode(node);
  }

  private onSelectedVirtualNode(virtualNode: VirtualNode): void {
    this.selectedNode = this.deviceRootNode;
    this.isDevicesTreeViewSelected = false;
    this.onSelectedChanged.emit({ selectedNode: this.selectedNode, virtualNodeName: virtualNode.name });
    this.panel.parameters = virtualNode.name
    this.isActive = true;
  }

  private onSelectNode(node: TreeNodeDTO): void {
    this.selectedNode = node;
    this.isDevicesTreeViewSelected = true;
    this.onSelectedChanged.emit({ selectedNode: this.selectedNode, virtualNodeName: null });
    this.panel.parameters = "GatewayRootNode"
  }

  private onToggleNode(node: any): void {
    this.sendCurrentNodesToWS();
  }

  private sendCurrentNodesToWS(): void {
    //BroadcastEventTypeEnumDTO.RefreshUI might call this method while the WebSocket is not open yet (in case of reconnect)
    if (this.nodesWebsocket != null && this.nodesWebsocket.readyState === WebSocket.OPEN) {
      let nodelist: Array<any> = this.listExpandedNodes(this.deviceRootNode);
      let jsonWSNodesList = JSON.stringify(nodelist);
      this.nodesWebsocket.send(jsonWSNodesList);
    }
  }

  private listExpandedNodes(currentNode: TreeNodeDTO): Array<any> {
    let nodelist: Array<any> = [];
    if (currentNode.children != null) {
      for (let currentNodeChild of currentNode.children) {
        let nodeChildlist: Array<TreeNodeDTO> = this.listExpandedNodes(currentNodeChild);
        nodelist.push({
          "nodeFullName": currentNodeChild.nodeFullName,
          "nodeCollectionKind": currentNodeChild.nodeCollectionKind
        });
        if (nodeChildlist.length > 0) {
          for (let nodeChild of nodeChildlist) {
            nodelist.push(nodeChild);
          }
        }
      }
    }
    return nodelist;
  }

  private wsGetNodeData(): void {
    this.nodeServiceSubscription = this.nodesWSApi.getNodesData(this.nodesWebsocket).subscribe((event) => {
      if (event.type === 'message') {
        this.nodesWSReconnectAttempts = 0;
        let wsNodes: Array<TreeNodeDTO> = JSON.parse(event.data);
        let rootIsHealthy: boolean = true;
        wsNodes.forEach((wsNode) => {
          let wsNodeIsHealthy: boolean = this.isNodeHealthyWS(this.deviceRootNode, wsNode);
          if (!wsNodeIsHealthy)
            rootIsHealthy = wsNodeIsHealthy;
        });
        this.deviceRootNode.isHealthy = rootIsHealthy;
        this.virtualNodeList[0].isHealthy = rootIsHealthy;
        this.virtualNodeList[1].isHealthy = rootIsHealthy;
      }
      else if (event.type === 'close') {
        if (this.nodesWSReconnectAttempts < 3) {
          setTimeout(() => {
            const currentPath: string = this.router.url;
            const pageName: string = currentPath.split('/').pop();
            if (currentPath !== "/dashboard" && currentPath !== "/config" && currentPath !== "/")
              return;
            this.nodesWSReconnectAttempts++;
            this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getNodes", reconnectAttempt: this.nodesWSReconnectAttempts }).subscribe(res => {
              this.alertService.debug(res);
            });
            this.nodesWSApi.openWebsocket().subscribe(
              data => {
                this.nodesWebsocket = data;
                this.sendCurrentNodesToWS();
                this.wsGetNodeData();
              },
              error => { this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getNodes", reason: "Reopen fail" }); }
            );
          }, 5000);
        }
      }
    });
  }

  private isNodeHealthyWS(node: TreeNodeDTO, wsNode: TreeNodeDTO): boolean {
    let nodeIsHealthy: boolean = true;
    if (node.nodeFullName == wsNode.nodeFullName && node.nodeCollectionKind == wsNode.nodeCollectionKind) {
      node.isHealthy = wsNode.isHealthy;
      node.nodeDescription = wsNode.nodeDescription;
      if (!node.isHealthy)
        nodeIsHealthy = false;
    }
    node?.children?.forEach((nodeChild) => {
      let wsNodeAddress = wsNode.nodeFullName.split(".");
      let nodeChildAddress = nodeChild.nodeFullName.split(".");
      if (nodeChild.nodeFullName == wsNode.nodeFullName && nodeChild.nodeCollectionKind == wsNode.nodeCollectionKind) {
        nodeChild.isHealthy = wsNode.isHealthy;
        nodeChild.nodeDescription = wsNode.nodeDescription;
        if (!nodeChild.isHealthy)
          nodeIsHealthy = false;
      }
      else if (wsNodeAddress[0] == nodeChildAddress[0] && nodeChild.nodeCollectionKind == wsNode.nodeCollectionKind && nodeChild.children != null && nodeChild.children.length !=0) {
        if (!this.isNodeHealthyWS(nodeChild, wsNode))
          nodeIsHealthy = false;
      }
    });
    return nodeIsHealthy;
  }

  private loadNodesTreeviewInit(): void {
    try {
      this.nodesService.getNodes(null, null, false, true, "ALL").subscribe(
        data => {
          if (data.children)
            data.children = GlobalJsonUtil.sortByProperty(data.children, "nodeFullName");
          this.deviceRootNode = data
          if (data) {
            this.selectedNode = data;
            this.sendCurrentNodesToWS();
            if ((localStorage.getItem("SDGIsWarningOnRootActive") != null && localStorage.getItem("SDGIsWarningOnRootActive") == "true") || localStorage.getItem("SDGIsWarningOnRootActive") == null)
              this.deviceRootNode.isWarningActive = true;
            else
              this.deviceRootNode.isWarningActive = false;
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        },
        () => {
          if (this.panel.parameters != "" && (this.panel.parameters == "warning" || this.panel.parameters == "health" || this.panel.parameters == "performance")) {
            let selectedVirtualNode: VirtualNode = this.virtualNodeList.filter(vn => vn.name == this.panel.parameters)[0];
            this.dashboardConfigDeviceVirtualNodeComponent.onSelectVirtualNode(selectedVirtualNode);
          }
          else {
            this.onSelectNode(this.deviceRootNode);
          }
        }
      );
    }
    catch (err) {
      return null;
    }
  }

  private loadNodeTreeview(nodeFullName: string, nodeCollectionKind: any, onlyFirstChildrenLevel = true): Observable<void> {
    try {
      return this.nodesService.getNodes(nodeFullName, null, false, onlyFirstChildrenLevel, nodeCollectionKind).pipe(
      map(
        data => {
          let deviceNode: TreeNodeDTO = this.deviceRootNode;
          if (nodeFullName != null) {
            deviceNode = this.searchTreeNode(this.deviceRootNode, nodeFullName, nodeCollectionKind);
            if (deviceNode == null)
              return;
            if (deviceNode != this.selectedNode)
              this.onSelectNode(deviceNode);
          }
          this.RefreshNodeTreeview(deviceNode, data);
          this.loadNodeTreeviewChildren(deviceNode, data);
          //if (this.deviceRootNode?.children !== null)
          //  this.deviceRootNode.children = GlobalJsonUtil.sortByProperty(this.deviceRootNode.children, "nodeFullName");
          if (deviceNode?.children !== null && typeof deviceNode?.children !== 'undefined')
            deviceNode.children = GlobalJsonUtil.sortByProperty(deviceNode.children, "nodeFullName");
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
        }
      ));
    }
    catch (err) {
      return null;
    }
  }

  private RefreshNodeTreeview(deviceNode: TreeNodeDTO, data: TreeNodeDTO): void {
    try {
      deviceNode.nodeDescription = data.nodeDescription;
    }
    catch (err) {
    }
  }

  private loadNodeTreeviewChildren(deviceNode: TreeNodeDTO, data: TreeNodeDTO): void {
    try {
      let deviceNodeChildren = deviceNode.children == null ? [] : deviceNode.children;
      let dataChildren = data.children == null ? [] : data.children;
      let addedNodes = this.compareJSON(dataChildren, deviceNodeChildren);
      let removedNodes = this.compareJSON(deviceNodeChildren, dataChildren);
      for (let addedNode of addedNodes) {
        if (!deviceNode.children)
          deviceNode.children = [];
        deviceNode.children.push(addedNode);
        deviceNode.hasChildren = data.hasChildren;
        if (this.selectedNode != null && this.selectedNode.hasChildren)
          this.selectedNode.isExpanded = true;
      }
      for (let removedNode of removedNodes) {
        let removedNodeIndex = deviceNode.children.findIndex(({ nodeFullName, nodeCollectionKind }) => nodeFullName == removedNode.nodeFullName && nodeCollectionKind == removedNode.nodeCollectionKind);
        if (removedNodeIndex != -1) {
          deviceNode.children.splice(removedNodeIndex, 1);
          deviceNode.hasChildren = data.hasChildren;
        }
      }
      for (let deviceNodeChildren of deviceNode.children) {
        for (let dataChildren of data.children) {
          if (deviceNodeChildren.hasChildren && dataChildren.hasChildren && deviceNodeChildren.nodeName == dataChildren.nodeName) {
            this.loadNodeTreeviewChildren(deviceNodeChildren, dataChildren);
          }
        }
      }
    }
    catch (err) {
    }
  }

  private searchTreeNode(element: TreeNodeDTO, nodeFullName: string, nodeCollectionKind: string): TreeNodeDTO {
    if (element.nodeFullName == nodeFullName && element.nodeCollectionKind.toString() == nodeCollectionKind) {
      return element;
    }
    else if (element.children != null) {
      let result = null;
      for (let i = 0; result == null && i < element.children.length; i++) {
        result = this.searchTreeNode(element.children[i], nodeFullName, nodeCollectionKind);
      }
      return result;
    }
    return null;
  }

  private onDrop(dragData: any): void {
    this.onDropNode.emit(dragData);
  }

  private compareJSON(array1: any, array2: any): any {
    let keys: Array<any> = Object.keys(array2.reduce((a, { nodeFullName, isUsedFor }) => Object.assign(a, { [nodeFullName + "_" + isUsedFor]: undefined }), {}));
    return array1.filter(({ nodeFullName, isUsedFor }) => !keys.includes(nodeFullName + "_" + isUsedFor))
  }
}