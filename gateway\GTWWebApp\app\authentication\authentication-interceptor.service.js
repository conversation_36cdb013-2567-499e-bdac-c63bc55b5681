System.register(["@angular/core", "@angular/common/http", "./authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, http_1, authentication_service_1, AuthenticationInterceptorService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            AuthenticationInterceptorService = (function () {
                function AuthenticationInterceptorService(authenticationService) {
                    this.authenticationService = authenticationService;
                }
                AuthenticationInterceptorService.prototype.intercept = function (request, next) {
                    request = request.clone({
                        params: (request.params ? request.params : new http_1.HttpParams())
                            .set('token', this.authenticationService.userApiConfig.apiKeys["Authorization"])
                    });
                    return next.handle(request);
                };
                AuthenticationInterceptorService = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService])
                ], AuthenticationInterceptorService);
                return AuthenticationInterceptorService;
            }());
            exports_1("AuthenticationInterceptorService", AuthenticationInterceptorService);
        }
    };
});
//# sourceMappingURL=authentication-interceptor.service.js.map