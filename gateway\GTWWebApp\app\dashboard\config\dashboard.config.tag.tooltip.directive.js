System.register(["@angular/core", "@ngx-translate/core", "../../modules/alert/alert.service", "../../authentication/authentication.service", "../../data/model/models", "../../data/api/api", "../../modules/angular-tooltip/tooltip-item", "../../modules/angular-tooltip/tooltip.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, core_2, alert_service_1, authentication_service_1, models_1, api_1, tooltip_item_1, tooltip_service_1, DashboardConfigTagTooltipDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (tooltip_item_1_1) {
                tooltip_item_1 = tooltip_item_1_1;
            },
            function (tooltip_service_1_1) {
                tooltip_service_1 = tooltip_service_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagTooltipDirective = (function () {
                function DashboardConfigTagTooltipDirective(translateService, tooltipService, alertService, authenticationService, tagsService) {
                    this.translateService = translateService;
                    this.tooltipService = tooltipService;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.tagsService = tagsService;
                }
                DashboardConfigTagTooltipDirective.prototype.onMouseEnter = function (event) {
                    var _this = this;
                    this.tooltipId = setTimeout(function () {
                        _this.loadTooltip(event);
                    }, 2000);
                    event.preventDefault();
                };
                DashboardConfigTagTooltipDirective.prototype.onMouseLeave = function (event) {
                    clearTimeout(this.tooltipId);
                    this.tooltipService.hide.next({ event: event, obj: null });
                };
                DashboardConfigTagTooltipDirective.prototype.loadTooltip = function (event) {
                    var _this = this;
                    var tooltipItems = [];
                    this.tagsService.getTag(this.tagFullName).subscribe(function (data) {
                        if (data != null) {
                            _this.translateService.get("TR_NAME").subscribe(function (res) {
                                tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + data.tagName));
                            });
                            if (data.tagUserName) {
                                _this.translateService.get("TR_USER_TAG_NAME").subscribe(function (res) {
                                    tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + data.tagUserName));
                                });
                            }
                            _this.translateService.get("TR_VALUE").subscribe(function (res) {
                                tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + data.tagValue));
                            });
                            _this.translateService.get("TR_QUALITY").subscribe(function (res) {
                                tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + data.tagQuality));
                            });
                            _this.translateService.get("TR_TIME").subscribe(function (res) {
                                tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + data.tagTime));
                            });
                            _this.translateService.get("TR_OPTIONS").subscribe(function (res) {
                                tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + data.tagOptions));
                            });
                            _this.translateService.get("TR_TYPE").subscribe(function (res) {
                                var valueType = models_1.TagObjectDTO.getTagPropertyMaskStringValue(data.tagPropertyMask, models_1.TagObjectDTO.TagPropertyMaskEnum.VALUE_TYPE);
                                tooltipItems.push(new tooltip_item_1.TooltipItem(res + ": " + valueType));
                            });
                        }
                        if (tooltipItems.length > 0) {
                            _this.tooltipService.show.next({ event: event, obj: tooltipItems });
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    });
                };
                __decorate([
                    core_1.Input("dashboardConfigTagTooltipDirective"),
                    __metadata("design:type", Object)
                ], DashboardConfigTagTooltipDirective.prototype, "tagFullName", void 0);
                DashboardConfigTagTooltipDirective = __decorate([
                    core_1.Directive({
                        selector: "[dashboardConfigTagTooltipDirective]",
                        host: {
                            "(mouseenter)": "onMouseEnter($event)",
                            "(mouseleave)": "onMouseLeave($event)"
                        }
                    }),
                    __metadata("design:paramtypes", [core_2.TranslateService, tooltip_service_1.TooltipService, alert_service_1.AlertService, authentication_service_1.AuthenticationService, api_1.TagsService])
                ], DashboardConfigTagTooltipDirective);
                return DashboardConfigTagTooltipDirective;
            }());
            exports_1("DashboardConfigTagTooltipDirective", DashboardConfigTagTooltipDirective);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.tooltip.directive.js.map