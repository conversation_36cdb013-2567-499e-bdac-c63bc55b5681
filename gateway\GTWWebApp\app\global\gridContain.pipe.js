System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var core_1, GridContain;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            GridContain = (function () {
                function GridContain() {
                }
                GridContain.prototype.transform = function (rows, columnFilter) {
                    try {
                        if (rows == null || !Array.isArray(rows) || rows.length == 0)
                            return;
                        return rows.filter(function (rowFiltered) {
                            var propertyNames = Object.getOwnPropertyNames(rowFiltered);
                            var isRowValid = true;
                            propertyNames.forEach(function (propertyName) {
                                if (columnFilter[propertyName] && rowFiltered[propertyName].toString().toLowerCase().indexOf(columnFilter[propertyName].toLowerCase()) == -1)
                                    isRowValid = false;
                            });
                            if (isRowValid) {
                                return rowFiltered;
                            }
                        });
                    }
                    catch (err) {
                        console.log(err);
                    }
                };
                GridContain = __decorate([
                    core_1.Pipe({ name: "gridContain", pure: false })
                ], GridContain);
                return GridContain;
            }());
            exports_1("GridContain", GridContain);
        }
    };
});
//# sourceMappingURL=gridContain.pipe.js.map