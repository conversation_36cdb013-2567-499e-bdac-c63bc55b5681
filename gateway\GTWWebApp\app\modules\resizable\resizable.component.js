System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, ResizableComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            ResizableComponent = (function () {
                function ResizableComponent() {
                    this.width = null;
                }
                ResizableComponent.prototype.onResize = function (width) {
                    this.width = width;
                };
                __decorate([
                    core_1.HostBinding("style.width.px"),
                    __metadata("design:type", Number)
                ], ResizableComponent.prototype, "width", void 0);
                ResizableComponent = __decorate([
                    core_1.Component({
                        selector: "th[resizable]",
                        template: "\n\t<div class=\"wrapper\">\n\t\t<div class=\"content\">\n\t\t\t<ng-content></ng-content>\n\t\t</div>\n\t\t<div class=\"bar\" (resizable)=\"onResize($event)\"></div>\n\t</div>\n\t\t\t",
                        styles: ["\n\t\t:host:last-child .bar {\n\t\t\t display: none;\n\t\t}\n\t\t.wrapper {\n\t\t\t display: flex;\n\t\t\t justify-content: flex-end;\n\t\t}\n\t\t.content {\n\t\t\t flex: 1;\n\t\t}\n\t\t.bar {\n\t\t\twidth: 4px;\n\t\t\tjustify-self: flex-end;\n\t\t\tborder-left: 1px solid transparent;\n\t\t\tborder-right: 1px solid transparent;\n\t\t\tbackground: #ffffff;\n\t\t\tcursor: ew-resize;\n\t\t}\n\t\t.bar:hover, .bar:active {\n\t\t\t opacity: 1;\n\t\t}"],
                    })
                ], ResizableComponent);
                return ResizableComponent;
            }());
            exports_1("ResizableComponent", ResizableComponent);
        }
    };
});
//# sourceMappingURL=resizable.component.js.map