﻿export namespace TagPurposeFilterEnumDTO {
  export enum TagPurposeFilterEnum {
    GTWTYPES_TAG_PURPOSE_MASK_HEALTH = 0x01,
    GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE = 0x02,
    GTWTYPES_TAG_PURPOSE_MASK_DATA = 0x04,
    GTWTYPES_TAG_PURPOSE_ALL = GTWTYPES_TAG_PURPOSE_MASK_HEALTH | GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE | GTWTYPES_TAG_PURPOSE_MASK_DATA,
    GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY = 0x08,
    GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV = 0x10,
    GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV = 0x20,
    GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO = 0x40
  }
}