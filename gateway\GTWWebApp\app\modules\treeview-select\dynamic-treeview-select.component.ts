import { Component, Input, Output, EventEmitter, OnInit} from "@angular/core";
import { NodeSelect } from './node-select';

@Component({
  selector: "dynamicTreeviewSelectComponent",
  styles: [`
		  .iconButton {
			  display: inline-block; 
			  margin-right: 5px;
			  cursor: pointer;
			  color:#ecc96a;
		  }
		  .selectNode {

		  }
		  ul {
			  padding-left: 12px;
			  list-style-type: none;
		  }
		  li{
        padding-left: 2px;
			  margin-top: 4px;
		  }
		  ol, ul {
			  margin-top: 0;
		  }
      .leaf:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
        font-weight: bold;
      }
      .leaf{
			  cursor: pointer;
      }
      .glyphicon-none {
        padding-right: 12px;  
        color: transparent !important;
      }
  `],
  template: `
    <ul>
			  <div *ngIf="!node.hasChildren" class="glyphicon-none iconButton"></div>
			  <div *ngIf="node.hasChildren && node.isExpanded" class="glyphicon glyphicon-minus iconButton" title="{{TR_COLLAPSE_CHILDREN | translate}}" (click)="nodeToggle(node)"></div>
			  <div *ngIf="node.hasChildren && !node.isExpanded" class="glyphicon glyphicon-plus iconButton" title="{{TR_EXPAND_CHILDREN | translate}}" (click)="nodeToggle(node);"></div>
        <span><input type="checkbox" [indeterminate]="node.indeterminated" [checked]="node.checked" (change)="nodeCheck(node, $event)"/>&nbsp;{{node.nodeName}}</span>
      <ng-container *ngIf="node.isExpanded">
        <li *ngFor="let nodeChildren of node.children">
          <dynamicTreeviewSelectComponent [node]="nodeChildren" [(selectedNodeFullName)]="selectedNodeFullName" (selectedNodeFullNameChange)="clickAction($event)" (selectedNodeLoadChildren)="nodeToggle($event)"></dynamicTreeviewSelectComponent>
        </li>
      </ng-container>
    </ul>
    `
})

export class DynamicTreeviewSelectComponent implements OnInit{
  @Input() node: NodeSelect;
  @Input() selectedNodeFullName;
  @Output() selectedNodeFullNameChange: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectedNodeLoadChildren: EventEmitter<NodeSelect> = new EventEmitter<NodeSelect>();

  public ngOnInit(): void {
  }

  private clickAction(selectedNodeFullName: string): void {
    this.selectedNodeFullName = selectedNodeFullName;
    this.selectedNodeFullNameChange.emit(this.selectedNodeFullName);
  }

  private nodeToggle(node: NodeSelect): void {
    if (node.hasChildren && node.children.length === 0)
      this.selectedNodeLoadChildren.emit(node);
    else if (node.hasChildren)
      node.toggle();
  }

  private nodeCheck(node: NodeSelect, event): void {
    //if (node.hasChildren && node.children.length === 0) {
    //  node.checked = event.target.checked;
    //  this.selectedNodeLoadChildren.emit(node);
    //}
    //else {
      node.check(event.target.checked, node);
/*    }*/
  }
}