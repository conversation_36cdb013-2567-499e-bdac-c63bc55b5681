﻿import { Directive, Input } from "@angular/core";
import { ContextMenuService } from "./context-menu.service";

@Directive({
  selector: "[context-menu]",
  host: { "(contextmenu)": "rightClicked($event)" }
})

export class ContextMenuDirective {
  @Input("context-menu") contextMenuitems;

  constructor(private contextMenuService: ContextMenuService) {
  }

  private rightClicked(event: MouseEvent): void {
    this.contextMenuService.show.next({ event: event, obj: this.contextMenuitems });
    event.preventDefault();
  }
}