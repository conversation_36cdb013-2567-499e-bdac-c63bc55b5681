﻿import { Component, OnInit } from "@angular/core";
import { AuthenticationService } from "../authentication/authentication.service";
import { PanelService, TMW_LAYOUT_DASHBOARD_DIRECTION } from '../modules/panel/panel.service';
import { Panel } from '../modules/panel/panel';

@Component({
  selector: 'dashboardManagerComponent',
  host: { "style": "height: 100%" },
  styles: [`
			.layoutButton {
        background-color: transparent;
        border: none;
        color: white;
			}
      .panel-config-icon{
        width: 20px;
        height: 20px;
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
        vertical-align: top;
        margin: 3px
      }
      .title{
        text-align: center;
        font-weight: bold;
        font-size: 14px;
        margin: -4px 0px 6px 0px;
      }
      .btn-panel{
        text-align: center;
        margin: 14px 6px -12px 6px;
        background-color: black !important;
      }
      .btn-direction{
        margin: 0px 2px 0px 2px;
      }
      .btn-direction:hover{
        background-color: #cdcdcd !important;
      }
      `
  ],
  template: `
		<div class="layoutButton panel panel-default">
      <div class="title">{{'TR_DASHBOARD_MANAGER' | translate}}</div>
			<div *ngFor="let panel of this.panelService.panelList" >
					<input type="checkbox" name="options" class="form-check" value="{{panel.lsName}}" [(ngModel)]="panel.visible" (change)="updatePanelList(panel, $event)"/>
					<img [src]="'../../images/' + panel.icon" class="panel-config-icon" *ngIf="panel.icon!=''"/> {{panel.title | translate}}
			</div>
			<div class="btn-panel">
			  <button type="button"	class="btn-xs btn-default btn-direction" [ngStyle]="{'background-color': this.panelService.panelLayoutDirection == 0 ? '#65e577' : 'transparent'}" (click)="this.panelService.toggleLayoutDasboardCustom()" title="{{'TR_CUSTOM_DIPLAY' | translate}}"><img src="../../images/customSplit.svg" class="panel-config-icon" /></button>
			  <button type="button"	class="btn-xs btn-default btn-direction" [ngStyle]="{'background-color': this.panelService.panelLayoutDirection == 1 ? '#65e577' : 'transparent'}" (click)="this.panelService.toggleLayoutDasboard(1)" title="{{'TR_VERTICAL_DISPLAY' | translate}}"><img src="../../images/verticalSplit.svg" class="panel-config-icon" /></button>
			  <button type="button" class="btn-xs btn-default btn-direction" [ngStyle]="{'background-color': this.panelService.panelLayoutDirection == 2 ? '#65e577' : 'transparent'}" (click)="this.panelService.toggleLayoutDasboard(2)" title="{{'TR_HORIZONTAL_DISPLAY' | translate}}"><img src="../../images/horizontalSplit.svg" class="panel-config-icon" /></button>
      </div>		
    </div>
		`
})

export class DashboardManagerComponent implements OnInit {
  private isPanelConfigOpen: boolean;

  constructor(private panelService: PanelService) {}

  public ngOnInit(): void {
    if (this.panelService.panelList.length == 0) {
      let lsDashboardLayout = localStorage.getItem("SDGDashboardLayout");
      if (lsDashboardLayout == null) {
        this.isPanelConfigOpen = true;
      }
    }
  }

  private updatePanelList(eventPanel: Panel, event): void {
    if (this.panelService.panelLayoutDirection != TMW_LAYOUT_DASHBOARD_DIRECTION.CUSTOM) {
      this.panelService.toggleLayoutDasboard(this.panelService.panelLayoutDirection);
    }
    else {
      if (eventPanel.visible) {
        eventPanel.zindex = 1;
      }
    }
  }
}

