System.register(["@angular/core", "../../authentication/authentication.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, authentication_service_1, DashboardConfigGridComponent, Column, Cell;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            }
        ],
        execute: function () {
            DashboardConfigGridComponent = (function () {
                function DashboardConfigGridComponent(authenticationService) {
                    this.authenticationService = authenticationService;
                    this.onClickActionGrid = new core_1.EventEmitter();
                    this.onSortChange = new core_1.EventEmitter();
                    this.selectedRows = [];
                    this.firstSelectedRowIndex = -1;
                }
                DashboardConfigGridComponent.prototype.ngOnChanges = function (changes) {
                    this.selectedRows = [];
                };
                DashboardConfigGridComponent.prototype.selectedAllRows = function (selectedRows, rows) {
                    var _this = this;
                    rows.forEach(function (row) {
                        _this.addRemoveArrayByIndex(selectedRows, row, false);
                    });
                };
                DashboardConfigGridComponent.prototype.unSelectedAllRows = function (selectedRows) {
                    selectedRows.splice(0, selectedRows.length);
                };
                DashboardConfigGridComponent.prototype.trackByFn = function (index, item) {
                    return index;
                };
                DashboardConfigGridComponent.prototype.clickActionSelect = function (tag, index, event) {
                    if (this.firstSelectedRowIndex > index && event.shiftKey) {
                        for (var i = this.firstSelectedRowIndex; i >= index; i--) {
                            this.addRemoveArrayByIndex(this.selectedRows, this.rows[i], false);
                        }
                        this.firstSelectedRowIndex = -1;
                    }
                    else if (this.firstSelectedRowIndex < index && event.shiftKey) {
                        for (var i = this.firstSelectedRowIndex + 1; i <= index; i++) {
                            this.addRemoveArrayByIndex(this.selectedRows, this.rows[i], false);
                        }
                        this.firstSelectedRowIndex = -1;
                    }
                    else {
                        this.firstSelectedRowIndex = index;
                        this.addRemoveArrayByIndex(this.selectedRows, tag);
                    }
                };
                DashboardConfigGridComponent.prototype.clickAction = function (action, tag) {
                    this.onClickActionGrid.emit({ action: action, tag: tag });
                };
                DashboardConfigGridComponent.prototype.onDragStart = function (event, tag, index) {
                    if ((this.selectedRows.findIndex(function (x) { return x.tagName == tag.tagName; }) > -1 && this.selectedRows.length == 1) || this.selectedRows.length == 0) {
                        event.dataTransfer.setData("text", JSON.stringify(tag));
                    }
                    else {
                        this.addRemoveArrayByIndex(this.selectedRows, tag, false);
                    }
                };
                DashboardConfigGridComponent.prototype.addRemoveArrayByIndex = function (array, tag, isDeleteAllowed) {
                    if (isDeleteAllowed === void 0) { isDeleteAllowed = true; }
                    var foundIndex = array.findIndex(function (x) { return x.tagName == tag.tagName; });
                    if (foundIndex < 0)
                        array.push(tag);
                    else if (isDeleteAllowed)
                        array.splice(foundIndex, 1);
                };
                DashboardConfigGridComponent.prototype.clickActionEvent = function (action, item, index, event) {
                    item.checkbox = event.target.checked;
                    this.onClickActionGrid.emit({ action: action, item: item, event: event });
                };
                DashboardConfigGridComponent.prototype.sort = function (columnClicked, isSortAscending) {
                    this.columns.forEach(function (column) {
                        column.isSortAscending = null;
                    });
                    columnClicked.isSortAscending = isSortAscending;
                    this.onSortChange.emit(columnClicked);
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Array)
                ], DashboardConfigGridComponent.prototype, "rows", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Array)
                ], DashboardConfigGridComponent.prototype, "columns", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigGridComponent.prototype, "onClickActionGrid", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigGridComponent.prototype, "onSortChange", void 0);
                DashboardConfigGridComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigGridComponent",
                        styles: ["\n      .table>tbody>tr>td,\n      .table>thead>tr>th{\n\t\t    padding: 0px !important;\n        vertical-align: middle !important;\n        line-height: 22px;\n      }\n      .table thead th {\n        position: sticky;\n        top: 0;\n        z-index: 1;\n        background-color: #ddd;\n       }\n      .table>tbody>tr:hover{\n        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n      }\n      .table{\n        border: 1px solid lightgray;\n      }\n      .table-striped>tbody>tr:nth-of-type(odd) {\n        background-color: transparent !important;\n      }\n      .table-striped>tbody>tr:nth-of-type(odd):hover {\n        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n      }\n      .table-striped>tbody>tr:nth-child(odd)>td,\n      .table-striped>tbody>tr:nth-child(odd)>th {\n        background-color: rgba(0, 0, 0, 0.03) !important;\n      }\n      .full-cell\n      {\n        width: 100%;\n        height: 100%;\n        padding: 2px;\n      }\n      .pointer{\n        cursor: pointer;\n      }\n      .delete-mapping-button{\n        display: inline-block;\n        float: right;\n        margin-right: 10px;\n        height: 20px;\n        width: 24px;\n        border-radius: 50%;\n        border: 1px solid gray;\n        background-color: #f8f8f8;\n        text-align: center;\n        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n        cursor: pointer;\n      }\n      .delete-mapping-button:hover {\n        background-color: #cdcdcd;\n      }\n      .image-delete {\n        width: 20px;\n        height: 20px;\n        margin-top: -6px;\n      }\n\t    .sort-icon {\n\t\t    font-size: 10px;\n\t\t    cursor:pointer;\n\t     }\n      .header {\n        font-weight: bold;\n        margin-left: 6px;\n      }\n      .ellipsis {\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n      .ellipsis-container {\n          position: relative;\n          max-width: 100%;\n          padding: 0 !important;\n          display: -webkit-flex;\n          display: -moz-flex;\n          display: flex;\n          vertical-align: text-bottom !important;\n      }\n      .ellipsis {\n          position: absolute;\n          white-space: nowrap;\n          overflow-y: visible;\n          overflow-x: hidden;\n          text-overflow: ellipsis;\n          -ms-text-overflow: ellipsis;\n          -o-text-overflow: ellipsis;\n          max-width: 100%;\n          min-width: 0;\n          width:100%;\n          top: 0;\n          left: 0;\n      }\n      .ellipsis-container:after,\n      .ellipsis:after {\n          content: '-';\n          display: inline;\n          visibility: hidden;\n          width: 0;\n      }\n    "],
                        template: "<table *ngIf=\"rows?.length > 0\" class=\"table table-striped\">\n              <thead>\n\t\t\t          <tr style=\"height: 28px;\">\n                  <th resizable *ngFor=\"let col of columns\">\n                    <div *ngIf=\"col.isSortable\" class=\"header\" >\n                      {{col.header | translate}}\n                      <div *ngIf=\"col.isSortAscending == false\" class=\"glyphicon glyphicon glyphicon-triangle-bottom sort-icon\" (click)=\"sort(col, true)\"></div>\n                      <div *ngIf=\"col.isSortAscending == true\" class=\"glyphicon glyphicon glyphicon-triangle-top sort-icon\" (click)=\"sort(col, false)\"></div>\n                      <div *ngIf=\"col.isSortAscending == null\" class=\"glyphicon glyphicon glyphicon-triangle-top sort-icon\" (click)=\"sort(col, false)\"></div>\n                      <div *ngIf=\"col.isSortAscending == null\" class=\"glyphicon glyphicon glyphicon-triangle-bottom sort-icon\" (click)=\"sort(col, true)\"></div>\n                    </div>\n                    <div *ngIf=\"!col.isSortable\" class=\"header\" >{{col.header | translate}}</div>\n                  </th>\n\t\t\t          </tr>\n              </thead>\n              <tbody>\n\t\t\t          <tr *ngFor=\"let row of rows;let item of items; let i = index\" [ngClass]=\"{'is-selected': (this.selectedRows.indexOf(row) > -1)}\">\n\t\t\t\t          <td *ngFor=\"let col of columns\">\n\t\t\t\t            <ng-container *ngIf=\"!col.action.includes('action$')\"><div class=\"ellipsis-container\"><div [tooltip]=\"[col.tooltip]\" class=\"pointer ellipsis inline\" (click)=\"clickAction('', row, i)\">{{row[col.field]}}</div></div></ng-container>\n                    <ng-container *ngIf=\"col.action.includes('action$tooltip')\"><div class=\"ellipsis-container\"><div [tooltip]=\"[row[col.tooltip]]\" class=\"pointer ellipsis inline\" (click)=\"clickAction('', row, i)\">{{row[col.field]}}</div></div></ng-container>\n\t\t\t\t            <ng-container *ngIf=\"col.action.includes('action$image')\"><img [src]=\"'../../images/gtw_objects/' + (row[col.field] | gtwImage)\" class=\"image\" [ngClass]=\"{'blinking-red' : !row[col.arg]}\"/></ng-container>\n\t\t\t\t            <ng-container *ngIf=\"col.action.includes('action$list')\">\n                      <div *ngFor=\"let item of row[col.arg];trackBy: trackByFn\">\n                        <img [src]=\"'../../images/' + item.image + '.svg'\" class=\"image\"/>\n                        <div *ngIf=\"!item.action.includes('action$lookupMapping')\">{{item.arg.rightTag}}</div>\n                        <div *ngIf=\"item.action.includes('action$lookupMapping')\" class=\"pointer inline\" [dashboardConfigTagTooltipDirective]=\"item.arg.rightTag\">{{item.arg.rightTag}}</div>\n                        <div *ngIf=\"item.action.includes('action$deleteMapping') && this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'\" class=\"delete-mapping-button\" title=\"{{'TR_DELETE_MAPPING' | translate}}\" (click)=\"clickAction(item.action, item.arg, 0)\"><img [src]=\"'../../images/' + item.image + 'Delete.svg'\" class=\"image-delete\"/></div>\n                      </div>\n                    </ng-container>\n                    <ng-container *ngIf=\"col.action.includes('action$context-menu-device')\"><div class=\"pointer full-cell\" [myDraggable]=\"{data: selectedRows}\" [dashboardConfigContextMenuDirective]=\"row[col.field]\" [selectedRows]=\"selectedRows\" [rows]=\"rows\" [tag]=\"row[col.arg]\" [tooltip]=\"[row[col.tooltip]]\" (click)=\"clickActionSelect(row, i, $event)\" (dragstart)=\"onDragStart($event, row, i)\">{{row[col.field]}}</div></ng-container>\n\t\t\t\t            <ng-container *ngIf=\"col.action.includes('action$custom')\"><button class=\"btn btn-default\" (click)=\"clickAction(col.action, row, i)\" [tooltip]=\"[col.tooltip]\">{{col.header}}</button></ng-container>\n\t\t\t\t            <ng-container *ngIf=\"col.action.includes('action$changeValue') && row.tagCanChangeValue && this.authenticationService.role | checkRole:'OPERATOR_ROLE'\"><div class=\"round-button\" [tooltip]=\"[col.tooltip]\" (click)=\"clickAction(col.action, row)\"><img src=\"../../images/change_value.svg\" class=\"image-button\"/></div></ng-container>\n\t\t\t\t            <ng-container *ngIf=\"col.action.includes('action$edit') && row.tagCanEdit  && this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'\"><div class=\"round-button\" [tooltip]=\"[col.tooltip]\" (click)=\"clickAction(col.action, row)\"><img src=\"../../images/edit.svg\" class=\"image-button\"/></div></ng-container>\n\t\t\t\t            <ng-container *ngIf=\"col.action.includes('action$delete') && row.tagCanDelete && this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'\"><div class=\"round-button\" [tooltip]=\"[col.tooltip]\" (click)=\"clickAction(col.action, row)\"><img src=\"../../images/delete.svg\" class=\"image-button\"/></div></ng-container>\n                    <ng-container *ngIf=\"col.action.includes('action$checkbox')\"><div><input type=\"checkbox\" (change)=\"clickActionEvent(col.action, row, i, $event)\"/></div></ng-container>\n\t\t\t\t          </td>\n\t\t\t          </tr>\n              </tbody>\n\t\t        </table>\n\t\t        <div *ngIf=\"!rows || !rows?.length\" class=\"grid-no-results\">{{'TR_NO_RESULTS' | translate}}</div>"
                    }),
                    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService])
                ], DashboardConfigGridComponent);
                return DashboardConfigGridComponent;
            }());
            exports_1("DashboardConfigGridComponent", DashboardConfigGridComponent);
            Column = (function () {
                function Column(field, header, action, tooltip, arg, isSortable, isSortAscending) {
                    if (arg === void 0) { arg = null; }
                    if (isSortable === void 0) { isSortable = false; }
                    if (isSortAscending === void 0) { isSortAscending = null; }
                    this.field = field;
                    this.header = header;
                    this.action = action;
                    this.tooltip = tooltip;
                    this.arg = arg;
                    this.isSortable = isSortable;
                    this.isSortAscending = isSortAscending;
                }
                return Column;
            }());
            exports_1("Column", Column);
            Cell = (function () {
                function Cell(image, arg, action) {
                    this.image = image;
                    this.arg = arg;
                    this.action = action;
                }
                return Cell;
            }());
            exports_1("Cell", Cell);
        }
    };
});
//# sourceMappingURL=dashboard.config.grid.component.js.map