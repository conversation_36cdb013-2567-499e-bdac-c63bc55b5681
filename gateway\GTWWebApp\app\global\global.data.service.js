System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, GlobalDataService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            GlobalDataService = (function () {
                function GlobalDataService() {
                    this.config = null;
                }
                Object.defineProperty(GlobalDataService.prototype, "SDGConfig", {
                    get: function () {
                        return this.config;
                    },
                    set: function (value) {
                        this.config = value;
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(GlobalDataService.prototype, "isQuickStartVisible", {
                    get: function () {
                        return this._isQuickStartVisible;
                    },
                    set: function (value) {
                        this._isQuickStartVisible = value;
                        localStorage.setItem("SDGIsQuickStartVisible", JSON.stringify(value));
                    },
                    enumerable: false,
                    configurable: true
                });
                GlobalDataService = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [])
                ], GlobalDataService);
                return GlobalDataService;
            }());
            exports_1("GlobalDataService", GlobalDataService);
        }
    };
});
//# sourceMappingURL=global.data.service.js.map