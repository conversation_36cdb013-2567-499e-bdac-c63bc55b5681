﻿import { Component, <PERSON><PERSON><PERSON>, OnInit, Input, Output, EventEmitter, HostListener} from "@angular/core";
import { PanelService, TMW_LAYOUT_DASHBOARD_DIRECTION} from './panel.service';
import { Panel } from './panel';

@Component({
	selector: "panelComponent",
  styles: [`
    .button-close {
      position: absolute;
      top: 4px;
      right: 6px;
      line-height: 22px;
    }
		.panel-main {
			position: fixed;
    }
    .panel-default>.panel-heading {
      background-image: url(../../../images/background_7.svg), linear-gradient(#000000, #c5c5c5);
      background-size: 20%;
    }
    .panel-heading-bg {
      background-image: linear-gradient(to right, rgba(200, 200, 175, 0.90), rgba(250, 250, 250, 0.80));
      padding: 2px;
      margin-bottom: -1px;
    }
		.panel-heading-main {
			cursor: move;
    }
    .panel-corner-resize {
      position: absolute;
      width: 10px;
      height: 10px;
    }
		.panel-min-size{
			min-width:220px;
			min-width:220px;
		}
    .panel-icon{
      width: 40px;
      height: 26px;
      filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
      padding: 2px 8px 2px 8px;
      vertical-align: bottom;
    }
    .panel-top-left-resize { top: 0px; left: 0px; cursor: nwse-resize;}
    .panel-top-right-resize { top: 0px; right: 0px; cursor: nesw-resize;}
    .panel-bottom-left-resize { bottom: 0px; left: 0px; cursor: nesw-resize;}
    .panel-bottom-right-resize { bottom: 0px; right: 0px; cursor: nwse-resize;}
	`],
	template: `
		<div class="panel-main panel-min-size" [style.top.px]="this.panel.y" [style.left.px]="this.panel.x"
			[style.width.px]="this.panel.width" [style.height.px]="this.panel.height"  [style.z-index]="this.panel.zindex">
			<div (mousedown)="onCornerClick($event, topLeftResize)" class="panel-top-left-resize panel-corner-resize"></div>    
			<div (mousedown)="onCornerClick($event, topRightResize)" class="panel-top-right-resize panel-corner-resize"></div>    
			<div class="panel panel-default">
        <div class="button-close round-button" title="{{'TR_CLOSE_PANEL' | translate}}" (click)="onPanelClose()"><img [src]="'../../images/close.svg'" class="image-button"/></div>
				<div class="panel-heading-main panel-heading" *ngIf="this.panel.title" (mousedown)="mouseDown($event)">
					<div class="{{this.panel.panelHeadingCSS}}" title="{{this.panel.titleToolTip}}"><img [src]="'../../images/' + this.panel.icon" class="panel-icon" *ngIf="this.panel.icon!=''"/>{{this.panel.title | translate}}</div>
				</div>
				<div [style.width.px]="this.panel.width" [style.height.px]="this.panel.height - 34" class="panel-min-size">
					<dashboardConfigComponent *ngIf="this.panel.component === 'dashboardConfigComponent'" [panel]="this.panel"></dashboardConfigComponent>
					<dashboardLogComponent *ngIf="this.panel.component === 'dashboardLogComponent'" [panel]="this.panel"></dashboardLogComponent>
				</div>
			</div>
 			<div (mousedown)="onCornerClick($event, bottomLeftResize)" class="panel-bottom-left-resize panel-corner-resize"></div>    
			<div (mousedown)="onCornerClick($event, bottomRightResize)" class="panel-bottom-right-resize panel-corner-resize"></div>  
		</div> `
})

export class PanelComponent implements OnInit {
	@Input("panel") panel: Panel;
	@Output() onPanelCloseParent: EventEmitter<string> = new EventEmitter();

  private px: number;
  private py: number;
  private minArea: number;
  private draggingCorner: boolean;
  private draggingWindow: boolean;
  private resizer: Function;
  private navBarElementRect: any;

  constructor(private zone: NgZone, private panelService: PanelService) {
    this.draggingCorner = false;
    this.draggingWindow = false;
    this.minArea = 160000;
  }

	public ngOnInit(): void {}

	private onPanelClose(): void {
		this.panel.visible = false;
		this.onPanelCloseParent.emit();
	}

  ngAfterViewInit() {
    var navBarElement = document.querySelector(".navbar-red");
    this.navBarElementRect = navBarElement.getBoundingClientRect();
  }

  private area(): number {
    return this.panel.width * this.panel.height;
  }

	private bringToFront(): void {
		for (let panel of this.panelService.panelList) {
			if (panel.lsName === this.panel.lsName)
				panel.zindex = 1;
			else	
				panel.zindex = 0;
		}
  }

  private mouseDown(event: MouseEvent): void {
    this.draggingWindow = true;
    this.px = event.clientX;
    this.py = event.clientY;
    this.bringToFront();
    this.zone.runOutsideAngular(() => {
      window.document.addEventListener('mousemove', this.onWindowDrag.bind(this));
    });
  }

  private onWindowDrag(event: MouseEvent): void {
    if (!this.draggingWindow)
      return;
    if (event.clientX >= window.innerWidth || event.clientX <= 0) 
      return;
    if (event.clientY >= window.innerHeight || event.clientY <= 0)
      return;

    let offsetX = event.clientX - this.px;
    let offsetY = event.clientY - this.py;

    this.panel.x += offsetX;
    this.panel.y += offsetY;
    this.px = event.clientX;
    this.py = event.clientY;

    //To avoid hiding the title bar behind the top banner
    if (this.py < this.navBarElementRect.bottom)
      this.panel.y = this.navBarElementRect.bottom;

    this.bringToFront();
    if(event.stopPropagation) event.stopPropagation();
    if(event.preventDefault) event.preventDefault();
    event.cancelBubble = true;
    event.returnValue = false;

    localStorage.setItem("SDGDashboardLayout", JSON.stringify(this.panelService.panelList));
    this.panelService.panelLayoutDirection = TMW_LAYOUT_DASHBOARD_DIRECTION.CUSTOM;
  }

  @HostListener('document:mouseup', ['$event'])
  onCornerRelease(event: MouseEvent) {
    this.draggingWindow = false;
    this.draggingCorner = false;

    //To avoid hiding the title bar behind the top banner
    if (this.py) {
      if (this.py < this.navBarElementRect.bottom)
        this.panel.y = this.navBarElementRect.bottom;
    }
  }

  private onCornerClick(event: MouseEvent, resizer?: Function): void {
    this.draggingCorner = true;
    this.px = event.clientX;
    this.py = event.clientY;
    this.resizer = resizer;
    event.preventDefault();
    event.stopPropagation();
    this.bringToFront();

  }

  private topLeftResize(offsetX: number, offsetY: number): void {
    this.panel.x += offsetX;
    this.panel.y += offsetY;
    this.panel.width -= offsetX;
    this.panel.height -= offsetY;
  }

  private topRightResize(offsetX: number, offsetY: number): void {
    this.panel.y += offsetY;
    this.panel.width += offsetX;
    this.panel.height -= offsetY;
  }

  private bottomLeftResize(offsetX: number, offsetY: number): void {
    this.panel.x += offsetX;
    this.panel.width -= offsetX;
    this.panel.height += offsetY;
  }

  private bottomRightResize(offsetX: number, offsetY: number): void {
    this.panel.width += offsetX;
    this.panel.height += offsetY;
  }

  @HostListener('document:mousemove', ['$event'])
  private onCornerMove(event: MouseEvent) {
    if (!this.draggingCorner)
      return;
    if (event.clientX >= window.innerWidth || event.clientX <= 0)
      return;
    if (event.clientY >= window.innerHeight || event.clientY <= 0)
      return;

    let offsetX = event.clientX - this.px;
    let offsetY = event.clientY - this.py;

    let lastX = this.panel.x;
    let lastY = this.panel.y;
    let pWidth = this.panel.width;
    let pHeight = this.panel.height;

    this.resizer(offsetX, offsetY);
    if (this.area() < this.minArea) {
			this.panel.x = lastX;
			this.panel.y = lastY;
			this.panel.width = pWidth;
			this.panel.height = pHeight;
    }
    this.px = event.clientX;
    this.py = event.clientY;

    localStorage.setItem("SDGDashboardLayout", JSON.stringify(this.panelService.panelList));
    this.panelService.panelLayoutDirection = TMW_LAYOUT_DASHBOARD_DIRECTION.CUSTOM;
  }
}