﻿import { Injectable } from '@angular/core';
import { SDGCheckAuthDTO } from "../data/model/models";
import { Configuration } from "../data/configuration";
import { Router } from '@angular/router';
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { AuthenticationLoginModal } from './authentication.login.modal';
import { AuthenticationPasswordModal } from "./authentication.password.modal";
import { DialogRef } from 'ngx-modialog-7';
import { GlobalDataService } from "../global/global.data.service";

@Injectable()
export class AuthenticationService {
  public userApiConfig: Configuration = new Configuration({apiKeys: {"Authorization": this.uuid()}})
  private authenticationLoginModalRef: DialogRef<any>;

	get role(): string {
		if (this.userApiConfig.accessToken != null)
			return this.userApiConfig.accessToken.toString();
		else
			return "";
	}

  constructor(public modal: Modal, private router: Router, public globalDataService: GlobalDataService) {
    
  }

	public login(authentication: SDGCheckAuthDTO): void {
    this.userApiConfig.username = authentication.username;
    this.userApiConfig.accessToken = UserRoleEnum[authentication.role];
	}

	public logoff(): void {
		this.userApiConfig = new Configuration();
	}

  public onLoginFailed(route?: string): void {
    if (!this.authenticationLoginModalRef) {
      this.authenticationLoginModalRef = this.modal.open(AuthenticationLoginModal, overlayConfigFactory({ userApiConfig: this.userApiConfig }, BSModalContext));
      this.router.navigate(["/blank"]);
      this.authenticationLoginModalRef.result.then(data => {
        if (data.status == 419) {
          this.modal.open(AuthenticationPasswordModal, overlayConfigFactory({ authenticationService: this, username: data.username, isPasswordReset: true }, BSModalContext));
        }
        else {
          let isRememberMeActive: boolean = this.authenticationLoginModalRef.context.isRememberMeActive;
          this.login(data);
          if (route != null)
            this.router.navigate([route]);
          this.authenticationLoginModalRef = null;
        }
      });
    }
  }

  private uuid(): string {
    return '00000000-0000-0000-0000-000000000000'.replace(/[018]/g, c =>
      (+c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> +c / 4).toString(16)
    );
  }
}
export enum UserRoleEnum {
  NO_ROLE = 0,
  VIEWER_ROLE = 1,
  OPERATOR_ROLE = 2,
  CONFIGURATOR_ROLE = 3,
  SU_ROLE = 4
}