/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';

import { EditorContextMenuDTO } from '../model/editorContextMenuDTO';

import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class EditorContextMenuService {

    public basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }


    /**
     * Get an editor context menu for an editable object.
     * 
     * @param objectFullName object name/path (i.e. mmb).  An editor context menu will be obtained for this node. Empty string for the root node.
     * @param objectClassName object type (i.e. class name).
     * @param objectCollectionKind whether this object&#39;s collection has MDOs or SDOs in it.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getEditorContextMenu(objectFullName?: string, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'body', reportProgress?: boolean): Observable<Array<EditorContextMenuDTO>>;
    public getEditorContextMenu(objectFullName?: string, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<EditorContextMenuDTO>>>;
    public getEditorContextMenu(objectFullName?: string, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<EditorContextMenuDTO>>>;
    public getEditorContextMenu(objectFullName?: string, objectClassName?: string, objectCollectionKind?: 'MDO' | 'SDO' | 'ALL', observe: any = 'body', reportProgress: boolean = false ): Observable<any> {




        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (objectFullName !== undefined && objectFullName !== null) {
            queryParameters = queryParameters.set('objectFullName', <any>objectFullName);
        }
        if (objectClassName !== undefined && objectClassName !== null) {
            queryParameters = queryParameters.set('objectClassName', <any>objectClassName);
        }
        if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
            queryParameters = queryParameters.set('objectCollectionKind', <any>objectCollectionKind);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<Array<EditorContextMenuDTO>>(`${this.basePath}/editor_context_menu`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
