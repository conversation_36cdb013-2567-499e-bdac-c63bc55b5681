﻿export class GlobalObjectToCSVUtil{

  public static copyToCSV(filename: string, data: Array<any>): void {
    let csvData = this.arrayObjToCSV(data);
    let blob = new Blob([csvData], { type: 'text/csv' });
    let url = window.URL.createObjectURL(blob);

    if (navigator.msSaveOrOpenBlob) {
      navigator.msSaveBlob(blob, filename);
    } else {
      var a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
    window.URL.revokeObjectURL(url);
  }

  public static isNotEmpty(obj): boolean {
    return obj != null && Object.keys(obj).length != 0
  }

  private static arrayObjToCSV(data: Array<any>): string {
    let columnDelimiter: string = ","
    let lineDelimiter: string = "\n"
    let result: string;
    let ctr: number;
    let keys: Array<string> = [];

    if (data === null || !data.length)
      return "";

    keys = Object.keys(data[0])

    for (var i = 0; i < keys.length; i++) {
      keys[i] = keys[i].replace(/,/g, ' ');
    }

    result = ""
    result += keys.join(columnDelimiter)
    result += lineDelimiter

    data.forEach(item => {
      ctr = 0
      keys.forEach(key => {
        if (ctr > 0)
          result += columnDelimiter
        if (typeof item[key] === "string" && item[key].includes(lineDelimiter))
          item[key] = item[key].replace(/[\n]/g, ' ');

        result += (typeof item[key] === "string" && item[key].includes(columnDelimiter)) ? item[key].replace(/,/g, ' ') : item[key];
        ctr++
      })
      result += lineDelimiter
    })
    return result
  }
}
