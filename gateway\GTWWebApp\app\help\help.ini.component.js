System.register(["@angular/core", "../data/api/api", "../modules/grid/column", "../modules/alert/alert.service", "../authentication/authentication.service", "@ngx-translate/core", "../global/translateKey.pipe", "../global/global.json.util", "../modules/loader/loader.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, api_1, column_1, alert_service_1, authentication_service_1, core_2, translateKey_pipe_1, global_json_util_1, loader_service_1, HelpIniComponent, HelpIniContentDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (column_1_1) {
                column_1 = column_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (translateKey_pipe_1_1) {
                translateKey_pipe_1 = translateKey_pipe_1_1;
            },
            function (global_json_util_1_1) {
                global_json_util_1 = global_json_util_1_1;
            },
            function (loader_service_1_1) {
                loader_service_1 = loader_service_1_1;
            }
        ],
        execute: function () {
            HelpIniComponent = (function () {
                function HelpIniComponent(helpService, alertService, authenticationService, translate, translateKeyPipe, loaderService) {
                    this.helpService = helpService;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.translate = translate;
                    this.translateKeyPipe = translateKeyPipe;
                    this.loaderService = loaderService;
                    this.isHelpIniVisible = false;
                    this.helpIniContent = [];
                    this.isLoading = false;
                    this.columns = this.getColumns();
                }
                HelpIniComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    this.isLoadingChangeSubscriptionShow = this.loaderService.isLoadingChange.subscribe(function (value) {
                        _this.isLoading = value;
                    });
                };
                HelpIniComponent.prototype.ngOnDestroy = function () {
                    this.isLoadingChangeSubscriptionShow.unsubscribe();
                };
                HelpIniComponent.prototype.show = function () {
                    this.isHelpIniVisible = true;
                    this.getIniParametersHelp();
                };
                HelpIniComponent.prototype.close = function () {
                    this.isHelpIniVisible = false;
                };
                HelpIniComponent.prototype.getIniParametersHelp = function () {
                    var _this = this;
                    if (this.helpIniContent.length == 0) {
                        this.loaderService.toggleIsLoading(true);
                        this.helpService.getINIhelp().subscribe(function (data) {
                            if (data) {
                                data = global_json_util_1.GlobalJsonUtil.sortByProperty(data, "paramName");
                                data.forEach(function (iniParam) {
                                    var helpIniContentTxt = "";
                                    helpIniContentTxt += "<b>" + iniParam.paramName + " - " + _this.translateKeyPipe.transform(iniParam.paramLabelKey, iniParam.paramLabel, _this.translate) + "</b><br>";
                                    helpIniContentTxt += "<i>" + iniParam.paramSection + "</i><br>";
                                    helpIniContentTxt += _this.translateKeyPipe.transform(iniParam.paramHelpKey, iniParam.paramHelp, _this.translate) + "<br>";
                                    helpIniContentTxt += iniParam.paramRange ? iniParam.paramRange + "<br>" : "";
                                    _this.translate.get("TR_DEFAULT").subscribe(function (res) {
                                        helpIniContentTxt += res + ": " + iniParam.paramDefault + "<br><br>";
                                    });
                                    _this.helpIniContent.push(new HelpIniContentDTO(helpIniContentTxt));
                                });
                            }
                        }, function (error) {
                            _this.loaderService.toggleIsLoading(false);
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("LICENSE.ERROR_CAN_T_READ_LICENSE");
                            }
                        }, function () {
                            _this.loaderService.toggleIsLoading(false);
                        });
                    }
                };
                HelpIniComponent.prototype.getColumns = function () {
                    return [
                        new column_1.Column("helpIniContentTxt", "TR_SEARCH", "action$innerHTML", ""),
                    ];
                };
                HelpIniComponent = __decorate([
                    core_1.Component({
                        selector: "helpIniComponent",
                        providers: [translateKey_pipe_1.TranslateKeyPipe],
                        styles: ["\n      .panel {\n        position: absolute;\n        z-index: 999;\n        margin: auto;\n        width: 50%;\n        top: 80px;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n      .panel-heading{\n        padding: 4px;\n      }\n      .panel-body{\n        padding: 8px;\n        \n      }\n      .button-close {\n        position: absolute;\n        top: 6px;\n        right: 6px;\n      }\n      "
                        ],
                        template: "\n      <div class=\"panel-main panel panel-default\" *ngIf=\"isHelpIniVisible\" style=\"min-width:400px\">\n        <div class=\"panel-heading\">\n          {{'TR_INI_FILE_PARAMETER_HELP' | translate}}\n          <div class=\"button-close round-button\" title=\"{{'TR_CLOSE' | translate}}\" (click)=\"close()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n        </div>\n\t      <div class=\"panel-body\" style=\"overflow-y: scroll; height:80vh;\">\n\t\t      <gridComponent [rows]='helpIniContent' [columns]='columns'></gridComponent>\n\t      </div>\n \n      </div>"
                    }),
                    __metadata("design:paramtypes", [api_1.HelpService, alert_service_1.AlertService, authentication_service_1.AuthenticationService, core_2.TranslateService, translateKey_pipe_1.TranslateKeyPipe, loader_service_1.LoaderService])
                ], HelpIniComponent);
                return HelpIniComponent;
            }());
            exports_1("HelpIniComponent", HelpIniComponent);
            HelpIniContentDTO = (function () {
                function HelpIniContentDTO(helpIniContentTxt) {
                    this.helpIniContentTxt = helpIniContentTxt;
                }
                return HelpIniContentDTO;
            }());
            exports_1("HelpIniContentDTO", HelpIniContentDTO);
        }
    };
});
//# sourceMappingURL=help.ini.component.js.map