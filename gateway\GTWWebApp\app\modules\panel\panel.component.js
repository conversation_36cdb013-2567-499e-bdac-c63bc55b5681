System.register(["@angular/core", "./panel.service", "./panel"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, panel_service_1, panel_1, PanelComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (panel_service_1_1) {
                panel_service_1 = panel_service_1_1;
            },
            function (panel_1_1) {
                panel_1 = panel_1_1;
            }
        ],
        execute: function () {
            PanelComponent = (function () {
                function PanelComponent(zone, panelService) {
                    this.zone = zone;
                    this.panelService = panelService;
                    this.onPanelCloseParent = new core_1.EventEmitter();
                    this.draggingCorner = false;
                    this.draggingWindow = false;
                    this.minArea = 160000;
                }
                PanelComponent.prototype.ngOnInit = function () { };
                PanelComponent.prototype.onPanelClose = function () {
                    this.panel.visible = false;
                    this.onPanelCloseParent.emit();
                };
                PanelComponent.prototype.ngAfterViewInit = function () {
                    var navBarElement = document.querySelector(".navbar-red");
                    this.navBarElementRect = navBarElement.getBoundingClientRect();
                };
                PanelComponent.prototype.area = function () {
                    return this.panel.width * this.panel.height;
                };
                PanelComponent.prototype.bringToFront = function () {
                    for (var _i = 0, _a = this.panelService.panelList; _i < _a.length; _i++) {
                        var panel = _a[_i];
                        if (panel.lsName === this.panel.lsName)
                            panel.zindex = 1;
                        else
                            panel.zindex = 0;
                    }
                };
                PanelComponent.prototype.mouseDown = function (event) {
                    var _this = this;
                    this.draggingWindow = true;
                    this.px = event.clientX;
                    this.py = event.clientY;
                    this.bringToFront();
                    this.zone.runOutsideAngular(function () {
                        window.document.addEventListener('mousemove', _this.onWindowDrag.bind(_this));
                    });
                };
                PanelComponent.prototype.onWindowDrag = function (event) {
                    if (!this.draggingWindow)
                        return;
                    if (event.clientX >= window.innerWidth || event.clientX <= 0)
                        return;
                    if (event.clientY >= window.innerHeight || event.clientY <= 0)
                        return;
                    var offsetX = event.clientX - this.px;
                    var offsetY = event.clientY - this.py;
                    this.panel.x += offsetX;
                    this.panel.y += offsetY;
                    this.px = event.clientX;
                    this.py = event.clientY;
                    if (this.py < this.navBarElementRect.bottom)
                        this.panel.y = this.navBarElementRect.bottom;
                    this.bringToFront();
                    if (event.stopPropagation)
                        event.stopPropagation();
                    if (event.preventDefault)
                        event.preventDefault();
                    event.cancelBubble = true;
                    event.returnValue = false;
                    localStorage.setItem("SDGDashboardLayout", JSON.stringify(this.panelService.panelList));
                    this.panelService.panelLayoutDirection = 0;
                };
                PanelComponent.prototype.onCornerRelease = function (event) {
                    this.draggingWindow = false;
                    this.draggingCorner = false;
                    if (this.py) {
                        if (this.py < this.navBarElementRect.bottom)
                            this.panel.y = this.navBarElementRect.bottom;
                    }
                };
                PanelComponent.prototype.onCornerClick = function (event, resizer) {
                    this.draggingCorner = true;
                    this.px = event.clientX;
                    this.py = event.clientY;
                    this.resizer = resizer;
                    event.preventDefault();
                    event.stopPropagation();
                    this.bringToFront();
                };
                PanelComponent.prototype.topLeftResize = function (offsetX, offsetY) {
                    this.panel.x += offsetX;
                    this.panel.y += offsetY;
                    this.panel.width -= offsetX;
                    this.panel.height -= offsetY;
                };
                PanelComponent.prototype.topRightResize = function (offsetX, offsetY) {
                    this.panel.y += offsetY;
                    this.panel.width += offsetX;
                    this.panel.height -= offsetY;
                };
                PanelComponent.prototype.bottomLeftResize = function (offsetX, offsetY) {
                    this.panel.x += offsetX;
                    this.panel.width -= offsetX;
                    this.panel.height += offsetY;
                };
                PanelComponent.prototype.bottomRightResize = function (offsetX, offsetY) {
                    this.panel.width += offsetX;
                    this.panel.height += offsetY;
                };
                PanelComponent.prototype.onCornerMove = function (event) {
                    if (!this.draggingCorner)
                        return;
                    if (event.clientX >= window.innerWidth || event.clientX <= 0)
                        return;
                    if (event.clientY >= window.innerHeight || event.clientY <= 0)
                        return;
                    var offsetX = event.clientX - this.px;
                    var offsetY = event.clientY - this.py;
                    var lastX = this.panel.x;
                    var lastY = this.panel.y;
                    var pWidth = this.panel.width;
                    var pHeight = this.panel.height;
                    this.resizer(offsetX, offsetY);
                    if (this.area() < this.minArea) {
                        this.panel.x = lastX;
                        this.panel.y = lastY;
                        this.panel.width = pWidth;
                        this.panel.height = pHeight;
                    }
                    this.px = event.clientX;
                    this.py = event.clientY;
                    localStorage.setItem("SDGDashboardLayout", JSON.stringify(this.panelService.panelList));
                    this.panelService.panelLayoutDirection = 0;
                };
                __decorate([
                    core_1.Input("panel"),
                    __metadata("design:type", panel_1.Panel)
                ], PanelComponent.prototype, "panel", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], PanelComponent.prototype, "onPanelCloseParent", void 0);
                __decorate([
                    core_1.HostListener('document:mouseup', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [MouseEvent]),
                    __metadata("design:returntype", void 0)
                ], PanelComponent.prototype, "onCornerRelease", null);
                __decorate([
                    core_1.HostListener('document:mousemove', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [MouseEvent]),
                    __metadata("design:returntype", void 0)
                ], PanelComponent.prototype, "onCornerMove", null);
                PanelComponent = __decorate([
                    core_1.Component({
                        selector: "panelComponent",
                        styles: ["\n    .button-close {\n      position: absolute;\n      top: 4px;\n      right: 6px;\n      line-height: 22px;\n    }\n\t\t.panel-main {\n\t\t\tposition: fixed;\n    }\n    .panel-default>.panel-heading {\n      background-image: url(../../../images/background_7.svg), linear-gradient(#000000, #c5c5c5);\n      background-size: 20%;\n    }\n    .panel-heading-bg {\n      background-image: linear-gradient(to right, rgba(200, 200, 175, 0.90), rgba(250, 250, 250, 0.80));\n      padding: 2px;\n      margin-bottom: -1px;\n    }\n\t\t.panel-heading-main {\n\t\t\tcursor: move;\n    }\n    .panel-corner-resize {\n      position: absolute;\n      width: 10px;\n      height: 10px;\n    }\n\t\t.panel-min-size{\n\t\t\tmin-width:220px;\n\t\t\tmin-width:220px;\n\t\t}\n    .panel-icon{\n      width: 40px;\n      height: 26px;\n      filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n      padding: 2px 8px 2px 8px;\n      vertical-align: bottom;\n    }\n    .panel-top-left-resize { top: 0px; left: 0px; cursor: nwse-resize;}\n    .panel-top-right-resize { top: 0px; right: 0px; cursor: nesw-resize;}\n    .panel-bottom-left-resize { bottom: 0px; left: 0px; cursor: nesw-resize;}\n    .panel-bottom-right-resize { bottom: 0px; right: 0px; cursor: nwse-resize;}\n\t"],
                        template: "\n\t\t<div class=\"panel-main panel-min-size\" [style.top.px]=\"this.panel.y\" [style.left.px]=\"this.panel.x\"\n\t\t\t[style.width.px]=\"this.panel.width\" [style.height.px]=\"this.panel.height\"  [style.z-index]=\"this.panel.zindex\">\n\t\t\t<div (mousedown)=\"onCornerClick($event, topLeftResize)\" class=\"panel-top-left-resize panel-corner-resize\"></div>    \n\t\t\t<div (mousedown)=\"onCornerClick($event, topRightResize)\" class=\"panel-top-right-resize panel-corner-resize\"></div>    \n\t\t\t<div class=\"panel panel-default\">\n        <div class=\"button-close round-button\" title=\"{{'TR_CLOSE_PANEL' | translate}}\" (click)=\"onPanelClose()\"><img [src]=\"'../../images/close.svg'\" class=\"image-button\"/></div>\n\t\t\t\t<div class=\"panel-heading-main panel-heading\" *ngIf=\"this.panel.title\" (mousedown)=\"mouseDown($event)\">\n\t\t\t\t\t<div class=\"{{this.panel.panelHeadingCSS}}\" title=\"{{this.panel.titleToolTip}}\"><img [src]=\"'../../images/' + this.panel.icon\" class=\"panel-icon\" *ngIf=\"this.panel.icon!=''\"/>{{this.panel.title | translate}}</div>\n\t\t\t\t</div>\n\t\t\t\t<div [style.width.px]=\"this.panel.width\" [style.height.px]=\"this.panel.height - 34\" class=\"panel-min-size\">\n\t\t\t\t\t<dashboardConfigComponent *ngIf=\"this.panel.component === 'dashboardConfigComponent'\" [panel]=\"this.panel\"></dashboardConfigComponent>\n\t\t\t\t\t<dashboardLogComponent *ngIf=\"this.panel.component === 'dashboardLogComponent'\" [panel]=\"this.panel\"></dashboardLogComponent>\n\t\t\t\t</div>\n\t\t\t</div>\n \t\t\t<div (mousedown)=\"onCornerClick($event, bottomLeftResize)\" class=\"panel-bottom-left-resize panel-corner-resize\"></div>    \n\t\t\t<div (mousedown)=\"onCornerClick($event, bottomRightResize)\" class=\"panel-bottom-right-resize panel-corner-resize\"></div>  \n\t\t</div> "
                    }),
                    __metadata("design:paramtypes", [core_1.NgZone, panel_service_1.PanelService])
                ], PanelComponent);
                return PanelComponent;
            }());
            exports_1("PanelComponent", PanelComponent);
        }
    };
});
//# sourceMappingURL=panel.component.js.map