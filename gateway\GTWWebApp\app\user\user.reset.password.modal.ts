﻿import { Component, OnInit } from "@angular/core";
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { AlertService } from "../modules/alert/alert.service";
import { UserObjectDTO } from "../data/model/models";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "userResetPasswordModal",
  styles: [`
      .user-roles-checkbox {
	      display: inline-block;
	      width: 140px;
	      height: 10px;
	      margin: 1px 0 0 8px;
      }      
      .password {
        display: inline-block;
        width: 94%;
        margin-bottom: 0px;
      }
    `],
  template: `
	  <div class="container-fluid">
		  <div class="modal-heading">{{'TR_RESET_USER_PASSWORD_OF' | translate}}&nbsp;{{currentUser.username}}</div>
		  <div class="modal-body">
        <form name="form" (ngSubmit)="f.form.valid && save()" #f="ngForm" novalidate>
          <div>
	          <div class="form-group password">
		          <label for="password">{{'TR_TEMPORARY_PASSWORD' | translate}}</label>
              <img class="help-icon" src="../../images/help.svg" title="{{'TR_PASSWORD_COMPLEXITY_REQUIREMENTS' | translate}}">
		          <input type="password" class="form-control" name="password" [(ngModel)]="currentUser.password" #password="ngModel" [required]="true" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@$#!\\-+*_%.])\\S{8,24}$" show-password>
	          </div>
		        <small class="text-danger" *ngIf="f.submitted && !password.valid" [innerHtml]="'TR_YOUR_PASSWORD_DOESNT_MEET_COMPLEXITY_REQUIREMENTS' | translate"></small>
          </div>
          <div *ngIf="!currentUser.isactive" class="alert-danger" [innerHtml]="'TR_THIS_USER_IS_NOT_ACTIVE' | translate">
	        </div>
          <br>
	        <div class="form-group" style="display: inline-block;width:99%; text-align: center;">
		        <div style="display: table-cell;">
		        <button class="btn btn-default" (click)="cancel()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CANCEL' | translate}}</button>
		        </div>
		          <div style="display: table-cell;width:99%"></div>
		        <div style="display: table-cell;">
		        <button type="submit" class="btn btn-default"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_SAVE' | translate}}</button>
		        </div>
	        </div>
	        <alertStatusBarComponent></alertStatusBarComponent>
        </form>
      </div>
	  </div>`
})
export class UserResetPasswordModal implements CloseGuard, ModalComponent<UserResetPasswordModalContext> {
  private context: UserResetPasswordModalContext;
  private currentUser: UserObjectDTO;
  private users: UserObjectDTO[] = [];

  constructor(public dialog: DialogRef<UserResetPasswordModalContext>, private alertService: AlertService, private translate: TranslateService) {
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-md";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }

  public ngOnInit(): void {
    this.currentUser = Object.assign({}, this.dialog.context.currentUser);
  }

  beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private cancel() {
    this.dialog.close(this.dialog.context.currentUser);
  }

  private save() {
    this.dialog.context.currentUser = this.currentUser;
    this.dialog.close(this.dialog.context.currentUser);
  }
}

export class UserResetPasswordModalContext extends BSModalContext {
  public currentUser: UserObjectDTO = {};
}