System.register(["@angular/core", "../../data/api/api", "../../data/wsApi/wsApi", "../../data/model/models", "../../authentication/authentication.service", "../../modules/alert/alert.service", "@ngx-translate/core", "../../modules/tab/tab.component", "../../global/global.json.util"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, api_1, wsApi_1, models_1, authentication_service_1, alert_service_1, core_2, tab_component_1, global_json_util_1, DashboardLogExtraComponent, Source, Severity;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (tab_component_1_1) {
                tab_component_1 = tab_component_1_1;
            },
            function (global_json_util_1_1) {
                global_json_util_1 = global_json_util_1_1;
            }
        ],
        execute: function () {
            DashboardLogExtraComponent = (function () {
                function DashboardLogExtraComponent(logService, alertService, authenticationService, translateService, broadcastEventWSApi) {
                    this.logService = logService;
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.translateService = translateService;
                    this.broadcastEventWSApi = broadcastEventWSApi;
                    this.isDataLoaded = false;
                    this.optionSDGCategoryList = [];
                    this.optionSCLCategoryList = [];
                    this.option6TCategoryList = [];
                    this.logDevices = [];
                    this.severitymaskComponentData = [];
                    this.editorSDGSeverityFieldcontrolValue = "";
                    this.editor6TSeverityFieldcontrolValue = "";
                    this.editorSCLSeverityFieldcontrolValue = "";
                }
                DashboardLogExtraComponent.prototype.ngOnInit = function () {
                    this.getLogFilterConfig();
                    this.displayNodeToLogFilter();
                    this.optionSeverityMaskComponentDataOnInit();
                    this.openGlobalBroadcastServiceSubscription();
                };
                DashboardLogExtraComponent.prototype.ngOnDestroy = function () {
                    if (this.globalBroadcastServiceSubscription != null)
                        this.globalBroadcastServiceSubscription.unsubscribe();
                };
                DashboardLogExtraComponent.prototype.ngAfterViewInit = function () {
                    var selectedTabLogFilterLS = localStorage.getItem("SDGSelectedTabLogFilter");
                    if (selectedTabLogFilterLS) {
                        var selectedTabLogFilter = this.tabLogFilters.filter(function (tab) { return tab.title == selectedTabLogFilterLS; })[0];
                        if (selectedTabLogFilter) {
                            this.tabLogFilters.first.active = false;
                            selectedTabLogFilter.active = true;
                        }
                    }
                };
                DashboardLogExtraComponent.prototype.openGlobalBroadcastServiceSubscription = function () {
                    var _this = this;
                    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(function (event) {
                        if (event.type === 'message') {
                            var broadcastEventData = JSON.parse(event.data);
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.RefreshLogParameter.toString()) {
                                _this.getLogFilterConfig();
                                _this.displayNodeToLogFilter();
                            }
                        }
                    });
                };
                DashboardLogExtraComponent.prototype.getLogFilterConfig = function () {
                    var _this = this;
                    this.logService.getLogFilterConfig().subscribe(function (data) {
                        _this.logConfigMaskSDG = data.filter(function (logConfig) { return logConfig.source == Source.SDG; })[0];
                        _this.optionSDGMaskCategoryOnInit();
                        _this.getLogVerboseFilter(Source.SDG, _this.logConfigMaskSDG);
                        for (var i in _this.optionSDGCategoryList) {
                            if (_this.optionSDGCategoryList[i].value & _this.logConfigMaskSDG.categorymask)
                                _this.optionSDGCategoryList[i].isChecked = true;
                            else
                                _this.optionSDGCategoryList[i].isChecked = false;
                        }
                        _this.logConfigMaskSCL = data.filter(function (logConfig) { return logConfig.source == Source.SCL; })[0];
                        _this.optionSCLMaskCategoryOnInit();
                        _this.getLogVerboseFilter(Source.SCL, _this.logConfigMaskSCL);
                        for (var i in _this.optionSCLCategoryList) {
                            if (_this.optionSCLCategoryList[i].value & _this.logConfigMaskSCL.categorymask)
                                _this.optionSCLCategoryList[i].isChecked = true;
                            else
                                _this.optionSCLCategoryList[i].isChecked = false;
                        }
                        _this.logConfigMask6T = data.filter(function (logConfig) { return logConfig.source == Source._6T; })[0];
                        _this.option6TMaskCategoryOnInit();
                        _this.getLogVerboseFilter(Source._6T, _this.logConfigMask6T);
                        for (var i in _this.option6TCategoryList) {
                            if (_this.option6TCategoryList[i].value & _this.logConfigMask6T.categorymask)
                                _this.option6TCategoryList[i].isChecked = true;
                            else
                                _this.option6TCategoryList[i].isChecked = false;
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    }, function () { _this.isDataLoaded = true; });
                };
                DashboardLogExtraComponent.prototype.displayNodeToLogFilter = function () {
                    var _this = this;
                    this.logService.getLogFilterDevice()
                        .subscribe(function (data) {
                        if (data) {
                            _this.logDevices = data;
                            _this.logDevices = global_json_util_1.GlobalJsonUtil.sortByProperty(_this.logDevices, "name");
                        }
                        else {
                            _this.logDevices = [];
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR");
                        }
                    });
                };
                DashboardLogExtraComponent.prototype.removeAllDevice = function () {
                    var _this = this;
                    var logDevices = [];
                    this.logDevices.forEach(function (logDevice) {
                        logDevices.push({ name: logDevice.name, add: false });
                    });
                    this.logService.putLogFilterDevice(logDevices)
                        .subscribe(function (data) {
                        _this.alertService.success("TR_NODES_REMOVED");
                        _this.logDevices = [];
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR");
                        }
                    });
                };
                DashboardLogExtraComponent.prototype.removeNodeToLogFilter = function (nodeFullName) {
                    var _this = this;
                    var logDevices = [];
                    logDevices.push({ name: nodeFullName, add: false });
                    this.logService.putLogFilterDevice(logDevices)
                        .subscribe(function (data) {
                        _this.alertService.success("TR_NODE_REMOVED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR");
                        }
                    });
                };
                DashboardLogExtraComponent.prototype.tabFilterOnChange = function (tab) {
                    if (tab.active == true) {
                        localStorage.setItem("SDGSelectedTabLogFilter", tab.title);
                    }
                };
                DashboardLogExtraComponent.prototype.setSaveLogToTextFile = function (e) {
                    var _this = this;
                    this.logService.putMirrorAllToLogFile(e.target.checked).subscribe(function (data) { }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    });
                };
                DashboardLogExtraComponent.prototype.setLogVerboseFilter = function (editorSeverityFieldcontrolValue, logConfigMask) {
                    logConfigMask.severitymask = 0;
                    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Exception)]) != -1)
                        logConfigMask.severitymask |= Severity.Exception;
                    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Error)]) != -1)
                        logConfigMask.severitymask |= Severity.Error;
                    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Warning)]) != -1)
                        logConfigMask.severitymask |= Severity.Warning;
                    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Information)]) != -1)
                        logConfigMask.severitymask |= Severity.Information;
                    this.saveLogFilterConfig(logConfigMask);
                };
                DashboardLogExtraComponent.prototype.getLogVerboseFilter = function (sourceSeverity, logConfigMask) {
                    var logConfigMaskValue = [];
                    if ((Severity.Exception & logConfigMask.severitymask) == Severity.Exception)
                        logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Exception)]);
                    if ((Severity.Error & logConfigMask.severitymask) == Severity.Error)
                        logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Error)]);
                    if ((Severity.Warning & logConfigMask.severitymask) == Severity.Warning)
                        logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Warning)]);
                    if ((Severity.Information & logConfigMask.severitymask) == Severity.Information)
                        logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Information)]);
                    if (sourceSeverity == Source.SDG)
                        this.editorSDGSeverityFieldcontrolValue = JSON.stringify(logConfigMaskValue);
                    if (sourceSeverity == Source.SCL)
                        this.editorSCLSeverityFieldcontrolValue = JSON.stringify(logConfigMaskValue);
                    if (sourceSeverity == Source._6T)
                        this.editor6TSeverityFieldcontrolValue = JSON.stringify(logConfigMaskValue);
                };
                DashboardLogExtraComponent.prototype.saveLogFilterConfig = function (logConfigMask) {
                    var _this = this;
                    var logConfigMasks = [];
                    logConfigMasks.push(logConfigMask);
                    this.logService.putLogFilterConfig(logConfigMasks).subscribe(function (data) { }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                        }
                    });
                };
                DashboardLogExtraComponent.prototype.logFilterOnChange = function (logFilter, logConfigMask) {
                    logFilter.isChecked = !logFilter.isChecked;
                    if (logFilter.isChecked)
                        logConfigMask.categorymask = logConfigMask.categorymask | logFilter.value;
                    else
                        logConfigMask.categorymask = logConfigMask.categorymask & (~(logFilter.value));
                    this.saveLogFilterConfig(logConfigMask);
                };
                DashboardLogExtraComponent.prototype.optionSDGMaskCategoryOnInit = function () {
                    if (this.optionSDGCategoryList.length == 0) {
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC", value: 0x00000020, description: "TR_SDG_CATEGORY_OPC", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC_SU", value: 0x00000040, description: "TR_SDG_CATEGORY_OPC_SU", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC_UA", value: 0x00000080, description: "TR_SDG_CATEGORY_OPC_UA", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC_DEEP", value: 0x00000100, description: "TR_SDG_CATEGORY_OPC_DEEP", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_DNP", value: 0x00000200, description: "TR_SDG_CATEGORY_DNP", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_870", value: 0x00000400, description: "TR_SDG_CATEGORY_870", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_MODBUS", value: 0x00008000, description: "TR_SDG_CATEGORY_MODBUS", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_61850", value: 0x00000800, description: "TR_SDG_CATEGORY_61850", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_TASE2", value: 0x00001000, description: "TR_SDG_CATEGORY_TASE2", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_ODBC", value: 0x00002000, description: "TR_SDG_CATEGORY_ODBC", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_EQUATION", value: 0x00020000, description: "TR_SDG_CATEGORY_EQUATION", isChecked: false });
                        this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_POINTMAP", value: 0x00080000, description: "TR_SDG_CATEGORY_POINTMAP", isChecked: false });
                    }
                };
                DashboardLogExtraComponent.prototype.optionSCLMaskCategoryOnInit = function () {
                    if (this.optionSCLCategoryList.length == 0) {
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_+++USER", value: 0x00000010, description: "TR_SCL_CATEGORY_+++USER", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_===APPLICATION", value: 0x00000008, description: "TR_SCL_CATEGORY_===APPLICATION", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_~~~TRANSPORT", value: 0x00000004, description: "TR_SCL_CATEGORY_~~~TRANSPORT", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_---DATA_LINK", value: 0x00000002, description: "TR_SCL_CATEGORY_---DATA_LINK", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_...PHYSICAL", value: 0x00000001, description: "TR_SCL_CATEGORY_...PHYSICAL", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_MMI", value: 0x00000020, description: "TR_SCL_CATEGORY_MMI", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_TARGET", value: 0x00080000, description: "TR_SCL_CATEGORY_TARGET", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_STATIC_DATA", value: 0x00000080, description: "TR_SCL_CATEGORY_STATIC_DATA", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_STATIC_HDRS", value: 0x00000100, description: "TR_SCL_CATEGORY_STATIC_HDRS", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_EVENT_DATA", value: 0x00000200, description: "TR_SCL_CATEGORY_EVENT_DATA", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_EVENT_HDRS", value: 0x00000400, description: "TR_SCL_CATEGORY_EVENT_HDRS", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_CYCLIC_DATA", value: 0x00000800, description: "TR_SCL_CATEGORY_CYCLIC_DATA", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_CYCLIC_HDRS", value: 0x00001000, description: "TR_SCL_CATEGORY_CYCLIC_HDRS", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_SECURITY_DATA", value: 0x00002000, description: "TR_SCL_CATEGORY_SECURITY_DATA", isChecked: false });
                        this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_SECURITY_HDRS", value: 0x00004000, description: "TR_SCL_CATEGORY_SECURITY_HDRS", isChecked: false });
                    }
                };
                DashboardLogExtraComponent.prototype.option6TMaskCategoryOnInit = function () {
                    if (this.option6TCategoryList.length == 0) {
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_SCL", value: 0X00000001, description: "TR_6T_CATEGORY_C_SCL", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_STACK", value: 0X00000002, description: "TR_6T_CATEGORY_C_STACK", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CASM", value: 0X00000004, description: "TR_6T_CATEGORY_C_CASM", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TEST", value: 0X00000008, description: "TR_6T_CATEGORY_C_TEST", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_APP", value: 0X00000010, description: "TR_6T_CATEGORY_C_APP", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_EXTREF", value: 0X00000020, description: "TR_6T_CATEGORY_C_EXTREF", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TRANSPORT", value: 0X00000080, description: "TR_6T_CATEGORY_C_TRANSPORT", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TRANSLOW", value: 0X00000100, description: "TR_6T_CATEGORY_C_TRANSLOW", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CLIENT", value: 0X00000200, description: "TR_6T_CATEGORY_C_CLIENT", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CLIENTRPT", value: 0X00000400, description: "TR_6T_CATEGORY_C_CLIENTRPT", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CLIENTSTATE", value: 0X00000800, description: "TR_6T_CATEGORY_C_CLIENTSTATE", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TIME", value: 0X00001000, description: "TR_6T_CATEGORY_C_TIME", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TARGET", value: 0X00002000, description: "TR_6T_CATEGORY_C_TARGET", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_DYNAMIC_DATASETS", value: 0X00004000, description: "TR_6T_CATEGORY_C_DYNAMIC_DATASETS", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_GENERAL", value: 0X00010000, description: "TR_6T_CATEGORY_GENERAL", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_READ", value: 0X00020000, description: "TR_6T_CATEGORY_READ", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_WRITE", value: 0X00040000, description: "TR_6T_CATEGORY_WRITE", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_XML", value: 0X00080000, description: "TR_6T_CATEGORY_XML", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_APP", value: 0X00100000, description: "TR_6T_CATEGORY_APP", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_GOOSE", value: 0X00200000, description: "TR_6T_CATEGORY_GOOSE", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_EXTREF", value: 0X00400000, description: "TR_6T_CATEGORY_EXTREF", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_DISCOVERY", value: 0X00800000, description: "TR_6T_CATEGORY_DISCOVERY", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_REPORT", value: 0X01000000, description: "TR_6T_CATEGORY_REPORT", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_CONTROL", value: 0X02000000, description: "TR_6T_CATEGORY_CONTROL", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_CLIENTPARSEVALUES", value: 0X10000000, description: "TR_6T_CATEGORY_CLIENTPARSEVALUES", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_READ_HANDLER", value: 0X20000000, description: "TR_6T_CATEGORY_READ_HANDLER", isChecked: false });
                        this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_TIME_SYNCH", value: 0X80000000, description: "TR_6T_CATEGORY_TIME_SYNCH", isChecked: false });
                    }
                };
                DashboardLogExtraComponent.prototype.optionSeverityMaskComponentDataOnInit = function () {
                    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Exception)] });
                    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Error)] });
                    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Warning)] });
                    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Information)] });
                };
                __decorate([
                    core_1.ViewChildren(tab_component_1.TabComponent),
                    __metadata("design:type", core_1.QueryList)
                ], DashboardLogExtraComponent.prototype, "tabLogFilters", void 0);
                __decorate([
                    core_1.ViewChild('checkboxSDGSaveLogToTextFile'),
                    __metadata("design:type", core_1.ElementRef)
                ], DashboardLogExtraComponent.prototype, "checkboxSDGSaveLogToTextFile", void 0);
                DashboardLogExtraComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardLogExtraComponent",
                        styles: ["\n    .image-button-sm {\n      width: 12px;\n      height: 12px;\n      filter: drop-shadow(1px 1px 1px #404040);\n    }\n    .round-button-sm {\n      display: inline-block;\n      min-height: 18px;\n      max-height: 18px;\n      min-width: 18px;\n      max-width: 18px;\n      padding-bottom: 1px;\n      text-align: center;\n      line-height: 15px;\n      border: 1px solid gray;\n      border-radius: 50%;\n      background-color: #ebebeb;\n      filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));\n      cursor: pointer;\n    }\n\n    .round-button-sm:hover {\n      background-color: #cdcdcd;\n    }\n    .filter-device  {\n      margin: 0;\n      padding: 0;\n      margin-left: 10px;\n      list-style: none;\n      max-height: 100px;\n      overflow: auto;\n    }\n    .filter-checkbox  {\n      margin: 0; \n      padding: 0; \n      margin-left: 20px; \n      list-style: none; \n    }\n    .filter-checkbox li { \n      border: 1px transparent solid; \n      display:inline-block;\n      width:280px;\n    } \n    legend {\n      font-size: 12px;\n      margin: 2px;\n      border: 0;\n      width: auto;\n\t\t}\n    fieldset {\n      padding: 4px;\n      border: 1px solid silver;\n\t\t}"
                        ],
                        template: "\n      <div style=\"padding: 4px;\">\n        <fieldset>\n          <legend>{{'TR_DEVICE_LOGGED' | translate}}</legend>\n          <ul class=\"filter-device\">\n            <li *ngFor=\"let logDevice of logDevices;\"><label><div class=\"round-button-sm\" title=\"{{'TR_REMOVE' | translate}}\" (click)=\"removeNodeToLogFilter(logDevice.name)\" ><img [src]=\"'../../images/delete.svg'\" class=\"image-button-sm\"/></div>&nbsp;&nbsp;{{logDevice.name}}</label></li>\n          </ul>\n          <div style=\"width:99%; text-align: right;\"><button class=\"btn btn-default btn-sm\" (click)=\"removeAllDevice()\"><img src=\"../../images/delete.svg\" class=\"image-button\"/>&nbsp;{{'TR_REMOVE_ALL' | translate}}</button></div>\n        </fieldset>\n      </div>\n      <tabset (onSelect)=\"tabFilterOnChange($event)\">\n        <tab title=\"{{'TR_SDG_FILTER' | translate}}\">\n          <div *ngIf=\"isDataLoaded\" style=\"margin: 6px;\">\n            <span>{{'TR_GLOBAL_MESSAGE_SEVERITY' | translate}}</span>: <div style=\"display: inline-table;\"><comboboxEditorMultiselectComponent [componentData]=\"severitymaskComponentData\" [(editorFieldcontrolValue)]=\"editorSDGSeverityFieldcontrolValue\" (OnComboboxChange)=\"setLogVerboseFilter($event, logConfigMaskSDG)\"></comboboxEditorMultiselectComponent></div>\n          </div>\n          <div class=\"container-fluid\">\n            <div class=\"row\" style=\"max-height: 330px;overflow-x: hidden;overflow-y: auto; margin: 0px;\">\n              <div style=\"padding: 4px;\" class=\"col-md-3\">\n                <fieldset>\n                  <legend>{{'TR_SDG_OPC' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of optionSDGCategoryList.slice(0,4);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMaskSDG)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n              <div style=\"padding: 4px;\" class=\"col-md-3\">\n                <fieldset>\n                  <legend>{{'TR_SDG_SCL' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of optionSDGCategoryList.slice(4,7);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMaskSDG)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n              <div style=\"padding: 4px;\" class=\"col-md-3\">\n                <fieldset>\n                  <legend>{{'TR_SDG_MMS' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of optionSDGCategoryList.slice(7,9);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMaskSDG)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n              <div style=\"padding: 4px;\" class=\"col-md-3\">\n                <fieldset>\n                  <legend>{{'TR_SDG_OTHER' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of optionSDGCategoryList.slice(9,12);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMaskSDG)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n            </div>\n          </div>\n        </tab>\n        <tab title=\"{{'TR_SCL_FILTER' | translate}}\">\n          <div *ngIf=\"isDataLoaded\" style=\"margin: 6px;\">\n            <span>{{'TR_SCL_MESSAGE_SEVERITY' | translate}}</span>: <div style=\"display: inline-table;\"><comboboxEditorMultiselectComponent [componentData]=\"severitymaskComponentData\" [(editorFieldcontrolValue)]=\"editorSCLSeverityFieldcontrolValue\" (OnComboboxChange)=\"setLogVerboseFilter($event, logConfigMaskSCL)\"></comboboxEditorMultiselectComponent></div>\n          </div>\n          <div class=\"container-fluid\">\n            <div class=\"row\" style=\"max-height: 330px;overflow-x: hidden;overflow-y: auto; margin: 0px;\">\n              <div style=\"padding: 4px;\" class=\"col col-sm-6\">\n                <fieldset>\n                  <legend>{{'TR_SCL_PROTOCOL_LAYER' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of optionSCLCategoryList.slice(0,7);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMaskSCL)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n              <div style=\"padding: 4px;\" class=\"col col-sm-6\">\n                <fieldset>\n                  <legend>{{'TR_SCL_DATABASE' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of optionSCLCategoryList.slice(7,15);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMaskSCL)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n            </div>\n          </div>\n        </tab>\n        <tab title=\"{{'TR_6T_FILTER' | translate}}\">\n          <div *ngIf=\"isDataLoaded\" style=\"margin: 6px;\">\n            <span>{{'TR_MMS_MESSAGE_SEVERITY' | translate}}</span>: <div style=\"display: inline-table;\"><comboboxEditorMultiselectComponent [componentData]=\"severitymaskComponentData\" [(editorFieldcontrolValue)]=\"editor6TSeverityFieldcontrolValue\" (OnComboboxChange)=\"setLogVerboseFilter($event, logConfigMask6T)\"></comboboxEditorMultiselectComponent></div>\n          </div>\n          <div class=\"container-fluid\">\n            <div class=\"row\" style=\"max-height: 330px;overflow-x: hidden;overflow-y: auto; margin: 0px;\">\n              <div style=\"padding: 4px;\" class=\"col-sm-6\">\n                <fieldset>\n                  <legend>{{'TR_6T_LOW_LEVEL_STACK' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of option6TCategoryList.slice(0,14);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMask6T)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n              <div style=\"padding: 4px;\" class=\"col-sm-6\">\n                <fieldset>\n                  <legend>{{'TR_6T_STANDARD_STACK' | translate}}</legend>\n                  <ul class=\"filter-checkbox\"> \n                    <li *ngFor=\"let logFilter of option6TCategoryList.slice(14,28);\"><input type=\"checkbox\" [checked]=logFilter.isChecked (change)=\"logFilterOnChange(logFilter, logConfigMask6T)\"/><label for=\"cb1\">&nbsp;{{logFilter.name | translate}}</label></li> \n                  </ul> \n                </fieldset>\n              </div>\n            </div>\n          </div>\n        </tab>\n      </tabset>\n"
                    }),
                    __metadata("design:paramtypes", [api_1.LogService, alert_service_1.AlertService, authentication_service_1.AuthenticationService, core_2.TranslateService, wsApi_1.BroadcastEventWSApi])
                ], DashboardLogExtraComponent);
                return DashboardLogExtraComponent;
            }());
            exports_1("DashboardLogExtraComponent", DashboardLogExtraComponent);
            (function (Source) {
                Source["SDG"] = "SDG";
                Source["SCL"] = "SCL";
                Source["_6T"] = "6T";
            })(Source || (Source = {}));
            (function (Severity) {
                Severity[Severity["Exception"] = 1] = "Exception";
                Severity[Severity["Error"] = 2] = "Error";
                Severity[Severity["Warning"] = 4] = "Warning";
                Severity[Severity["Information"] = 8] = "Information";
                Severity[Severity["Debug"] = 32] = "Debug";
            })(Severity || (Severity = {}));
        }
    };
});
//# sourceMappingURL=dashboard.log.extra.component.js.map