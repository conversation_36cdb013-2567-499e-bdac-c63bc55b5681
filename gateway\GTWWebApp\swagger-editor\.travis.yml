language: node_js
node_js:
- '6.9'
services:
  - docker
branches:
  only:
  - master
  - /^v\d+\.\d+(\.\d+)?(-\S*)?$/
before_deploy:
  - "npm run build"
env:
  - DOCKER_IMAGE_NAME=swaggerapi/swagger-editor
deploy:
  - provider: npm
    email: <EMAIL>
    skip_cleanup: true
    api_key:
      secure: "jFzdukXjNKiESkh/61VUNIYHavpovSKAoVHg4ConrpnYimkrjB13mTB2ZMWXdEbNyUxfI48dYBIlnKRZfwaJ5jZ/rtyQONKslE/SDV1EgEXaL1KPv1rB2mbC3U2YiXV/QuusnmNLdxu8iAqnVcJwuCUMAb11Toq9p2KmAc2Ty8g="
    on:
      tags: true
      repo: swagger-api/swagger-editor
      node: '6.9'
  - provider: script
    skip_cleanup: true
    script: swagger-editor-dist-package/deploy.sh
    on:
      tags: true
      repo: swagger-api/swagger-editor
      node: '6.9'
after_success:
  - if [ $DOCKER_HUB_USERNAME ]; then
      docker login --email=$DOCKER_HUB_EMAIL --username=$DOCKER_HUB_USERNAME --password=$DOCKER_HUB_PASSWORD;

      if [ ! -z "$TRAVIS_TAG" ]; then
        DOCKER_IMAGE_TAG=$TRAVIS_TAG;
      else
        DOCKER_IMAGE_TAG=unstable;
      fi;
      docker build -t $DOCKER_IMAGE_NAME .;
      if [ ! -z "$TRAVIS_TAG" ]; then
        docker tag $DOCKER_IMAGE_NAME $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG;
        docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG;
        docker tag $DOCKER_IMAGE_NAME $DOCKER_IMAGE_NAME:latest;
        docker push $DOCKER_IMAGE_NAME:latest;
      else
        docker tag $DOCKER_IMAGE_NAME $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG;
        docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG;
      fi;
    fi;
