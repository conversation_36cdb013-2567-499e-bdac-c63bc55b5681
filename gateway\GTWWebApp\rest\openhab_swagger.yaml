---
swagger: "2.0"
info:
  description: "OpenHAB Examples API (for development only)"
  version: "1.0.0"
  title: "openHAB REST API"
basePath: "/rest"
tags:
- name: "bindings"
- name: "links"
- name: "config-descriptions"
- name: "thing-types"
- name: "sitemaps"
- name: "discovery"
- name: "items"
- name: "uuid"
- name: "channel-types"
- name: "services"
- name: "extensions"
- name: "things"
- name: "persistence"
- name: "inbox"
- name: "voice"
paths:
  /:
    get:
      operationId: "getRoot"
      produces:
      - "application/json"
      parameters: []
      responses:
        default:
          description: "successful operation"
  /bindings:
    get:
      tags:
      - "bindings"
      summary: "Get all bindings."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /bindings/{bindingId}/config:
    get:
      tags:
      - "bindings"
      summary: "Get binding configuration for given binding ID."
      description: ""
      operationId: "getConfiguration"
      produces:
      - "application/json"
      parameters:
      - name: "bindingId"
        in: "path"
        description: "service ID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        500:
          description: "Configuration can not be read due to internal error"
        404:
          description: "Binding does not exist"
    put:
      tags:
      - "bindings"
      summary: "Updates a binding configuration for given binding ID and returns the old configuration."
      description: ""
      operationId: "updateConfiguration"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "bindingId"
        in: "path"
        description: "service ID"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          type: "object"
          additionalProperties:
            type: "object"
      responses:
        200:
          description: "OK"
        500:
          description: "Configuration can not be updated due to internal error"
        204:
          description: "No old configuration"
        404:
          description: "Binding does not exist"
  /channel-types:
    get:
      tags:
      - "channel-types"
      summary: "Gets all available channel types."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /channel-types/{channelTypeUID}:
    get:
      tags:
      - "channel-types"
      summary: "Gets channel type by UID."
      description: ""
      operationId: "getByUID"
      produces:
      - "application/json"
      parameters:
      - name: "channelTypeUID"
        in: "path"
        description: "channelTypeUID"
        required: true
        type: "string"
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      responses:
        200:
          description: "Channel type with provided channelTypeUID does not exist."
        404:
          description: "No content"
  /config-descriptions:
    get:
      tags:
      - "config-descriptions"
      summary: "Gets all available config descriptions."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /config-descriptions/{uri}:
    get:
      tags:
      - "config-descriptions"
      summary: "Gets a config description by URI."
      description: ""
      operationId: "getByURI"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      - name: "uri"
        in: "path"
        description: "uri"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Invalid URI syntax"
        404:
          description: "Not found"
  /discovery:
    get:
      tags:
      - "discovery"
      summary: "Gets all bindings that support discovery."
      description: ""
      operationId: "getDiscoveryServices"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "OK"
  /discovery/bindings/{bindingId}/scan:
    post:
      tags:
      - "discovery"
      summary: "Starts asynchronous discovery process for a binding and returns the timeout in seconds of the discovery operation."
      description: ""
      operationId: "scan"
      produces:
      - "text/plain"
      parameters:
      - name: "bindingId"
        in: "path"
        description: "bindingId"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
  /extensions:
    get:
      tags:
      - "extensions"
      summary: "Get all extensions."
      description: ""
      operationId: "getExtensions"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /extensions/types:
    get:
      tags:
      - "extensions"
      summary: "Get all extension types."
      description: ""
      operationId: "getTypes"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /extensions/{extensionId}:
    get:
      tags:
      - "extensions"
      summary: "Get extension with given ID."
      description: ""
      operationId: "getById"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "extensionId"
        in: "path"
        description: "extension ID"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9-]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Not found"
  /extensions/{extensionId}/install:
    post:
      tags:
      - "extensions"
      summary: "Installs the extension with the given ID."
      description: ""
      operationId: "installExtension"
      parameters:
      - name: "extensionId"
        in: "path"
        description: "extension ID"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9-]*"
      responses:
        200:
          description: "OK"
  /extensions/{extensionId}/uninstall:
    post:
      tags:
      - "extensions"
      operationId: "uninstallExtension"
      parameters:
      - name: "extensionId"
        in: "path"
        description: "extension ID"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9-]*"
      responses:
        200:
          description: "OK"
  /iconsets:
    get:
      operationId: "getAll"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        required: false
        type: "string"
      responses:
        default:
          description: "successful operation"
  /inbox:
    get:
      tags:
      - "inbox"
      summary: "Get all discovered things."
      description: ""
      operationId: "getAll"
      produces:
      - "*/*"
      parameters: []
      responses:
        200:
          description: "OK"
  /inbox/{thingUID}:
    delete:
      tags:
      - "inbox"
      summary: "Removes the discovery result from the inbox."
      description: ""
      operationId: "delete"
      parameters:
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Discovery result not found in the inbox."
  /inbox/{thingUID}/approve:
    post:
      tags:
      - "inbox"
      summary: "Approves the discovery result by adding the thing to the registry."
      description: ""
      operationId: "approve"
      consumes:
      - "text/plain"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "thing label"
        required: false
        schema:
          type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Thing not found in the inbox."
        409:
          description: "No binding found that supports this thing."
  /inbox/{thingUID}/ignore:
    post:
      tags:
      - "inbox"
      summary: "Flags a discovery result as ignored for further processing."
      description: ""
      operationId: "ignore"
      parameters:
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
  /inbox/{thingUID}/unignore:
    post:
      tags:
      - "inbox"
      summary: "Removes ignore flag from a discovery result."
      description: ""
      operationId: "unignore"
      parameters:
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
  /items:
    get:
      tags:
      - "items"
      summary: "Get all available items."
      description: ""
      operationId: "getItems"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "type"
        in: "query"
        description: "item type filter"
        required: false
        type: "string"
      - name: "tags"
        in: "query"
        description: "item tag filter"
        required: false
        type: "string"
      - name: "recursive"
        in: "query"
        description: "get member items recursivly"
        required: false
        type: "boolean"
        default: false
      responses:
        200:
          description: "OK"
  /items/{itemName}/members/{memberItemName}:
    put:
      tags:
      - "items"
      summary: "Adds a new member to a group item."
      description: ""
      operationId: "addMember"
      parameters:
      - name: "itemName"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "memberItemName"
        in: "path"
        description: "member item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Item or member item not found or item is not of type group item."
        405:
          description: "Member item is not editable."
    delete:
      tags:
      - "items"
      summary: "Removes an existing member from a group item."
      description: ""
      operationId: "removeMember"
      parameters:
      - name: "itemName"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "memberItemName"
        in: "path"
        description: "member item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Item or member item not found or item is not of type group item."
        405:
          description: "Member item is not editable."
  /items/{itemname}:
    get:
      tags:
      - "items"
      summary: "Gets a single item."
      description: ""
      operationId: "getItemData"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Item not found"
    post:
      tags:
      - "items"
      summary: "Sends a command to an item."
      description: ""
      operationId: "postItemCommand"
      consumes:
      - "text/plain"
      parameters:
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - in: "body"
        name: "body"
        description: "valid item command (e.g. ON, OFF, UP, DOWN, REFRESH)"
        required: true
        schema:
          type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Item command null"
        404:
          description: "Item not found"
    put:
      tags:
      - "items"
      summary: "Adds a new item to the registry or updates the existing item."
      description: ""
      operationId: "createOrUpdateItem"
      consumes:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - in: "body"
        name: "body"
        description: "item data"
        required: true
        schema:
          $ref: "#/definitions/GroupItemDTO"
      responses:
        200:
          description: "OK"
        201:
          description: "Item created."
        400:
          description: "Item null."
        404:
          description: "Item not found."
        405:
          description: "Item not editable."
    delete:
      tags:
      - "items"
      summary: "Removes an item from the registry."
      description: ""
      operationId: "removeItem"
      parameters:
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Item not found or item is not editable."
  /items/{itemname}/state:
    get:
      tags:
      - "items"
      summary: "Gets the state of an item."
      description: ""
      operationId: "getPlainItemState"
      produces:
      - "text/plain"
      parameters:
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Item not found"
    put:
      tags:
      - "items"
      summary: "Updates the state of an item."
      description: ""
      operationId: "putItemState"
      consumes:
      - "text/plain"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - in: "body"
        name: "body"
        description: "valid item state (e.g. ON, OFF)"
        required: true
        schema:
          type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Item state null"
        404:
          description: "Item not found"
  /items/{itemname}/tags/{tag}:
    put:
      tags:
      - "items"
      summary: "Adds a tag to an item."
      description: ""
      operationId: "addTag"
      parameters:
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "tag"
        in: "path"
        description: "tag"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Item not found."
        405:
          description: "Item not editable."
    delete:
      tags:
      - "items"
      summary: "Removes a tag from an item."
      description: ""
      operationId: "removeTag"
      parameters:
      - name: "itemname"
        in: "path"
        description: "item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "tag"
        in: "path"
        description: "tag"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Item not found."
        405:
          description: "Item not editable."
  /links:
    get:
      tags:
      - "links"
      summary: "Gets all available links."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "OK"
  /links/auto:
    get:
      tags:
      - "links"
      summary: "Tells whether automatic link mode is active or not"
      description: ""
      operationId: "isAutomatic"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "OK"
  /links/{itemName}/{channelUID}:
    put:
      tags:
      - "links"
      summary: "Links item to a channel."
      description: ""
      operationId: "link"
      parameters:
      - name: "itemName"
        in: "path"
        description: "itemName"
        required: true
        type: "string"
      - name: "channelUID"
        in: "path"
        description: "channelUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Item already linked to the channel."
    delete:
      tags:
      - "links"
      summary: "Unlinks item from a channel."
      description: ""
      operationId: "unlink"
      parameters:
      - name: "itemName"
        in: "path"
        description: "itemName"
        required: true
        type: "string"
      - name: "channelUID"
        in: "path"
        description: "channelUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Link not found."
        405:
          description: "Link not editable."
  /persistence:
    get:
      tags:
      - "persistence"
      summary: "Gets a list of persistence services."
      description: ""
      operationId: "httpGetPersistenceServices"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /persistence/items:
    get:
      tags:
      - "persistence"
      summary: "Gets a list of items available via a specific persistence service."
      description: ""
      operationId: "httpGetPersistenceServiceItems"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "query"
        description: "Id of the persistence service. If not provided the default service will be used"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /persistence/items/{itemname}:
    get:
      tags:
      - "persistence"
      summary: "Gets item persistence data from the persistence service."
      description: ""
      operationId: "httpGetPersistenceItemData"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "query"
        description: "Id of the persistence service. If not provided the default service will be used"
        required: false
        type: "string"
      - name: "itemname"
        in: "path"
        description: "The item name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "starttime"
        in: "query"
        description: "Start time of the data to return. Will default to 1 day before endtime. [yyyy-MM-dd'T'HH:mm:ss.SSSZ]"
        required: false
        type: "string"
      - name: "endtime"
        in: "query"
        description: "End time of the data to return. Will default to current time. [yyyy-MM-dd'T'HH:mm:ss.SSSZ]"
        required: false
        type: "string"
      - name: "page"
        in: "query"
        description: "Page number of data to return. This parameter will enable paging."
        required: false
        type: "integer"
        format: "int32"
      - name: "pagelength"
        in: "query"
        description: "The length of each page."
        required: false
        type: "integer"
        format: "int32"
      - name: "boundary"
        in: "query"
        description: "Gets one value before and after the requested period."
        required: false
        type: "boolean"
      responses:
        200:
          description: "OK"
        404:
          description: "Unknown Item or persistence service"
    put:
      tags:
      - "persistence"
      summary: "Stores item persistence data into the persistence service."
      description: ""
      operationId: "httpPutPersistenceItemData"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "query"
        description: "Id of the persistence service. If not provided the default service will be used"
        required: false
        type: "string"
      - name: "itemname"
        in: "path"
        description: "The item name."
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "time"
        in: "query"
        description: "Time of the data to be stored. Will default to current time. [yyyy-MM-dd'T'HH:mm:ss.SSSZ]"
        required: true
        type: "string"
      - name: "state"
        in: "query"
        description: "The state to store."
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Unknown Item or persistence service"
    delete:
      tags:
      - "persistence"
      summary: "Delete item data from a specific persistence service."
      description: ""
      operationId: "httpDeletePersistenceServiceItem"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "query"
        description: "Id of the persistence service."
        required: true
        type: "string"
      - name: "itemname"
        in: "path"
        description: "The item name."
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "starttime"
        in: "query"
        description: "Start time of the data to return. [yyyy-MM-dd'T'HH:mm:ss.SSSZ]"
        required: true
        type: "string"
      - name: "endtime"
        in: "query"
        description: "End time of the data to return. [yyyy-MM-dd'T'HH:mm:ss.SSSZ]"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Invalid filter parameters"
        404:
          description: "Unknown persistence service"
  /services:
    get:
      tags:
      - "services"
      summary: "Get all configurable services."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "OK"
  /services/{serviceId}:
    get:
      tags:
      - "services"
      summary: "Get configurable service for given service ID."
      description: ""
      operationId: "getById"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "path"
        description: "service ID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Not found"
  /services/{serviceId}/config:
    get:
      tags:
      - "services"
      summary: "Get service configuration for given service ID."
      description: ""
      operationId: "getConfiguration"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "path"
        description: "service ID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        500:
          description: "Configuration can not be read due to internal error"
    put:
      tags:
      - "services"
      summary: "Updates a service configuration for given service ID and returns the old configuration."
      description: ""
      operationId: "updateConfiguration"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "path"
        description: "service ID"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        required: false
        schema:
          type: "object"
          additionalProperties:
            type: "object"
      responses:
        200:
          description: "OK"
        500:
          description: "Configuration can not be updated due to internal error"
        204:
          description: "No old configuration"
    delete:
      tags:
      - "services"
      summary: "Deletes a service configuration for given service ID and returns the old configuration."
      description: ""
      operationId: "deleteConfiguration"
      produces:
      - "application/json"
      parameters:
      - name: "serviceId"
        in: "path"
        description: "service ID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        500:
          description: "Configuration can not be deleted due to internal error"
        204:
          description: "No old configuration"
  /sitemaps:
    get:
      tags:
      - "sitemaps"
      summary: "Get all available sitemaps."
      description: ""
      operationId: "getSitemaps"
      produces:
      - "application/json"
      parameters: []
      responses:
        200:
          description: "OK"
  /sitemaps/events/subscribe:
    post:
      tags:
      - "sitemaps"
      summary: "Creates a sitemap event subscription."
      description: ""
      operationId: "createEventSubscription"
      parameters: []
      responses:
        200:
          description: "successful operation"
          schema:
            type: "object"
        201:
          description: "Subscription created."
  /sitemaps/events/{subscriptionid}:
    get:
      tags:
      - "sitemaps"
      summary: "Get sitemap events."
      description: ""
      operationId: "getSitemapEvents"
      produces:
      - "text/event-stream"
      parameters:
      - name: "subscriptionid"
        in: "path"
        description: "subscription id"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9-]*"
      - name: "sitemap"
        in: "query"
        description: "sitemap name"
        required: false
        type: "string"
      - name: "pageid"
        in: "query"
        description: "page id"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Subscription not found."
  /sitemaps/{sitemapname}:
    get:
      tags:
      - "sitemaps"
      summary: "Get sitemap by name."
      description: ""
      operationId: "getSitemapData"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "sitemapname"
        in: "path"
        description: "sitemap name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "type"
        in: "query"
        required: false
        type: "string"
      - name: "jsoncallback"
        in: "query"
        required: false
        type: "string"
        default: "callback"
      responses:
        200:
          description: "OK"
  /sitemaps/{sitemapname}/{pageid}:
    get:
      tags:
      - "sitemaps"
      summary: "Polls the data for a sitemap."
      description: ""
      operationId: "getPageData"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "sitemapname"
        in: "path"
        description: "sitemap name"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "pageid"
        in: "path"
        description: "page id"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      - name: "subscriptionid"
        in: "query"
        description: "subscriptionid"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "Invalid subscription id has been provided."
        404:
          description: "Sitemap with requested name does not exist or page does not exist, or page refers to a non-linkable widget"
  /thing-types:
    get:
      tags:
      - "thing-types"
      summary: "Gets all available thing types without config description, channels and properties."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
  /thing-types/{thingTypeUID}:
    get:
      tags:
      - "thing-types"
      summary: "Gets thing type by UID."
      description: ""
      operationId: "getByUID"
      produces:
      - "application/json"
      parameters:
      - name: "thingTypeUID"
        in: "path"
        description: "thingTypeUID"
        required: true
        type: "string"
      - name: "Accept-Language"
        in: "header"
        description: "Accept-Language"
        required: false
        type: "string"
      responses:
        200:
          description: "Thing type with provided thingTypeUID does not exist."
        404:
          description: "No content"
  /things:
    get:
      tags:
      - "things"
      summary: "Get all available things."
      description: ""
      operationId: "getAll"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
    post:
      tags:
      - "things"
      summary: "Creates a new thing and adds it to the registry."
      description: ""
      operationId: "create"
      consumes:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - in: "body"
        name: "body"
        description: "thing data"
        required: true
        schema:
          $ref: "#/definitions/ThingDTO"
      responses:
        200:
          description: "OK"
        400:
          description: "A uid must be provided, if no binding can create a thing of this type."
        409:
          description: "A thing with the same uid already exists."
  /things/{thingUID}:
    get:
      tags:
      - "things"
      summary: "Gets thing by UID."
      description: ""
      operationId: "getByUID"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Thing with provided thingUID does not exist."
    put:
      tags:
      - "things"
      summary: "Updates a thing."
      description: ""
      operationId: "update"
      consumes:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "thing"
        required: true
        schema:
          $ref: "#/definitions/ThingDTO"
      responses:
        200:
          description: "OK"
        404:
          description: "Thing not found"
    delete:
      tags:
      - "things"
      summary: "Removes a thing from the registry. Set 'force' to __true__ if you want the thing te be removed immediately."
      description: ""
      operationId: "remove"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      - name: "force"
        in: "query"
        description: "force"
        required: false
        type: "boolean"
        default: false
      responses:
        200:
          description: "OK, was deleted."
        202:
          description: "ACCEPTED for asynchronous deletion."
        404:
          description: "Thing not found."
        409:
          description: "CONFLICT, Thing could not be deleted because it's not managed."
  /things/{thingUID}/channels/{channelId}/link:
    post:
      tags:
      - "things"
      summary: "Links item to a channel. Creates item if such does not exist yet."
      description: ""
      operationId: "link"
      consumes:
      - "text/plain"
      parameters:
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      - name: "channelId"
        in: "path"
        description: "channelId"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "item name"
        required: false
        schema:
          type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Thing not found or channel not found"
    delete:
      tags:
      - "things"
      summary: "Unlinks item from a channel."
      description: ""
      operationId: "unlink"
      parameters:
      - name: "thingUID"
        in: "path"
        description: "thingUID"
        required: true
        type: "string"
      - name: "channelId"
        in: "path"
        description: "channelId"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "channelId"
        required: false
        schema:
          type: "string"
      responses:
        200:
          description: "OK"
  /things/{thingUID}/config:
    put:
      tags:
      - "things"
      summary: "Updates thing's configuration."
      description: ""
      operationId: "updateConfiguration"
      consumes:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        required: false
        type: "string"
      - name: "thingUID"
        in: "path"
        description: "thing"
        required: true
        type: "string"
      - in: "body"
        name: "body"
        description: "configuration parameters"
        required: false
        schema:
          type: "object"
          additionalProperties:
            type: "object"
      responses:
        200:
          description: "OK"
        404:
          description: "Thing not found"
  /things/{thingUID}/config/status:
    get:
      tags:
      - "things"
      summary: "Gets thing's config status."
      description: ""
      operationId: "getConfigStatus"
      parameters:
      - name: "Accept-Language"
        in: "header"
        required: false
        type: "string"
      - name: "thingUID"
        in: "path"
        description: "thing"
        required: true
        type: "string"
      responses:
        200:
          description: "OK"
        404:
          description: "Thing not found."
  /uuid:
    get:
      tags:
      - "uuid"
      summary: "A unified unique id."
      description: ""
      operationId: "getInstanceUUID"
      produces:
      - "text/plain"
      parameters: []
      responses:
        200:
          description: "OK"
  /voice/interpreters:
    get:
      tags:
      - "voice"
      summary: "Get the list of all interpreters."
      description: ""
      operationId: "getInterpreters"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      responses:
        200:
          description: "OK"
    post:
      tags:
      - "voice"
      summary: "Sends a text to the default human language interpreter."
      description: ""
      operationId: "interpret"
      consumes:
      - "text/plain"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - in: "body"
        name: "body"
        description: "text to interpret"
        required: true
        schema:
          type: "string"
      responses:
        200:
          description: "OK"
        400:
          description: "interpretation exception occurs"
        404:
          description: "No human language interpreter was found."
  /voice/interpreters/{id}:
    get:
      tags:
      - "voice"
      summary: "Gets a single interpreters."
      description: ""
      operationId: "getInterpreter"
      produces:
      - "application/json"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - name: "id"
        in: "path"
        description: "interpreter id"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        404:
          description: "Interpreter not found"
    post:
      tags:
      - "voice"
      summary: "Sends a text to a given human language interpreter."
      description: ""
      operationId: "interpret"
      consumes:
      - "text/plain"
      parameters:
      - name: "Accept-Language"
        in: "header"
        description: "language"
        required: false
        type: "string"
      - in: "body"
        name: "body"
        description: "text to interpret"
        required: true
        schema:
          type: "string"
      - name: "id"
        in: "path"
        description: "interpreter id"
        required: true
        type: "string"
        pattern: "[a-zA-Z_0-9]*"
      responses:
        200:
          description: "OK"
        400:
          description: "interpretation exception occurs"
        404:
          description: "No human language interpreter was found."
definitions:
  SitemapDTO:
    type: "object"
    properties:
      name:
        type: "string"
      icon:
        type: "string"
      label:
        type: "string"
      link:
        type: "string"
      homepage:
        $ref: "#/definitions/PageDTO"
  ChannelDTO:
    type: "object"
    properties:
      uid:
        type: "string"
      id:
        type: "string"
      channelTypeUID:
        type: "string"
      itemType:
        type: "string"
      kind:
        type: "string"
      label:
        type: "string"
      description:
        type: "string"
      defaultTags:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      properties:
        type: "object"
        additionalProperties:
          type: "string"
      configuration:
        type: "object"
        additionalProperties:
          type: "object"
  ChannelGroupDefinitionDTO:
    type: "object"
    properties:
      id:
        type: "string"
      description:
        type: "string"
      label:
        type: "string"
      channels:
        type: "array"
        items:
          $ref: "#/definitions/ChannelDefinitionDTO"
  ConfigurableServiceDTO:
    type: "object"
    properties:
      id:
        type: "string"
      label:
        type: "string"
      category:
        type: "string"
      configDescriptionURI:
        type: "string"
  BindingInfoDTO:
    type: "object"
    properties:
      author:
        type: "string"
      description:
        type: "string"
      id:
        type: "string"
      name:
        type: "string"
      configDescriptionURI:
        type: "string"
  WidgetDTO:
    type: "object"
    properties:
      widgetId:
        type: "string"
      type:
        type: "string"
      name:
        type: "string"
      label:
        type: "string"
      icon:
        type: "string"
      labelcolor:
        type: "string"
      valuecolor:
        type: "string"
      mappings:
        type: "array"
        items:
          $ref: "#/definitions/MappingDTO"
      switchSupport:
        type: "boolean"
        default: false
      sendFrequency:
        type: "integer"
        format: "int32"
      separator:
        type: "string"
      refresh:
        type: "integer"
        format: "int32"
      height:
        type: "integer"
        format: "int32"
      minValue:
        type: "number"
      maxValue:
        type: "number"
      step:
        type: "number"
      url:
        type: "string"
      encoding:
        type: "string"
      service:
        type: "string"
      period:
        type: "string"
      item:
        $ref: "#/definitions/EnrichedItemDTO"
      linkedPage:
        $ref: "#/definitions/PageDTO"
      widgets:
        type: "array"
        items:
          $ref: "#/definitions/WidgetDTO"
  ItemHistoryDTO:
    type: "object"
    properties:
      name:
        type: "string"
      totalrecords:
        type: "string"
      datapoints:
        type: "string"
      data:
        type: "array"
        items:
          $ref: "#/definitions/HistoryDataBean"
  EventOutput:
    type: "object"
    properties:
      type:
        $ref: "#/definitions/Type"
      closed:
        type: "boolean"
        default: false
  EnrichedItemDTO:
    type: "object"
    properties:
      type:
        type: "string"
      name:
        type: "string"
      label:
        type: "string"
      category:
        type: "string"
      tags:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      groupNames:
        type: "array"
        items:
          type: "string"
      link:
        type: "string"
      state:
        type: "string"
      stateDescription:
        $ref: "#/definitions/StateDescription"
  FilterCriteriaDTO:
    type: "object"
    properties:
      value:
        type: "string"
      name:
        type: "string"
  GroupFunctionDTO:
    type: "object"
    properties:
      name:
        type: "string"
      params:
        type: "array"
        items:
          type: "string"
  StateDescription:
    type: "object"
    properties:
      minimum:
        type: "number"
      maximum:
        type: "number"
      step:
        type: "number"
      pattern:
        type: "string"
      readOnly:
        type: "boolean"
        default: false
      options:
        type: "array"
        items:
          $ref: "#/definitions/StateOption"
  ConfigDescriptionParameterDTO:
    type: "object"
    properties:
      context:
        type: "string"
      defaultValue:
        type: "string"
      description:
        type: "string"
      label:
        type: "string"
      name:
        type: "string"
      required:
        type: "boolean"
        default: false
      type:
        type: "string"
        enum:
        - "TEXT"
        - "INTEGER"
        - "DECIMAL"
        - "BOOLEAN"
      min:
        type: "number"
      max:
        type: "number"
      stepsize:
        type: "number"
      pattern:
        type: "string"
      readOnly:
        type: "boolean"
        default: false
      multiple:
        type: "boolean"
        default: false
      multipleLimit:
        type: "integer"
        format: "int32"
      groupName:
        type: "string"
      advanced:
        type: "boolean"
        default: false
      limitToOptions:
        type: "boolean"
        default: false
      unit:
        type: "string"
      unitLabel:
        type: "string"
      options:
        type: "array"
        items:
          $ref: "#/definitions/ParameterOptionDTO"
      filterCriteria:
        type: "array"
        items:
          $ref: "#/definitions/FilterCriteriaDTO"
  ConfigDescriptionDTO:
    type: "object"
    properties:
      uri:
        type: "string"
      parameters:
        type: "array"
        items:
          $ref: "#/definitions/ConfigDescriptionParameterDTO"
      parameterGroups:
        type: "array"
        items:
          $ref: "#/definitions/ConfigDescriptionParameterGroupDTO"
  PageDTO:
    type: "object"
    properties:
      id:
        type: "string"
      title:
        type: "string"
      icon:
        type: "string"
      link:
        type: "string"
      parent:
        $ref: "#/definitions/PageDTO"
      leaf:
        type: "boolean"
        default: false
      widgets:
        type: "array"
        items:
          $ref: "#/definitions/WidgetDTO"
  ThingDTO:
    type: "object"
    properties:
      label:
        type: "string"
      bridgeUID:
        type: "string"
      configuration:
        type: "object"
        additionalProperties:
          type: "object"
      properties:
        type: "object"
        additionalProperties:
          type: "string"
      UID:
        type: "string"
      thingTypeUID:
        type: "string"
      channels:
        type: "array"
        items:
          $ref: "#/definitions/ChannelDTO"
      location:
        type: "string"
  ThingTypeDTO:
    type: "object"
    properties:
      UID:
        type: "string"
      label:
        type: "string"
      description:
        type: "string"
      listed:
        type: "boolean"
        default: false
      supportedBridgeTypeUIDs:
        type: "array"
        items:
          type: "string"
      bridge:
        type: "boolean"
        default: false
      channels:
        type: "array"
        items:
          $ref: "#/definitions/ChannelDefinitionDTO"
      channelGroups:
        type: "array"
        items:
          $ref: "#/definitions/ChannelGroupDefinitionDTO"
      configParameters:
        type: "array"
        items:
          $ref: "#/definitions/ConfigDescriptionParameterDTO"
      parameterGroups:
        type: "array"
        items:
          $ref: "#/definitions/ConfigDescriptionParameterGroupDTO"
      properties:
        type: "object"
        additionalProperties:
          type: "string"
  ChannelTypeDTO:
    type: "object"
    properties:
      parameters:
        type: "array"
        items:
          $ref: "#/definitions/ConfigDescriptionParameterDTO"
      parameterGroups:
        type: "array"
        items:
          $ref: "#/definitions/ConfigDescriptionParameterGroupDTO"
      description:
        type: "string"
      label:
        type: "string"
      category:
        type: "string"
      itemType:
        type: "string"
      kind:
        type: "string"
      stateDescription:
        $ref: "#/definitions/StateDescription"
      tags:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      UID:
        type: "string"
      advanced:
        type: "boolean"
        default: false
  ThingStatusInfo:
    type: "object"
    properties:
      status:
        type: "string"
        enum:
        - "UNINITIALIZED"
        - "INITIALIZING"
        - "UNKNOWN"
        - "ONLINE"
        - "OFFLINE"
        - "REMOVING"
        - "REMOVED"
      statusDetail:
        type: "string"
        enum:
        - "NONE"
        - "HANDLER_MISSING_ERROR"
        - "HANDLER_REGISTERING_ERROR"
        - "HANDLER_INITIALIZING_ERROR"
        - "HANDLER_CONFIGURATION_PENDING"
        - "CONFIGURATION_PENDING"
        - "COMMUNICATION_ERROR"
        - "CONFIGURATION_ERROR"
        - "BRIDGE_OFFLINE"
        - "FIRMWARE_UPDATING"
        - "DUTY_CYCLE"
      description:
        type: "string"
  ParameterOptionDTO:
    type: "object"
    properties:
      label:
        type: "string"
      value:
        type: "string"
  ItemChannelLinkDTO:
    type: "object"
    properties:
      itemName:
        type: "string"
      channelUID:
        type: "string"
  StrippedThingTypeDTO:
    type: "object"
    properties:
      UID:
        type: "string"
      label:
        type: "string"
      description:
        type: "string"
      listed:
        type: "boolean"
        default: false
      supportedBridgeTypeUIDs:
        type: "array"
        items:
          type: "string"
      bridge:
        type: "boolean"
        default: false
  StateOption:
    type: "object"
    properties:
      value:
        type: "string"
      label:
        type: "string"
  Extension:
    type: "object"
    properties:
      id:
        type: "string"
      label:
        type: "string"
      version:
        type: "string"
      link:
        type: "string"
      installed:
        type: "boolean"
        default: false
      type:
        type: "string"
      description:
        type: "string"
      backgroundColor:
        type: "string"
      imageLink:
        type: "string"
  ConfigDescriptionParameterGroupDTO:
    type: "object"
    properties:
      name:
        type: "string"
      context:
        type: "string"
      advanced:
        type: "boolean"
        default: false
      label:
        type: "string"
      description:
        type: "string"
  Type:
    type: "object"
    properties:
      typeName:
        type: "string"
  ChannelDefinitionDTO:
    type: "object"
    properties:
      description:
        type: "string"
      id:
        type: "string"
      label:
        type: "string"
      tags:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      properties:
        type: "object"
        additionalProperties:
          type: "string"
      category:
        type: "string"
      stateDescription:
        $ref: "#/definitions/StateDescription"
      advanced:
        type: "boolean"
        default: false
      typeUID:
        type: "string"
  GroupItemDTO:
    type: "object"
    properties:
      type:
        type: "string"
      name:
        type: "string"
      label:
        type: "string"
      category:
        type: "string"
      tags:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
      groupNames:
        type: "array"
        items:
          type: "string"
      groupType:
        type: "string"
      function:
        $ref: "#/definitions/GroupFunctionDTO"
  EnrichedThingDTO:
    type: "object"
    properties:
      label:
        type: "string"
      bridgeUID:
        type: "string"
      configuration:
        type: "object"
        additionalProperties:
          type: "object"
      properties:
        type: "object"
        additionalProperties:
          type: "string"
      UID:
        type: "string"
      thingTypeUID:
        type: "string"
      channels:
        type: "array"
        items:
          $ref: "#/definitions/ChannelDTO"
      location:
        type: "string"
      statusInfo:
        $ref: "#/definitions/ThingStatusInfo"
  ExtensionType:
    type: "object"
    properties:
      id:
        type: "string"
      label:
        type: "string"
  HumanLanguageInterpreterDTO:
    type: "object"
    properties:
      id:
        type: "string"
      label:
        type: "string"
      locales:
        type: "array"
        uniqueItems: true
        items:
          type: "string"
  MappingDTO:
    type: "object"
    properties:
      command:
        type: "string"
      label:
        type: "string"
  HistoryDataBean:
    type: "object"
    properties:
      time:
        type: "integer"
        format: "int64"
      state:
        type: "string"
