/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum TargetTCPModeEnumDTO {
    SERVER = <any> 'TMWTARGTCP_MODE_SERVER',
    CLIENT = <any> 'TMWTARGTCP_MODE_CLIENT',
    UDP = <any> 'TMWTARGTCP_MODE_UDP',
    DUALENDPOINT = <any> 'TMWTARGTCP_MODE_DUAL_ENDPOINT'
}
