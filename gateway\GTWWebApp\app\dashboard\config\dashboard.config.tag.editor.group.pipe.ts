﻿import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'editorGroupPipe' })
export class DashboardConfigTagEditorGroupPipe implements PipeTransform {
  transform(items: any[], groupPanelName: any, fieldsetName: any): any {
    let result = [];
    try {
      if (!items || !(groupPanelName || fieldsetName)) {
        return items;
      }
      if (groupPanelName && !fieldsetName){
        result = items.filter(item => item.groupPanelName == groupPanelName);
      }
      if (!groupPanelName && fieldsetName) {
        result = items.filter(item => item.fieldsetName == fieldsetName);
      }
      if (groupPanelName && fieldsetName) {
        result = items.filter(item => item.fieldsetName == fieldsetName && item.groupPanelName == groupPanelName);
      }
    }
    catch (err) {
    }
    return result;
  }
}
