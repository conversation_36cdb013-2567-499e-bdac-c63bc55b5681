﻿import { Component, AfterViewChecked, ElementRef, ViewChild, OnInit } from "@angular/core";
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { Message } from './message';

@Component({
  selector: "alertLogModal",
  template: `<div class="container-fluid tag-options-modal-container">
                <div class="tag-modal-heading">
                 {{this.title | translate}}
	              </div>
                <div #scrollMe style="max-height:550px;width:550px;overflow: auto; margin:20px">
                  <table style="width:100%;background-image: linear-gradient(to bottom, rgba(231, 240, 247, 0.85), rgba(231, 240, 247, 0.95));">
									  <tr *ngFor="let message of messages;">
                      <td *ngIf="message.type=='information'" class="td-icon"><div class="glyphicon glyphicon-info-sign icon" style="color: blue;"></div></td>
                      <td *ngIf="message.type=='warning'" class="td-icon"><div class="glyphicon glyphicon-exclamation-sign icon" style="color: orange;"></div></td>
                      <td *ngIf="message.type=='error'" class="td-icon"><div class="glyphicon glyphicon-warning-sign icon" style="color: red;"></div></td>
                      <td *ngIf="message.type=='success'" class="td-icon"><div class="glyphicon glyphicon-ok-sign icon" style="color: green;"></div></td>
                      <td *ngIf="message.type=='debug'" class="td-icon"><div class="glyphicon glyphicon-bell icon" style="color: yellow;"></div></td>
										  <td>{{message.type | translate }}</td>
                      <td>{{message.text | translate: { arg1: message.arg1, arg2: message.arg2, arg3: message.arg3 } }}</td>
									  </tr>
							  </table>
              </div>
              <div style="margin-left: 480px;margin-bottom: 16px;">
		            <button type="reset" class="btn btn-default" (click)="close()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CLOSE' | translate}}</button>
		          </div>
            </div>`,
  styles: [`
      td {
				padding-right: 6px;
				padding-left: 6px;
        padding-top: 6px;
      }
      tr:nth-of-type(odd) {
          background-color: rgba(125, 125, 0, 0.05) !important;
      }
      .td-icon {
        width: 10px;
      }
      .icon {
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
      }
      .tag-modal-heading {
        background-color: #d8d8d8;
        padding: 8px 10px 6px 10px;
        font-size: 22px;
        font-weight: bold;
        border-bottom: 1px solid #a31c3f;
        overflow-wrap: break-word;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }
      `]
})


export class AlertLogModal implements OnInit, AfterViewChecked, CloseGuard, ModalComponent<MessagesModalContext> {
  private context: MessagesModalContext;
  private messages: Array<Message> = [];
  private title: string;
  @ViewChild("scrollMe", { static: false }) private myScrollContainer: ElementRef;

  constructor(public dialog: DialogRef<MessagesModalContext>) {
    this.context = dialog.context;
    this.messages = this.context.messages;
    this.title = this.context.title;
    this.context.dialogClass = "modal-dialog modal-md";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }

  public ngOnInit(): void {
    this.scrollToBottom();
  }

  public ngAfterViewChecked(): void {
    this.scrollToBottom();
  }

  private close() {
    this.dialog.close({ result: true });
  }

  public beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private scrollToBottom(): void {
    try {
      if ((this.myScrollContainer.nativeElement.scrollHeight - this.myScrollContainer.nativeElement.scrollTop)<200)
        this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    }
    catch (err) { }
  }
}
export class MessagesModalContext extends BSModalContext {
  public messages: Array<Message> = [];
  public title: string;
}
