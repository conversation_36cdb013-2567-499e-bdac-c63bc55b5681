﻿/**
 * System configuration for Angular 2 samples
 * Adjust as necessary for your application needs.
 */
(function (global) {
  System.config({
    paths: {
      // paths serve as alias
      "npm:": "./node_modules/"
    },
    // map tells the System loader where to look for things
    map: {
      // our app is within the app folder
      app: "dist",
      // angular bundles
      "@angular/core": "npm:@angular/core/bundles/core.umd.js",
      "@angular/common": "npm:@angular/common/bundles/common.umd.js",
      "@angular/common/http": "npm:@angular/common/bundles/common-http.umd.js",
      "tslib": "npm:tslib/tslib.js",
      "@angular/compiler": "npm:@angular/compiler/bundles/compiler.umd.js",
      "@angular/platform-browser": "npm:@angular/platform-browser/bundles/platform-browser.umd.js",
      "@angular/platform-browser-dynamic": "npm:@angular/platform-browser-dynamic/bundles/platform-browser-dynamic.umd.js",
      "@angular/http": "npm:@angular/http/bundles/http.umd.js",
      "@angular/router": "npm:@angular/router/bundles/router.umd.js",
      "@angular/forms": "npm:@angular/forms/bundles/forms.umd.js",
      // other libraries
      "rxjs": "npm:rxjs",
      'ngx-modialog-7': 'npm:ngx-modialog-7/bundles/ngx-modialog-7.umd.js',
      'ngx-modialog-7/plugins/bootstrap': 'npm:ngx-modialog-7/plugins/bootstrap/bundles/ngx-modialog-7-plugins-bootstrap.umd.js',
      "@ngx-translate/core": "npm:@ngx-translate/core/bundles/ngx-translate-core.umd.js",
      "@ngx-translate/http-loader": "npm:@ngx-translate/http-loader/bundles/ngx-translate-http-loader.umd.js"
    },
    // packages tells the System loader how to load when no filename and/or no extension
    packages: {
      app: {
        main: "./app.main.js",
        defaultExtension: "js"
      },
      rxjs: {
        main: "dist/bundles/rxjs.umd.min.js",
        defaultExtension: "js"
      },
      "rxjs/operators": {
        main: "../dist/cjs/operators/index.js",
        defaultExtension: "js"
      }
    }
  });
})(this);