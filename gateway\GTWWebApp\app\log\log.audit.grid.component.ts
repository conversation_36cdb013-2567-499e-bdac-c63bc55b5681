import { Component, OnInit } from "@angular/core";
import { AuditService } from "../data/api/api";
import { AuditLogEntryDTO, EditorFieldObjectDTO } from "../data/model/models";
import { AlertService } from "../modules/alert/alert.service";
import { AuthenticationService } from "../authentication/authentication.service";
import { Column } from "../modules/grid/column";
import { SearchField } from "../modules/grid/grid.search.component";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { TranslateService } from "@ngx-translate/core";
import { DashboardConfigDevicesComponent } from "../dashboard/config/dashboard.config.devices.component";
import { GlobalObjectToCSVUtil } from '../global/global.objectToCSV.util';

@Component({
  selector: "logAuditGridComponent",
  styles: [`  
  .title {
    font-size: 16px; 
    font-weight: bold;
    padding-left: 20px;
    height: 30px;
  }`],
  template: `
		<div>
		  <div class="panel panel-default panel-main" style="width:99%; margin-left:6px; margin-top:60px">
        <div class="panel-heading"><img src="../../images/auditLog.svg" class="module-icon" />{{'TR_AUDIT_LOG' | translate}}
        </div>
	      <div class="panel-body">
          <div class="panel-content">
            <div class="title">
              {{'TR_SEARCH_CRITERIA' | translate}}
            </div>
		        <div class="form-group" styel="margin: 10px 5px 15px 200px;">
              <label class="col-lg-3 col-form-label" title="{{TR_USERNAME | translate}}">{{ 'TR_USERNAME' | translate}}</label>
              <div class="col-lg-9"><input type="text" style="width:300px" class="form-control input-sm" [(ngModel)]="usernameFilter"></div>
              <label class="col-lg-3 col-form-label" title="{{TR_START_DATE | translate}}">{{ 'TR_START_DATE' | translate}}</label>
              <div class="col-lg-9"><input style="width:140px" type="date" class="form-control input-sm" [(ngModel)]="startDateFilter"></div>
              <label class="col-lg-3 col-form-label" title="{{TR_END_DATE | translate}}">{{ 'TR_END_DATE' | translate}}</label>
              <div class="col-lg-9"><input style="width:140px" type="date" class="form-control input-sm" [(ngModel)]="endDateFilter"></div>
            </div>
            <div style="height: 100px;">
              <button class="btn btn-default btn-sm" style="float: left;margin: 10px" (click)="onSearchClick()"><img src="../../images/magnifier.svg" class="image-button"/>&nbsp;{{'TR_SEARCH' | translate}}</button>
              <button class="btn btn-default btn-sm" style="float: right;margin: 10px" (click)="copyToCSV()"><img src="../../images/download.svg" class="image-button"/>&nbsp;{{'TR_DOWNLOAD_DISPLAYED_LOG_ENTRIES' | translate}}</button>
            </div>
          </div>
          <gridComponent [rows]='auditLogEntries' [columns]='columns' #auditLogGrid></gridComponent>
	      </div>
      </div>
		</div>`
})

export class LogAuditGridComponent implements OnInit {
  private usernameFilter: string = null;
  private startDateFilter: Date = null;
  private endDateFilter: Date = null;
  private auditLogEntries: Array<AuditLogEntryDTO> = [];
  private columns: Array<Column>;

  constructor(private modal: Modal, private auditService: AuditService, private alertService: AlertService, private authenticationService: AuthenticationService, private translate: TranslateService) {
    this.columns = this.getColumns();
  }

  public ngOnInit(): void {
    this.getAudiList();
  }

  private onSearchClick(searchFields: SearchField[]): void {
    this.getAudiList();
  }

  private copyToCSV(): void {
    GlobalObjectToCSVUtil.copyToCSV("AuditLog.csv", this.auditLogEntries);
  }

  private getAudiList(): void {
    let startDateFilterNumeric: number = null;
    let endDateFilterNumeric: number = null;

    this.auditLogEntries = [];

    if (this.startDateFilter) {
      let myDate = new Date(this.startDateFilter);
      startDateFilterNumeric = myDate.getTime() / 1000.0;
    }

    if (this.endDateFilter) {
    let myDate = new Date(this.endDateFilter);
    myDate.setDate(myDate.getDate() + 1);
    endDateFilterNumeric = myDate.getTime() / 1000.0;
    }

    this.auditService.getLogEntries(this.usernameFilter, startDateFilterNumeric, endDateFilterNumeric).subscribe(
      data => {
        if (data.length > 0) {
          data.forEach((AuditLogEntryDTO) => {
            if (AuditLogEntryDTO.logTime) {
              let d = new Date(0); // The 0 there is the key, which sets the date to the epoch
              d.setUTCSeconds(AuditLogEntryDTO.logTime);

              AuditLogEntryDTO["dateLog"] = [d.getFullYear(),
              this.pad(d.getMonth() + 1),
              this.pad(d.getDate())].join('/')
              + ' ' +
              [this.pad(d.getHours()),
              this.pad(d.getMinutes()),
              this.pad(d.getSeconds())].join(':');
            }
            else {
              AuditLogEntryDTO["dateLog"] = "";
            }
            // Convert commas and newlines to spaces and <br> respectively
            if (AuditLogEntryDTO.changeValue)
              AuditLogEntryDTO.changeValue = AuditLogEntryDTO.changeValue.replace(/,/g, ' ').replace(/\r?\n/g, '<br>');

            this.auditLogEntries.push(AuditLogEntryDTO);
          });
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_READ_AUDIT_DATABASE"); }
      }
    );
  }

  private pad(n: number): number {
    return (n < 10) ? (0 + n) : n;
  }

  private getColumns(): Array<Column> {
    return [
      new Column("username", "TR_USERNAME", "", ""),
      new Column("role", "TR_ROLE", "", ""),
      new Column("ipAddr", "TR_IP_ADDRESS", "", ""),
      new Column("dateLog", "TR_DATE_TIME", "", ""),
      new Column("action", "TR_ACTION", "", ""),
      new Column("changeValue", "TR_CHANGE_VALUE", "action$innerHTML", "")
    ];
  }
}
