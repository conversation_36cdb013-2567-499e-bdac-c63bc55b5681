﻿import { Component, Input, Output, EventEmitter, OnInit } from "@angular/core";

@Component({
  selector: "dashboardConfigDeviceVirtualNodeComponent",
  styles: [`
		  .icon-button {
        font-size: 8px;
			  display: inline-block; 
			  margin-right: 5px;
			  cursor: pointer;
			  color:#fffaaa;
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
		  }
		  ul {
			  padding-left: 6px;
			  list-style-type: none;
		  }
		  li{
			  margin-top: 4px;
		  }
		  ol, ul {
			  margin-top: 0;
		  }
      .leaf{
        white-space: nowrap;
      }
      .leaf:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .glyphicon-none {
        padding-right: 8px;  
        color: transparent !important;
      }
      .nodeImage{
        width: 20px;
        height: 20px;
        filter: drop-shadow( 2px 2px 1px rgba(0, 0, 0, .7));
      }
      .node-text{
        cursor: pointer;
        color:black;
        text-decoration:none;
        vertical-align: bottom;
        padding-left: 4px;
      }
      .hide {
        display: none;
      }
      `
    ],
    template: `
          <ul *ngFor="let virtualNode of virtualNodeList">
					  <li>
              <div class="leaf" [class.is-selected]="selectedVirtualNode === virtualNode && !isDevicesTreeViewSelected">
                <img [src]="'../../images/' + virtualNode.icon" class="nodeImage" [ngClass]="{'blinking-red' : !virtualNode.isHealthy}"/> 
							  <a (click)="onSelectVirtualNode(virtualNode)" class="node-text" [tooltip]="[virtualNode.description]" [dashboardConfigContextMenuDirective]="virtualNode">{{virtualNode.label | translate}}</a>
              </div>
					  </li>
				  </ul>`
})

export class DashboardConfigDeviceVirtualNodeComponent implements OnInit {
  @Input() isDevicesTreeViewSelected: boolean;
  @Input() virtualNodeList: Array<VirtualNode> = [];
  @Output() onSelectedChanged: EventEmitter<VirtualNode> = new EventEmitter();
  private selectedVirtualNode: VirtualNode;

  constructor() { }

  public ngOnInit(): void {
  }

  public onSelectVirtualNode(virtualNode: VirtualNode): void {
    this.selectedVirtualNode = virtualNode;
    this.onSelectedChanged.emit(virtualNode);
  }
}

export class VirtualNode {
  constructor(
    public name: string,
    public label: string,
    public description: string,
    public isHealthy: boolean,
    public icon: string,
  ) { }
}