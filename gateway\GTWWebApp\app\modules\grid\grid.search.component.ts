import { Component, Input, Output, EventEmitter } from "@angular/core";
import { EditorFieldObjectDTO } from "../../data/model/models";
@Component({
  selector: "gridSearchComponent",
  styles: [`
  .hide {
    display: none;
  }
  .title {
    font-size: 16px; 
    font-weight: bold;
    padding-left: 20px;
    margin-top: -3px;
  }
  .panel-content {
    padding: 6px
  }
  .search-button {
    top: -2px !important;
    left: -6px;
    position: relative;
  }
  .panel-heading {
    background-color: transparent;
    position: relative;
    padding: 0 0 0 8px;
    top: 30px;
    margin-top: -30px;
  }`
  ],
  template: `
      <div class="panel-heading">
        <div *ngIf="searchIsActive" class="glyphicon glyphicon-chevron-up glyphicon-size cell" title="{{'TR_COLLAPSE' | translate}}" (click)="toggle()"></div>
        <div *ngIf="!searchIsActive" class="glyphicon glyphicon-chevron-down glyphicon-size cell" title="{{'TR_EXPAND' | translate}}" (click)="toggle()"></div>
      </div>
      <div class="panel-content" [ngClass]="{hide: !searchIsActive}">
        <div class="title">
          {{'TR_SEARCH_CRITERIA' | translate}}
        </div>
		    <div class="form-group" style="margin:10px">
          <ng-container *ngFor="let searchField of searchFields">
            <label class="col-lg-3 col-form-label" title="{{ searchField.label | translate}}">{{ searchField.label | translate}}</label>
            <div *ngIf="(searchField.controlType === controlTypeEnum.Text)" class="col-lg-9" ><input type="text" class="form-control input-sm" [(ngModel)]="searchField.value"></div>
            <div *ngIf="(searchField.controlType === controlTypeEnum.Combobox)" class="col-lg-9" >
			        <select class="form-control input-sm" [(ngModel)]="searchField.value">
				          <option *ngFor="let item of searchField.controlSource" [ngValue]="item.value">{{item.text}}</option>
			        </select>
            </div>
          </ng-container>   
        </div>
        <div style="height: 40px;">
          <button class="btn btn-default btn-sm" style="float: right;" (click)="onSearchClick()"><img src="../../images/magnifier.svg" class="image-button" />&nbsp;{{ 'TR_SEARCH' | translate }}</button>
        </div>
      </div>`
})

export class GridSearchComponent {
  @Output() onSearch: EventEmitter<object> = new EventEmitter();
  @Input() searchFields: SearchField[];
  private searchIsActive: boolean;
  private controlTypeEnum = EditorFieldObjectDTO.ControlTypeEnum;

  private toggle(): void {
    this.searchIsActive = !this.searchIsActive;
    if (!this.searchIsActive) {
      this.onSearch.emit(null);
    }
  }

  private onSearchClick(): void {
    this.onSearch.emit(this.searchFields);
  }
}

export class SearchField {
  constructor(
    public id: string,
    public label: string,
    public value: string,
    public controlType: EditorFieldObjectDTO.ControlTypeEnum,
    public controlSource: Array<Object>
  ) { }
}