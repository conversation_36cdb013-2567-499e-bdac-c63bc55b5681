System.register([], function (exports_1, context_1) {
    "use strict";
    var Message;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            Message = (function () {
                function Message(text, type, dateTime, arg1, arg2, arg3) {
                    if (arg1 === void 0) { arg1 = ""; }
                    if (arg2 === void 0) { arg2 = ""; }
                    if (arg3 === void 0) { arg3 = ""; }
                    this.text = text;
                    this.type = type;
                    this.dateTime = dateTime;
                }
                return Message;
            }());
            exports_1("Message", Message);
        }
    };
});
//# sourceMappingURL=message.js.map