System.register([], function (exports_1, context_1) {
    "use strict";
    var EditorSpecificationObjectDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (EditorSpecificationObjectDTO) {
                var EditorKind;
                (function (EditorKind) {
                    EditorKind[EditorKind["CancelOk"] = 'CancelOk'] = "CancelOk";
                    EditorKind[EditorKind["AddItem"] = 'AddItem'] = "AddItem";
                    EditorKind[EditorKind["CloseSave"] = 'CloseSave'] = "CloseSave";
                    EditorKind[EditorKind["Close"] = 'Close'] = "Close";
                })(EditorKind = EditorSpecificationObjectDTO.EditorKind || (EditorSpecificationObjectDTO.EditorKind = {}));
            })(EditorSpecificationObjectDTO || (EditorSpecificationObjectDTO = {}));
            exports_1("EditorSpecificationObjectDTO", EditorSpecificationObjectDTO);
        }
    };
});
//# sourceMappingURL=EditorSpecificationObjectDTO.js.map