/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum EditorCommandsDTO {
  MENUCMDNONE = <any>'MENU_CMD_NONE',
  MENUCMDSEPARATOR = <any>'MENU_CMD_SEPARATOR',
  MENUCMDEDIT = <any>'MENU_CMD_EDIT',
  MENUCMDEDITWORKSPACEPARAMETERS = <any>'MENU_CMD_EDIT_WORKSPACE_PARAMETERS',
  MENUCMDAUTOCREATETAGS = <any>'MENU_CMD_AUTO_CREATE_TAGS',
  MENUCMDADDMBPCHANNEL = <any>'MENU_CMD_ADD_MBP_CHANNEL',
  MENUCMDADDTCPCHANNELOUTSTATIONSLAVE = <any>'MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE',
  MENUCMDADDTCPCHANNELMASTER = <any>'MENU_CMD_ADD_TCP_CHANNEL_MASTER',
  MENUCMDADDDNP3UDPTCPCHANNELOUTSTATIONSLAVE = <any>'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE',
  MENUCMDADDDNP3UDPTCPCHANNELMASTER = <any>'MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER',
  MENUCMDADDDNP3XMLDEVICE = <any>'MENU_CMD_ADD_DNP3_XML_DEVICE',
  MENUCMDADDSERIALCHANNELOUTSTATIONSLAVE = <any>'MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE',
  MENUCMDADDSERIALCHANNELMASTER = <any>'MENU_CMD_ADD_SERIAL_CHANNEL_MASTER',
  MENUCMDADDMODEMPOOLCHANNEL = <any> 'MENU_CMD_ADD_MODEM_POOL_CHANNEL',
  MENUCMDADDMODEM = <any> 'MENU_CMD_ADD_MODEM',
  MENUCMDADDMODEMPOOL = <any> 'MENU_CMD_ADD_MODEM_POOL',
  MENUCMDADDOPCCLIENT = <any> 'MENU_CMD_ADD_OPC_CLIENT',
  MENUCMDADDOPCUACLIENT = <any>'MENU_CMD_ADD_OPC_UA_CLIENT',
  MENUCMDADDOPCUACERTIFICATE = <any>'MENU_CMD_ADD_OPC_UA_CERTIFICATE',
  MENUCMDEDITOPCUASERVER = <any>'MENU_CMD_EDIT_OPC_UA_SERVER',
  MENUCMDADDOPCAECLIENT = <any> 'MENU_CMD_ADD_OPC_AE_CLIENT',
  MENUCMDADD61850CLIENT = <any> 'MENU_CMD_ADD_61850_CLIENT',
  MENUCMDADDTASE2CLIENT = <any>'MENU_CMD_ADD_TASE2_CLIENT',
  MENUCMDADDTASE2SERVER = <any>'MENU_CMD_ADD_TASE2_SERVER',
  MENUCMDADDTASE2CLIENTSERVER = <any>'MENU_CMD_ADD_TASE2_CLIENT_SERVER',
  MENUCMDADD61850CONTROLTOOPCMAPPING = <any>'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING',
  MENUCMDADD61850CONTROLTOOPCMAPPINGITEM = <any>'MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM',
  MENUCMDADD61850SERVER = <any> 'MENU_CMD_ADD_61850_SERVER',
  MENUCMDADDTASE2LOGICALDEVICE = <any> 'MENU_CMD_ADD_TASE2_LOGICAL_DEVICE',
  MENUCMDRESTARTTASE2SERVER = <any>'MENU_CMD_RESTART_TASE2_SERVER',
  MENUCMDSHOWCONFIGTASE2SERVER = <any>'MENU_CMD_SHOW_CONFIG_TASE2_SERVER',
  MENUCMDSHOWCONFIGTASE2CLIENT = <any>'MENU_CMD_SHOW_CONFIG_TASE2_CLIENT',
  MENUCMDDISCONNECTTASE2SERVER = <any> 'MENU_CMD_DISCONNECT_TASE2_SERVER',
  MENUCMDRESTART61850SERVER = <any> 'MENU_CMD_RESTART_61850_SERVER',
  MENUCMDSAVETASE2SERVER = <any> 'MENU_CMD_SAVE_TASE2_SERVER',
  MENUCMDADDODBCCLIENT = <any>'MENU_CMD_ADD_ODBC_CLIENT',
  MENUCMDADDODBCITEM = <any>'MENU_CMD_ADD_ODBC_ITEM',
  MENUCMDADDEQMDO = <any> 'MENU_CMD_ADD_EQ_MDO',
  MENUCMDADDINTERNALMDO = <any> 'MENU_CMD_ADD_INTERNAL_MDO',
  MENUCMDADD61850REPORT = <any> 'MENU_CMD_ADD_61850_REPORT',
  MENUCMDENABLERCB = <any> 'MENU_CMD_ENABLE_RCB',
  MENUCMDDISABLERCB = <any> 'MENU_CMD_DISABLE_RCB',
  MENUCMDVERIFYDATASET = <any> 'MENU_CMD_VERIFY_DATASET',
  MENUCMDADD61850GOOSE = <any> 'MENU_CMD_ADD_61850_GOOSE',
  MENUCMDADD61850POLLEDDATASET = <any> 'MENU_CMD_ADD_61850_POLLED_DATA_SET',
  MENUCMDADD61850POLLEDPOINTSET = <any> 'MENU_CMD_ADD_61850_POLLED_POINT_SET',
  MENUCMDADD61850COMMANDPOINT = <any>'MENU_CMD_ADD_61850_COMMAND_POINT',
  MENUCMDADD61850COMMANDPOINTSET = <any>'MENU_CMD_ADD_61850_COMMAND_POINT_SET',
  MENUCMDADD61850WRITABLEPOINT = <any> 'MENU_CMD_ADD_61850_WRITABLE_POINT',
  MENUCMDADD61850WRITABLEPOINTSET = <any> 'MENU_CMD_ADD_61850_WRITABLE_POINT_SET',
  MENUCMDADD61850DATASET = <any>'MENU_CMD_ADD_61850_DATASET',
  MENUCMDCHANGE61850DATASET = <any>'MENU_CMD_CHANGE_61850_DATASET',
  MENUCMDADDGOOSEMONITOR = <any> 'MENU_CMD_ADD_GOOSE_MONITOR',
  MENUCMDADDREDUNDANTSLAVECHANNEL = <any>'MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL',
  MENUCMDADDREDUNDANTMASTERCHANNEL = <any>'MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL',
  MENUCMDDELETEREDUNDANTCHANNEL = <any>'MENU_CMD_DELETE_REDUNDANT_CHANNEL',
  MENUCMDADD61400ALARMSNODE = <any> 'MENU_CMD_ADD_61400_ALARMS_NODE',
  MENUCMDADD61400ALARMMDO = <any> 'MENU_CMD_ADD_61400_ALARM_MDO',
  MENUCMDADD61850ITEM = <any> 'MENU_CMD_ADD_61850_ITEM',
  MENUCMDCONNECTTO61850SERVER = <any> 'MENU_CMD_CONNECT_TO_61850_SERVER',
  MENUCMDDISCONNECTFROM61850SERVER = <any>'MENU_CMD_DISCONNECT_FROM_61850_SERVER',
  MENU_CMD_EDIT_TASE2_EDIT_MODEL = <any>'MENU_CMD_EDIT_TASE2_EDIT_MODEL',
  MENUCMDEDITTASE2DATAPOINTS = <any> 'MENU_CMD_EDIT_TASE2_DATA_POINTS',
  MENUCMDADDTASE2DSTS = <any> 'MENU_CMD_ADD_TASE2_DSTS',
  MENUCMDADDTASE2POLLEDDATASET = <any> 'MENU_CMD_ADD_TASE2_POLLED_DATA_SET',
  MENUCMDADDTASE2POLLEDPOINTSET = <any> 'MENU_CMD_ADD_TASE2_POLLED_POINT_SET',
  MENUCMDADDTASE2COMMANDPOINT = <any>'MENU_CMD_ADD_TASE2_COMMAND_POINT',
  MENUCMDADDTASE2COMMANDPOINTSET = <any>'MENU_CMD_ADD_TASE2_COMMAND_POINT_SET',
  MENUCMDADDTASE2ITEM = <any> 'MENU_CMD_ADD_TASE2_ITEM',
  MENUCMDADDTASE2DATASET = <any>'MENU_CMD_ADD_TASE2_DATASET',
  MENUCMDMANAGETASE2DATASET = <any>'MENU_CMD_MANAGE_TASE2_DATASET',
  MENUCMDMANAGETASE2DATASETFULLEDIT = <any>'MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT',
  MENUCMDADDTASE2DOMAIN = <any>'MENU_CMD_ADD_TASE2_DOMAIN',
  MENUCMDADDTASE2DATAATTRIBUTE = <any>'MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE',
  MENUCMDOPERATETASE2CONTROL = <any>'MENU_CMD_OPERATE_TASE2_CONTROL',



  MENUCMDCONNECTTOTASE2SERVER = <any>'MENU_CMD_CONNECT_TO_TASE2_SERVER',
  MENUCMDCONNECTTODISCOVERTASE2SERVER = <any>'MENU_CMD_CONNECT_TO_DISCOVER_TASE2_SERVER',



  MENUCMDDISCONNECTFROMTASE2SERVER = <any>'MENU_CMD_DISCONNECT_FROM_TASE2_SERVER',
  MENUCMDSAVEMODELTOFILE = <any>'MENU_CMD_SAVE_MODEL_TO_FILE',
  MENUCMDADDOPCITEM = <any>'MENU_CMD_ADD_OPC_ITEM',
  MENUCMDADDMULTIPLEOPCITEM = <any>'MENU_CMD_ADD_MULTIPLE_OPC_ITEM',
  MENUCMDADDMULTIPLEOPCUAITEM = <any>'MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM',
  MENUCMDADDOPCAEITEM = <any>'MENU_CMD_ADD_OPC_AE_ITEM',
  MENUCMDADDOPCUAITEM = <any>'MENU_CMD_ADD_OPC_UA_ITEM',
  MENUCMDADDSESSION = <any> 'MENU_CMD_ADD_SESSION',
  MENUCMDADDMDO = <any>'MENU_CMD_ADD_MDO',
  MENUCMDADDMULTIPLEMDO = <any>'MENU_CMD_ADD_MULTIPLE_MDO',
  MENUCMDADDMAPPINGSDO = <any>'MENU_CMD_ADD_MAPPING_SDO',
  MENUCMDADDMAPPINGSDOS = <any>'MENU_CMD_ADD_MAPPING_SDOS',
  MENUCMDADDMAPPINGMDO = <any>'MENU_CMD_ADD_MAPPING_MDO',
  MENUCMDADDMAPPINGMDOS = <any>'MENU_CMD_ADD_MAPPING_MDOS',
  MENUCMDDROPONFOLDER = <any>'MENU_CMD_DROP_ON_FOLDER',
  MENUCMDADDSECTOR = <any> 'MENU_CMD_ADD_SECTOR',
  MENUCMDADDDATATYPE = <any> 'MENU_CMD_ADD_DATA_TYPE',
  MENUCMDADDDNPPROTO = <any> 'MENU_CMD_ADD_DNP_PROTO',
  MENUCMDADDDNPDESCP = <any> 'MENU_CMD_ADD_DNP_DESCP',
  MENUCMDADDDATASETELEMENT = <any> 'MENU_CMD_ADD_DATASET_ELEMENT',
  MENUCMDADDOPCAEATTR = <any> 'MENU_CMD_ADD_OPC_AE_ATTR',
  MENUCMDDELETE = <any> 'MENU_CMD_DELETE',
  MENUCMDCHANGEVALUE = <any>'MENU_CMD_CHANGE_VALUE',
  MENUCMDSETVALUEANDQUALITY = <any>'MENU_CMD_SET_VALUE_AND_QUALITY',
  MENUCMDREADDNPPROTO = <any> 'MENU_CMD_READ_DNP_PROTO',
  MENUCMDWRITEDNPPROTO = <any> 'MENU_CMD_WRITE_DNP_PROTO',
  MENUCMDREADDNPDESCP = <any> 'MENU_CMD_READ_DNP_DESCP',
  MENUCMDWRITEDNPDESCP = <any> 'MENU_CMD_WRITE_DNP_DESCP',
  MENUCMDRESET61850RETRYCONNECTCOUNT = <any> 'MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT',
  MENUCMDREAD61850MDO = <any>'MENU_CMD_READ_61850_MDO',
  MENUCMDREADICCPMDO = <any>'MENU_CMD_READ_ICCP_MDO',
  MENUCMDREAD = <any>'MENU_CMD_READ_61850_MDO',
  MENUCMDADDWRITEACTION = <any> 'MENU_CMD_ADD_WRITE_ACTION',
  MENUCMDADDMULTIPOINT = <any> 'MENU_CMD_ADD_MULTI_POINT',
  MENUCMDPERFORMWRITEACTION = <any> 'MENU_CMD_PERFORM_WRITE_ACTION',
  MENUCMDWRITEMULTIPOINT = <any> 'MENU_CMD_WRITE_MULTI_POINT',
  MENUCMDSELECTDATAATTRIBUTE = <any>'MENU_CMD_SELECT_DATA_ATTRIBUTE',
  MENUCMDSUBSCRIBEGOOSESTREAM = <any>'MENU_CMD_SUBSCRIBE_GOOSE_STREAM',
  MENUCMDUNSUBSCRIBEGOOSESTREAM = <any>'MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM',
  MENUCMDCREATETHXMLPOINTFILE = <any>'MENU_CMD_CREATE_THXML_POINT_FILE',
  MENUCMDCREATEDTMCSVPOINTFILE = <any>'MENU_CMD_CREATE_DTM_CSV_POINT_FILE',
  MENUCMDCONNECTOPCSERVER = <any>'MENU_CMD_CONNECT_OPC_SERVER',
  MENUCMDDISCONNECTOPCSERVER = <any>'MENU_CMD_DISCONNECT_OPC_SERVER',
  MENUCMDCONNECTOPCAESERVER = <any>'MENU_CMD_CONNECT_OPC_AE_SERVER',
  MENUCMDDISCONNECTOPCAESERVER = <any>'MENU_CMD_DISCONNECT_OPC_AE_SERVER',
  MENUCMDCONNECTOPCUASERVER = <any>'MENU_CMD_CONNECT_OPC_UA_SERVER',
  MENUCMDDISCONNECTOPCUASERVER = <any>'MENU_CMD_DISCONNECT_OPC_UA_SERVER',
  MENUCMDOPCUAGETSERVERSTATUS = <any>'MENU_CMD_OPC_UA_GET_SERVER_STATUS',
  MENUCMDENABLEDSTS = <any>'MENU_CMD_ENABLE_DSTS',
  MENUCMDDISABLEDSTS = <any>'MENU_CMD_DISABLE_DSTS',
  MENUCMDSWITCHTORCHANNEL = <any>'MENU_CMD_SWITCH_TO_RCHANNEL',
  MENUCMDADDUSERDEFINEDFOLDER = <any>'MENU_CMD_ADD_USER_DEFINED_FOLDER',
  MENUCMDREADDATASET = <any>'MENU_CMD_READ_DATASET',
  MENUCMDREADOPCUAMDO = <any>'MENU_CMD_READ_OPC_UA_MDO',
  MENUCMDREADOPCDAMDO = <any>'MENU_CMD_READ_OPC_DA_MDO',
  MENUCMDRESETAVERAGEMDOUPDATERATE = <any>'MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE'
}
