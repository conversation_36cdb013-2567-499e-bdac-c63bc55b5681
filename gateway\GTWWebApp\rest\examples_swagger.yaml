---
swagger: "2.0"
info:
  description: "Examples API (for development only)"
  version: "1.0.0"
  title: "REST Examples"
#host: "localhost:58090"
basePath: "/rest"
schemes:
- "http"
- "https"
paths:
  /work:
    get:
      tags:
      - "examples"
      summary: "Get example simulating heavy work (for ~5 seconds) in a spawned thread."
      operationId: "workGET"
      produces:
      - text/html
      parameters: []
      responses:
        200:
          description: "Work GET"
      x-swagger-router-controller: "Default"
  /veryLarge/:
    get:
      tags:
      - "examples"
      summary: "Reply with A very large json array."
      operationId: "veryLarge"
      produces:
      - "application/json"
      parameters:
      - name: "size"
        in: "query"
        description: "number of items to generate"
        required: true
        type: "number"
      responses:
        200:
          description: "A very large json array"
      x-swagger-router-controller: "Default"
  /info:
    get:
      tags:
      - "examples"
      summary: "Reply with header info."
      operationId: "infoGET"
      produces:
      - text/html
      parameters: []
      responses:
        200:
          description: "Info GET"
      x-swagger-router-controller: "Default"
  /throw_error:
    get:
      tags:
      - "examples"
      summary: "throw exception to test on_error."
      operationId: "errorGET"
      produces:
      - text/html
      parameters: []
      responses:
        200:
          description: "Error GET"
      x-swagger-router-controller: "Default"
  /echo/:
    get:
      tags:
      - "examples"
      summary: "Reply with no content."
      operationId: "echoGET"
      parameters: []
      responses:
        200:
          description: "Echo GET"
      x-swagger-router-controller: "Default"
    post:
      tags:
      - "examples"
      summary: "Reply with the query"
      operationId: "echoPOST"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "name"
        in: "formData"
        description: "name"
        required: false
        type: "string"
      - name: "year"
        in: "formData"
        description: "year"
        required: false
        type: "string"
      responses:
        200:
          description: "Echo POST"
      x-swagger-router-controller: "Default"
  /string/:
    get:
      tags:
      - "examples"
      summary: "Reply with no content."
      operationId: "stringGET"
      produces:
      - "application/json"
      parameters:
      - name: "stringFilter"
        in: "query"
        description: "string filter"
        required: false
        type: "string"
      - name: "stringNumber"
        in: "query"
        description: "number"
        required: false
        type: "number"
      responses:
        200:
          description: "String GET"
      x-swagger-router-controller: "Default"
    post:
      tags:
      - "examples"
      summary: "Reply with the query"
      operationId: "stringPOST"
      consumes:
      - application/x-www-form-urlencoded
      parameters:
      - name: "name"
        in: "formData"
        description: "name"
        required: false
        type: "string"
      - name: "year"
        in: "formData"
        description: "year"
        required: false
        type: "string"
      responses:
        200:
          description: "String POST"
      x-swagger-router-controller: "Default"
  /test-path/{id}:
    get:
      tags:
      - "examples"
      summary: "Reply with the id path"
      operationId: "test_pathIdGET"
      parameters:
      - name: "id"
        in: "path"
        description: "ID"
        required: true
        type: "string"
      responses:
        200:
          description: "Echo test-path"
      x-swagger-router-controller: "Default"
  /json:
    post:
      tags:
      - "examples"
      summary: "Process json request"
      description: '{"firstName":"John","lastName":"Smith","age":25}'
      operationId: "test_json"
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "body"
        in: "body"
        required: true
        schema:
           $ref:  '#/definitions/JsonObjectRequestDTO'
      responses:
        200:
          description: "OK"
          schema:
            $ref: '#/definitions/JsonObjectResponseDTO'
        400:
          description: "Bad Request"
  /uploadfile:
    post:
      tags:
      - "examples"
      summary: "upload file"
      description: ""
      operationId: "filePost"
      produces:
      - "application/json"
      consumes:
      - multipart/form-data
      parameters:
      - name: "fileName"
        in: "formData"
        description: "file name/path"
        required: true
        type: "file"
      responses:
        200:
          description: "Upload file"
        400:
          description: "Bad Request"
definitions: 
  JsonObjectRequestDTO:
    type: "object"
    properties:
      firstName:
        type: "string"
      lastName:
        type: "string"
      age:
        type: "integer"
  JsonObjectResponseDTO:
    type: "object"
    properties:
      firstName:
        type: "string"
      lastName:
        type: "string"
  Pet:
    discriminator: petType
    required:
      - name
      - petType # required for inheritance to work
    properties:
      name: 
        type: string
      petType:
        type: string
  Cat:
    allOf:
      - $ref: '#/definitions/Pet' # Cat has all properties of a Pet
      - properties: # extra properties only for cats
          huntingSkill:
            type: string
            default: lazy
            enum:
              - lazy
              - aggressive
  Dog:
    allOf:
      - $ref: '#/definitions/Pet' # Dog has all properties of a Pet
      - properties: # extra properties only for dogs
          packSize:
            description: The size of the pack the dog is from
            type: integer