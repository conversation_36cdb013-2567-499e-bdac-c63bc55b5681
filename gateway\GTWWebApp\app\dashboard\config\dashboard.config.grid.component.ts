import { Component, Input, Output, EventEmitter, OnChanges } from "@angular/core";
import { TagObjectDTO } from "../../data/model/models";
import { AuthenticationService } from "../../authentication/authentication.service";

@Component({
  selector: "dashboardConfigGridComponent",
  styles: [`
      .table>tbody>tr>td,
      .table>thead>tr>th{
		    padding: 0px !important;
        vertical-align: middle !important;
        line-height: 22px;
      }
      .table thead th {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #ddd;
       }
      .table>tbody>tr:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .table{
        border: 1px solid lightgray;
      }
      .table-striped>tbody>tr:nth-of-type(odd) {
        background-color: transparent !important;
      }
      .table-striped>tbody>tr:nth-of-type(odd):hover {
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .table-striped>tbody>tr:nth-child(odd)>td,
      .table-striped>tbody>tr:nth-child(odd)>th {
        background-color: rgba(0, 0, 0, 0.03) !important;
      }
      .full-cell
      {
        width: 100%;
        height: 100%;
        padding: 2px;
      }
      .pointer{
        cursor: pointer;
      }
      .delete-mapping-button{
        display: inline-block;
        float: right;
        margin-right: 10px;
        height: 20px;
        width: 24px;
        border-radius: 50%;
        border: 1px solid gray;
        background-color: #f8f8f8;
        text-align: center;
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
        cursor: pointer;
      }
      .delete-mapping-button:hover {
        background-color: #cdcdcd;
      }
      .image-delete {
        width: 20px;
        height: 20px;
        margin-top: -6px;
      }
	    .sort-icon {
		    font-size: 10px;
		    cursor:pointer;
	     }
      .header {
        font-weight: bold;
        margin-left: 6px;
      }
      .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ellipsis-container {
          position: relative;
          max-width: 100%;
          padding: 0 !important;
          display: -webkit-flex;
          display: -moz-flex;
          display: flex;
          vertical-align: text-bottom !important;
      }
      .ellipsis {
          position: absolute;
          white-space: nowrap;
          overflow-y: visible;
          overflow-x: hidden;
          text-overflow: ellipsis;
          -ms-text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          max-width: 100%;
          min-width: 0;
          width:100%;
          top: 0;
          left: 0;
      }
      .ellipsis-container:after,
      .ellipsis:after {
          content: '-';
          display: inline;
          visibility: hidden;
          width: 0;
      }
    `],
  template: `<table *ngIf="rows?.length > 0" class="table table-striped">
              <thead>
			          <tr style="height: 28px;">
                  <th resizable *ngFor="let col of columns">
                    <div *ngIf="col.isSortable" class="header" >
                      {{col.header | translate}}
                      <div *ngIf="col.isSortAscending == false" class="glyphicon glyphicon glyphicon-triangle-bottom sort-icon" (click)="sort(col, true)"></div>
                      <div *ngIf="col.isSortAscending == true" class="glyphicon glyphicon glyphicon-triangle-top sort-icon" (click)="sort(col, false)"></div>
                      <div *ngIf="col.isSortAscending == null" class="glyphicon glyphicon glyphicon-triangle-top sort-icon" (click)="sort(col, false)"></div>
                      <div *ngIf="col.isSortAscending == null" class="glyphicon glyphicon glyphicon-triangle-bottom sort-icon" (click)="sort(col, true)"></div>
                    </div>
                    <div *ngIf="!col.isSortable" class="header" >{{col.header | translate}}</div>
                  </th>
			          </tr>
              </thead>
              <tbody>
			          <tr *ngFor="let row of rows;let item of items; let i = index" [ngClass]="{'is-selected': (this.selectedRows.indexOf(row) > -1)}">
				          <td *ngFor="let col of columns">
				            <ng-container *ngIf="!col.action.includes('action$')"><div class="ellipsis-container"><div [tooltip]="[col.tooltip]" class="pointer ellipsis inline" (click)="clickAction('', row, i)">{{row[col.field]}}</div></div></ng-container>
                    <ng-container *ngIf="col.action.includes('action$tooltip')"><div class="ellipsis-container"><div [tooltip]="[row[col.tooltip]]" class="pointer ellipsis inline" (click)="clickAction('', row, i)">{{row[col.field]}}</div></div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$image')"><img [src]="'../../images/gtw_objects/' + (row[col.field] | gtwImage)" class="image" [ngClass]="{'blinking-red' : !row[col.arg]}"/></ng-container>
				            <ng-container *ngIf="col.action.includes('action$list')">
                      <div *ngFor="let item of row[col.arg];trackBy: trackByFn">
                        <img [src]="'../../images/' + item.image + '.svg'" class="image"/>
                        <div *ngIf="!item.action.includes('action$lookupMapping')">{{item.arg.rightTag}}</div>
                        <div *ngIf="item.action.includes('action$lookupMapping')" class="pointer inline" [dashboardConfigTagTooltipDirective]="item.arg.rightTag">{{item.arg.rightTag}}</div>
                        <div *ngIf="item.action.includes('action$deleteMapping') && this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'" class="delete-mapping-button" title="{{'TR_DELETE_MAPPING' | translate}}" (click)="clickAction(item.action, item.arg, 0)"><img [src]="'../../images/' + item.image + 'Delete.svg'" class="image-delete"/></div>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="col.action.includes('action$context-menu-device')"><div class="pointer full-cell" [myDraggable]="{data: selectedRows}" [dashboardConfigContextMenuDirective]="row[col.field]" [selectedRows]="selectedRows" [rows]="rows" [tag]="row[col.arg]" [tooltip]="[row[col.tooltip]]" (click)="clickActionSelect(row, i, $event)" (dragstart)="onDragStart($event, row, i)">{{row[col.field]}}</div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$custom')"><button class="btn btn-default" (click)="clickAction(col.action, row, i)" [tooltip]="[col.tooltip]">{{col.header}}</button></ng-container>
				            <ng-container *ngIf="col.action.includes('action$changeValue') && row.tagCanChangeValue && this.authenticationService.role | checkRole:'OPERATOR_ROLE'"><div class="round-button" [tooltip]="[col.tooltip]" (click)="clickAction(col.action, row)"><img src="../../images/change_value.svg" class="image-button"/></div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$edit') && row.tagCanEdit  && this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'"><div class="round-button" [tooltip]="[col.tooltip]" (click)="clickAction(col.action, row)"><img src="../../images/edit.svg" class="image-button"/></div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$delete') && row.tagCanDelete && this.authenticationService.role | checkRole:'CONFIGURATOR_ROLE'"><div class="round-button" [tooltip]="[col.tooltip]" (click)="clickAction(col.action, row)"><img src="../../images/delete.svg" class="image-button"/></div></ng-container>
                    <ng-container *ngIf="col.action.includes('action$checkbox')"><div><input type="checkbox" (change)="clickActionEvent(col.action, row, i, $event)"/></div></ng-container>
				          </td>
			          </tr>
              </tbody>
		        </table>
		        <div *ngIf="!rows || !rows?.length" class="grid-no-results">{{'TR_NO_RESULTS' | translate}}</div>`

})

export class DashboardConfigGridComponent implements OnChanges {
  @Input() rows: Array<any>;
  @Input() columns: Array<Column>;
  @Output() onClickActionGrid: EventEmitter<object> = new EventEmitter();
  @Output() onSortChange: EventEmitter<Column> = new EventEmitter();

  constructor(private authenticationService: AuthenticationService,) { }

  private selectedRows: Array<TagObjectDTO> = [];
  private firstSelectedRowIndex: number = -1;

  public ngOnChanges(changes): void {
    this.selectedRows = [];
  }

  public selectedAllRows(selectedRows: Array<TagObjectDTO>, rows: Array<any>): void {
    rows.forEach((row) => {
      this.addRemoveArrayByIndex(selectedRows, row, false);
    });
  }

  public unSelectedAllRows(selectedRows: Array<TagObjectDTO>): void {
    selectedRows.splice(0, selectedRows.length);
  }

  private trackByFn(index, item) {
    return index; // or item.id
  }

  private clickActionSelect(tag: TagObjectDTO, index: number, event: any): void {
    if (this.firstSelectedRowIndex > index && event.shiftKey) {
      for (var i = this.firstSelectedRowIndex; i >= index; i--) {
        this.addRemoveArrayByIndex(this.selectedRows, this.rows[i], false);
      }
      this.firstSelectedRowIndex = - 1
    }
    else if (this.firstSelectedRowIndex < index && event.shiftKey) {
      for (var i = this.firstSelectedRowIndex + 1; i <= index; i++) {
        this.addRemoveArrayByIndex(this.selectedRows, this.rows[i], false);
      }
      this.firstSelectedRowIndex = - 1
    }
    else {
      this.firstSelectedRowIndex = index;
      this.addRemoveArrayByIndex(this.selectedRows, tag);
    }
  }

  private clickAction(action: any, tag: TagObjectDTO): void {
    this.onClickActionGrid.emit({ action, tag });
  }

  private onDragStart(event: DragEvent, tag: TagObjectDTO, index: number) {
    if ((this.selectedRows.findIndex(x => x.tagName == tag.tagName) > -1 && this.selectedRows.length == 1) || this.selectedRows.length == 0) {
      event.dataTransfer.setData("text", JSON.stringify(tag));
    }
    else {
      this.addRemoveArrayByIndex(this.selectedRows, tag, false);
    }
  }

  private addRemoveArrayByIndex(array: Array<TagObjectDTO>, tag: TagObjectDTO, isDeleteAllowed: boolean = true): void {
    let foundIndex: number = array.findIndex(x => x.tagName == tag.tagName);
    if (foundIndex < 0)
      array.push(tag);
    else if (isDeleteAllowed)
      array.splice(foundIndex, 1);
  }

  private clickActionEvent(action: any, item: any, index: number, event: any): void {
    item.checkbox = event.target.checked;
    this.onClickActionGrid.emit({ action, item, event});
  }

  private sort(columnClicked: Column, isSortAscending): any {
    this.columns.forEach((column) => {
      column.isSortAscending = null;
    });
    columnClicked.isSortAscending = isSortAscending;
    this.onSortChange.emit(columnClicked);
  }
}

export class Column {
  constructor(
    public field: string,
    public header: string,
    public action: string,
    public tooltip: string,
    public arg: any = null,
    public isSortable: boolean = false,
    public isSortAscending: boolean = null
  ) { }
}
export class Cell {
  constructor(
    public image: string,
    public arg: any,
    public action: string,
  ) { }
}