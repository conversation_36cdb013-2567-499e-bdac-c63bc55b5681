﻿import { Component, Input, Output, EventEmitter, SimpleChanges, OnChanges } from "@angular/core";
import { EditorFieldObjectDTO } from "../../data/model/models";
import { FormControl } from '@angular/forms';
import { KeysPipe } from "../../global/keys.pipe";

@Component({
  selector: "comboboxEditorMultiselectComponent",
  styles: [`
    .panel {
      margin-bottom: 2px; !important;
      background-color: rgba(255, 255, 255, 0.7);
      border: 1px solid #ccc;
    }
 		.chevron {
			font-size: 12px;
			margin-left: 6px;
			margin-right: 4px;
		}
    .input {
      padding: 1px 12px;
      width: 330px;
      height: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      }
    .list{
      padding:8px; 
      position:absolute; 
      background-color: rgba(255, 255, 255);
      border: 1px solid #ccc;
      z-index:99;
    }
    .truncate {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }`
  ],
  providers: [KeysPipe],
  template: `
		<div class="panel" (mouseenter)="selectOpenToggle($event, true)"  (mouseleave)="selectOpenToggle($event, false)">
			<div style="display: inline-block;">
				<div class="input" title="{{editorFieldcontrolValue.replace('[','').replace(']','')}}">{{editorFieldcontrolValue.replace('[','').replace(']','')}}</div>
			</div>
			<div style="display: inline-block; vertical-align:middle;">
				<div class="glyphicon glyphicon-chevron-down chevron"></div>
			</div>
			<div *ngIf="isSelectOpen && this.comboboxItemList?.length > 0" class="panel list" style="">
				<div *ngFor="let item of this.comboboxItemList">
					<label class="truncate">
							<input type="checkbox"
								class="form-check"
								[checked]=item.isChecked
								(change)="itemListOnChange(item)"/>
								<span [tooltip]=[item.value]>&nbsp;{{item.value}}</span>
					</label>
				</div>
			</div>
		</div>
		`
})

export class ComboboxEditorMultiselectComponent implements OnChanges {
  @Input() editorFieldcontrolValue: string;
  @Input() componentData: any;
  @Output() OnComboboxChange: EventEmitter<string> = new EventEmitter();
  private isSelectOpen: boolean;
  private comboboxItemList: comboboxMultiselectItem[] = [];

  constructor(private keysPipe: KeysPipe) { }

  private selectOpenToggle(event: MouseEvent, isOpen: boolean): void {
    let eventRelatedTarget: any = event.relatedTarget;
    if (eventRelatedTarget != null && eventRelatedTarget.className != null && eventRelatedTarget.className.includes("tooltipComponent"))
      return;

    this.isSelectOpen = isOpen;
    event.preventDefault();
  }

  public ngOnChanges(changes: SimpleChanges) {
    if (changes["componentData"]) {
      this.comboboxItemList = [];
      if (this.componentData) {
        this.componentData.forEach((data) => {
          this.comboboxItemList.push({ value: data.value, isChecked: false });
        });
      }
    }
    if (changes["editorFieldcontrolValue"]) {
      if (this.componentData) {
        for (var i = 0; i < this.comboboxItemList.length; i++) {
          this.comboboxItemList[i].isChecked = false;
        }
        if (this.editorFieldcontrolValue && this.editorFieldcontrolValue != "") {
          let itemsList = JSON.parse(this.editorFieldcontrolValue);
          for (var i = 0; i < itemsList.length; i++) {
            for (var y = 0; y < this.comboboxItemList.length; y++) {
              if (this.comboboxItemList[y].value == itemsList[i]) {
                this.comboboxItemList[y].isChecked = true;
              }
            }
          }
        }
      }
    }
  }

  private itemListOnChange(item: comboboxMultiselectItem): void {
    let comboboxItemResult: any[] = [];
    item.isChecked = !item.isChecked;
    for (var i = 0; i < this.comboboxItemList.filter(p => p.isChecked).length; i++) {
      comboboxItemResult.push(this.comboboxItemList.filter(p => p.isChecked)[i].value);
    }
    this.editorFieldcontrolValue = JSON.stringify(comboboxItemResult);
    this.OnComboboxChange.emit(this.editorFieldcontrolValue);
  }
}

export interface comboboxMultiselectItem {
  key?: string;
  value?: number;
  isChecked?: boolean;
}