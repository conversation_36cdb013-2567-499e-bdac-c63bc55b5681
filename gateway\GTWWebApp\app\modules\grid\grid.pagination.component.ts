import { Component, Input, Output, EventEmitter, SimpleChanges, OnChanges } from "@angular/core";
import { GlobalDataService } from "../../global/global.data.service";
import { EdgePairDTO } from "../../data/model/models";

@Component({
  selector: "gridPaginationComponent",
  styles: [`
    .page {
      display: inline-block;
      margin-left:16px;
      margin-right:16px;
      font-size: 12px;
      font-weight:bold;
			color: gray;
      cursor: pointer;
    }
    .page:hover {
      background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
    }
    .pages{
      text-align: center;
      overflow-y: visible;
    }
  `],
  template: `
    <div>
      <div class="pages">
        <ng-container *ngFor="let page of pages">
          <div class="page" (click)="onPageClick(page)" *ngIf="page!=currentPage"><span title="{{page.startText}}">{{page.start}}</span> - <span title="{{page.endText}}">{{page.end}}</span></div>
          <div class="page is-selected" *ngIf="page===currentPage"><span title="{{page.startText}}">{{page.start}}</span> - <span title="{{page.endText}}">{{page.end}}</span></div>
        </ng-container>
      </div>
    </div>`
})

export class GridPaginationComponent {
  @Output() onPageSelect: EventEmitter<Page> = new EventEmitter();
  @Input() itemCount: number = 0;
  @Input() pageEdgePairs: Array<EdgePairDTO> = [];

  private currentPage: Page;
  private pages: Page[] = [];
  private itemPerPage: number = 0;

  constructor(private globalDataService: GlobalDataService) {
  }

  public ngOnChanges(changes: SimpleChanges) {
    for (let propName in changes) {
      let change = changes[propName];
      let curVal = JSON.stringify(change.currentValue);
      let prevVal = JSON.stringify(change.previousValue);
      if (curVal == prevVal) {
        return;
      }
    }
    this.pages = [];
    if (this.pageEdgePairs.length > 0) {
      this.pageEdgePairs.forEach((page) => {
        this.pages.push(new Page(page.startIndex, page.endIndex, page.startName, page.endName));
      });
      this.currentPage = this.pages[0];
    }
    else {
      this.itemPerPage = this.globalDataService.SDGConfig.gtwHttpPageBlockSize;
      if (this.itemCount > 0 && this.itemPerPage > 0) {
        let pageCount = Math.ceil(this.itemCount / this.itemPerPage);
        if (pageCount >= 1) {
          for (let i = 1; i <= pageCount; i++) {
            if (i * this.itemPerPage > this.itemCount)
              this.pages.push(new Page(i * this.itemPerPage - (this.itemPerPage - 1), this.itemCount, "", ""));
            else
              this.pages.push(new Page(i * this.itemPerPage - (this.itemPerPage - 1), i * this.itemPerPage, "", ""));
          }
        }
        this.currentPage = this.pages[0];
      }
    }
  }

  private onPageClick(currentPage: Page): void {
    this.currentPage = currentPage;
    this.onPageSelect.emit(currentPage);
  }
}

export class Page {
  constructor(
    public start: number,
    public end: number,
    public startText: string,
    public endText: string
  ) { }
}