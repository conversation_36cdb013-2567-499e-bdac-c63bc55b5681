﻿import { Component, Input, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild } from "@angular/core";
import { FileService } from "../data/api/api";
import { AuthenticationService } from "../authentication/authentication.service";
import { Panel } from '../modules/panel/panel';
import { LogEntryDTO } from "../data/model/models";
import { AlertService } from "../modules/alert/alert.service";
import { KeysPipe } from "../global/keys.pipe";
import { DownloadComponent } from "../modules/download/download.component";

@Component({
  selector: "logMonitorComponent",
  providers: [KeysPipe],
  styles: [`
      .panel-scroll{
        height: calc(100% - 78px);
        overflow-x: auto;
      }
			.header-log-grid {
        margin: 30px 0px 0px 0px;
        background-color: transparent;
			}
			.scrollButton {
        position: absolute;
        top: 102px;
        right: 16px;
        z-index: 999;
        padding: 4px;
        background: transparent;
      }
      .grid-header {
        font-weight:bold;
      }
      .input-xs {
        height: 22px;
        padding: 2px 5px;
        font-size: 12px;
        line-height: 1.5; /* If Placeholder of the input is moved up, rem/modify this. */
        border-radius: 3px;
      }
      .download-log {
        position: absolute;
        top: 106px;
        z-index: 999;
      }
      `],
  template: `
  		<div style="min-width: 960px">
		    <div class="panel panel-default panel-main" style="width:99%; margin-left:6px; margin-top:60px">
          <div class="panel-heading"> <img src="../../images/log.svg" class= "module-icon" /> {{ 'TR_MONITOR_LOG' | translate }}</div>
	        <div class="panel-body">
            <div class="download-log" *ngIf="this.authenticationService.role | checkRole:'OPERATOR_ROLE'">
              <select class="input-sm" [(ngModel)]="selectedLogFileName" [ngClass]="{'placeholder':selectedLogFileName==''}">
                <option value="" disabled selected>{{'TR_SELECT' | translate}}</option>
                <option *ngFor="let logFileName of logFileNameList" [ngValue]="logFileName.value">{{logFileName.value}}</option>
              </select>
              <button type="button" id="downloadFile" (click)="onlogFileDownloadClick()" class="btn btn-default btn-sm"><img src="../../images/download.svg" class="image-button"/>&nbsp;{{ 'TR_DOWNLOAD_SELECTED_LOG_FILE' | translate }}</button>
            </div>
            <div class="header-log-grid panel panel-default">
              <table style="table-layout:fixed; width:100%">
                <tr>
                  <td style="width: 15%" class="grid-header">{{'TR_TIME_STAMP' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.timeStamp" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
                  <td style="width: 10%" class="grid-header">{{'TR_SOURCE' | translate}}<br/><input type="text" class="form-control input-xs " [(ngModel)]="logEntryFilter.source" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
                  <td style="width: 10%" class="grid-header">{{'TR_CATEGORY' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.category" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
                  <td style="width: 10%" class="grid-header">{{'TR_SEVERITY' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.severity" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
                  <td style="width: 55%" class="grid-header">{{'TR_MESSAGE' | translate}}<br/><input type="text" class="form-control input-xs" [(ngModel)]="logEntryFilter.message" placeholder="{{'TR_ENTER_FILTER' | translate}}"/></td>
                </tr>
              </table>
            </div>
            <div #scrollMe class="panel-scroll">
              <div>
                <logMonitorGridComponent [logEntryFilter]="logEntryFilter"></logMonitorGridComponent>
              </div>
              <div class="scrollButton">
                <button *ngIf="isScrollLock" type="button" (click)="onClickLockScroll(false)" class="btn btn-default btn-sm"><img src="../../images/scroll.svg" class="image-button"/>&nbsp;{{'TR_UNLOCK_SCROLL' | translate}}</button>
                <button *ngIf="!isScrollLock" type="button" (click)="onClickLockScroll(true)" class="btn btn-default btn-sm"><img src="../../images/scrollNo.svg" class="image-button"/>&nbsp;{{'TR_LOCK_SCROLL' | translate}}</button>
              </div>
            </div>
	        </div>
        </div>
		</div>
    <downloadComponent #download></downloadComponent>`
})

export class LogMonitorComponent implements OnInit {
  @ViewChild("scrollMe", { static: false }) private myScrollContainer: ElementRef;
  @ViewChild("download", { static: false }) download: DownloadComponent;
  @Input("panel") panel: Panel;
  private logEntryFilter: LogEntryDTO = {};
  private isExtraPanelOpen: boolean;
  private isScrollLock: boolean = true;
  private logFileNameList: string[];
  private selectedLogFileName: string = "";

  constructor(private authenticationService: AuthenticationService, private fileService: FileService, private alertService: AlertService, private keysPipe: KeysPipe) { }

  public ngOnInit(): void {
    this.logEntryFilter = { timeStamp: "", source: "", category: "", severity: "", message: "" };
    this.getFileNameList();
  }

  private extraPanelToggle(): void {
    this.isExtraPanelOpen = !this.isExtraPanelOpen;
  }

  private getFileNameList(): void {
    this.fileService.filesGet("SDGLogs", ".txt").subscribe(
      (data: any) => {
        if (data.result)
          this.logFileNameList = this.keysPipe.transformJson(data.files);
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED"); }
      }
    );
  }

  private onlogFileDownloadClick(): void {
    if (this.selectedLogFileName != undefined && this.selectedLogFileName != "") {
      this.download.downloadClick(this.selectedLogFileName, "SDGLogs");
    }
    else {
      this.alertService.error("TR_PLEASE_SELECT_A_FILE");
    }
  }

  public ngAfterViewChecked(): void {
    if (this.isScrollLock)
      this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
  }

  private onClickLockScroll(isScrollLock): void {
    try {
      this.isScrollLock = isScrollLock;
      if (this.isScrollLock)
        this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    }
    catch (err) { }
  }
}