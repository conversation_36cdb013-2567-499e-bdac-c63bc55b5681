﻿﻿import { Component, Input } from "@angular/core";
import { DialogR<PERSON>, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { Modal, BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { overlayConfigFactory } from "ngx-modialog-7";
import { AlertService } from "../modules/alert/alert.service";
import { UserObjectDTO, SDGCheckAuthDTO } from "../data/model/models";
import { AuthService } from "../data/api/api";
import { TranslateService } from "@ngx-translate/core";
import { AuthenticationService } from "./authentication.service";
import { Configuration } from "../data/configuration";

@Component({
	selector: "authenticationLoginModal",
	providers: [AuthService],
  styles: [`
      .password {
        display:inline-block;
        width:90%;
        margin-bottom: 0px;
      }
      .status-bar{
        margin: 2px;
      }
    `],
	template: `
					<div class="container-fluid">
						<div class="modal-heading">{{'TR_LOGIN' | translate}}</div>
						<div class="modal-body">
              <form name="form" (ngSubmit)="f.form.valid && login()" #f="ngForm" novalidate>
	              <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !username.valid }">
			            <label for="username">{{'TR_USERNAME' | translate}}</label>
			            <input type="text" class="form-control" name="username" [(ngModel)]="currentUser.username" #username="ngModel" required />
			            <small class="text-danger" *ngIf="f.submitted && !username.valid">{{'TR_USERNAME_IS_REQUIRED' | translate}}</small>
	              </div>
	              <div class="form-group password" [ngClass]="{ 'has-error': f.submitted && !password.valid }">
			            <label for="password">{{'TR_PASSWORD' | translate}}</label>
			            <input type="password" class="form-control" name="password" [(ngModel)]="currentUser.password" #password="ngModel" required show-password/> 
	              </div>
                <small class="text-danger" *ngIf="f.submitted && !password.valid">{{'TR_PASSWORD_IS_REQUIRED' | translate}}</small>
	              <div class="form-group" style="display: inline-block;width:99%; text-align: left; margin-top: 8px;">
		              <div style="display: table-cell; text-align: right;">
			              <button class="btn btn-default"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_LOGIN_BUTTON' | translate}}</button>
		              </div>
	              </div>
                <br>
		            <alertStatusBarComponent class="status-bar"></alertStatusBarComponent>
              </form>
					  </div>
					</div>`
})
export class AuthenticationLoginModal implements CloseGuard, ModalComponent<LoginModalContext> {
	private context: LoginModalContext;
	private currentUser: UserObjectDTO = {};
	private authentication: SDGCheckAuthDTO = {};

	constructor(public dialog: DialogRef<LoginModalContext>, private authService: AuthService, private alertService: AlertService, private translate: TranslateService, private modal: Modal) {
		this.context = dialog.context;
		this.context.dialogClass = "modal-dialog modal-sm";
		dialog.setCloseGuard(this);
		dialog.inElement = true;
  }

	beforeDismiss(): boolean {
		return true; // prevent closing modal by using Esc
	}

	private login(isForcingUsersLogoff: boolean = false) {
		this.authService.configuration = this.dialog.context.userApiConfig;
		this.authService.loginUser(this.currentUser.username, this.currentUser.password, isForcingUsersLogoff)
		.subscribe(
			data => {
				this.authentication = data;
				this.alertService.clearMessage();
				this.dialog.close(this.authentication);
			},
			error => {
				if (error.status == 419) {
          this.alertService.clearMessage();
          this.dialog.close({ status: error.status, username: this.currentUser.username});
				}
				else if (error.status == 409) {
					let confirmSaveModalRef;
					this.translate.get("TR_LOG_IN_FAILED_OTHER_USERS_ARE_ALREADY_LOGGED_IN").subscribe(res => {
					confirmSaveModalRef = this.modal.confirm()
					.size('lg')
					.showClose(false)
					.title(this.translate.instant('TR_WARNING'))
					.okBtn(this.translate.instant('TR_YES'))
					.cancelBtn(this.translate.instant('TR_NO'))
					.okBtnClass('btn btn-default')
					.body(`
						<div class="panel panel-warning">
							<div class="panel-heading"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + res + `</div>
						</div>
						`)
					.open()
					});
					confirmSaveModalRef.result.then(
						result => this.login(true),
						() => this.currentUser.password = ""
					);
				}
				else if (error.status == 406) {
					this.translate.get("TR_UNAUTHORIZED_SU_ALREADY_LOGIN").subscribe(res => {
						this.alertService.error(res);
					});
					this.alertService.debug(error.message.toString());
				}
				else if (error.status == 412) {
					this.translate.get("TR_ADMIN_USER_IS_NOT_CONFIGURED").subscribe(res => {
						this.alertService.error(res);
					});
					this.alertService.debug(error.message.toString());
				}
				else if (error.status == 420) {
					this.translate.get("TR_UNAUTHORIZED_MAX_FAILED_LOGIN_ATTEMPT_REACHED").subscribe(res => {
						this.alertService.error(res);
					});
					this.alertService.debug(error.message.toString());
				}
        else {
          this.translate.get("TR_UNAUTHORIZED").subscribe(res => {
            this.alertService.error(res);
          });
					this.alertService.debug(error.message.toString());
        }
			}
		);
	}
}
export class LoginModalContext extends BSModalContext {
  userApiConfig: Configuration  = null;
}