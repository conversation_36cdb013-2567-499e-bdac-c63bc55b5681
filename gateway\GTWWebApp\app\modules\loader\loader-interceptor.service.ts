import { Injectable } from "@angular/core";
import { HttpResponse, HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { LoaderService } from "./loader.service";
import { map, catchError } from "rxjs/operators";

@Injectable()
export class LoaderInterceptorService implements HttpInterceptor {
  constructor(private loaderService: LoaderService) { }
  pendingRequests: number = 0;

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (request.url.includes("/gettag") ||
      request.url.includes("_log_filter_") ||
      request.url.includes("/get_mirror_all_to_log_file")
    ) {
      this.pendingRequests = 0;
    }
    else {
      this.loaderService.toggleIsLoading(true);
      this.pendingRequests++;
    }
    return next.handle(request)
      .pipe(catchError((err) => {
        this.pendingRequests = 0;
        this.loaderService.toggleIsLoading(false);
        return throwError(err);
      }))
      .pipe(map<HttpEvent<any>, any>((evt: HttpEvent<any>) => {
        if (evt instanceof HttpResponse) {
          if (true) {
            this.pendingRequests--;
            if (this.pendingRequests <= 0)
              this.loaderService.toggleIsLoading(false);
            this.pendingRequests = 0;
          }
        }
        return evt;
    }));
  }
}