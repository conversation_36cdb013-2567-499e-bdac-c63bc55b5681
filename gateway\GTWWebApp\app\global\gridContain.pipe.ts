﻿import { Pipe, PipeTransform } from "@angular/core";
import { LogEntryDTO } from "../data/model/models";

@Pipe({ name: "gridContain", pure: false})
export class GridContain implements PipeTransform {
  transform(rows: Array<any>, columnFilter: Array<any>): Array<any> {
    try {
      if (rows == null || !Array.isArray(rows) || rows.length == 0)
        return 
      return rows.filter(rowFiltered => {
        let propertyNames = Object.getOwnPropertyNames(rowFiltered);
        let isRowValid: boolean = true;
        propertyNames.forEach((propertyName) => {
          if (columnFilter[propertyName] && rowFiltered[propertyName].toString().toLowerCase().indexOf(columnFilter[propertyName].toLowerCase()) == -1)
            isRowValid = false;
        });
        if (isRowValid) {
          return rowFiltered;
        }
      });
    }
    catch (err) {
      console.log(err);
    }
  }
}


