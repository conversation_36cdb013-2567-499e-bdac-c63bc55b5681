System.register(["@angular/core", "@angular/router", "../../data/model/models", "../../modules/alert/alert.service", "../../authentication/authentication.service", "./dashboard.config.device.virtual-node.component", "../../data/api/api", "../../data/wsApi/wsApi", "@ngx-translate/core", "../../modules/panel/panel", "../../global/global.json.util", "../../modules/loader/loader.service", "./dashboard.config.import-export.modal", "ngx-modialog-7/plugins/bootstrap", "ngx-modialog-7", "rxjs/operators"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, router_1, models_1, alert_service_1, authentication_service_1, dashboard_config_device_virtual_node_component_1, api_1, wsApi_1, core_2, panel_1, global_json_util_1, loader_service_1, dashboard_config_import_export_modal_1, bootstrap_1, ngx_modialog_7_1, operators_1, DashboardConfigDevicesComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (router_1_1) {
                router_1 = router_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (dashboard_config_device_virtual_node_component_1_1) {
                dashboard_config_device_virtual_node_component_1 = dashboard_config_device_virtual_node_component_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (panel_1_1) {
                panel_1 = panel_1_1;
            },
            function (global_json_util_1_1) {
                global_json_util_1 = global_json_util_1_1;
            },
            function (loader_service_1_1) {
                loader_service_1 = loader_service_1_1;
            },
            function (dashboard_config_import_export_modal_1_1) {
                dashboard_config_import_export_modal_1 = dashboard_config_import_export_modal_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            }
        ],
        execute: function () {
            DashboardConfigDevicesComponent = (function () {
                function DashboardConfigDevicesComponent(alertService, manageService, nodesService, nodesWSApi, broadcastEventWSApi, authenticationService, translate, loaderService, fileService, tagsService, mappingsService, logService, modal, router) {
                    this.alertService = alertService;
                    this.manageService = manageService;
                    this.nodesService = nodesService;
                    this.nodesWSApi = nodesWSApi;
                    this.broadcastEventWSApi = broadcastEventWSApi;
                    this.authenticationService = authenticationService;
                    this.translate = translate;
                    this.loaderService = loaderService;
                    this.fileService = fileService;
                    this.tagsService = tagsService;
                    this.mappingsService = mappingsService;
                    this.logService = logService;
                    this.modal = modal;
                    this.router = router;
                    this.onSelectedChanged = new core_1.EventEmitter();
                    this.onDropNode = new core_1.EventEmitter();
                    this.isNodeTreeFromSearch = false;
                    this.isDevicesTreeViewSelected = true;
                    this.virtualNodeList = [];
                    this.nodesWSReconnectAttempts = 0;
                }
                DashboardConfigDevicesComponent.prototype.ngAfterViewInit = function () {
                    var _this = this;
                    this.manageService.healthGET().subscribe(function (data) {
                        if (data) {
                            var healthObject = data;
                            if (healthObject.isIniCsvDirty)
                                _this.panel.panelHeadingCSS = "panel-heading-config-dirty-bg";
                            else
                                _this.panel.panelHeadingCSS = "panel-heading-config-bg";
                        }
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
                        }
                    });
                    this.openGlobalBroadcastServiceSubscription();
                    this.nodesWSApi.openWebsocket().subscribe(function (data) {
                        _this.nodesWebsocket = data;
                        _this.loadNodesTreeviewInit();
                        _this.wsGetNodeData();
                    }, function (error) { _this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getNodes", reason: "Open fail" }); });
                    this.virtualNodeList.push({ name: "warning", label: "TR_WARNING_VIEW", description: "TR_WARNING_DESC", isHealthy: true, icon: "warningView.svg" });
                    this.virtualNodeList.push({ name: "health", label: "TR_HEALTH_VIEW", description: "TR_HEALTH_DESC", isHealthy: true, icon: "healthView.svg" });
                    this.virtualNodeList.push({ name: "performance", label: "TR_PERFORMANCE_VIEW", description: "TR_PERFORMANCE_DESC", isHealthy: true, icon: "performanceView.svg" });
                };
                DashboardConfigDevicesComponent.prototype.ngOnDestroy = function () {
                    if (this.nodesWebsocket != null && this.nodesWebsocket.readyState === WebSocket.OPEN)
                        this.nodesWebsocket.close(3000, "panelClose");
                    if (this.nodeServiceSubscription != null)
                        this.nodeServiceSubscription.unsubscribe();
                    if (this.globalBroadcastServiceSubscription != null)
                        this.globalBroadcastServiceSubscription.unsubscribe();
                };
                DashboardConfigDevicesComponent.prototype.saveConfig = function () {
                    var _this = this;
                    this.loaderService.toggleIsLoading(true);
                    this.manageService.saveFile().subscribe(function (data) {
                        _this.alertService.success("TR_FILE_SAVED");
                        _this.loaderService.toggleIsLoading(false);
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.show({ messageKey: "TR_THERE_WERE_PROBLEMS_SAVING_THE_INI_CSV_FILES", messageLogMask: 2, messageType: models_1.BroadcastEventTypeEnumDTO.MessageError }, false);
                            _this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
                        }
                        _this.loaderService.toggleIsLoading(false);
                    }, function () { return _this.loaderService.toggleIsLoading(false); });
                };
                DashboardConfigDevicesComponent.prototype.onWarningOnRootActiveChange = function (node) {
                    this.deviceRootNode = node;
                    node.isWarningActive = !node.isWarningActive;
                    localStorage.setItem("SDGIsWarningOnRootActive", node.isWarningActive.toString());
                };
                DashboardConfigDevicesComponent.prototype.expandChild = function (node, rootNode) {
                    var _this = this;
                    this.deviceRootNode = rootNode;
                    node.isExpanded = true;
                    if (node.hasChildren) {
                        this.nodesService.getNodes(node.nodeFullName, null, false, false, node.nodeCollectionKind.toString())
                            .subscribe(function (data) {
                            if (node.hasChildren) {
                                node.hasChildren = data.hasChildren;
                                node.isExpanded = true;
                                node.children = data.children;
                                _this.expandCollapseChildRecursively(node, true);
                            }
                            _this.sendCurrentNodesToWS();
                        });
                    }
                };
                DashboardConfigDevicesComponent.prototype.collapseChild = function (node) {
                    this.expandCollapseChildRecursively(node, false);
                };
                DashboardConfigDevicesComponent.prototype.importExportCSV = function (nodeFullName) {
                    var _this = this;
                    var dashboardConfigImportExportModalRef = this.modal.open(dashboard_config_import_export_modal_1.DashboardConfigImportExportModal, ngx_modialog_7_1.overlayConfigFactory({ nodeFullName: nodeFullName, tagsService: this.tagsService, mappingsService: this.mappingsService, fileService: this.fileService }, bootstrap_1.BSModalContext));
                    dashboardConfigImportExportModalRef.result.then(function (result) { }, function (error) { _this.alertService.debug(error.toString()); });
                };
                DashboardConfigDevicesComponent.prototype.addNodeToLogFilter = function (nodeFullName) {
                    var _this = this;
                    var logDevices = [];
                    logDevices.push({ name: nodeFullName, add: true });
                    this.logService.putLogFilterDevice(logDevices)
                        .subscribe(function (data) {
                        _this.alertService.success("TR_NODE_ADDED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR");
                        }
                    });
                };
                DashboardConfigDevicesComponent.prototype.openGlobalBroadcastServiceSubscription = function () {
                    var _this = this;
                    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(function (event) {
                        if (event.type === 'message') {
                            var broadcastEventData = JSON.parse(event.data);
                            if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.RefreshUI.toString() && broadcastEventData.parameters != null && broadcastEventData.parameters.refreshWebBrowser == null) {
                                var objectName = null;
                                var objectCollectionKind = null;
                                if (broadcastEventData.parameters != null) {
                                    if (broadcastEventData.parameters.objectName != null)
                                        objectName = broadcastEventData.parameters.objectName;
                                    if (broadcastEventData.parameters.objectCollectionKind)
                                        objectCollectionKind = broadcastEventData.parameters.objectCollectionKind;
                                }
                                _this.panel.panelHeadingCSS = "panel-heading-config-dirty-bg";
                                _this.loadNodeTreeview(objectName, objectCollectionKind, false).subscribe(function (value) {
                                    _this.sendCurrentNodesToWS();
                                });
                            }
                            else if (broadcastEventData.messageType === models_1.BroadcastEventTypeEnumDTO.RefreshDirtyFlag.toString()) {
                                if (broadcastEventData.parameters != null && broadcastEventData.parameters.flag == false)
                                    _this.panel.panelHeadingCSS = "panel-heading-config-bg";
                                else if (broadcastEventData.parameters != null && broadcastEventData.parameters.flag == true)
                                    _this.panel.panelHeadingCSS = "panel-heading-config-dirty-bg";
                            }
                        }
                    });
                };
                DashboardConfigDevicesComponent.prototype.toggle = function () {
                    this.isActive = !this.isActive;
                };
                DashboardConfigDevicesComponent.prototype.expandCollapseChildRecursively = function (node, isExpanded) {
                    node.isExpanded = isExpanded;
                    if (node.hasChildren && node.children != null) {
                        for (var i = 0; i < node.children.length; i++) {
                            node.children[i].isExpanded = isExpanded;
                            if (node.children[i].hasChildren && node.children != null)
                                this.expandCollapseChildRecursively(node.children[i], isExpanded);
                        }
                    }
                };
                DashboardConfigDevicesComponent.prototype.onSearchChanged = function (searchResult) {
                    var node = searchResult.data;
                    var isNodeTreeFromSearch = searchResult.isNodeTreeFromSearch;
                    this.deviceRootNode = node;
                    this.isNodeTreeFromSearch = isNodeTreeFromSearch;
                    if (node)
                        this.onSelectNode(node);
                };
                DashboardConfigDevicesComponent.prototype.onSelectedVirtualNode = function (virtualNode) {
                    this.selectedNode = this.deviceRootNode;
                    this.isDevicesTreeViewSelected = false;
                    this.onSelectedChanged.emit({ selectedNode: this.selectedNode, virtualNodeName: virtualNode.name });
                    this.panel.parameters = virtualNode.name;
                    this.isActive = true;
                };
                DashboardConfigDevicesComponent.prototype.onSelectNode = function (node) {
                    this.selectedNode = node;
                    this.isDevicesTreeViewSelected = true;
                    this.onSelectedChanged.emit({ selectedNode: this.selectedNode, virtualNodeName: null });
                    this.panel.parameters = "GatewayRootNode";
                };
                DashboardConfigDevicesComponent.prototype.onToggleNode = function (node) {
                    this.sendCurrentNodesToWS();
                };
                DashboardConfigDevicesComponent.prototype.sendCurrentNodesToWS = function () {
                    if (this.nodesWebsocket != null && this.nodesWebsocket.readyState === WebSocket.OPEN) {
                        var nodelist = this.listExpandedNodes(this.deviceRootNode);
                        var jsonWSNodesList = JSON.stringify(nodelist);
                        this.nodesWebsocket.send(jsonWSNodesList);
                    }
                };
                DashboardConfigDevicesComponent.prototype.listExpandedNodes = function (currentNode) {
                    var nodelist = [];
                    if (currentNode.children != null) {
                        for (var _i = 0, _a = currentNode.children; _i < _a.length; _i++) {
                            var currentNodeChild = _a[_i];
                            var nodeChildlist = this.listExpandedNodes(currentNodeChild);
                            nodelist.push({
                                "nodeFullName": currentNodeChild.nodeFullName,
                                "nodeCollectionKind": currentNodeChild.nodeCollectionKind
                            });
                            if (nodeChildlist.length > 0) {
                                for (var _b = 0, nodeChildlist_1 = nodeChildlist; _b < nodeChildlist_1.length; _b++) {
                                    var nodeChild = nodeChildlist_1[_b];
                                    nodelist.push(nodeChild);
                                }
                            }
                        }
                    }
                    return nodelist;
                };
                DashboardConfigDevicesComponent.prototype.wsGetNodeData = function () {
                    var _this = this;
                    this.nodeServiceSubscription = this.nodesWSApi.getNodesData(this.nodesWebsocket).subscribe(function (event) {
                        if (event.type === 'message') {
                            _this.nodesWSReconnectAttempts = 0;
                            var wsNodes = JSON.parse(event.data);
                            var rootIsHealthy_1 = true;
                            wsNodes.forEach(function (wsNode) {
                                var wsNodeIsHealthy = _this.isNodeHealthyWS(_this.deviceRootNode, wsNode);
                                if (!wsNodeIsHealthy)
                                    rootIsHealthy_1 = wsNodeIsHealthy;
                            });
                            _this.deviceRootNode.isHealthy = rootIsHealthy_1;
                            _this.virtualNodeList[0].isHealthy = rootIsHealthy_1;
                            _this.virtualNodeList[1].isHealthy = rootIsHealthy_1;
                        }
                        else if (event.type === 'close') {
                            if (_this.nodesWSReconnectAttempts < 3) {
                                setTimeout(function () {
                                    var currentPath = _this.router.url;
                                    var pageName = currentPath.split('/').pop();
                                    if (currentPath !== "/dashboard" && currentPath !== "/config" && currentPath !== "/")
                                        return;
                                    _this.nodesWSReconnectAttempts++;
                                    _this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getNodes", reconnectAttempt: _this.nodesWSReconnectAttempts }).subscribe(function (res) {
                                        _this.alertService.debug(res);
                                    });
                                    _this.nodesWSApi.openWebsocket().subscribe(function (data) {
                                        _this.nodesWebsocket = data;
                                        _this.sendCurrentNodesToWS();
                                        _this.wsGetNodeData();
                                    }, function (error) { _this.alertService.error("TR_WEBSOCKET_CLOSE", { websocketName: "getNodes", reason: "Reopen fail" }); });
                                }, 5000);
                            }
                        }
                    });
                };
                DashboardConfigDevicesComponent.prototype.isNodeHealthyWS = function (node, wsNode) {
                    var _this = this;
                    var _a;
                    var nodeIsHealthy = true;
                    if (node.nodeFullName == wsNode.nodeFullName && node.nodeCollectionKind == wsNode.nodeCollectionKind) {
                        node.isHealthy = wsNode.isHealthy;
                        node.nodeDescription = wsNode.nodeDescription;
                        if (!node.isHealthy)
                            nodeIsHealthy = false;
                    }
                    (_a = node === null || node === void 0 ? void 0 : node.children) === null || _a === void 0 ? void 0 : _a.forEach(function (nodeChild) {
                        var wsNodeAddress = wsNode.nodeFullName.split(".");
                        var nodeChildAddress = nodeChild.nodeFullName.split(".");
                        if (nodeChild.nodeFullName == wsNode.nodeFullName && nodeChild.nodeCollectionKind == wsNode.nodeCollectionKind) {
                            nodeChild.isHealthy = wsNode.isHealthy;
                            nodeChild.nodeDescription = wsNode.nodeDescription;
                            if (!nodeChild.isHealthy)
                                nodeIsHealthy = false;
                        }
                        else if (wsNodeAddress[0] == nodeChildAddress[0] && nodeChild.nodeCollectionKind == wsNode.nodeCollectionKind && nodeChild.children != null && nodeChild.children.length != 0) {
                            if (!_this.isNodeHealthyWS(nodeChild, wsNode))
                                nodeIsHealthy = false;
                        }
                    });
                    return nodeIsHealthy;
                };
                DashboardConfigDevicesComponent.prototype.loadNodesTreeviewInit = function () {
                    var _this = this;
                    try {
                        this.nodesService.getNodes(null, null, false, true, "ALL").subscribe(function (data) {
                            if (data.children)
                                data.children = global_json_util_1.GlobalJsonUtil.sortByProperty(data.children, "nodeFullName");
                            _this.deviceRootNode = data;
                            if (data) {
                                _this.selectedNode = data;
                                _this.sendCurrentNodesToWS();
                                if ((localStorage.getItem("SDGIsWarningOnRootActive") != null && localStorage.getItem("SDGIsWarningOnRootActive") == "true") || localStorage.getItem("SDGIsWarningOnRootActive") == null)
                                    _this.deviceRootNode.isWarningActive = true;
                                else
                                    _this.deviceRootNode.isWarningActive = false;
                            }
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                            }
                        }, function () {
                            if (_this.panel.parameters != "" && (_this.panel.parameters == "warning" || _this.panel.parameters == "health" || _this.panel.parameters == "performance")) {
                                var selectedVirtualNode = _this.virtualNodeList.filter(function (vn) { return vn.name == _this.panel.parameters; })[0];
                                _this.dashboardConfigDeviceVirtualNodeComponent.onSelectVirtualNode(selectedVirtualNode);
                            }
                            else {
                                _this.onSelectNode(_this.deviceRootNode);
                            }
                        });
                    }
                    catch (err) {
                        return null;
                    }
                };
                DashboardConfigDevicesComponent.prototype.loadNodeTreeview = function (nodeFullName, nodeCollectionKind, onlyFirstChildrenLevel) {
                    var _this = this;
                    if (onlyFirstChildrenLevel === void 0) { onlyFirstChildrenLevel = true; }
                    try {
                        return this.nodesService.getNodes(nodeFullName, null, false, onlyFirstChildrenLevel, nodeCollectionKind).pipe(operators_1.map(function (data) {
                            var deviceNode = _this.deviceRootNode;
                            if (nodeFullName != null) {
                                deviceNode = _this.searchTreeNode(_this.deviceRootNode, nodeFullName, nodeCollectionKind);
                                if (deviceNode == null)
                                    return;
                                if (deviceNode != _this.selectedNode)
                                    _this.onSelectNode(deviceNode);
                            }
                            _this.RefreshNodeTreeview(deviceNode, data);
                            _this.loadNodeTreeviewChildren(deviceNode, data);
                            if ((deviceNode === null || deviceNode === void 0 ? void 0 : deviceNode.children) !== null && typeof (deviceNode === null || deviceNode === void 0 ? void 0 : deviceNode.children) !== 'undefined')
                                deviceNode.children = global_json_util_1.GlobalJsonUtil.sortByProperty(deviceNode.children, "nodeFullName");
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error });
                            }
                        }));
                    }
                    catch (err) {
                        return null;
                    }
                };
                DashboardConfigDevicesComponent.prototype.RefreshNodeTreeview = function (deviceNode, data) {
                    try {
                        deviceNode.nodeDescription = data.nodeDescription;
                    }
                    catch (err) {
                    }
                };
                DashboardConfigDevicesComponent.prototype.loadNodeTreeviewChildren = function (deviceNode, data) {
                    try {
                        var deviceNodeChildren = deviceNode.children == null ? [] : deviceNode.children;
                        var dataChildren = data.children == null ? [] : data.children;
                        var addedNodes = this.compareJSON(dataChildren, deviceNodeChildren);
                        var removedNodes = this.compareJSON(deviceNodeChildren, dataChildren);
                        for (var _i = 0, addedNodes_1 = addedNodes; _i < addedNodes_1.length; _i++) {
                            var addedNode = addedNodes_1[_i];
                            if (!deviceNode.children)
                                deviceNode.children = [];
                            deviceNode.children.push(addedNode);
                            deviceNode.hasChildren = data.hasChildren;
                            if (this.selectedNode != null && this.selectedNode.hasChildren)
                                this.selectedNode.isExpanded = true;
                        }
                        var _loop_1 = function (removedNode) {
                            var removedNodeIndex = deviceNode.children.findIndex(function (_a) {
                                var nodeFullName = _a.nodeFullName, nodeCollectionKind = _a.nodeCollectionKind;
                                return nodeFullName == removedNode.nodeFullName && nodeCollectionKind == removedNode.nodeCollectionKind;
                            });
                            if (removedNodeIndex != -1) {
                                deviceNode.children.splice(removedNodeIndex, 1);
                                deviceNode.hasChildren = data.hasChildren;
                            }
                        };
                        for (var _a = 0, removedNodes_1 = removedNodes; _a < removedNodes_1.length; _a++) {
                            var removedNode = removedNodes_1[_a];
                            _loop_1(removedNode);
                        }
                        for (var _b = 0, _c = deviceNode.children; _b < _c.length; _b++) {
                            var deviceNodeChildren_1 = _c[_b];
                            for (var _d = 0, _e = data.children; _d < _e.length; _d++) {
                                var dataChildren_1 = _e[_d];
                                if (deviceNodeChildren_1.hasChildren && dataChildren_1.hasChildren && deviceNodeChildren_1.nodeName == dataChildren_1.nodeName) {
                                    this.loadNodeTreeviewChildren(deviceNodeChildren_1, dataChildren_1);
                                }
                            }
                        }
                    }
                    catch (err) {
                    }
                };
                DashboardConfigDevicesComponent.prototype.searchTreeNode = function (element, nodeFullName, nodeCollectionKind) {
                    if (element.nodeFullName == nodeFullName && element.nodeCollectionKind.toString() == nodeCollectionKind) {
                        return element;
                    }
                    else if (element.children != null) {
                        var result = null;
                        for (var i = 0; result == null && i < element.children.length; i++) {
                            result = this.searchTreeNode(element.children[i], nodeFullName, nodeCollectionKind);
                        }
                        return result;
                    }
                    return null;
                };
                DashboardConfigDevicesComponent.prototype.onDrop = function (dragData) {
                    this.onDropNode.emit(dragData);
                };
                DashboardConfigDevicesComponent.prototype.compareJSON = function (array1, array2) {
                    var keys = Object.keys(array2.reduce(function (a, _a) {
                        var _b;
                        var nodeFullName = _a.nodeFullName, isUsedFor = _a.isUsedFor;
                        return Object.assign(a, (_b = {}, _b[nodeFullName + "_" + isUsedFor] = undefined, _b));
                    }, {}));
                    return array1.filter(function (_a) {
                        var nodeFullName = _a.nodeFullName, isUsedFor = _a.isUsedFor;
                        return !keys.includes(nodeFullName + "_" + isUsedFor);
                    });
                };
                __decorate([
                    core_1.ViewChild("dashboardConfigDeviceVirtualNodeComponent", { static: false }),
                    __metadata("design:type", dashboard_config_device_virtual_node_component_1.DashboardConfigDeviceVirtualNodeComponent)
                ], DashboardConfigDevicesComponent.prototype, "dashboardConfigDeviceVirtualNodeComponent", void 0);
                __decorate([
                    core_1.Input("panel"),
                    __metadata("design:type", panel_1.Panel)
                ], DashboardConfigDevicesComponent.prototype, "panel", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDevicesComponent.prototype, "onSelectedChanged", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigDevicesComponent.prototype, "onDropNode", void 0);
                __decorate([
                    core_1.ViewChild("container", { read: core_1.ViewContainerRef, static: true }),
                    __metadata("design:type", core_1.ViewContainerRef)
                ], DashboardConfigDevicesComponent.prototype, "container", void 0);
                DashboardConfigDevicesComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigDevicesComponent",
                        styles: ["\n      .title {\n        font-size: 14px;\n        font-weight: bold;\n        text-align: center;\n        vertical-align: middle;\n        display: table-cell;\n        width: 100%;\n      }\n      .panel-content {\n        padding: 6px\n      }\n      .panel-heading {\n        padding: 2px 6px;\n        width: 100%;\n        border: 1px solid #f5f5f5;\n        border-radius: 3px;\n      }\n      .cell{\n        display: table-cell;\n        padding: 2px;\n      }\n      "
                        ],
                        template: "\n      <div>\n        <div class=\"panel-heading shadow-bg\">\n          <div *ngIf=\"isActive\" class=\"glyphicon glyphicon-chevron-down glyphicon-size cell\" title=\"{{'TR_COLLAPSE' | translate}}\" (click)=\"toggle()\"></div>\n          <div *ngIf=\"!isActive\" class=\"glyphicon glyphicon-chevron-right glyphicon-size cell\" title=\"{{'TR_EXPAND' | translate}}\" (click)=\"toggle()\"></div>\n          <div class=\"title\">{{'TR_VIEWS' | translate}}</div>\n        </div>\n        <div class=\"panel-content shadow-bg\" [ngClass]=\"{hide: !isActive}\">\n          <dashboardConfigDeviceVirtualNodeComponent #dashboardConfigDeviceVirtualNodeComponent [isDevicesTreeViewSelected]=\"isDevicesTreeViewSelected\" [virtualNodeList]=\"virtualNodeList\" (onSelectedChanged)=\"onSelectedVirtualNode($event)\"></dashboardConfigDeviceVirtualNodeComponent>\n        </div>\n        <dashboardConfigDevicesSearchComponent (onSearchChanged)=\"onSearchChanged($event)\"></dashboardConfigDevicesSearchComponent>\n        <dashboardConfigDevicesTreeViewComponent [deviceNode]=\"deviceRootNode\" [deviceRootNode]=deviceRootNode [isDevicesTreeViewSelected]=\"isDevicesTreeViewSelected\" [selectedNode]=\"selectedNode\" [isNodeTreeFromSearch]=\"isNodeTreeFromSearch\" (onSelectedChanged)=\"onSelectNode($event)\" (onDropNode)=\"onDrop($event)\" (onToggleNode)=\"onToggleNode($event)\"></dashboardConfigDevicesTreeViewComponent>\n      </div>\n      <ng-container #container></ng-container>\n  "
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, api_1.ManageService, api_1.NodesService,
                        wsApi_1.NodesWSApi, wsApi_1.BroadcastEventWSApi,
                        authentication_service_1.AuthenticationService, core_2.TranslateService,
                        loader_service_1.LoaderService, api_1.FileService,
                        api_1.TagsService, api_1.MappingsService,
                        api_1.LogService, bootstrap_1.Modal, router_1.Router])
                ], DashboardConfigDevicesComponent);
                return DashboardConfigDevicesComponent;
            }());
            exports_1("DashboardConfigDevicesComponent", DashboardConfigDevicesComponent);
        }
    };
});
//# sourceMappingURL=dashboard.config.devices.component.js.map