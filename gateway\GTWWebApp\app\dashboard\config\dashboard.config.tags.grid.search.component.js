System.register(["@angular/core", "../../data/model/models"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, models_1, DashboardConfigTagsGridSearchComponent, SearchCriteria, SearchField, SearchTypeEnum;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagsGridSearchComponent = (function () {
                function DashboardConfigTagsGridSearchComponent() {
                    this.onSearch = new core_1.EventEmitter();
                    this.onSearchIsActive = new core_1.EventEmitter();
                    this.searchIsActive = false;
                    this.controlTypeEnum = models_1.EditorFieldObjectDTO.ControlTypeEnum;
                    this.searchTypeEnum = SearchTypeEnum;
                }
                DashboardConfigTagsGridSearchComponent.prototype.toggle = function () {
                    this.searchIsActive = !this.searchIsActive;
                    this.onSearchIsActive.emit(this.searchIsActive);
                    if (!this.searchIsActive) {
                        this.onSearch.emit(null);
                    }
                };
                DashboardConfigTagsGridSearchComponent.prototype.onSearchClick = function (searchType) {
                    var searchFields = this.searchCriteria.searchFields;
                    switch (searchType) {
                        case SearchTypeEnum.Search:
                            this.searchCriteria.searchType = searchType;
                            this.onSearch.emit(this.searchCriteria);
                            break;
                        case SearchTypeEnum.DeepSearch:
                            this.searchCriteria.searchType = searchType;
                            this.onSearch.emit(this.searchCriteria);
                            break;
                        case SearchTypeEnum.Clear:
                            this.searchCriteria.searchFields.forEach(function (searchField) {
                                searchField.value = "";
                            });
                            this.onSearch.emit();
                            break;
                    }
                };
                DashboardConfigTagsGridSearchComponent.prototype.onKeyPress = function (event) {
                    var input = String.fromCharCode(event.keyCode);
                    if (!((event.key == "$") || (event.key == ".") || (event.key == "*") || (event.key == "_") || (/[a-zA-Z0-9]/.test(input)))) {
                        event.preventDefault();
                        return false;
                    }
                };
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigTagsGridSearchComponent.prototype, "onSearch", void 0);
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", core_1.EventEmitter)
                ], DashboardConfigTagsGridSearchComponent.prototype, "onSearchIsActive", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", SearchCriteria)
                ], DashboardConfigTagsGridSearchComponent.prototype, "searchCriteria", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigTagsGridSearchComponent.prototype, "isRecursive", void 0);
                __decorate([
                    core_1.HostListener('keypress', ['$event']),
                    __metadata("design:type", Function),
                    __metadata("design:paramtypes", [KeyboardEvent]),
                    __metadata("design:returntype", void 0)
                ], DashboardConfigTagsGridSearchComponent.prototype, "onKeyPress", null);
                DashboardConfigTagsGridSearchComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagsGridSearchComponent",
                        styles: ["\n  .hide {\n    display: none;\n  }\n  .title {\n    font-size: 16px; \n    font-weight: bold;\n    padding-left: 30px;\n  }\n  .panel-content {\n    padding: 6px\n  }\n  .search-button {\n    top: -2px !important;\n    left: -6px;\n    position: relative;\n  }\n  .panel-heading {\n    background-color: transparent;\n    position: relative;\n    padding: 0 0 0 2px;\n    top: 30px;\n    margin-top: -30px;\n  }\n  .front {\n    position: relative;\n    z-index: 2;\n  }"],
                        template: "\n      <div class=\"panel-heading\">\n        <div *ngIf=\"searchIsActive\" class=\"round-button front\" title=\"{{'TR_COLLAPSE' | translate}}\" (click)=\"toggle()\"><img [src]=\"'../../images/magnifier.svg'\" class=\"image-button\"/></div>\n        <div *ngIf=\"!searchIsActive\" class=\"round-button front\" title=\"{{'TR_EXPAND' | translate}}\" (click)=\"toggle()\"><img [src]=\"'../../images/magnifier.svg'\" class=\"image-button\"/></div>\n      </div>\n      <div class=\"panel-content\" [ngClass]=\"{hide: !searchIsActive}\">\n        <div class=\"title\">\n          {{'TR_SEARCH_CRITERIA' | translate}}\n        </div>\n\t\t    <div class=\"form-group\">\n          <ng-container *ngFor=\"let searchField of searchCriteria?.searchFields\">\n            <div style=\"display:inline-block\"><img *ngIf=\"searchField.searchHelp != ''\" src=\"../../../images/help.svg\" class=\"help-icon\" title=\"{{ searchField.searchHelp | translate }}\" /></div>\n            <div style=\"display:inline-block;vertical-align: bottom;\" class=\"col-form-label\" title=\"{{ searchField.label | translate}}\">{{ searchField.label | translate}}</div>\n            <div *ngIf=\"(searchField.controlType === controlTypeEnum.Text)\"><input type=\"text\" class=\"form-control input-sm\" [(ngModel)]=\"searchField.value\"></div>\n            <div *ngIf=\"(searchField.controlType === controlTypeEnum.Combobox)\">\n\t\t\t        <select class=\"form-control input-sm\" [(ngModel)]=\"searchField.value\">\n\t\t\t\t          <option *ngFor=\"let item of searchField.controlSource\" [ngValue]=\"item.value\">{{item.text}}</option>\n\t\t\t        </select>\n            </div>\n          </ng-container>   \n        </div>\n        <div>\n          <button class=\"btn btn-default btn-sm\" style=\"float: right; margin: 10px;\" (click)=\"onSearchClick(searchTypeEnum.DeepSearch)\" title=\"{{ 'TR_DEEP_GRID_SEARCH' | translate }}\"><img src=\"../../images/magnifier.svg\" class=\"image-button\" />&nbsp;{{ 'TR_DEEP_SEARCH' | translate }}</button>\n          <button *ngIf=\"!isRecursive\" class=\"btn btn-default btn-sm\" style=\"float: right;margin: 10px;\" (click)=\"onSearchClick(searchTypeEnum.Search)\" title=\"{{ 'TR_GRID_SEARCH' | translate }}\"><img src=\"../../images/magnifier.svg\" class=\"image-button\" />&nbsp;{{ 'TR_SEARCH' | translate }}</button>\n          <button class=\"btn btn-default btn-sm\" style=\"float: right; margin: 10px;\" (click)=\"onSearchClick(searchTypeEnum.Clear)\" title=\"{{ 'TR_CLEAR_SEARCH' | translate }}\"><img src=\"../../images/delete.svg\" class=\"image-button\" />&nbsp;{{ 'TR_CLEAR_SEARCH' | translate }}</button>\n        </div>\n      </div>"
                    })
                ], DashboardConfigTagsGridSearchComponent);
                return DashboardConfigTagsGridSearchComponent;
            }());
            exports_1("DashboardConfigTagsGridSearchComponent", DashboardConfigTagsGridSearchComponent);
            SearchCriteria = (function () {
                function SearchCriteria(searchFields, searchType) {
                    this.searchFields = searchFields;
                    this.searchType = searchType;
                }
                return SearchCriteria;
            }());
            exports_1("SearchCriteria", SearchCriteria);
            SearchField = (function () {
                function SearchField(id, label, value, controlType, controlSource, searchHelp) {
                    if (searchHelp === void 0) { searchHelp = ""; }
                    this.id = id;
                    this.label = label;
                    this.value = value;
                    this.controlType = controlType;
                    this.controlSource = controlSource;
                    this.searchHelp = searchHelp;
                }
                return SearchField;
            }());
            exports_1("SearchField", SearchField);
            (function (SearchTypeEnum) {
                SearchTypeEnum[SearchTypeEnum["Clear"] = 0] = "Clear";
                SearchTypeEnum[SearchTypeEnum["Search"] = 1] = "Search";
                SearchTypeEnum[SearchTypeEnum["DeepSearch"] = 2] = "DeepSearch";
            })(SearchTypeEnum || (SearchTypeEnum = {}));
            exports_1("SearchTypeEnum", SearchTypeEnum);
        }
    };
});
//# sourceMappingURL=dashboard.config.tags.grid.search.component.js.map