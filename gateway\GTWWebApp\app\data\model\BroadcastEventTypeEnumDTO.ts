/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum BroadcastEventTypeEnumDTO {
  RefreshUI = <any>'refresh_ui',
  RefreshDirtyFlag = <any>'refresh_dirty_flag',
  RefreshLogParameter = <any>'refresh_log_parameter',
  ForceLogOffUser = <any>'force_log_off_user',
  MessageInfo = <any>'message_info',
  MessageWarning = <any>'message_warning',
  MessageError = <any>'message_error',
  MessageDebug = <any>'message_debug',
  MessageSuccess = <any>'message_success'
}
