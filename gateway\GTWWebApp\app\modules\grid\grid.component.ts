import { Component, OnInit, Input, Output, EventEmitter, <PERSON>Chang<PERSON>, HostListener, HostBinding } from "@angular/core";
import { Column } from "./column";

@Component({
  selector: "gridComponent",
  styles: [`
      .table>tbody>tr>td,
      .table>thead>tr>th{
		    padding: 4px !important;
        vertical-align: middle !important;
        line-height: 22px;
      }
      .table thead th {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #ddd;
       }
      .table>tbody>tr:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .table{
        border: 1px solid lightgray;
        word-break: break-all
      }
      .table-striped>tbody>tr:nth-of-type(odd) {
        background-color: transparent !important;
      }
      .table-striped>tbody>tr:nth-of-type(odd):hover {
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
      }
      .table-striped>tbody>tr:nth-child(odd)>td,
      .table-striped>tbody>tr:nth-child(odd)>th {
        background-color: rgba(0, 0, 0, 0.03) !important;
      }
		 .image{
        width: 20px;
        height: 20px;
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
      }
		 .shadow{
        filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
      }
      .pointer{
        cursor: pointer;
      }
      .input-xs {
        height: 20px;
        padding: 2px 5px;
        font-size: 10px;
        line-height: 1.5; /* If Placeholder of the input is moved up, rem/modify this. */
        border-radius: 3px;
      }
      .col-header{
        white-space: nowrap; 
      }
    `],
  template: `<table *ngIf="rows?.length > 0" class="table table-striped">
              <thead>
			          <tr>
                  <th resizable *ngFor="let col of columns;">
                    <ng-container *ngIf="!col.action.includes('action$edit') &&
                                         !col.action.includes('action$delete') &&
                                         !col.action.includes('action$edit') &&
                                         !col.action.includes('action$image-click') &&
                                         !col.action.includes('action$checkbox')">
                      <a class="pointer" (click)="sort(col.field)"><b><span [innerHTML]="col.header | translate" class="col-header"></span></b></a><br>
                      <input type="text" class="form-control input-xs" placeholder="{{'TR_ENTER_FILTER' | translate}}" [(ngModel)]="columnFilters[col.field]" (ngModelChange)="onFilterChange()"/>
                    </ng-container>
                    <ng-container *ngIf="col.action.includes('action$checkbox')">
                      <input type="checkbox" (change)="clickCheckboxHeader(col, $event)" [(ngModel)]="col.checked" /><br>
                    </ng-container>
                  </th>
			          </tr>
              </thead>
              <tbody>
			          <tr *ngFor="let row of rows | gridContain:columnFilters; let i = index" [ngClass]="{'is-selected': (i === this.selectedIndex)}">
				          <td *ngFor="let col of columns">
				            <ng-container *ngIf="!col.action.includes('action$')"><div title="{{row[col.tooltip]}}" class="pointer inline" (click)="clickAction('', row, i)">{{row[col.field]}}</div></ng-container>
                    <ng-container *ngIf="col.action.includes('action$innerHTML')"><div class="inline" [innerHTML]="row[col.field]"></div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$item-click')"><a class="pointer" (click)="clickAction(col.action, row, i)" title="{{row[col.tooltip]}}" [myDraggable]="{data: row}">{{row[col.field]}}</a></ng-container>
				            <ng-container *ngIf="col.action.includes('action$custom')"><button class="btn btn-default" (click)="clickAction(col.action, row, i)" title="{{col.tooltip}}">{{col.header}}</button></ng-container>
				            <ng-container *ngIf="col.action.includes('action$edit')"><div class="round-button" title="{{col.tooltip | translate}}" (click)="clickAction(col.action, row, i)"><img src="../../images/edit.svg" class="image-button"/></div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$delete')"><div class="round-button" title="{{col.tooltip | translate}}" (click)="clickAction(col.action, row, i)"><img src="../../images/delete.svg" class="image-button"/></div></ng-container>
				            <ng-container *ngIf="col.action.includes('action$image-click')"><div class="round-button" title="{{col.tooltip | translate}}" (click)="clickAction(col.action, row, i, col)"><img [src]="'../../images/' + col.image + '.svg'" class="image-button"/></div></ng-container>
                    <ng-container *ngIf="col.action.includes('action$draggable')"><a class="pointer" (click)="clickAction(col.action, row, i)" title="{{row[col.tooltip]}}" [myDraggable]="{data: row.draggableField}">{{row[col.field]}}</a></ng-container>
                    <ng-container *ngIf="col.action.includes('action$checkbox')"><div><input type="checkbox" (change)="clickActionEvent(col.action, col, row, i, $event)" [(ngModel)]="row[col.field]" /></div></ng-container>
				          </td>
			          </tr>
              </tbody>
		        </table>
		        <div *ngIf="!rows || !rows?.length" class="grid-no-results">{{'TR_NO_RESULTS' | translate}}</div>`
})

export class GridComponent implements OnInit {
  @Input() rows: Array<any>;
  @Input() columns: Array<Column>;
  @Input() columnFilters: Array<any> = [];
  @Input() selectedIndex: number;
  @Output() onClickActionGrid: EventEmitter<object> = new EventEmitter();

  @HostBinding('attr.tabIndex') tabIndex = -1;
  @HostListener('window:keydown', ['$event']) keypressed(event: KeyboardEvent) {this.activeKey = event.key;}
  @HostListener('window:keyup') keyup() { this.activeKey = null; }

  private activeKey: string;
  private selectedPreviousIndex: number;
  private sorter = new Sorter();

  public ngOnInit(): void {
    if (this.columns) {
      this.columns.forEach((col) => {
        this.columnFilters[col.field] = "";
      });
    }
  }

  public ngOnChanges(changes: SimpleChanges) {
    if (this.columns && this.columns["TR_ITEM_NAME"]) {
      var nameColumn = this.columns.filter(x => x.header == "TR_ITEM_NAME")[0];
      if (nameColumn && this.rows && this.rows.length > 0) {
        this.sorter.direction = -1;
        this.sort(nameColumn.field);
      } 
    }
    this.checkCheckboxHeader();
  }

  private onFilterChange(): void {
    this.selectedIndex = null;
  }

  private clickAction(action: any, item: any, index: number, column: Column = null): void {
    this.selectedIndex = index;
    this.onClickActionGrid.emit({ action, item, index, column });
  }

  private checkCheckboxHeader() {
    //Check Header if all check are checked
    if (this.columns != null) {
      this.columns.forEach((column) => {
        let checkedItemsNumber: number = 0;
        let checkboxNumber: number = 0;
        if (column.action.includes('action$checkbox')) {
          column.checked = false;
          this.rows.forEach((row) => {
            checkboxNumber++
            if (row.checkbox == true)
              checkedItemsNumber++;
          });
          if (checkedItemsNumber == checkboxNumber) {
            column.checked = true;
          }
        }
      });
    }
  }

  private clickActionEvent(action: any, col: Column, row: any, index: number, event: any): void {
    row.checkbox = event.target.checked;

    if (this.selectedPreviousIndex > index && this.activeKey == "Shift") {
      for (var i = this.selectedPreviousIndex; i >= index; i--) {
        this.rows[i].checkbox = true;
      }
      this.selectedPreviousIndex = - 1
    }
    else if (this.selectedPreviousIndex < index && this.activeKey == "Shift") {
      for (var i = this.selectedPreviousIndex; i <= index; i++) {
        this.rows[i].checkbox = true;
      }
      this.selectedPreviousIndex = - 1
    }
    else {
      this.selectedPreviousIndex = index;
    }

    let checkedItem: Array<string> = [];
    this.rows.forEach(rowItem => {
      if (rowItem.checkbox == true) {
        checkedItem.push(rowItem);
      };
    });

    this.checkCheckboxHeader();
    this.onClickActionGrid.emit({ action, checkedItem, index, event});
  }

  private clickCheckboxHeader(col: any, event: any): void {
    let checkedItem: Array<string> = [];
    this.rows.filter(rowFiltered => {
      let propertyNames = Object.getOwnPropertyNames(rowFiltered);
      let isRowValid: boolean = true;
      propertyNames.forEach((propertyName) => {
        if (this.columnFilters[propertyName] && rowFiltered[propertyName].toString().toLowerCase().indexOf(this.columnFilters[propertyName].toLowerCase()) == -1)
          isRowValid = false;
      });
      if (isRowValid) {
        rowFiltered.checkbox = event.target.checked;
        if (rowFiltered.checkbox == true) {
          checkedItem.push(rowFiltered);
        };
      }
    });

    this.onClickActionGrid.emit({checkedItem, event });
  }

  private sort(key): any {
    this.sorter.sort(key, this.rows);
    this.selectedIndex = null;
  }
}

class Sorter {
  public direction: number;
  private key: string;

  constructor() {
    this.direction = 1;
  }

  public sort(key: string, data: any[]): any {
    if (this.key === key)
      this.direction = -this.direction;
    else
      this.direction = 1;

    this.key = key;

    data.sort((a, b) => {
      if (a[key] === b[key])
        return 0;
      else if (a[key] > b[key])
        return this.direction;
      else
        return -this.direction;
    });
  }
}