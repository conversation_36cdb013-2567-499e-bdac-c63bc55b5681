/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import {
  HttpClient, HttpHeaders, HttpParams,
  HttpResponse, HttpEvent
} from '@angular/common/http';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { Observable } from 'rxjs';

import { AuditLogEntryDTO } from '../model/AuditLogEntryDTO';

import { BASE_PATH, COLLECTION_FORMATS } from '../variables';
import { Configuration } from '../configuration';


@Injectable()
export class AuditService {

  protected basePath = 'http://localhost/rest';
  public defaultHeaders = new HttpHeaders();
  public configuration = new Configuration();

  constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
    if (basePath) {
      this.basePath = basePath;
    }
    if (configuration) {
      this.configuration = configuration;
      this.basePath = basePath || configuration.basePath || this.basePath;
    }
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    for (const consume of consumes) {
      if (form === consume) {
        return true;
      }
    }
    return false;
  }


  /**
   * Get all log entries.
   * filters are all ored together, time is a C language time_t representing the number of seconds elapsed since 00:00 hours, Jan 1, 1970 UTC
   * @param userFilter name filter for username field in AuditLogEntryDTO, blank to disable
   * @param startDateFilter start date filter for log_time field in AuditLogEntryDTO, 0 to disable, must also spec endDate
   * @param endDateFilter end date filter for log_time field in AuditLogEntryDTO, 0 to disable, must also spec startDate
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getLogEntries(userFilter?: string, startDateFilter?: number, endDateFilter?: number, observe?: 'body', reportProgress?: boolean): Observable<Array<AuditLogEntryDTO>>;
  public getLogEntries(userFilter?: string, startDateFilter?: number, endDateFilter?: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<AuditLogEntryDTO>>>;
  public getLogEntries(userFilter?: string, startDateFilter?: number, endDateFilter?: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<AuditLogEntryDTO>>>;
  public getLogEntries(userFilter?: string, startDateFilter?: number, endDateFilter?: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {




    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    if (userFilter !== undefined && userFilter !== null) {
      queryParameters = queryParameters.set('userFilter', <any>userFilter);
    }
    if (startDateFilter !== undefined && startDateFilter !== null) {
      queryParameters = queryParameters.set('startDateFilter', <any>startDateFilter);
    }
    if (endDateFilter !== undefined && endDateFilter !== null) {
      queryParameters = queryParameters.set('endDateFilter', <any>endDateFilter);
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/json'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
    ];

    return this.httpClient.get<Array<AuditLogEntryDTO>>(`${this.basePath}/auditlogentries`,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

}
