System.register(["@angular/core", "ngx-modialog-7/plugins/bootstrap", "ngx-modialog-7", "./dashboard.config.tag.options.modal", "../../data/model/models", "./dashboard.config.tag.editor.logic", "../../data/api/api", "../../authentication/authentication.service", "../../modules/alert/alert.service", "../../global/keys.pipe", "@ngx-translate/core", "../../modules/grid/column", "../../modules/treeview/node", "../../modules/treeview-select/node-select", "../../modules/download/download.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, bootstrap_1, ngx_modialog_7_1, dashboard_config_tag_options_modal_1, models_1, dashboard_config_tag_editor_logic_1, api_1, authentication_service_1, alert_service_1, keys_pipe_1, core_2, column_1, node_1, node_select_1, dashboard_config_tag_options_modal_2, download_component_1, DashboardConfigTagEditorComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (bootstrap_1_1) {
                bootstrap_1 = bootstrap_1_1;
            },
            function (ngx_modialog_7_1_1) {
                ngx_modialog_7_1 = ngx_modialog_7_1_1;
            },
            function (dashboard_config_tag_options_modal_1_1) {
                dashboard_config_tag_options_modal_1 = dashboard_config_tag_options_modal_1_1;
                dashboard_config_tag_options_modal_2 = dashboard_config_tag_options_modal_1_1;
            },
            function (models_1_1) {
                models_1 = models_1_1;
            },
            function (dashboard_config_tag_editor_logic_1_1) {
                dashboard_config_tag_editor_logic_1 = dashboard_config_tag_editor_logic_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (keys_pipe_1_1) {
                keys_pipe_1 = keys_pipe_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (column_1_1) {
                column_1 = column_1_1;
            },
            function (node_1_1) {
                node_1 = node_1_1;
            },
            function (node_select_1_1) {
                node_select_1 = node_select_1_1;
            },
            function (download_component_1_1) {
                download_component_1 = download_component_1_1;
            }
        ],
        execute: function () {
            DashboardConfigTagEditorComponent = (function () {
                function DashboardConfigTagEditorComponent(alertService, authenticationService, fileService, modal, keysPipe, DashboardRuntimeTagEditorLogic, translate, renderer) {
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.fileService = fileService;
                    this.modal = modal;
                    this.keysPipe = keysPipe;
                    this.DashboardRuntimeTagEditorLogic = DashboardRuntimeTagEditorLogic;
                    this.translate = translate;
                    this.renderer = renderer;
                    this.submitted = false;
                    this.controlTypeEnum = models_1.EditorFieldObjectDTO.ControlTypeEnum;
                }
                DashboardConfigTagEditorComponent.prototype.ngOnInit = function () {
                    var _this = this;
                    if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Combobox) {
                        this.listComponentData(this.editorField.controlSource);
                        this.fileService.configuration = this.authenticationService.userApiConfig;
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.FileManagement || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.FileManagementCombobox) {
                        this.listComponentDataFile(this.editorField.controlSource);
                        this.fileService.configuration = this.authenticationService.userApiConfig;
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Number) {
                        if (this.editorField.range != "") {
                            var range = JSON.parse(this.editorField.range);
                            this.rangeMin = range.min;
                            this.rangeMax = range.max;
                        }
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Grid) {
                        this.setGridData(this.editorField.controlSource);
                        if (this.tagForm.controls[this.editorField.schemaName].value == "")
                            setTimeout(function () {
                                _this.selectGridDataMember(0);
                            }, 500);
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.GridNoDefault) {
                        this.setGridData(this.editorField.controlSource);
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Treeview || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewMutliChoice
                        || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamic || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicMutliChoice) {
                        this.setTreeviewData(this.editorField.controlSource, false);
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicSelectMutliChoice) {
                        this.setTreeviewData(this.editorField.controlSource, true);
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.OptionsEditor) {
                        this.tagForm.controls[this.editorField.schemaName].value = dashboard_config_tag_options_modal_2.TagOption.formatTagOptionValue(this.editorField.controlSource, this.editorField.value);
                    }
                    this.onInitChange();
                    this.tagForm.controls[this.editorField.schemaName].component = this;
                };
                DashboardConfigTagEditorComponent.prototype.ngAfterViewInit = function () {
                    if (this.inputElement != null) {
                        this.renderer.listen(this.inputElement.nativeElement, 'paste', function (event) {
                        });
                    }
                };
                Object.defineProperty(DashboardConfigTagEditorComponent.prototype, "inputNativeElement", {
                    get: function () {
                        return this.el.nativeElement;
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(DashboardConfigTagEditorComponent.prototype, "componentData", {
                    get: function () {
                        return this._componentData;
                    },
                    set: function (componentData) {
                        this._componentData = componentData;
                    },
                    enumerable: false,
                    configurable: true
                });
                DashboardConfigTagEditorComponent.prototype.selectLastGridDataMember = function () {
                    try {
                        if (this.componentData !== "") {
                            this.selectGridDataMember(this.componentData.length - 1);
                        }
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.selectGridDataMember = function (selectedIndex) {
                    try {
                        if (this.componentData !== "" && this.componentData != null) {
                            var gridSelectedValue = {};
                            gridSelectedValue.item = this.componentData[selectedIndex];
                            gridSelectedValue.index = selectedIndex;
                            this.onChange(gridSelectedValue);
                        }
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.CheckGridDataMember = function () {
                    try {
                        if (this.componentData !== "" && this.componentData != null)
                            return true;
                        else
                            return false;
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                        return false;
                    }
                };
                DashboardConfigTagEditorComponent.prototype.clearSelected = function () {
                    try {
                        this.tagForm.controls[this.editorField.schemaName].setValue("");
                        this.selectedIndex = -1;
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.setGridData = function (controlSource) {
                    var _this = this;
                    try {
                        if (!controlSource)
                            return;
                        var jsonSource = void 0;
                        if (typeof controlSource === "string")
                            jsonSource = JSON.parse(controlSource);
                        else
                            jsonSource = controlSource;
                        if (controlSource != null) {
                            this.gridColumns = [];
                            jsonSource.columns.forEach(function (column) {
                                if (column["header"] != "HIDDEN") {
                                    if (column["field"] == "checkbox")
                                        _this.gridColumns.push(new column_1.Column(column["field"], column["header"], "action$checkbox", ""));
                                    else
                                        _this.gridColumns.push(new column_1.Column(column["field"], column["header"], "action$draggable", ""));
                                }
                            });
                            this.componentData = jsonSource.data;
                        }
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.setTreeviewData = function (controlSource, isSelect) {
                    try {
                        var jsonSource = void 0;
                        var node = void 0;
                        if (typeof controlSource === "string")
                            jsonSource = JSON.parse(controlSource);
                        else
                            jsonSource = controlSource;
                        if (controlSource != null) {
                            if (isSelect)
                                node = new node_select_1.NodeSelect(jsonSource[0].nodeName, jsonSource[0].nodeFullName, null, jsonSource[0].checked, jsonSource[0].displayCheckbox);
                            else
                                node = new node_1.Node(jsonSource[0].nodeName, jsonSource[0].nodeFullName, null, jsonSource[0].checked, jsonSource[0].displayCheckbox);
                            node.children = this.setTreeviewNode(jsonSource[0], node, isSelect);
                            if (jsonSource[0].hasChildren || node.children.length > 0)
                                node.hasChildren = true;
                            this.componentData = node;
                        }
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.listComponentData = function (controlSource) {
                    try {
                        if (controlSource != null && controlSource != "") {
                            var jsonSource = void 0;
                            if (typeof controlSource === "string")
                                jsonSource = JSON.parse(controlSource);
                            else
                                jsonSource = controlSource;
                            this.componentData = this.keysPipe.transformJson(jsonSource);
                        }
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.listComponentDataFile = function (controlSource) {
                    try {
                        var jsonSource = JSON.parse(controlSource);
                        this.componentData = {};
                        if (jsonSource.extensions && jsonSource.extensions != "")
                            this.componentData.extensions = jsonSource.extensions.replace(/ /g, ", ");
                        this.componentData.fileType = jsonSource.fileType;
                        this.componentData.files = this.keysPipe.transformJson(jsonSource.files);
                    }
                    catch (error) {
                        this.alertService.debug(error.toString());
                    }
                };
                DashboardConfigTagEditorComponent.prototype.setTreeviewNode = function (parentSource, parent, isSelect) {
                    var _this = this;
                    var children = [];
                    var child;
                    if (parentSource.children != null) {
                        parentSource.children.forEach(function (childJson) {
                            if (isSelect)
                                child = new node_select_1.NodeSelect(childJson.nodeName, childJson.nodeFullName, parent, childJson.checked, childJson.displayCheckbox);
                            else
                                child = new node_1.Node(childJson.nodeName, childJson.nodeFullName, parent, childJson.checked, childJson.displayCheckbox);
                            child.children = _this.setTreeviewNode(childJson, child, isSelect);
                            if (childJson.hasChildren || child.children.length > 0)
                                child.hasChildren = true;
                            children.push(child);
                        });
                    }
                    return children;
                };
                DashboardConfigTagEditorComponent.prototype.openOptionsEditor = function (schemaName) {
                    var _this = this;
                    var dashboardConfigTagOptionModalRef = this.modal.open(dashboard_config_tag_options_modal_1.DashboardConfigTagOptionsModal, ngx_modialog_7_1.overlayConfigFactory({ controlSource: this.editorField.controlSource, tagOptionsValue: this.tagForm.controls[schemaName].value }, bootstrap_1.BSModalContext));
                    dashboardConfigTagOptionModalRef.result.then(function (data) {
                        if (data != null) {
                            var optionText_1 = "";
                            data.forEach(function (option) {
                                if (option[1] != "")
                                    optionText_1 += option[0] + " " + option[1] + "\n";
                                else
                                    optionText_1 += option[0] + "\n";
                            });
                            if (optionText_1.length > 1)
                                optionText_1 = optionText_1.substring(0, optionText_1.length - 1);
                            _this.tagForm.controls[schemaName].setValue(optionText_1);
                        }
                    }, function (error) { _this.alertService.debug(error.toString()); });
                };
                DashboardConfigTagEditorComponent.prototype.clearOptionsEditor = function (schemaName) {
                    var result = true;
                    this.translate.get("TR_DELETE_ALL_THE_OPTIONS").subscribe(function (res) {
                        result = confirm(res);
                    });
                    if (!result)
                        return;
                    this.tagForm.controls[schemaName].setValue("");
                };
                DashboardConfigTagEditorComponent.prototype.onInitChange = function () {
                    this.customError = this.DashboardRuntimeTagEditorLogic.manageForm(true, this.modal, this.tagForm, this.editorField, this.editorType, this.objectCollectionKind, this.collapsiblePanels, this.editorsService, this.editorCommand, this.authenticationService, this.alertService, this.translate);
                };
                DashboardConfigTagEditorComponent.prototype.onChange = function (selectedObject) {
                    if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Grid || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.GridNoDefault) {
                        this.tagForm.controls[this.editorField.schemaName].setValue(selectedObject);
                        if (selectedObject != null)
                            this.selectedIndex = selectedObject.index;
                        else
                            this.selectedIndex = null;
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.Treeview || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamic) {
                        this.tagForm.controls[this.editorField.schemaName].setValue(selectedObject);
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewMutliChoice) {
                        this.tagForm.controls[this.editorField.schemaName].setValue(this.componentData);
                    }
                    else if (this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicMutliChoice || this.editorField.controlType == models_1.EditorFieldObjectDTO.ControlTypeEnum.TreeviewDynamicSelectMutliChoice) {
                        this.tagForm.controls[this.editorField.schemaName].setValue(selectedObject);
                    }
                    this.customError = this.DashboardRuntimeTagEditorLogic.manageForm(false, this.modal, this.tagForm, this.editorField, this.editorType, this.objectCollectionKind, this.collapsiblePanels, this.editorsService, this.editorCommand, this.authenticationService, this.alertService, this.translate);
                };
                DashboardConfigTagEditorComponent.prototype.omitSpecialChar = function (event, editorField) {
                    if (editorField.schemaName !== "objectName" && editorField.controlType !== this.controlTypeEnum.TextId)
                        return true;
                    var input = String.fromCharCode(event.keyCode);
                    if (!((event.key == "-") || (event.key == "_") || (event.key == "$") || (/[a-zA-Z0-9]/.test(input)))) {
                        event.preventDefault();
                        return false;
                    }
                };
                DashboardConfigTagEditorComponent.prototype.onlyNumericChar = function (event, editorField) {
                    if (editorField.controlType !== this.controlTypeEnum.Number)
                        return true;
                    var input = String.fromCharCode(event.keyCode);
                    if ((/[^-.0-9]/.test(input))) {
                        event.preventDefault();
                        return false;
                    }
                };
                DashboardConfigTagEditorComponent.prototype.onPaste = function (event) {
                    event.preventDefault();
                };
                DashboardConfigTagEditorComponent.prototype.onFileUploadChange = function (event, schemaName, fileType) {
                    var _this = this;
                    var fileList = event.target.files;
                    if (fileList.length > 0) {
                        var file_1 = fileList[0];
                        var result_1 = true;
                        if (this.componentData.files && this.componentData.files.some(function (item) { return item.key === file_1.name; })) {
                            this.translate.get("TR_FILE_ALREADY_EXISTS_OVERWRITE_IT").subscribe(function (res) {
                                result_1 = confirm(res);
                            });
                        }
                        if (!result_1)
                            return;
                        this.fileService.filePost(file_1, "", "").subscribe(function (data) {
                            _this.alertService.success("TR_FILE_SAVED");
                            if (_this.componentData.files && !_this.componentData.files.some(function (item) { return item.key === file_1.name; })) {
                                if (data.fileDescription && data.fileDescription != "")
                                    _this.componentData.files.splice(1, 0, { key: file_1.name, value: data.fileDescription });
                                else
                                    _this.componentData.files.splice(1, 0, { key: file_1.name, value: file_1.name });
                            }
                            _this.tagForm.controls[schemaName].setValue(file_1.name);
                            _this.onChange();
                        }, function (error) {
                            if (error.status == 401) {
                                _this.authenticationService.onLoginFailed("/");
                            }
                            else {
                                _this.alertService.error("TR_ERROR_FILE_NOT_SAVED");
                            }
                        });
                    }
                };
                DashboardConfigTagEditorComponent.prototype.onFileDownloadClick = function (schemaName, fileType) {
                    if (this.tagForm.controls[schemaName].value != "") {
                        var fileName = this.tagForm.controls[schemaName].value.replace(/^.*[\\\/]/, '');
                        if (fileName != "") {
                            this.download.downloadClick(fileName, "CurrentWorkspace");
                        }
                    }
                    else {
                        this.alertService.warning("TR_NO_FILE_TO_DOWNLOAD");
                    }
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigTagEditorComponent.prototype, "editorField", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object)
                ], DashboardConfigTagEditorComponent.prototype, "tagForm", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigTagEditorComponent.prototype, "submitted", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Array)
                ], DashboardConfigTagEditorComponent.prototype, "collapsiblePanels", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", String)
                ], DashboardConfigTagEditorComponent.prototype, "editorType", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", String)
                ], DashboardConfigTagEditorComponent.prototype, "objectCollectionKind", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", api_1.EditorsService)
                ], DashboardConfigTagEditorComponent.prototype, "editorsService", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", String)
                ], DashboardConfigTagEditorComponent.prototype, "editorCommand", void 0);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean)
                ], DashboardConfigTagEditorComponent.prototype, "showHelp", void 0);
                __decorate([
                    core_1.ViewChild("currentInput", { static: false }),
                    __metadata("design:type", core_1.ElementRef)
                ], DashboardConfigTagEditorComponent.prototype, "el", void 0);
                __decorate([
                    core_1.ViewChild("download", { static: false }),
                    __metadata("design:type", download_component_1.DownloadComponent)
                ], DashboardConfigTagEditorComponent.prototype, "download", void 0);
                DashboardConfigTagEditorComponent = __decorate([
                    core_1.Component({
                        selector: "dashboardConfigTagEditorComponent",
                        styles: ["\n    .options-button {\n      margin-left: 4px;\n    }\n    .form-group {\n      margin-bottom: 8px;\n      margin-left: 0px;\n    }\n    input[type=checkbox] {\n      transform: scale(1.2);\n    }\n    .radioCheckbox{\n      width: 13px;\n      height: 13px;\n      background-color: white;\n      border-radius: 50%;\n      vertical-align: middle;\n      border: 1px solid #ddd;\n      appearance: none;\n      -webkit-appearance: none;\n      outline: none;\n      cursor: pointer;\n    }\n    .radioCheckbox:checked {\n      background-color: #0066ff;\n      outline: 5px auto white;\n      outline-offset: -2px;\n    }\n    .radioCheckbox:focus {\n      outline: 5px auto white;\n      outline-offset: -2px;\n    }\n    input, textarea, select{\n      background-color: rgba(255, 255, 255, 0.7);\n    }\n    .warning-editable{\n      color: #ff4242;\n      z-index: 1;\n      float: right;\n      margin-right: 99%;\n      margin-bottom: -12px;\n      position: relative;\n    }\n    .disabled-field {\n      pointer-events: none;\n      opacity: 0.7;\n    }\n    .hide-element{\n      display: none;\n    }\n    .help{\n      word-break: break-word;\n      margin-bottom: 6px !important;\n      background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));\n      border: 1px solid #bbbbbb;\n      border-radius: 4px;\n      max-width: 96%;\n      white-space: pre-wrap;\n    }\n    .control-info{\n      word-break: break-word;\n      margin-bottom: 6px !important;\n      background-color: rgb(225, 225, 225);\n      border: 1px solid #c8c7c7;\n      border-radius: 4px;\n      padding: 3px;\n    }\n    .flex{\n      display: flex;\n    }\n    .form-group {\n        margin-right: -8px;\n    }\n    .asterisk {\n      float: right;\n      margin-top: -38px;\n      color: red;\n      font-weight: 900;\n    }\n    .grid-tree{\n      overflow: auto;\n      max-height: 300px;\n      padding-left: 0px;\n      margin-bottom:15px;\n      width:99%\n    }\n    textarea{\n      background: url(../../../images/arrowTopBottom.svg)no-repeat rgba(71, 108, 193, 0.52) 100% 100%;\n      background-size: 18px;\n      resize: vertical;\n      overflow: hidden;\n      background-color: #eee;\n    }\n    .actionHorizontal{\n      float:right;\n      padding-bottom:8px;\n    }\n  "],
                        providers: [keys_pipe_1.KeysPipe, api_1.FileService],
                        template: "\n\t<div [formGroup]=\"tagForm\" [ngClass]=\"{'actionHorizontal' : editorField.controlType === controlTypeEnum.Action}\">\n    <div class=\"glyphicon glyphicon-warning-sign warning-editable\" *ngIf=\"editorField.isEditableAtRuntime==='No' && editorField.controlType != controlTypeEnum.Hidden\" title=\"{{'TR_REQUIRES_RESTART' | translate}}\"></div>\n    <div *ngIf=\"(editorField.controlType === controlTypeEnum.Text || editorField.controlType === controlTypeEnum.TextAction)\" class=\"form-group row\" [ngClass]=\"{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>  \n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br>{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>      \n      <div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <ng-container *ngIf=\"editorField.controlType !== controlTypeEnum.TextAction\"><input type=\"text\" #currentInput class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\"></ng-container>\n\t\t\t  <ng-container *ngIf=\"editorField.controlType === controlTypeEnum.TextAction\"><input type=\"text\" #currentInput class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\"></ng-container>\n        <div *ngIf=\"editorField.controlType === controlTypeEnum.TextAction\" class=\"round-button\" (click)=\"onChange()\"><img src=\"../../images/validation.svg\" class=\"image-button\"/></div>\n      </div>\n\t\t</div>\n    <div *ngIf=\"(editorField.controlType === controlTypeEnum.TextId)\" class=\"form-group row\" [ngClass]=\"{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>  \n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br>{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>      \n      <div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <ng-container><input type=\"text\" #currentInput class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\" (keypress)=\"omitSpecialChar($event, editorField)\" (paste)=\"onPaste($event)\"></ng-container>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"(editorField.controlType === controlTypeEnum.TextMultiLine)\" class=\"form-group row\">\n      <label class=\"col-lg-12 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-12\">\n        <div><textarea [attr.disabled]=\"editorField.isEditable==='No' ? '' : null\" style=\"height:80px; overflow:auto;\" class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\"></textarea></div>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"(editorField.controlType === controlTypeEnum.TextPassword)\"  class=\"form-group row\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <div style=\"width: 90%; display: inline-block;\"><input type=\"password\" #currentInput class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\" show-password /></div>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Number\" class=\"form-group row\" [ngClass]=\"{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <input type=\"number\" class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\" min=\"{{rangeMin}}\" max=\"{{rangeMax}}\" (keypress)=\"onlyNumericChar($event, editorField)\"/>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"(editorField.controlType === controlTypeEnum.OptionsEditor)\"  class=\"form-group row\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <div><textarea class=\"form-control input-sm\" style=\"overflow:auto;\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" cols=\"60\" rows=\"5\"></textarea></div>\n        <div class=\"round-button\" style=\"vertical-align: top;margin-left: 2px;\" (click)=\"openOptionsEditor(editorField.schemaName)\" title=\"{{ 'TR_EDIT' | translateKey:'Edit':translate }}\"><img src=\"../../images/edit.svg\" class=\"image-button\"/></div>\n        <div class=\"round-button\" style=\"vertical-align: top;margin-left: 2px;\" (click)=\"clearOptionsEditor(editorField.schemaName)\" title=\"{{ 'TR_DELETE' | translateKey:'Delete':translate }}\"><img src=\"../../images/delete.svg\" class=\"image-button\"/></div>      \n    </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.MaskEditor\" class=\"form-group row\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <bitmaskEditorComponent [editorField]=\"editorField\" [editorFieldcontrol]=\"this.tagForm.controls[editorField.schemaName]\" (bitmaskOnChange)=\"onChange()\"></bitmaskEditorComponent>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Checkbox\" class=\"form-group row\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n      <div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n\t\t\t  <input type=\"checkbox\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\"/>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.RadioButton\" class=\"form-group row\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n      <div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n\t\t\t  <input class=\"radioCheckbox\" type=\"checkbox\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\"/>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Combobox\" class=\"form-group row\" [ngClass]=\"{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n\t\t\t  <select class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\" [ngClass]=\"{'placeholder':this.tagForm.controls[editorField.schemaName].value==''}\">\n            <option [selected]=\"this.tagForm.controls[editorField.schemaName].value==''\" disabled>{{'TR_SELECT' | translate}}</option>\n\t\t\t\t    <option *ngFor=\"let item of componentData\" [ngValue]=\"item.key\">{{item.value}}</option>\n\t\t\t  </select>\n\t\t\t</div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.FileManagement\" class=\"form-group row\" [ngClass]=\"{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <div style=\"width: 82%; display: inline-block;\">\n\t\t\t    <select class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\" [ngClass]=\"{'placeholder':this.tagForm.controls[editorField.schemaName].value==''}\">\n            <option [selected]=\"this.tagForm.controls[editorField.schemaName].value==''\" disabled>{{'TR_SELECT' | translate}}</option>\n            <option value=\"\"></option>\n\t\t\t\t    <option *ngFor=\"let item of componentData.files\" [ngValue]=\"item.key\">{{item.value}}</option>\n\t\t\t    </select>\n\t      </div>\n        <div style=\"width: 18%; display: inline-block;\">\n          <label class=\"round-button\" style=\"margin-left:2px;\" title=\"{{'TR_UPLOAD_NEW_FILE' | translate}}\"><input style=\"display: none;\" type=\"file\" accept=\"{{componentData.extensions}}\" (change)=\"onFileUploadChange($event, editorField.schemaName, componentData.fileType)\"><img src=\"../../images/upload.svg\" class=\"image-button\"/></label>\n          <div class=\"round-button\" style=\"margin-left:2px;\" title=\"{{'TR_DOWNLOAD_FILE' | translate}}\" (click)=\"onFileDownloadClick(editorField.schemaName, componentData.fileType)\"><img src=\"../../images/download.svg\" class=\"image-button\"/></div>\n        </div>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.FileManagementCombobox\" class=\"form-group row\" [ngClass]=\"{'has-error': (!(this.tagForm.controls[editorField.schemaName].errors ==null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)))}\">\n      <label class=\"col-lg-6 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n\t\t\t<div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n\t\t\t  <select class=\"form-control input-sm\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\" (change)=\"onChange()\" [ngClass]=\"{'placeholder':this.tagForm.controls[editorField.schemaName].value==''}\">\n          <option [selected]=\"this.tagForm.controls[editorField.schemaName].value==''\" disabled>{{'TR_SELECT' | translate}}</option>\n          <option value=\"\"></option>\n\t\t\t\t  <option *ngFor=\"let item of componentData.files\" [ngValue]=\"item.key\">{{item.value}}</option>\n\t\t\t  </select>\n\t\t\t</div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Grid || editorField.controlType === controlTypeEnum.GridNoDefault\" class=\"form-group\">\n      <label class=\"col-lg-12 col-form-label\" [ngClass]=\"{'hide-element':showHelp}\" [attr.for]=\"editorField.name\" title=\"{{editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 help\" [ngClass]=\"{'hide-element':!showHelp}\"><label class=\"col-form-label\" [attr.for]=\"editorField.name\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label><br />{{editorField.help | translateKey:editorField.defaultHelp:translate }}</div>\n      <div class=\"col-lg-12 flex\" style=\"background-image: linear-gradient(to bottom, rgba(220, 220, 220, 0.85), rgba(234, 231, 247, 0.25));\" class=\"grid-tree\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <gridComponent style=\"width: 100%;\" [rows]=\"componentData\" [columns]=\"gridColumns\" [selectedIndex]=\"selectedIndex\" (onClickActionGrid)=\"onChange($event)\"></gridComponent>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Treeview\" class=\"form-group\">\n\t\t\t<label class=\"col-lg-12 col-form-label\" style=\"margin-left:-15px\"[attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 flex\" style=\"background-color:white;\" class=\"grid-tree\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <treeviewComponent style=\"width: 100%;\" [isCheckbox]=false [node]=\"componentData\" (selectedNodeFullNameChange)=\"onChange($event)\"></treeviewComponent>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.TreeviewMutliChoice\" class=\"form-group\">\n\t\t\t<label class=\"col-lg-12 col-form-label\" style=\"margin-left:-15px\"[attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 flex\" style=\"background-color:white;\" class=\"grid-tree\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <treeviewComponent style=\"width: 100%;\" [isCheckbox]=true [node]=\"componentData\" (change)=\"onChange()\"></treeviewComponent>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.TreeviewDynamic\" class=\"form-group\">\n\t\t\t<label class=\"col-lg-12 col-form-label\" style=\"margin-left:-15px\"[attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 flex\" style=\"background-color:white;\" class=\"grid-tree\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <dynamicTreeviewComponent style=\"width: 100%;\" [isCheckbox]=false [node]=\"componentData\" (selectedNodeFullNameChange)=\"onChange($event)\" (selectedNodeLoadChildren)=\"onChange($event)\"></dynamicTreeviewComponent>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.TreeviewDynamicMutliChoice\" class=\"form-group\">\n\t\t\t<label class=\"col-lg-12 col-form-label\" style=\"margin-left:-15px\"[attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 flex\" style=\"background-color:white;\" class=\"grid-tree\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <dynamicTreeviewComponent style=\"width: 100%;\" [isCheckbox]=true [node]=\"componentData\" (change)=\"onChange()\" (selectedNodeLoadChildren)=\"onChange($event)\"></dynamicTreeviewComponent>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.TreeviewDynamicSelectMutliChoice\" class=\"form-group\">\n\t\t\t<label class=\"col-lg-12 col-form-label\" style=\"margin-left:-15px\"[attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-12 flex\" style=\"background-color:white;\" class=\"grid-tree\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n        <dynamicTreeviewSelectComponent style=\"width: 100%;\" [node]=\"componentData\" (change)=\"onChange()\" (selectedNodeLoadChildren)=\"onChange($event)\"></dynamicTreeviewSelectComponent>\n\t\t  </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Label\" class=\"form-group row\">\n\t\t\t<label class=\"col-lg-12 flex col-form-label\" [attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Info\" class=\"form-group row\">\n\t\t\t<label class=\"col-lg-6 col-form-label\" [attr.for]=\"editorField.name\" title=\"{{ editorField.help | translateKey:editorField.defaultHelp:translate }}\">{{ editorField.name | translateKey:editorField.defaultName:translate }}</label>\n      <div class=\"col-lg-6 flex\" [ngClass]=\"{'disabled-field':editorField.isEditable==='No'}\">\n\t\t\t  <div class=\"control-info\" [innerHTML]=\"this.tagForm.controls[editorField.schemaName].value\"></div>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.Action\" class=\"form-group row\">\n      <div class=\"col-lg-12\">\n        <button type=\"button\" class=\"btn btn-default btn-sm\" style=\"float: right;\" (click)=\"onChange(); $event.preventDefault();\"><img src=\"../../images/check.svg\" class=\"image-button\"/>&nbsp;{{ editorField.name | translateKey:editorField.defaultName:translate }}</button>\n      </div>\n\t\t</div>\n\t\t<div *ngIf=\"editorField.controlType === controlTypeEnum.AdvanceParameters\" class=\"form-group row\">\n      <div class=\"col-lg-12 flex\">\n        <dashboardConfigTagEditorAdvanceComponent style=\"width: 100%;\" [editorField]=\"editorField\" [editorFieldcontrol]=\"this.tagForm.controls[editorField.schemaName]\" (onChange)=\"onChange()\"></dashboardConfigTagEditorAdvanceComponent>\n      </div>\n\t\t</div>\n    <div *ngIf=\"editorField.isRequired\" class=\"asterisk\">*</div>\n\t\t<ng-container *ngIf=\"editorField.controlType === controlTypeEnum.Hidden\">\n      <input type=\"hidden\" [id]=\"editorField.schemaName\" [formControlName]=\"editorField.schemaName\"/>\n\t\t</ng-container>\n\t  <div [hidden]=\"this.tagForm.controls[editorField.schemaName].errors == null || (this.tagForm.controls[editorField.schemaName].pristine && !submitted)\" style=\"text-align:right; margin-top:-8px;\">\n      <small class=\"text-danger\" *ngIf=\"this.tagForm.controls[editorField.schemaName].hasError('required')\">\n\t      {{editorField.name | translateKey:editorField.defaultName:translate}}{{ 'TR_IS_REQUIRED' | translate }}\n      </small>\n      <small class=\"text-danger\" *ngIf=\"this.tagForm.controls[editorField.schemaName].hasError('incorrect')\">\n\t      {{editorField.name | translateKey:editorField.defaultName:translate}}{{ 'TR_IS_INCORRECT' | translate }}: {{customError | translate}}\n      </small>\n\t  </div>\n\t</div>\n  <downloadComponent #download></downloadComponent>"
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, authentication_service_1.AuthenticationService, api_1.FileService, bootstrap_1.Modal, keys_pipe_1.KeysPipe,
                        dashboard_config_tag_editor_logic_1.DashboardConfigTagEditorLogic, core_2.TranslateService, core_1.Renderer2])
                ], DashboardConfigTagEditorComponent);
                return DashboardConfigTagEditorComponent;
            }());
            exports_1("DashboardConfigTagEditorComponent", DashboardConfigTagEditorComponent);
        }
    };
});
//# sourceMappingURL=dashboard.config.tag.editor.component.js.map