﻿import { Component, OnInit } from "@angular/core";
import { DialogRef, ModalComponent, CloseGuard } from "ngx-modialog-7";
import { BSModalContext } from "ngx-modialog-7/plugins/bootstrap";
import { Bitmask } from "../../modules/bitmask/bitmask.component";
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "dashboardConfigTagOptionsModal",
  styles: [`
    .table{
      margin-bottom: 0px;
      width: 90%;
      max-width: 90%;
      margin-left: 10px;
    }
    .table>tr>td,
    .table>tr>th{
		  padding: 2px !important;
      vertical-align: middle !important;
      line-height: 12px;
    }
    .tag-options-modal-container {
      margin-top: 240px;
    }
    .input-text-xs {
      height: 20px;
      font-size: 11px;
      line-height: 14px;
    }
    .input-number-xs{
      height: 20px;
      width: 100px;
      font-size: 11px;
      line-height: 14px;
    }
    .select-xs{
      height: 26px;
      font-size: 11px;
      line-height: 14px;
    }
    .inline {
      display: inline-block;
    }
    fieldset {
      border-radius: 6px;
      border: 1px LightGray solid;
      padding: 0px 6px 0px 6px;
      margin-bottom: 10px;
    }
    legend{
      width: inherit;
      font-size: 11px !important;
      padding: 0 10px;
      border-bottom: none;
      margin-bottom: 4px;
      font-weight: bold;
      font-size: 15px;
    }
    .tag-modal-heading {
      background-color: #d8d8d8;
      padding: 8px 10px 6px 10px;
      font-size: 22px;
      font-weight: bold;
      border-bottom: 1px solid #a31c3f;
      overflow-wrap: break-word;
			cursor: move;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
  `],
  template: `
	<div class="container-fluid tag-options-modal-container" *ngIf="isDataLoaded">
    <div class="tag-modal-heading">
       {{'TR_OPTIONS_EDITOR' | translate}}
	    </div> 
      <table class="table">
          <tr *ngFor="let tagOption of tagOptionList">
            <td><label title="{{tagOption.helpTR | translateKey:tagOption.helpText:translate}}">{{tagOption.name}}</label></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_MASK_VALUE')"><div style="font-size: 11px;"><bitmaskComponent [bitmaskString]="tagOption.value" (bitmaskOnChange)="optionMaskOnChange($event, tagOption)" (bitmaskOnInit)="optionMaskOnInit($event, tagOption.choices)"></bitmaskComponent></div></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_BOOL_VALUE')"><input type="checkbox" class="form-check" [(ngModel)]="tagOption.value"/></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_MDO')"><input type="text" class="form-control input-text-xs" [(ngModel)]="tagOption.value"/></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_CHOICE_VALUE')">
			        <select class="form-control select-xs" [(ngModel)]="tagOption.value">
				          <option *ngFor="let choice of tagOption.choices" [ngValue]="choice.value.toString()">{{choice.name}}</option>
			        </select>
            </td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_BOOL_CHOICE_VALUE')"><input [name]="tagOption.choices" ng-control="options" type="radio" [value]="tagOption.name" [checked]="tagOption.value == tagOption.valueToUse || !tagOption.valueToUse" (click)="changeOptionValue(tagOption.name, tagOption.choices)"></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_SCALE_VALUE')">
              <fieldset>
                <legend>{{'TR_RAW_UNITS_RANGE' | translate}}</legend>
                <div><div class="inline">{{'TR_MIN' | translate}}</div>&nbsp;&nbsp;<div class="inline"><input type="number" class="form-control input-number-xs" (change)="optionScaleOnChange($event, tagOption, 0)" min="0" [(ngModel)]="tagOption.value[0]" (keypress)="onlyNumericChar($event, editorField)"/></div></div>
                <div><div class="inline">{{'TR_MAX' | translate}}</div>&nbsp;&nbsp;<div class="inline"><input type="number" class="form-control input-number-xs" (change)="optionScaleOnChange($event, tagOption, 1)" min="0" [(ngModel)]="tagOption.value[1]" (keypress)="onlyNumericChar($event, editorField)"/></div></div>
              </fieldset>
              <fieldset>
                <legend>{{'TR_ENGINEERING_UNITS_RANGE' | translate}}</legend>
                <div><div class="inline">{{'TR_MIN' | translate}}</div>&nbsp;&nbsp;<div class="inline"><input type="number" class="form-control input-number-xs" (change)="optionScaleOnChange($event, tagOption, 2)" min="0" [(ngModel)]="tagOption.value[2]" (keypress)="onlyNumericChar($event, editorField)"/></div></div>
                <div><div class="inline">{{'TR_MAX' | translate}}</div>&nbsp;&nbsp;<div class="inline"><input type="number" class="form-control input-number-xs" (change)="optionScaleOnChange($event, tagOption, 3)" min="0" [(ngModel)]="tagOption.value[3]" (keypress)="onlyNumericChar($event, editorField)"/></div></div>
              </fieldset>
            </td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_FLOAT_DECIMAL_VALUE')"><input type="number" class="form-control input-number-xs" [(ngModel)]="tagOption.value" (keypress)="onlyNumericChar($event, editorField)"/></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_LONG_DECIMAL_VALUE')"><input type="number" class="form-control input-number-xs" [(ngModel)]="tagOption.value" (keypress)="onlyNumericChar($event, editorField)"/></td>
            <td *ngIf="(tagOption.optionType=='PARSED_OPTION_STRING_VALUE')"><input type="text" class="form-control input-text-xs" [(ngModel)]="tagOption.value"/></td>
          </tr>
      </table>
	  <div style="display: inline-block; width:99%; text-align: center; margin: 10px;">
		<div style="display: table-cell;">
		  <button type="reset" class="btn btn-default" (click)="cancel()"><img src="../../images/close.svg" class="image-button"/>&nbsp;{{'TR_CANCEL' | translate}}</button>
		</div>
		<div style="display: table-cell;width:75%"></div>
		  <div style="display: table-cell;">
		    <button class="btn btn-default" (click)="save()"><img src="../../images/ok.svg" class="image-button"/>&nbsp;{{'TR_OK' | translate}}</button>
		  </div>
	  </div>
    <alertStatusBarComponent></alertStatusBarComponent>
  </div>`
})

export class DashboardConfigTagOptionsModal implements CloseGuard, OnInit, ModalComponent<TagOptionsModalContext> {
  private context: TagOptionsModalContext;
  private tagOptionList: TagOption[] = [];
  private isDataLoaded: boolean = false;

  constructor(public dialog: DialogRef<TagOptionsModalContext>, private alertService: AlertService, private translate: TranslateService) { 
    this.context = dialog.context;
    this.context.dialogClass = "modal-dialog modal-md";
    dialog.setCloseGuard(this);
    dialog.inElement = true;
  }

  public ngOnInit(): void {
    this.tagOptionList = TagOption.initTagOptionList(this.dialog.context.controlSource, this.dialog.context.tagOptionsValue);
    this.isDataLoaded = true;
  }

  private optionMaskOnChange(value: any, tagOptionBitMask: TagOption): void {
    tagOptionBitMask.value = value;
  }

  private optionScaleOnChange(event: any, tagOption: TagOption, pos): void {
    tagOption.value[pos] = event.target.value;
  }

  private optionMaskOnInit(optionMaskList: Bitmask[], choices: any): void {
    optionMaskList.push({ name: "GTWDEFS_UPDTRSN_NONE", value: 0x00000000, description: "TR_GTWDEFS_UPDTRSN_NONE_DESC", isChecked: false });
    let jsonChoices = JSON.parse(choices);
    jsonChoices.forEach((choice) => {
      optionMaskList.push({ name: choice.name, value: choice.value, description: choice.description, isChecked: false });
    });
  }

  public beforeDismiss(): boolean {
    return true; // prevent closing modal by using Esc
  }

  private changeOptionValue(tagOptionName, tagOptionGroupName) {
    this.tagOptionList.forEach((option) => {
      if (tagOptionName != null && tagOptionGroupName != null && option.optionType == "PARSED_OPTION_BOOL_CHOICE_VALUE") {
        if (option.name == tagOptionName && option.choices == tagOptionGroupName)
          option.value = option.valueToUse;
        else if (option.name != tagOptionName && option.choices == tagOptionGroupName)
          option.value = null;
      }
    });
  }

  private cancel() {
    this.dialog.close(null);
  }

  private save() {
    let hasError: boolean = false; 
    type OptionItem = [string, string];
    let optionItemList: OptionItem[] = [];

    this.tagOptionList.forEach((option) => {
      if (option.value != null) {
        if (option.optionType == "PARSED_OPTION_BOOL_VALUE" && option.value != false) {
          optionItemList.push([option.name, ""]);
        }
        else if (option.optionType == "PARSED_OPTION_BOOL_CHOICE_VALUE" && option.value != false) {
          optionItemList.push([option.name, ""]);
        }
        else if (option.optionType == "PARSED_OPTION_SCALE_VALUE" && option.value != false && option.value.indexOf("") === -1 && option.value.length == 4) {
          try {
            let rawMin = parseFloat(option.value[0]);
            let rawMax = parseFloat(option.value[1]);
            let engineeringMin = parseFloat(option.value[2]);
            let engineeringMax = parseFloat(option.value[3]);

            if (rawMin > rawMax || engineeringMin > engineeringMax) {
              this.alertService.error("TR_ERROR_SCALE_OPTION_IS_INCORRECT");
              hasError = true;
            }
            else {
              optionItemList.push([option.name, option.value.join(" ")]);
            }
          }
          catch (e) {
            this.alertService.error("TR_ERROR_SCALE_INCORRECT");
            hasError = true;
          }
        }
        else if (option.optionType == "PARSED_OPTION_SCALE_VALUE" && option.value != false && option.value.length != 4) {
          this.alertService.error("TR_ERROR_SCALE_OPTION_IS_INCORRECT");
          hasError = true;
        }
        else if (option.optionType != "PARSED_OPTION_SCALE_VALUE" && option.value != false) {
          optionItemList.push([option.name, option.value]);
        }
        else if (option.optionType == "PARSED_OPTION_CHOICE_VALUE") {
          optionItemList.push([option.name, option.value]);
        }
      }
    });
    if (!hasError) {
      this.alertService.clearMessage();
      this.dialog.close(optionItemList);
    }
  }

  private onlyNumericChar(event, editorField): boolean {
    let input = String.fromCharCode(event.keyCode);
    if ((/[^-.0-9]/.test(input))) {
      event.preventDefault();
      return false;
    }
  }
}

export class TagOptionsModalContext extends BSModalContext {
  public controlSource: string;
  public tagOptionsValue: string;
}

export class TagOption {
  public name: string;
  public helpTR: string;
  public helpText: string;
  public optionType: string;
  public choices: any;
  public valueToUse: any;
  public value: any;

  constructor(name: string, helpTR: string, helpText: string, optionType: string, choices: any = null, value: any = null, valueToUse: any = null) {
    this.name = name;
    this.helpTR = helpTR;
    this.helpText = helpText;
    this.optionType = optionType;
    this.choices = choices;
    this.value = value;
    this.valueToUse = valueToUse;
  }

  public static initTagOptionList(controlSource: string, tagOptionsValue: string): TagOption[] {
    let tagOptionList: TagOption[] = [];
    let optionList = JSON.parse(controlSource);

    if (optionList) {
      optionList.forEach((option: TagOption) => {
        let key = Object.keys(option)[0];
        let helpText = unescape(option.helpText);
        if (option[key] == "PARSED_OPTION_CHOICE_VALUE") {
          let jsource = JSON.parse(option.choices);
          tagOptionList.push(new TagOption(key, option.helpTR, helpText, option[key], jsource, null));
        }
        else if (option[key] == "PARSED_OPTION_SCALE_VALUE") {
          tagOptionList.push(new TagOption(key, option.helpTR, helpText, option[key], option.choices, ["", "", "", ""]));
        }
        else {
          tagOptionList.push(new TagOption(key, option.helpTR, helpText, option[key], option.choices, null, option.valueToUse));
        }
      });
    }

    try {
      let tagOptionSplit = this.splitMulti(tagOptionsValue, [" ", "\n"]);
      for (var i = 0; i < tagOptionSplit.length; i++) {
        //find the begining of the option
        if (tagOptionList.filter(p => p.name == tagOptionSplit[i]).length > 0) {
          let isLatest: boolean = true;
          for (var y = i + 1; y < tagOptionSplit.length; y++) {
            //find the end of the option
            if (tagOptionList.filter(p => p.name == tagOptionSplit[y]).length > 0) {
              this.addTagOptionValue(tagOptionList.filter(p => p.name == tagOptionSplit[i])[0], tagOptionSplit.slice(i + 1, y));
              isLatest = false;
              break;
            }
          }
          //for the last option
          if (isLatest) {
            this.addTagOptionValue(tagOptionList.filter(p => p.name == tagOptionSplit[i])[0], tagOptionSplit.slice(i + 1, y));
          }
        }
      }
    }
    catch (e) { console.debug(e); }
    return tagOptionList;
  }

  private static addTagOptionValue(tagOption: TagOption, tagOptionsValue: any): void {
    try {
      if (tagOption.optionType == "PARSED_OPTION_SCALE_VALUE" && tagOptionsValue.length > 3)
        tagOption.value = tagOptionsValue.slice(0, 4);
      else if (tagOptionsValue.length == 0 && tagOption.optionType == "PARSED_OPTION_BOOL_VALUE")
        tagOption.value = true;
      else if (tagOptionsValue.length == 0 && tagOption.optionType == "PARSED_OPTION_BOOL_CHOICE_VALUE")
        tagOption.value = tagOption.valueToUse;
      else if (tagOption.optionType == "PARSED_OPTION_CHOICE_VALUE" && tagOptionsValue.length == 1)
        tagOption.value = tagOptionsValue[0];
      else
        tagOption.value = tagOptionsValue.join(" ");
    }
    catch (e) {
      tagOption.value = "";
    } 
  }

  public static formatTagOptionValue(controlSource: string, tagOptionsValue: string): string {
    let tagOptionsValueformated: string = "";
    let optionNameList: Array<string> = [];
    let optionList = JSON.parse(controlSource);
    if (optionList) {
      optionList.forEach((option) => {
        optionNameList.push(Object.keys(option)[0]);
      });
    }
    try {
      let tagOptionSplit = this.splitMulti(tagOptionsValue, [" ", "\n"]);
      for (var i = 0; i < tagOptionSplit.length; i++) {
        //find the begining of the option
        if (optionNameList.includes(tagOptionSplit[i])) {
          let isLatest: boolean = true;
          for (var y = i + 1; y < tagOptionSplit.length; y++) {
            //find the end of the option
            if (optionNameList.includes(tagOptionSplit[y])) {
              tagOptionsValueformated += tagOptionSplit.slice(i, y).join(" ") + "\n";
              isLatest = false;
              break;
            }
          }
          //for the last option
          if (isLatest) {
            tagOptionsValueformated += tagOptionSplit.slice(i, y).join(" ");
          }
        }
      }
    }
    catch (e) { }
    return tagOptionsValueformated;
  }

  private static splitMulti(str: string, tokens: Array<string>): Array<string> {
    var tempChar = tokens[0];
    for (var i = 1; i < tokens.length; i++) {
      str = str.split(tokens[i]).join(tempChar);
    }
    return str.split(tempChar);
  }
}