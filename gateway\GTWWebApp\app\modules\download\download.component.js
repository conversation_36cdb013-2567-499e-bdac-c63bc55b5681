System.register(["@angular/core", "../alert/alert.service", "../../authentication/authentication.service", "../../data/api/api"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, alert_service_1, authentication_service_1, api_1, DownloadComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (authentication_service_1_1) {
                authentication_service_1 = authentication_service_1_1;
            },
            function (api_1_1) {
                api_1 = api_1_1;
            }
        ],
        execute: function () {
            DownloadComponent = (function () {
                function DownloadComponent(alertService, authenticationService, fileService) {
                    this.alertService = alertService;
                    this.authenticationService = authenticationService;
                    this.fileService = fileService;
                }
                DownloadComponent.prototype.downloadClick = function (fileName, fileType) {
                    var _this = this;
                    this.fileService.fileGet(fileName, fileType).subscribe(function (data) {
                        _this.saveData(data, fileName);
                        _this.alertService.success("TR_FILE_DOWNLOADED");
                    }, function (error) {
                        if (error.status == 401) {
                            _this.authenticationService.onLoginFailed("/");
                        }
                        else {
                            _this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED");
                        }
                    });
                };
                DownloadComponent.prototype.saveData = function (blob, fileName) {
                    var a = document.createElement("a");
                    document.body.appendChild(a);
                    var url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = fileName.replace(/^.*[\\\/]/, '');
                    a.click();
                    window.URL.revokeObjectURL(url);
                    a.remove();
                };
                DownloadComponent = __decorate([
                    core_1.Component({
                        selector: "downloadComponent",
                        template: ""
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, authentication_service_1.AuthenticationService, api_1.FileService])
                ], DownloadComponent);
                return DownloadComponent;
            }());
            exports_1("DownloadComponent", DownloadComponent);
        }
    };
});
//# sourceMappingURL=download.component.js.map