System.register(["@angular/core", "./alert.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, alert_service_1, AlertStatusBarComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            }
        ],
        execute: function () {
            AlertStatusBarComponent = (function () {
                function AlertStatusBarComponent(alertService) {
                    var _this = this;
                    this.alertService = alertService;
                    this.subscription = alertService.getMessage().subscribe(function (message) { _this.message = message; });
                }
                AlertStatusBarComponent.prototype.ngOnDestroy = function () {
                    this.subscription.unsubscribe();
                };
                AlertStatusBarComponent = __decorate([
                    core_1.Component({
                        selector: 'alertStatusBarComponent',
                        template: "\n\t\t\t\t<div *ngIf=\"message\" [ngClass]=\"{ 'alert-success': message.type === 'success', 'alert-danger': message.type === 'error', 'alert-info': message.type === 'info' , 'alert-warning': message.type === 'warning'}\">{{message.text | translate}}</div>\n\t\t\t"
                    }),
                    __metadata("design:paramtypes", [alert_service_1.AlertService])
                ], AlertStatusBarComponent);
                return AlertStatusBarComponent;
            }());
            exports_1("AlertStatusBarComponent", AlertStatusBarComponent);
        }
    };
});
//# sourceMappingURL=alert.status-bar.component.js.map