﻿import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'gtwImage' })
export class GTWImagePipe implements PipeTransform {
  transform(nodeIcon: number): string {
    enum Icon {
      IDI_MDO_DATAOBJECT = 509,
      IDI_INTERNAL_PORT = 511,
      IDI_EQUATION = 516,
      IDI_PORT = 524,
      IDI_SESSION = 525,
      IDI_GATEWAY = 526,
      IDI_UNDEFINED = 527,
      IDI_OPC_CLIENT = 528,
      IDI_SECTOR = 529,
      IDI_DATAOBJECT = 531,
      IDI_DATATYPE = 532,
      IDI_INTERNAL_DATA_OBJECT = 533,
      IDI_SDO_DATAOBJECT = 534,
      IDI_INTERNAL_BDO = 539,
      IDI_OPC_SERVER = 565,
      IDI_MODEM_POOLS = 570,
      IDI_MODEM_POOL = 571,
      IDI_MODEM = 572,
      IDI_INTERNAL_USER_DATA_OBJECT = 573,
      IDI_DNP_PROTOTYPE = 1002,
      IDI_61850_MDO = 1003,
      IDI_61850_CLIENT = 1006,
      IDI_61850_REPORT = 1007,
      IDI_61850_POLLED_DATA_SET = 1008,
      IDI_OPC_AE_MDO = 1043,
      IDI_DNP_DATA_ELEMENT = 1045,
      IDI_ALARM = 1052,
      IDI_OPC_AE_CLIENT = 1053,
      IDI_TASE2_CLIENT = 1058,
      IDI_DNP_DESCRIPTOR = 1060,
      IDI_61850_POLLED_POINT_SET = 1061,
      IDI_61850_SERVER = 1064,
      IDI_LOGICAL_DEVICE = 1075,
      IDI_OPC_MDO = 1082,
      IDI_TASE2_SERVER = 1084,
      IDI_61850_REPORT1 = 1090,
      IDI_ICON1 = 1091,
      IDI_ICON2 = 1092,
      IDI_61850_GOOSE = 1095,
      IDI_ICON3 = 1100,
      IDI_ALARMS = 1102,
      IDI_ODBC_CLIENT = 1104,
      IDI_ODBC_QUERY = 1106,
      IDI_ODBC = 1932,
      IDI_ODBC_CLIENT1 = 1933,
      IDI_TASE2_MDO = 2219,
      IDI_TASE2_REPORT = 2220,
      IDI_TASE2_POLLED_POINT_SET = 2221,
      IDI_TASE2_POLLED_DATA_SET = 2222,
      IDI_61850_CLIENT_NODE = 2223,
      IDI_TASE2_CLIENT_NODE = 2224,
      IDI_USER_FOLDER = 999
    }

    if ( nodeIcon == Icon.IDI_INTERNAL_USER_DATA_OBJECT) return "internalUserDataObject.svg";
    if ( nodeIcon == Icon.IDI_INTERNAL_BDO) return "internal.svg";
    if ( nodeIcon == Icon.IDI_INTERNAL_DATA_OBJECT) return "internal.svg";
    if ( nodeIcon == Icon.IDI_EQUATION) return "equation.svg";
    if ( nodeIcon == Icon.IDI_DATATYPE) return "dataType.svg";
    if ( nodeIcon == Icon.IDI_SDO_DATAOBJECT) return "sdoData.svg";
    if ( nodeIcon == Icon.IDI_MDO_DATAOBJECT) return "mdoData.svg";
    if ( nodeIcon == Icon.IDI_DATAOBJECT) return "internal.svg";
    if ( nodeIcon == Icon.IDI_MODEM) return "modem.svg";
    if ( nodeIcon == Icon.IDI_MODEM_POOL) return "modem_pool.svg";
    if ( nodeIcon == Icon.IDI_MODEM_POOLS) return "modem_pools.svg";
    if ( nodeIcon == Icon.IDI_OPC_SERVER) return "opcServer.svg";
    if ( nodeIcon == Icon.IDI_OPC_CLIENT) return "opcClient.svg";
    if ( nodeIcon == Icon.IDI_OPC_MDO) return "opcMDO.svg";
    if ( nodeIcon == Icon.IDI_OPC_AE_CLIENT) return "opcAEClient.svg";
    if ( nodeIcon == Icon.IDI_OPC_AE_MDO) return "opcAEMDO.svg";
    //if ( nodeIcon == Icon.IDI_OPC_AE_SERVER) return "opcAEServer.svg";
    if ( nodeIcon == Icon.IDI_SECTOR) return "sector.svg";
    if ( nodeIcon == Icon.IDI_SESSION) return "session.svg";
    if ( nodeIcon == Icon.IDI_INTERNAL_PORT) return "internalPort.svg";
    if ( nodeIcon == Icon.IDI_PORT) return "port.svg";
    if ( nodeIcon == Icon.IDI_GATEWAY) return "gateway.png";
    if ( nodeIcon == Icon.IDI_UNDEFINED) return "undefine.svg";
    if ( nodeIcon == Icon.IDI_DNP_DATA_ELEMENT) return "dnpDataElement.svg";
    if ( nodeIcon == Icon.IDI_DNP_PROTOTYPE) return "dnpPrototype.svg";
    if ( nodeIcon == Icon.IDI_DNP_DESCRIPTOR) return "dnpDescriptor.svg";
    if ( nodeIcon == Icon.IDI_61850_SERVER) return "61850Server.svg";
    if ( nodeIcon == Icon.IDI_61850_CLIENT) return "61850Client.svg";
    if ( nodeIcon == Icon.IDI_61850_REPORT) return "61850Report.svg";
    if ( nodeIcon == Icon.IDI_61850_MDO) return "61850MDO.svg";
    if ( nodeIcon == Icon.IDI_61850_POLLED_DATA_SET) return "61850PolledDataSet.svg";
    if ( nodeIcon == Icon.IDI_61850_POLLED_POINT_SET) return "61850PolledPointSet.svg";
    if ( nodeIcon == Icon.IDI_61850_GOOSE) return "61850Goose.svg";
    if ( nodeIcon == Icon.IDI_ODBC_CLIENT) return "odbcClient.svg";
    if ( nodeIcon == Icon.IDI_ODBC_QUERY) return "odbcQuery.svg";
    if ( nodeIcon == Icon.IDI_TASE2_SERVER) return "tase2Server.svg";
    if ( nodeIcon == Icon.IDI_TASE2_CLIENT) return "tase2Client.svg";
    if ( nodeIcon == Icon.IDI_TASE2_REPORT) return "61850Report.svg";
    if ( nodeIcon == Icon.IDI_TASE2_MDO) return "61850MDO.svg";
    if ( nodeIcon == Icon.IDI_TASE2_POLLED_DATA_SET) return "61850PolledDataSet.svg";
    if ( nodeIcon == Icon.IDI_TASE2_POLLED_POINT_SET) return "61850PolledPointSet.svg";
    if ( nodeIcon == Icon.IDI_LOGICAL_DEVICE) return "tase2LogicalDevice.svg";
    if ( nodeIcon == Icon.IDI_ALARMS) return "alarms2.svg";
    if (nodeIcon == Icon.IDI_ALARM) return "alarms.svg";
    if (nodeIcon == Icon.IDI_61850_CLIENT_NODE) return "61850ClientNode.svg";
    if (nodeIcon == Icon.IDI_TASE2_CLIENT_NODE) return "tase2ClientNode.svg";
    if (nodeIcon == Icon.IDI_USER_FOLDER) return "userFolder.svg";
    return "undefine.svg"
  }
}