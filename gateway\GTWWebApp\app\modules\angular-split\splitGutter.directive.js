System.register(["@angular/core"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, SplitGutterDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            }
        ],
        execute: function () {
            SplitGutterDirective = (function () {
                function SplitGutterDirective(elementRef, renderer) {
                    this.elementRef = elementRef;
                    this.renderer = renderer;
                    this._disabled = false;
                }
                Object.defineProperty(SplitGutterDirective.prototype, "order", {
                    set: function (v) {
                        this.setStyle('order', v);
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(SplitGutterDirective.prototype, "direction", {
                    set: function (v) {
                        this._direction = v;
                        this.refreshStyle();
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(SplitGutterDirective.prototype, "size", {
                    set: function (v) {
                        this._size = v;
                        this.refreshStyle();
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(SplitGutterDirective.prototype, "disabled", {
                    set: function (v) {
                        this._disabled = v;
                        this.refreshStyle();
                    },
                    enumerable: false,
                    configurable: true
                });
                SplitGutterDirective.prototype.refreshStyle = function () {
                    this.setStyle('flex-basis', this._size + "px");
                    this.setStyle('height', (this._direction === 'vertical') ? this._size + "px" : "100%");
                    var state = (this._disabled === true) ? 'disabled' : this._direction;
                    this.setStyle('cursor', this.getCursor(state));
                    this.setStyle('background-image', "url(\"" + this.getImage(state) + "\")");
                };
                SplitGutterDirective.prototype.setStyle = function (key, value) {
                    this.renderer.setStyle(this.elementRef.nativeElement, key, value);
                };
                SplitGutterDirective.prototype.getCursor = function (state) {
                    switch (state) {
                        case 'disabled':
                            return 'default';
                        case 'vertical':
                            return 'row-resize';
                        case 'horizontal':
                            return 'col-resize';
                    }
                };
                SplitGutterDirective.prototype.getImage = function (state) {
                    switch (state) {
                        case 'disabled':
                            return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
                        case 'vertical':
                            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC';
                        case 'horizontal':
                            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==';
                    }
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Number),
                    __metadata("design:paramtypes", [Number])
                ], SplitGutterDirective.prototype, "order", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", String),
                    __metadata("design:paramtypes", [String])
                ], SplitGutterDirective.prototype, "direction", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Number),
                    __metadata("design:paramtypes", [Number])
                ], SplitGutterDirective.prototype, "size", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean),
                    __metadata("design:paramtypes", [Boolean])
                ], SplitGutterDirective.prototype, "disabled", null);
                SplitGutterDirective = __decorate([
                    core_1.Directive({
                        selector: 'split-gutter'
                    }),
                    __metadata("design:paramtypes", [core_1.ElementRef,
                        core_1.Renderer2])
                ], SplitGutterDirective);
                return SplitGutterDirective;
            }());
            exports_1("SplitGutterDirective", SplitGutterDirective);
        }
    };
});
//# sourceMappingURL=splitGutter.directive.js.map