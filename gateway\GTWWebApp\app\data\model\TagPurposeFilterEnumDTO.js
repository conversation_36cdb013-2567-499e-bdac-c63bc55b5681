System.register([], function (exports_1, context_1) {
    "use strict";
    var TagPurposeFilterEnumDTO;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            (function (TagPurposeFilterEnumDTO) {
                var TagPurposeFilterEnum;
                (function (TagPurposeFilterEnum) {
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_HEALTH"] = 1] = "GTWTYPES_TAG_PURPOSE_MASK_HEALTH";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE"] = 2] = "GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_DATA"] = 4] = "GTWTYPES_TAG_PURPOSE_MASK_DATA";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_ALL"] = 7] = "GTWTYPES_TAG_PURPOSE_ALL";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY"] = 8] = "GTWTYPES_TAG_PURPOSE_MASK_UNHEALTHY";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV"] = 16] = "GTWTYPES_TAG_PURPOSE_MASK_GET_MAPPINGS_CSV";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV"] = 32] = "GTWTYPES_TAG_PURPOSE_MASK_GET_POINTS_CSV";
                    TagPurposeFilterEnum[TagPurposeFilterEnum["GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO"] = 64] = "GTWTYPES_TAG_PURPOSE_MASK_RESET_TO_ZERO";
                })(TagPurposeFilterEnum = TagPurposeFilterEnumDTO.TagPurposeFilterEnum || (TagPurposeFilterEnumDTO.TagPurposeFilterEnum = {}));
            })(TagPurposeFilterEnumDTO || (TagPurposeFilterEnumDTO = {}));
            exports_1("TagPurposeFilterEnumDTO", TagPurposeFilterEnumDTO);
        }
    };
});
//# sourceMappingURL=TagPurposeFilterEnumDTO.js.map