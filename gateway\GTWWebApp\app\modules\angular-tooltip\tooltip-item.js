System.register([], function (exports_1, context_1) {
    "use strict";
    var TooltipItem;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            TooltipItem = (function () {
                function TooltipItem(content) {
                    this.content = content;
                }
                return TooltipItem;
            }());
            exports_1("TooltipItem", TooltipItem);
        }
    };
});
//# sourceMappingURL=tooltip-item.js.map