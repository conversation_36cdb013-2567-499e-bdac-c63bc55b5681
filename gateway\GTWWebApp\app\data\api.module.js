System.register(["@angular/core", "./configuration", "@angular/common/http", "./api/auth.service", "./api/config.service", "./api/editorContextMenu.service", "./api/editors.service", "./api/help.service", "./api/log.service", "./api/manage.service", "./api/mappings.service", "./api/nodes.service", "./api/tags.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, configuration_1, http_1, auth_service_1, config_service_1, editorContextMenu_service_1, editors_service_1, help_service_1, log_service_1, manage_service_1, mappings_service_1, nodes_service_1, tags_service_1, ApiModule;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (auth_service_1_1) {
                auth_service_1 = auth_service_1_1;
            },
            function (config_service_1_1) {
                config_service_1 = config_service_1_1;
            },
            function (editorContextMenu_service_1_1) {
                editorContextMenu_service_1 = editorContextMenu_service_1_1;
            },
            function (editors_service_1_1) {
                editors_service_1 = editors_service_1_1;
            },
            function (help_service_1_1) {
                help_service_1 = help_service_1_1;
            },
            function (log_service_1_1) {
                log_service_1 = log_service_1_1;
            },
            function (manage_service_1_1) {
                manage_service_1 = manage_service_1_1;
            },
            function (mappings_service_1_1) {
                mappings_service_1 = mappings_service_1_1;
            },
            function (nodes_service_1_1) {
                nodes_service_1 = nodes_service_1_1;
            },
            function (tags_service_1_1) {
                tags_service_1 = tags_service_1_1;
            }
        ],
        execute: function () {
            ApiModule = (function () {
                function ApiModule(parentModule, http) {
                    if (parentModule) {
                        throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
                    }
                    if (!http) {
                        throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
                            'See also https://github.com/angular/angular/issues/20575');
                    }
                }
                ApiModule_1 = ApiModule;
                ApiModule.forRoot = function (configurationFactory) {
                    return {
                        ngModule: ApiModule_1,
                        providers: [{ provide: configuration_1.Configuration, useFactory: configurationFactory }]
                    };
                };
                var ApiModule_1;
                ApiModule = ApiModule_1 = __decorate([
                    core_1.NgModule({
                        imports: [],
                        declarations: [],
                        exports: [],
                        providers: [
                            auth_service_1.AuthService,
                            config_service_1.ConfigService,
                            editorContextMenu_service_1.EditorContextMenuService,
                            editors_service_1.EditorsService,
                            help_service_1.HelpService,
                            log_service_1.LogService,
                            manage_service_1.ManageService,
                            mappings_service_1.MappingsService,
                            nodes_service_1.NodesService,
                            tags_service_1.TagsService
                        ]
                    }),
                    __param(0, core_1.Optional()),
                    __param(0, core_1.SkipSelf()),
                    __param(1, core_1.Optional()),
                    __metadata("design:paramtypes", [ApiModule,
                        http_1.HttpClient])
                ], ApiModule);
                return ApiModule;
            }());
            exports_1("ApiModule", ApiModule);
        }
    };
});
//# sourceMappingURL=api.module.js.map