System.register(["@angular/core", "@angular/common/http", "rxjs", "./loader.service", "rxjs/operators"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, http_1, rxjs_1, loader_service_1, operators_1, LoaderInterceptorService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
            },
            function (loader_service_1_1) {
                loader_service_1 = loader_service_1_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            }
        ],
        execute: function () {
            LoaderInterceptorService = (function () {
                function LoaderInterceptorService(loaderService) {
                    this.loaderService = loaderService;
                    this.pendingRequests = 0;
                }
                LoaderInterceptorService.prototype.intercept = function (request, next) {
                    var _this = this;
                    if (request.url.includes("/gettag") ||
                        request.url.includes("_log_filter_") ||
                        request.url.includes("/get_mirror_all_to_log_file")) {
                        this.pendingRequests = 0;
                    }
                    else {
                        this.loaderService.toggleIsLoading(true);
                        this.pendingRequests++;
                    }
                    return next.handle(request)
                        .pipe(operators_1.catchError(function (err) {
                        _this.pendingRequests = 0;
                        _this.loaderService.toggleIsLoading(false);
                        return rxjs_1.throwError(err);
                    }))
                        .pipe(operators_1.map(function (evt) {
                        if (evt instanceof http_1.HttpResponse) {
                            if (true) {
                                _this.pendingRequests--;
                                if (_this.pendingRequests <= 0)
                                    _this.loaderService.toggleIsLoading(false);
                                _this.pendingRequests = 0;
                            }
                        }
                        return evt;
                    }));
                };
                LoaderInterceptorService = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [loader_service_1.LoaderService])
                ], LoaderInterceptorService);
                return LoaderInterceptorService;
            }());
            exports_1("LoaderInterceptorService", LoaderInterceptorService);
        }
    };
});
//# sourceMappingURL=loader-interceptor.service.js.map