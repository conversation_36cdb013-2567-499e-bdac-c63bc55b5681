{"version": 3, "file": "license.component.js", "sourceRoot": "", "sources": ["license.component.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAwDE,0BAAoB,YAA0B,EAAU,qBAA4C,EAAU,aAA4B,EAAU,WAAwB,EAAU,SAA2B,EAAU,QAAkB,EAAU,aAA4B;oBAA/P,iBAAY,GAAZ,YAAY,CAAc;oBAAU,0BAAqB,GAArB,qBAAqB,CAAuB;oBAAU,kBAAa,GAAb,aAAa,CAAe;oBAAU,gBAAW,GAAX,WAAW,CAAa;oBAAU,cAAS,GAAT,SAAS,CAAkB;oBAAU,aAAQ,GAAR,QAAQ,CAAU;oBAAU,kBAAa,GAAb,aAAa,CAAe;oBAH3Q,gBAAW,GAAgB,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;oBAIlE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnC,CAAC;gBAEM,mCAAQ,GAAf;oBACE,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,CAAC;gBAEM,yCAAc,GAArB;oBAAA,iBAUC;oBATC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,SAAS,CACvC,UAAA,IAAI;wBACF,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;wBACxB,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,WAAW,CAAC,QAAQ,CAAA;oBACvC,CAAC,EACD,UAAA,KAAK;wBACH,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;4BAAE,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;yBAAE;6BAAM;4BAAE,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;yBAAE;oBACnJ,CAAC,CACF,CAAC;gBACJ,CAAC;gBAEO,mCAAQ,GAAhB,UAAiB,GAAG;oBAClB,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;gBACjC,CAAC;gBAEO,oCAAS,GAAjB,UAAkB,GAAG;oBACnB,OAAO,GAAG,IAAE,GAAG,CAAA,CAAC,CAAA,IAAI,CAAA,CAAC,CAAA,KAAK,CAAC;gBAC7B,CAAC;gBAEO,qCAAU,GAAlB;oBACE,OAAO;wBACL,IAAI,eAAM,CAAC,SAAS,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC;wBAC3C,IAAI,eAAM,CAAC,YAAY,EAAE,gBAAgB,EAAE,EAAE,EAAE,EAAE,CAAC;qBACnD,CAAC;gBACJ,CAAC;gBAEO,yCAAc,GAAtB;oBAAA,iBAUC;oBATC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,CACxC,UAAA,IAAI;wBACF,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;wBACvD,KAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC3C,CAAC,EACD,UAAA,KAAK;wBACH,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;4BAAE,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;yBAAE;oBAC7E,CAAC,CACF,CAAC;gBACJ,CAAC;gBAEO,2CAAgB,GAAxB;oBAAA,iBAkBC;oBAjBC,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,EAAE,EAAE;wBACtC,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,kBAAkB,CAAA;wBACjD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,SAAS,CACjI,UAAC,IAAS;4BACR,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE;gCAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gCACzB,IAAI,MAAM,EAAE;oCACV,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oCACxC,KAAI,CAAC,cAAc,EAAE,CAAC;iCACvB;6BACF;wBACH,CAAC,EACD,UAAA,KAAK;4BACH,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;gCAAE,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;6BAAE;iCAAM;gCAAE,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAmD,IAA2B,CAAC,CAAC;6BAAE;wBACxN,CAAC,CACF,CAAC;qBACH;gBACH,CAAC;gBAEO,iDAAsB,GAA9B;oBAAA,iBAmBC;oBAlBC,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,wBAAwB,CAAA;oBACvD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,SAAS,CACnG,UAAC,IAAS;wBACR,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE;4BAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;4BACzB,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;4BACvB,IAAI,MAAM,IAAI,OAAO,IAAI,EAAE,EAAE;gCAC3B,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;gCACrD,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gCAC3C,KAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gCACnC,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;6BACzC;yBACF;oBACH,CAAC,EACD,UAAA,KAAK;wBACH,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;4BAAE,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;yBAAE;6BAAM;4BAAE,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;yBAAE;oBAC9I,CAAC,CACF,CAAC;gBACJ,CAAC;gBAEO,iDAAsB,GAA9B,UAA+B,KAAK;oBAApC,iBAsBC;oBArBC,IAAI,QAAQ,GAAa,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC5C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;wBACvB,IAAI,IAAI,GAAS,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAC7B,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;wBAG5C,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,SAAS,CAC/C,UAAC,IAAS;4BACR,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE;gCAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gCACzB,IAAI,MAAM,EAAE;oCACV,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oCACxC,KAAI,CAAC,cAAc,EAAE,CAAC;iCACvB;6BACF;wBACH,CAAC,EACD,UAAA,KAAK;4BACH,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;gCAAE,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;6BAAE;iCAAM;gCAAE,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;6BAAE;wBAC9I,CAAC,CACF,CAAC;qBACH;gBACH,CAAC;gBAEO,iDAAsB,GAA9B;oBAAA,iBAUC;oBATC,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC,SAAS,CAC/C,UAAC,IAAS;wBACR,KAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;wBACvC,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;oBAClD,CAAC,EACD,UAAA,KAAK;wBACH,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;4BAAE,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;yBAAE;6BAAM;4BAAE,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;yBAAE;oBAC/I,CAAC,CACF,CAAC;gBACJ,CAAC;gBAEO,mCAAQ,GAAhB,UAAiB,IAAI,EAAE,QAAQ;oBAC7B,IAAI,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBACpC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;oBAC7B,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC3C,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;oBACb,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;oBACtB,CAAC,CAAC,KAAK,EAAE,CAAC;oBACV,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAChC,CAAC,CAAC,MAAM,EAAE,CAAC;gBACb,CAAC;gBA9I8C;oBAA9C,gBAAS,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;;uEAAoB;gBACxB;oBAAzC,gBAAS,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;8CAAW,sCAAiB;kEAAC;gBAF3D,gBAAgB;oBArC5B,gBAAS,CAAC;wBACT,QAAQ,EAAE,qBAAqB;wBAC/B,SAAS,EAAE,CAAC,oBAAQ,CAAC;wBACrB,WAAW,EAAE,6CAA6C;wBAC1D,MAAM,EAAE,CAAC,mvBA+BR,CAAC;qBACH,CAAC;qDAUkC,4BAAY,EAAiC,8CAAqB,EAAyB,mBAAa,EAAuB,iBAAW,EAAqB,uBAAgB,EAAoB,oBAAQ,EAAyB,8BAAa;mBATxQ,gBAAgB,CAgJ5B;gBAAD,uBAAC;aAAA,AAhJD;;YAiJA;gBACE,qBACS,WAAmB,EACnB,WAAmB,EACnB,YAAqB;oBAFrB,gBAAW,GAAX,WAAW,CAAQ;oBACnB,gBAAW,GAAX,WAAW,CAAQ;oBACnB,iBAAY,GAAZ,YAAY,CAAS;gBAC1B,CAAC;gBACP,kBAAC;YAAD,CAAC,AAND,IAMC;QAAA,CAAC"}