System.register(["@angular/common", "@angular/core", "rxjs/operators", "rxjs"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var common_1, core_1, operators_1, rxjs_1, ResizableDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (common_1_1) {
                common_1 = common_1_1;
            },
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
            }
        ],
        execute: function () {
            ResizableDirective = (function () {
                function ResizableDirective(documentRef, elementRef) {
                    var _this = this;
                    this.documentRef = documentRef;
                    this.elementRef = elementRef;
                    this.resizable = rxjs_1.fromEvent(this.elementRef.nativeElement, "mousedown").pipe(operators_1.tap(function (e) { return e.preventDefault(); }), operators_1.switchMap(function () {
                        var _a = _this.elementRef.nativeElement.closest("th").getBoundingClientRect(), width = _a.width, right = _a.right;
                        return rxjs_1.fromEvent(_this.documentRef, "mousemove").pipe(operators_1.map(function (_a) {
                            var clientX = _a.clientX;
                            return width + clientX - right;
                        }), operators_1.distinctUntilChanged(), operators_1.takeUntil(rxjs_1.fromEvent(_this.documentRef, "mouseup")));
                    }));
                }
                __decorate([
                    core_1.Output(),
                    __metadata("design:type", Object)
                ], ResizableDirective.prototype, "resizable", void 0);
                ResizableDirective = __decorate([
                    core_1.Directive({
                        selector: "[resizable]"
                    }),
                    __param(0, core_1.Inject(common_1.DOCUMENT)),
                    __param(1, core_1.Inject(core_1.ElementRef)),
                    __metadata("design:paramtypes", [Document, core_1.ElementRef])
                ], ResizableDirective);
                return ResizableDirective;
            }());
            exports_1("ResizableDirective", ResizableDirective);
        }
    };
});
//# sourceMappingURL=resizable.directive.js.map