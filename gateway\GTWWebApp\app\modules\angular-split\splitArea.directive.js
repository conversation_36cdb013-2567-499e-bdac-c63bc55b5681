System.register(["@angular/core", "./split.component"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, split_component_1, SplitAreaDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (split_component_1_1) {
                split_component_1 = split_component_1_1;
            }
        ],
        execute: function () {
            SplitAreaDirective = (function () {
                function SplitAreaDirective(elementRef, renderer, split) {
                    this.elementRef = elementRef;
                    this.renderer = renderer;
                    this.split = split;
                    this._order = null;
                    this._size = null;
                    this._minSizePixel = 0;
                    this._visible = true;
                    this.visibility = "block";
                    this.eventsLockFct = [];
                }
                Object.defineProperty(SplitAreaDirective.prototype, "order", {
                    set: function (v) {
                        this._order = !isNaN(v) ? v : null;
                        this.split.updateArea(this, this._order, this._size, this._minSizePixel);
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(SplitAreaDirective.prototype, "size", {
                    set: function (v) {
                        this._size = !isNaN(v) ? v : null;
                        this.split.updateArea(this, this._order, this._size, this._minSizePixel);
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(SplitAreaDirective.prototype, "minSizePixel", {
                    set: function (v) {
                        this._minSizePixel = (!isNaN(v) && v > 0) ? v : 0;
                        this.split.updateArea(this, this._order, this._size, this._minSizePixel);
                    },
                    enumerable: false,
                    configurable: true
                });
                Object.defineProperty(SplitAreaDirective.prototype, "visible", {
                    get: function () {
                        return this._visible;
                    },
                    set: function (v) {
                        this.visibility = v ? "block" : "none";
                        this._visible = v;
                        if (this.visible) {
                            this.split.showArea(this);
                        }
                        else {
                            this.split.hideArea(this);
                        }
                    },
                    enumerable: false,
                    configurable: true
                });
                SplitAreaDirective.prototype.ngOnInit = function () {
                    this.split.addArea(this, this._order, this._size, this._minSizePixel);
                };
                SplitAreaDirective.prototype.lockEvents = function () {
                    this.eventsLockFct.push(this.renderer.listen(this.elementRef.nativeElement, 'selectstart', function (e) { return false; }));
                    this.eventsLockFct.push(this.renderer.listen(this.elementRef.nativeElement, 'dragstart', function (e) { return false; }));
                };
                SplitAreaDirective.prototype.unlockEvents = function () {
                    while (this.eventsLockFct.length > 0) {
                        var fct = this.eventsLockFct.pop();
                        if (fct) {
                            fct();
                        }
                    }
                };
                SplitAreaDirective.prototype.setStyle = function (key, value) {
                    this.renderer.setStyle(this.elementRef.nativeElement, key, value);
                };
                SplitAreaDirective.prototype.ngOnDestroy = function () {
                    this.split.removeArea(this);
                };
                SplitAreaDirective.prototype.onTransitionEnd = function (evt) {
                    if (evt.propertyName === 'flex-basis')
                        this.split.notify('visibleTransitionEnd');
                };
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Number),
                    __metadata("design:paramtypes", [Number])
                ], SplitAreaDirective.prototype, "order", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Object),
                    __metadata("design:paramtypes", [Object])
                ], SplitAreaDirective.prototype, "size", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Number),
                    __metadata("design:paramtypes", [Number])
                ], SplitAreaDirective.prototype, "minSizePixel", null);
                __decorate([
                    core_1.Input(),
                    __metadata("design:type", Boolean),
                    __metadata("design:paramtypes", [Boolean])
                ], SplitAreaDirective.prototype, "visible", null);
                SplitAreaDirective = __decorate([
                    core_1.Directive({
                        selector: 'split-area',
                        host: {
                            '[style.flex-grow]': '"0"',
                            '[style.flex-shrink]': '"0"',
                            '[style.overflow-x]': '"auto"',
                            '[style.overflow-y]': '"auto"',
                            '[style.height]': '"100%"',
                            '[class.hided]': '!visible',
                            '(transitionend)': 'onTransitionEnd($event)'
                        }
                    }),
                    __metadata("design:paramtypes", [core_1.ElementRef,
                        core_1.Renderer2,
                        split_component_1.SplitComponent])
                ], SplitAreaDirective);
                return SplitAreaDirective;
            }());
            exports_1("SplitAreaDirective", SplitAreaDirective);
        }
    };
});
//# sourceMappingURL=splitArea.directive.js.map