﻿import { Injectable } from '@angular/core';
import { Router, NavigationStart } from '@angular/router';
import { Observable } from 'rxjs';
import { Subject } from 'rxjs';
import { ReplaySubject } from 'rxjs';
import { TranslateService } from "@ngx-translate/core";
import { Modal } from "ngx-modialog-7/plugins/bootstrap";
import { BroadcastEventTypeEnumDTO, BroadcastEventDTO } from "../../data/model/models";
import { Message } from './message';
import { DialogRef } from 'ngx-modialog-7';

@Injectable()
export class AlertService {
  private subject = new Subject<any>();
  private replaySubject = new ReplaySubject<any>(1000);
  private keepAfterNavigationChange = false;
  private modalAlertRef: DialogRef<any>;
  private modalPosition: number = 0;

  constructor(private router: Router, private modal: Modal, private translate: TranslateService) {
    // clear alert message on route change
    router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (this.keepAfterNavigationChange) {
          // only keep for a single location change
          this.keepAfterNavigationChange = false;
        } else {
          // clear alert
          this.subject.next("");
        }
      }
    });
  }

  public show(broadcastEventData: BroadcastEventDTO, keepAfterNavigationChange: boolean = false, alertTimeout: number = -1): void {
    try {
      this.keepAfterNavigationChange = keepAfterNavigationChange;

      if (broadcastEventData.messageKey == "" && broadcastEventData.messageText == "")
        return;

      if (broadcastEventData.messageKey != "" && broadcastEventData.parameters) {
        this.translate.get(broadcastEventData.messageKey, broadcastEventData.parameters).subscribe(messageText => {
          this.showBase(broadcastEventData, messageText, alertTimeout);
        });
      }
      else if (broadcastEventData.messageKey != "" && !broadcastEventData.parameters) {
        this.translate.get(broadcastEventData.messageKey).subscribe(messageText => {
          this.showBase(broadcastEventData, messageText, alertTimeout);
        });
      }
      else if (broadcastEventData.messageText != "") {
        this.showBase(broadcastEventData, broadcastEventData.messageText, alertTimeout);
      }
    } catch (err) {
      console.log(err);
    }
  }

  public info(message: string, keepAfterNavigationChange = false, destination: messageLogMask = messageLogMask.statusBar, alertTimeout: number = -1): void {
    let broadcastEvent: BroadcastEventDTO = { messageKey: message, messageLogMask: destination, messageType: BroadcastEventTypeEnumDTO.MessageInfo };
    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
  }

  public success(message: string, keepAfterNavigationChange = false, destination: messageLogMask = messageLogMask.statusBar, alertTimeout: number = -1): void {
    let broadcastEvent: BroadcastEventDTO = { messageKey: message, messageLogMask: destination, messageType: BroadcastEventTypeEnumDTO.MessageSuccess };
    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
  }

  public warning(message: string, keepAfterNavigationChange = false, destination: messageLogMask = messageLogMask.statusBar, alertTimeout: number = -1): void {
    let broadcastEvent: BroadcastEventDTO = { messageKey: message, messageLogMask: destination, messageType: BroadcastEventTypeEnumDTO.MessageWarning };
    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
  }

  public error(message: string, errorDetail: any = null , keepAfterNavigationChange = false, destination: messageLogMask = messageLogMask.statusBar, alertTimeout: number = -1): void {
    let broadcastEvent: BroadcastEventDTO = { messageKey: message, messageLogMask: destination, messageType: BroadcastEventTypeEnumDTO.MessageError, parameters: errorDetail };
    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
  }

  public debug(message: string, keepAfterNavigationChange = false, destination: messageLogMask = messageLogMask.statusBar, alertTimeout: number = -1): void {
    let broadcastEvent: BroadcastEventDTO = { messageKey: message, messageLogMask: destination, messageType: BroadcastEventTypeEnumDTO.MessageDebug };
    this.show(broadcastEvent, keepAfterNavigationChange, alertTimeout);
  }

  public getMessage(): Observable<any> {
    return this.subject.asObservable();
  }

  public getLogMessage(): Observable<any> {
    return this.replaySubject.asObservable();
  }

  public clearMessage(): void {
    this.subject.next("");
  }

  private showBase(broadcastEventData: BroadcastEventDTO, messageText: string, alertTimeout: number = -1) {
    try {
      if ((broadcastEventData.messageLogMask & messageLogMask.alertPopup) == messageLogMask.alertPopup) {
        let popupTitle: string = "";
        let popupContent: string = "";
        if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageInfo) {
          popupTitle = "TR_INFORMATION";
          popupContent = `<div class="panel panel-primary"><div class="panel-heading scroll-panel"><div class="glyphicon glyphicon-info-sign"></div>&nbsp;&nbsp;` + messageText + `</div></div>`;
        }
        if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageWarning) {
          popupTitle = "TR_WARNING";
          popupContent = `<div class="panel panel-warning"><div class="panel-heading scroll-panel"><div class="glyphicon glyphicon-exclamation-sign"></div>&nbsp;&nbsp;` + messageText + `</div></div>`;
        }
        if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageError) {
          popupTitle = "TR_ERROR";
          popupContent = `<div class="panel panel-danger"><div class="panel-heading scroll-panel"><div class="glyphicon glyphicon-warning-sign"></div>&nbsp;&nbsp;` + messageText + `</div></div>`;
        }
        if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageSuccess) {
          popupTitle = "TR_SUCCESS";
          popupContent = `<div class="panel panel-success"><div class="panel-heading scroll-panel"><div class="glyphicon glyphicon-ok-sign"></div>&nbsp;&nbsp;` + messageText + `</div></div>`;
        }
        if (!this.modalAlertRef || this.modalAlertRef.context.body.substring(0, this.modalAlertRef.context.body.indexOf("<div class=\"timestamp\">")) != popupContent) {
          if (this.modalPosition==6)
            this.modalPosition = 0
          this.modalPosition++;
          this.modalAlertRef = this.modal.alert()
            .size('lg')
            .showClose(true)
            .title(this.translate.instant(popupTitle))
            .okBtn(this.translate.instant('TR_CLOSE'))
            .okBtnClass('btn btn-default')
            .dialogClass('modal-dialog modalPosition' + this.modalPosition.toString())
            .body(popupContent + `<div class="timestamp">UTC:&nbsp;` + broadcastEventData.messageTime + `</div>`)
            .isBlocking(true)
            .open()
          this.modalAlertRef.result.then(data => {
            this.modalAlertRef = null;
            if (this.modalPosition>1)
              this.modalPosition--;
          });
        }
      }
      if ((broadcastEventData.messageLogMask & messageLogMask.statusBar) == messageLogMask.statusBar) {
        if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageInfo) {
          this.subject.next(new Message(messageText, 'info', null));
          if (alertTimeout > 0)
            setTimeout(() => { this.clearMessage(); }, alertTimeout);
          else if (alertTimeout == -1)
            setTimeout(() => { this.clearMessage(); }, 5000);
        }
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageWarning) {
          this.subject.next(new Message(messageText, 'warning', null));
          if (alertTimeout > 0)
            setTimeout(() => { this.clearMessage(); }, alertTimeout);
          else if (alertTimeout == -1)
            setTimeout(() => { this.clearMessage(); }, 20000);
        }
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageError) {
          this.subject.next(new Message(messageText, 'error', null));
          if (alertTimeout > 0)
            setTimeout(() => { this.clearMessage(); }, alertTimeout);
          else if (alertTimeout == -1)
            setTimeout(() => { this.clearMessage(); }, 20000);
        }
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageSuccess) {
          this.subject.next(new Message(messageText, 'success', null));
          if (alertTimeout > 0)
            setTimeout(() => { this.clearMessage(); }, alertTimeout);
          else if (alertTimeout == -1)
            setTimeout(() => { this.clearMessage(); }, 5000);
        }
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageDebug) {
          console.log(new Date().toUTCString() + ": " +  messageText);
        }
      }
      if ((broadcastEventData.messageLogMask & messageLogMask.eventLog) == messageLogMask.eventLog) {
        if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageInfo)
          this.replaySubject.next(new Message(messageText, 'TR_INFORMATION', broadcastEventData.messageTime));
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageWarning)
          this.replaySubject.next(new Message(messageText, 'TR_WARNING', broadcastEventData.messageTime));
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageError)
          this.replaySubject.next(new Message(messageText, 'TR_ERROR', broadcastEventData.messageTime));
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageSuccess)
          this.replaySubject.next(new Message(messageText, 'TR_SUCCESS', broadcastEventData.messageTime));
        else if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.MessageDebug) {
          this.replaySubject.next(new Message(messageText, 'TR_DEBUG', broadcastEventData.messageTime));
        }
      }
    } catch (err) {
      console.log(err);
    }
  }
}

export const enum messageLogMask {
  eventLog = 1,
  alertPopup = 2,
  statusBar = 4
}
