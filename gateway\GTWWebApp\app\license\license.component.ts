﻿import { Component, OnInit, ViewChild} from "@angular/core";
import { ConfigService, FileService } from "../data/api/api";
import { AlertService, messageLogMask } from "../modules/alert/alert.service";
import { Column } from "../modules/grid/column";
import { AuthenticationService } from "../authentication/authentication.service";
import { TranslateService } from "@ngx-translate/core";
import { KeysPipe } from "../global/keys.pipe";
import { LoaderService } from "../modules/loader/loader.service";
import { DownloadComponent } from "../modules/download/download.component";

@Component({
  selector: "logMonitorComponent",
  providers: [KeysPipe],
  templateUrl: "app/license/license.component.template.html",
  styles: [`
    td {
      padding: 10px;
    }
    .table>tr>td,
    .table>tr>th{
		  padding: 4px !important;
      vertical-align: middle !important;
      line-height: 22px;
    }
    .table>tr:hover{
      background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
    }
    .table{
      border: 1px solid lightgray;
    }
    .table-striped>tr:nth-child(odd)>td, 
    .table-striped>tr:nth-child(odd)>th {
      background-color: rgba(0, 0, 0, 0.03) !important;
    }
    legend {
      width: auto;
      margin-bottom: 0px;
      font-size: 12px;
      border-bottom: none;
    }
    fieldset {
      padding: 4px;
      margin-left: -4px;
      border: 1px solid lightgray;
  }
  `]
})
export class LicenseComponent implements OnInit {
  @ViewChild("uploadV2CFile", { static: false }) uploadV2CFile: any;
  @ViewChild("download", { static: false }) download: DownloadComponent;
  private licenseInfo: any;
  private columns: Array<Column>;
  private rows: Array<any>;
  private licenceForm: LicenceForm = new LicenceForm(null, "", false);
  private logFileNameList: string[];

  constructor(private alertService: AlertService, private authenticationService: AuthenticationService, private configService: ConfigService, private fileService: FileService, private translate: TranslateService, private keysPipe: KeysPipe, private loaderService: LoaderService) {
    this.columns = this.getColumns();
  }

  public ngOnInit(): void {
    this.licenceForm.isNewLicense = true;
    this.refreshLicense();
  }

  public refreshLicense(): void {
    this.configService.getLicense().subscribe(
      data => {
        this.licenseInfo = data;
        this.rows = this.licenseInfo.features
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("LICENSE.ERROR_CAN_T_READ_LICENSE"); }
      }
    );
  }

  private isNumber(val): boolean{
    return typeof val === 'number';
  }

  private isChecked(val): boolean {
    return val=="1"?true:false;
  }

  private getColumns(): Array<Column> {
    return [
      new Column("feature", "TR_OPTIONS", "", ""),
      new Column("isLicensed", "TR_IS_LICENSED", "", "")
    ];
  }

  private onStopMonClick(): void {
    this.configService.stopMon(true).subscribe(
      data => {
        this.alertService.success("TR_MONITOR_IS_RE_STARTING");
        this.loaderService.toggleIsLoading(true);
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); }
      }
    );
  }

  private onlineActivation(): void {
    if (this.licenceForm.product_key != "") {
      this.licenceForm.action_type = "onlineActivation"
      this.configService.saveLicense(this.licenceForm.action_type, this.licenceForm.isNewLicense, this.licenceForm.product_key).subscribe(
        (data: any) => {
          if (data != null && data != "") {
            let result = data.result;
            if (result) {
              this.alertService.success("TR_SUCCESS");
              this.onStopMonClick();
            }
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error(error.error, null, null, messageLogMask.alertPopup + messageLogMask.eventLog + messageLogMask.statusBar); }
        }
      );
    }
  }

  private offlineActivationStep1(): void {
    this.licenceForm.action_type = "offlineActivationStep1"
    this.configService.saveLicense(this.licenceForm.action_type, this.licenceForm.isNewLicense).subscribe(
      (data: any) => {
        if (data != null && data != "") {
          let result = data.result;
          let fileC2V = data.C2V;
          if (result && fileC2V != "") {
            let blob = new Blob([fileC2V], { type: "text/csv" });
            let url = window.URL.createObjectURL(blob);
            this.saveData(blob, "license.c2v");
            this.alertService.success("TR_SUCCESS");
          }
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_SAVE_LICENSE"); }
      }
    );
  }

  private offlineActivationStep2(event): void {
    let fileList: FileList = event.target.files;
    if (fileList.length > 0) {
      let file: File = fileList[0];
      this.uploadV2CFile.nativeElement.value = "";
      //let formData: FormData = new FormData();
      //formData.append("uploadFile", file, file.name);
      this.configService.sendV2CLicense(file).subscribe(
        (data: any) => {
          if (data != null && data != "") {
            let result = data.result;
            if (result) {
              this.alertService.success("TR_SUCCESS");
              this.onStopMonClick();
            }
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_CAN_T_SAVE_LICENSE"); }
        }
      );
    }
  }		

  private onlogFileDownloadClick(): void {
    this.fileService.licenseLogZipFileGet().subscribe(
      (data: any) => {
        this.saveData(data, "LicenseLogs.zip");
        this.alertService.success("TR_FILE_DOWNLOADED");
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_FILE_NOT_DOWNLOADED"); }
      }
    );
  }

  private saveData(blob, fileName): void {
    let a = document.createElement("a");
    document.body.appendChild(a);
    let url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
}
class LicenceForm {
  constructor(
    public action_type: string,
    public product_key: string,
    public isNewLicense: boolean,
  ) { }
}