import { Component, Input, Output, EventEmitter } from "@angular/core";
import { Node } from './node';

@Component({
  selector: "dynamicTreeviewComponent",
  styles: [`
		  .iconButton {
			  display: inline-block; 
			  margin-right: 5px;
			  cursor: pointer;
			  color:#ecc96a;
		  }
		  .selectNode {

		  }
		  ul {
			  padding-left: 12px;
			  list-style-type: none;
		  }
		  li{
        padding-left: 2px;
			  margin-top: 4px;
		  }
		  ol, ul {
			  margin-top: 0;
		  }
      .leaf:hover{
        background-image: linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35));
        font-weight: bold;
      }
      .leaf{
			  cursor: pointer;
      }
      .glyphicon-none {
        padding-right: 12px;  
        color: transparent !important;
      }
  `],
  template: `
    <ul>
      <ng-container *ngIf="!isCheckbox">  
			  <div *ngIf="!node.hasChildren" class="glyphicon-none iconButton"></div>
			  <div *ngIf="node.hasChildren && node.isExpanded" class="glyphicon glyphicon-minus iconButton" title="{{TR_COLLAPSE_CHILDREN | translate}}" (click)="nodeToggle(node)"></div>
			  <div *ngIf="node.hasChildren && !node.isExpanded" class="glyphicon glyphicon-plus iconButton" title="{{TR_EXPAND_CHILDREN | translate}}" (click)="nodeToggle(node)"></div>
        <span (click)="clickAction(node.nodeFullName)" [ngClass]="{'is-selected': (node.nodeFullName === this.selectedNodeFullName)}" class="leaf">{{node.nodeName}}</span>
      </ng-container>
      <ng-container *ngIf="isCheckbox">    
			  <div *ngIf="!node.hasChildren" class="glyphicon-none iconButton"></div>
			  <div *ngIf="node.hasChildren && node.isExpanded" class="glyphicon glyphicon-minus iconButton" title="{{TR_COLLAPSE_CHILDREN | translate}}" (click)="nodeToggle(node)"></div>
			  <div *ngIf="node.hasChildren && !node.isExpanded" class="glyphicon glyphicon-plus iconButton" title="{{TR_EXPAND_CHILDREN | translate}}" (click)="nodeToggle(node);"></div>
        <span><input type="checkbox" *ngIf="node.displayCheckbox" [checked]="node.checked" (click)="node.check()"/>&nbsp;{{node.nodeName}}</span>
      </ng-container>
      <ng-container *ngIf="node.isExpanded">
        <li *ngFor="let nodeChildren of node.children">
          <dynamicTreeviewComponent [node]="nodeChildren" [isCheckbox]="isCheckbox" [(selectedNodeFullName)]="selectedNodeFullName" (selectedNodeFullNameChange)="clickAction($event)" (selectedNodeLoadChildren)="nodeToggle($event)"></dynamicTreeviewComponent>
        </li>
      </ng-container>
    </ul>
    `
})

export class DynamicTreeviewComponent {
  @Input() isCheckbox;
  @Input() node: Node;
  @Input() selectedNodeFullName;
  @Output() selectedNodeFullNameChange: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectedNodeLoadChildren: EventEmitter<Node> = new EventEmitter<Node>();

  private clickAction(selectedNodeFullName: string): void {
    this.selectedNodeFullName = selectedNodeFullName;
    this.selectedNodeFullNameChange.emit(this.selectedNodeFullName);
  } 
  private nodeToggle(node: Node): void {
    if (node.children.length === 0)
      this.selectedNodeLoadChildren.emit(node);
    else
      node.toggle();
  } 
}