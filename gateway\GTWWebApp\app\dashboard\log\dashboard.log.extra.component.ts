import { Component, Output, <PERSON>E<PERSON>ter, OnInit, On<PERSON>estroy, AfterViewInit, ViewChildren, ViewChild, QueryList, ElementRef } from "@angular/core";
import { FormControl } from '@angular/forms';
import { LogService } from "../../data/api/api";
import { BroadcastEventWSApi } from "../../data/wsApi/wsApi";
import { LogConfigMaskDTO, LogDeviceGetDTO, LogDeviceDTO } from "../../data/model/models";
import { Subscription } from "rxjs"
import { BroadcastEventTypeEnumDTO } from "../../data/model/models";
import { AuthenticationService } from "../../authentication/authentication.service";
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { Bitmask } from "../../modules/bitmask/bitmask.component";
import { TabComponent } from "../../modules/tab/tab.component";
import { GlobalJsonUtil } from "../../global/global.json.util";

@Component({
  selector: "dashboardLogExtraComponent",
  styles: [`
    .image-button-sm {
      width: 12px;
      height: 12px;
      filter: drop-shadow(1px 1px 1px #404040);
    }
    .round-button-sm {
      display: inline-block;
      min-height: 18px;
      max-height: 18px;
      min-width: 18px;
      max-width: 18px;
      padding-bottom: 1px;
      text-align: center;
      line-height: 15px;
      border: 1px solid gray;
      border-radius: 50%;
      background-color: #ebebeb;
      filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .7));
      cursor: pointer;
    }

    .round-button-sm:hover {
      background-color: #cdcdcd;
    }
    .filter-device  {
      margin: 0;
      padding: 0;
      margin-left: 10px;
      list-style: none;
      max-height: 100px;
      overflow: auto;
    }
    .filter-checkbox  {
      margin: 0; 
      padding: 0; 
      margin-left: 20px; 
      list-style: none; 
    }
    .filter-checkbox li { 
      border: 1px transparent solid; 
      display:inline-block;
      width:280px;
    } 
    legend {
      font-size: 12px;
      margin: 2px;
      border: 0;
      width: auto;
		}
    fieldset {
      padding: 4px;
      border: 1px solid silver;
		}`
  ],
  template: `
      <div style="padding: 4px;">
        <fieldset>
          <legend>{{'TR_DEVICE_LOGGED' | translate}}</legend>
          <ul class="filter-device">
            <li *ngFor="let logDevice of logDevices;"><label><div class="round-button-sm" title="{{'TR_REMOVE' | translate}}" (click)="removeNodeToLogFilter(logDevice.name)" ><img [src]="'../../images/delete.svg'" class="image-button-sm"/></div>&nbsp;&nbsp;{{logDevice.name}}</label></li>
          </ul>
          <div style="width:99%; text-align: right;"><button class="btn btn-default btn-sm" (click)="removeAllDevice()"><img src="../../images/delete.svg" class="image-button"/>&nbsp;{{'TR_REMOVE_ALL' | translate}}</button></div>
        </fieldset>
      </div>
      <tabset (onSelect)="tabFilterOnChange($event)">
        <tab title="{{'TR_SDG_FILTER' | translate}}">
          <div *ngIf="isDataLoaded" style="margin: 6px;">
            <span>{{'TR_GLOBAL_MESSAGE_SEVERITY' | translate}}</span>: <div style="display: inline-table;"><comboboxEditorMultiselectComponent [componentData]="severitymaskComponentData" [(editorFieldcontrolValue)]="editorSDGSeverityFieldcontrolValue" (OnComboboxChange)="setLogVerboseFilter($event, logConfigMaskSDG)"></comboboxEditorMultiselectComponent></div>
          </div>
          <div class="container-fluid">
            <div class="row" style="max-height: 330px;overflow-x: hidden;overflow-y: auto; margin: 0px;">
              <div style="padding: 4px;" class="col-md-3">
                <fieldset>
                  <legend>{{'TR_SDG_OPC' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of optionSDGCategoryList.slice(0,4);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMaskSDG)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
              <div style="padding: 4px;" class="col-md-3">
                <fieldset>
                  <legend>{{'TR_SDG_SCL' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of optionSDGCategoryList.slice(4,7);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMaskSDG)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
              <div style="padding: 4px;" class="col-md-3">
                <fieldset>
                  <legend>{{'TR_SDG_MMS' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of optionSDGCategoryList.slice(7,9);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMaskSDG)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
              <div style="padding: 4px;" class="col-md-3">
                <fieldset>
                  <legend>{{'TR_SDG_OTHER' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of optionSDGCategoryList.slice(9,12);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMaskSDG)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
            </div>
          </div>
        </tab>
        <tab title="{{'TR_SCL_FILTER' | translate}}">
          <div *ngIf="isDataLoaded" style="margin: 6px;">
            <span>{{'TR_SCL_MESSAGE_SEVERITY' | translate}}</span>: <div style="display: inline-table;"><comboboxEditorMultiselectComponent [componentData]="severitymaskComponentData" [(editorFieldcontrolValue)]="editorSCLSeverityFieldcontrolValue" (OnComboboxChange)="setLogVerboseFilter($event, logConfigMaskSCL)"></comboboxEditorMultiselectComponent></div>
          </div>
          <div class="container-fluid">
            <div class="row" style="max-height: 330px;overflow-x: hidden;overflow-y: auto; margin: 0px;">
              <div style="padding: 4px;" class="col col-sm-6">
                <fieldset>
                  <legend>{{'TR_SCL_PROTOCOL_LAYER' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of optionSCLCategoryList.slice(0,7);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMaskSCL)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
              <div style="padding: 4px;" class="col col-sm-6">
                <fieldset>
                  <legend>{{'TR_SCL_DATABASE' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of optionSCLCategoryList.slice(7,15);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMaskSCL)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
            </div>
          </div>
        </tab>
        <tab title="{{'TR_6T_FILTER' | translate}}">
          <div *ngIf="isDataLoaded" style="margin: 6px;">
            <span>{{'TR_MMS_MESSAGE_SEVERITY' | translate}}</span>: <div style="display: inline-table;"><comboboxEditorMultiselectComponent [componentData]="severitymaskComponentData" [(editorFieldcontrolValue)]="editor6TSeverityFieldcontrolValue" (OnComboboxChange)="setLogVerboseFilter($event, logConfigMask6T)"></comboboxEditorMultiselectComponent></div>
          </div>
          <div class="container-fluid">
            <div class="row" style="max-height: 330px;overflow-x: hidden;overflow-y: auto; margin: 0px;">
              <div style="padding: 4px;" class="col-sm-6">
                <fieldset>
                  <legend>{{'TR_6T_LOW_LEVEL_STACK' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of option6TCategoryList.slice(0,14);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMask6T)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
              <div style="padding: 4px;" class="col-sm-6">
                <fieldset>
                  <legend>{{'TR_6T_STANDARD_STACK' | translate}}</legend>
                  <ul class="filter-checkbox"> 
                    <li *ngFor="let logFilter of option6TCategoryList.slice(14,28);"><input type="checkbox" [checked]=logFilter.isChecked (change)="logFilterOnChange(logFilter, logConfigMask6T)"/><label for="cb1">&nbsp;{{logFilter.name | translate}}</label></li> 
                  </ul> 
                </fieldset>
              </div>
            </div>
          </div>
        </tab>
      </tabset>
`
})

//TODO
// BroadcastEvent logFilterchange
// Filter Buttons 

export class DashboardLogExtraComponent implements OnInit, OnDestroy, AfterViewInit {
  private isDataLoaded: boolean = false;
  private globalBroadcastServiceSubscription: Subscription;
  private logConfigMaskSDG: LogConfigMaskDTO;
  private logConfigMaskSCL: LogConfigMaskDTO;
  private logConfigMask6T: LogConfigMaskDTO;
  private optionSDGCategoryList: Bitmask[] = [];
  private optionSCLCategoryList: Bitmask[] = [];
  private option6TCategoryList: Bitmask[] = [];
  private logDevices: Array<LogDeviceGetDTO> = [];
  @ViewChildren(TabComponent) tabLogFilters: QueryList<TabComponent>;
  @ViewChild('checkboxSDGSaveLogToTextFile') checkboxSDGSaveLogToTextFile: ElementRef;
  private severitymaskComponentData: SeverityMaskComponentData[] = [];
  private editorSDGSeverityFieldcontrolValue: string = "";
  private editor6TSeverityFieldcontrolValue: string = "";
  private editorSCLSeverityFieldcontrolValue: string = "";
  constructor(private logService: LogService, private alertService: AlertService, private authenticationService: AuthenticationService, private translateService: TranslateService, private broadcastEventWSApi: BroadcastEventWSApi) { }

  public ngOnInit(): void {
    this.getLogFilterConfig();
    this.displayNodeToLogFilter();
    this.optionSeverityMaskComponentDataOnInit();
    this.openGlobalBroadcastServiceSubscription();
  }

  public ngOnDestroy(): void {
    if (this.globalBroadcastServiceSubscription != null)
      this.globalBroadcastServiceSubscription.unsubscribe();
  }

  public ngAfterViewInit(): void {
    let selectedTabLogFilterLS = localStorage.getItem("SDGSelectedTabLogFilter");
    if (selectedTabLogFilterLS) {
      let selectedTabLogFilter = this.tabLogFilters.filter(tab => tab.title == selectedTabLogFilterLS)[0];
      if (selectedTabLogFilter) {
        this.tabLogFilters.first.active = false; //the first one is activated by default
        selectedTabLogFilter.active = true;
      }
    }
  }

  private openGlobalBroadcastServiceSubscription(): void {
    this.globalBroadcastServiceSubscription = this.broadcastEventWSApi.getBroadcastEventData().subscribe(
      event => {
        if (event.type === 'message') {
          let broadcastEventData = JSON.parse(event.data);
          if (broadcastEventData.messageType === BroadcastEventTypeEnumDTO.RefreshLogParameter.toString()) {
            this.getLogFilterConfig();
            this.displayNodeToLogFilter();
          }
        }
      }
    );
  }

  private getLogFilterConfig(): void {
    this.logService.getLogFilterConfig().subscribe(
      data => {
        this.logConfigMaskSDG = data.filter(logConfig => logConfig.source == Source.SDG)[0];
        this.optionSDGMaskCategoryOnInit();
        this.getLogVerboseFilter(Source.SDG, this.logConfigMaskSDG);
        for (let i in this.optionSDGCategoryList) {
          if (this.optionSDGCategoryList[i].value & this.logConfigMaskSDG.categorymask)
            this.optionSDGCategoryList[i].isChecked = true;
          else
            this.optionSDGCategoryList[i].isChecked = false;
        }

        this.logConfigMaskSCL = data.filter(logConfig => logConfig.source == Source.SCL)[0];
        this.optionSCLMaskCategoryOnInit();
        this.getLogVerboseFilter(Source.SCL, this.logConfigMaskSCL);
        for (let i in this.optionSCLCategoryList) {
          if (this.optionSCLCategoryList[i].value & this.logConfigMaskSCL.categorymask)
            this.optionSCLCategoryList[i].isChecked = true;
          else
            this.optionSCLCategoryList[i].isChecked = false;
        }

        this.logConfigMask6T = data.filter(logConfig => logConfig.source == Source._6T)[0];
        this.option6TMaskCategoryOnInit();
        this.getLogVerboseFilter(Source._6T, this.logConfigMask6T);
        for (let i in this.option6TCategoryList) {
          if (this.option6TCategoryList[i].value & this.logConfigMask6T.categorymask)
            this.option6TCategoryList[i].isChecked = true;
          else
            this.option6TCategoryList[i].isChecked = false;
        }
      },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      },
      () => { this.isDataLoaded = true; }
    );
  }

  private displayNodeToLogFilter(): void {
    this.logService.getLogFilterDevice()
      .subscribe(
        data => {
          if (data) {
            this.logDevices = data;
            this.logDevices = GlobalJsonUtil.sortByProperty(this.logDevices, "name");
          }
          else {
            this.logDevices = [];
          }
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR"); }
        }
      );
  }

  private removeAllDevice(): void {
    let logDevices: Array<LogDeviceDTO> = [];
    this.logDevices.forEach(logDevice => {
      logDevices.push({ name: logDevice.name, add: false });
    });
    this.logService.putLogFilterDevice(logDevices)
      .subscribe(
        data => {
          this.alertService.success("TR_NODES_REMOVED");
          this.logDevices = [];
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR"); }
        }
      );
  }

  public removeNodeToLogFilter(nodeFullName: string): void {
    let logDevices: Array<LogDeviceDTO> = [];
    logDevices.push({ name: nodeFullName, add: false });
    this.logService.putLogFilterDevice(logDevices)
      .subscribe(
        data => {
          this.alertService.success("TR_NODE_REMOVED");
        },
        error => {
          if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR"); }
        }
      );
  }

  private tabFilterOnChange(tab: TabComponent): void {
    if (tab.active == true) {
      localStorage.setItem("SDGSelectedTabLogFilter", tab.title);
    }
  }

  private setSaveLogToTextFile(e: any): void {
    this.logService.putMirrorAllToLogFile(e.target.checked).subscribe(
      data => { },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      }
    );
  }

  private setLogVerboseFilter(editorSeverityFieldcontrolValue: string, logConfigMask: LogConfigMaskDTO): void {
    logConfigMask.severitymask = 0;
    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Exception)]) != -1)
      logConfigMask.severitymask |= Severity.Exception;
    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Error)]) != -1)
      logConfigMask.severitymask |= Severity.Error;
    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Warning)]) != -1)
      logConfigMask.severitymask |= Severity.Warning;
    if (editorSeverityFieldcontrolValue.indexOf(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Information)]) != -1)
      logConfigMask.severitymask |= Severity.Information;
    this.saveLogFilterConfig(logConfigMask);
  }

  private getLogVerboseFilter(sourceSeverity: Source, logConfigMask: LogConfigMaskDTO): void {
    let logConfigMaskValue: string[] = [];
    if ((Severity.Exception & logConfigMask.severitymask) == Severity.Exception)
      logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Exception)]);
    if ((Severity.Error & logConfigMask.severitymask) == Severity.Error)
      logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Error)]);
    if ((Severity.Warning & logConfigMask.severitymask) == Severity.Warning)
      logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Warning)]);
    if ((Severity.Information & logConfigMask.severitymask) == Severity.Information)
      logConfigMaskValue.push(Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Information)]);

    if (sourceSeverity == Source.SDG)
      this.editorSDGSeverityFieldcontrolValue = JSON.stringify(logConfigMaskValue);
    if (sourceSeverity == Source.SCL)
      this.editorSCLSeverityFieldcontrolValue = JSON.stringify(logConfigMaskValue);
    if (sourceSeverity == Source._6T)
      this.editor6TSeverityFieldcontrolValue = JSON.stringify(logConfigMaskValue);
  }

  private saveLogFilterConfig(logConfigMask: LogConfigMaskDTO): void {
    let logConfigMasks: Array<LogConfigMaskDTO> = [];
    logConfigMasks.push(logConfigMask);
    this.logService.putLogFilterConfig(logConfigMasks).subscribe(
      data => { },
      error => {
        if (error.status == 401) { this.authenticationService.onLoginFailed("/"); } else { this.alertService.error("TR_ERROR_COMMAND_FAILED", { ERROR: error.error }); }
      }
    );
  }

  private logFilterOnChange(logFilter: Bitmask, logConfigMask: LogConfigMaskDTO): void {
    logFilter.isChecked = !logFilter.isChecked;
    if (logFilter.isChecked)
      logConfigMask.categorymask = logConfigMask.categorymask | logFilter.value;
    else
      logConfigMask.categorymask = logConfigMask.categorymask & (~(logFilter.value));

    this.saveLogFilterConfig(logConfigMask);
  }

  private optionSDGMaskCategoryOnInit(): void {
    if (this.optionSDGCategoryList.length == 0) {
      //OPC
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC", value: 0x00000020, description: "TR_SDG_CATEGORY_OPC", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC_SU", value: 0x00000040, description: "TR_SDG_CATEGORY_OPC_SU", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC_UA", value: 0x00000080, description: "TR_SDG_CATEGORY_OPC_UA", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_OPC_DEEP", value: 0x00000100, description: "TR_SDG_CATEGORY_OPC_DEEP", isChecked: false });
      //SCL
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_DNP", value: 0x00000200, description: "TR_SDG_CATEGORY_DNP", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_870", value: 0x00000400, description: "TR_SDG_CATEGORY_870", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_MODBUS", value: 0x00008000, description: "TR_SDG_CATEGORY_MODBUS", isChecked: false });
      //MMS
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_61850", value: 0x00000800, description: "TR_SDG_CATEGORY_61850", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_TASE2", value: 0x00001000, description: "TR_SDG_CATEGORY_TASE2", isChecked: false });
      //OTHER
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_ODBC", value: 0x00002000, description: "TR_SDG_CATEGORY_ODBC", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_EQUATION", value: 0x00020000, description: "TR_SDG_CATEGORY_EQUATION", isChecked: false });
      this.optionSDGCategoryList.push({ name: "TR_SDG_CATEGORY_POINTMAP", value: 0x00080000, description: "TR_SDG_CATEGORY_POINTMAP", isChecked: false });
    }
  }

  private optionSCLMaskCategoryOnInit(): void {
    if (this.optionSCLCategoryList.length == 0) {
      //SCL PROTOCOL LAYER
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_+++USER", value: 0x00000010, description: "TR_SCL_CATEGORY_+++USER", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_===APPLICATION", value: 0x00000008, description: "TR_SCL_CATEGORY_===APPLICATION", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_~~~TRANSPORT", value: 0x00000004, description: "TR_SCL_CATEGORY_~~~TRANSPORT", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_---DATA_LINK", value: 0x00000002, description: "TR_SCL_CATEGORY_---DATA_LINK", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_...PHYSICAL", value: 0x00000001, description: "TR_SCL_CATEGORY_...PHYSICAL", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_MMI", value: 0x00000020, description: "TR_SCL_CATEGORY_MMI", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_TARGET", value: 0x00080000, description: "TR_SCL_CATEGORY_TARGET", isChecked: false });
      //SCL DATABASE
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_STATIC_DATA", value: 0x00000080, description: "TR_SCL_CATEGORY_STATIC_DATA", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_STATIC_HDRS", value: 0x00000100, description: "TR_SCL_CATEGORY_STATIC_HDRS", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_EVENT_DATA", value: 0x00000200, description: "TR_SCL_CATEGORY_EVENT_DATA", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_EVENT_HDRS", value: 0x00000400, description: "TR_SCL_CATEGORY_EVENT_HDRS", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_CYCLIC_DATA", value: 0x00000800, description: "TR_SCL_CATEGORY_CYCLIC_DATA", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_CYCLIC_HDRS", value: 0x00001000, description: "TR_SCL_CATEGORY_CYCLIC_HDRS", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_SECURITY_DATA", value: 0x00002000, description: "TR_SCL_CATEGORY_SECURITY_DATA", isChecked: false });
      this.optionSCLCategoryList.push({ name: "TR_SCL_CATEGORY_SECURITY_HDRS", value: 0x00004000, description: "TR_SCL_CATEGORY_SECURITY_HDRS", isChecked: false });
    }
  }

  private option6TMaskCategoryOnInit(): void {
    if (this.option6TCategoryList.length == 0) {
      //LOW LEVEL STACK
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_SCL", value: 0X00000001, description: "TR_6T_CATEGORY_C_SCL", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_STACK", value: 0X00000002, description: "TR_6T_CATEGORY_C_STACK", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CASM", value: 0X00000004, description: "TR_6T_CATEGORY_C_CASM", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TEST", value: 0X00000008, description: "TR_6T_CATEGORY_C_TEST", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_APP", value: 0X00000010, description: "TR_6T_CATEGORY_C_APP", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_EXTREF", value: 0X00000020, description: "TR_6T_CATEGORY_C_EXTREF", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TRANSPORT", value: 0X00000080, description: "TR_6T_CATEGORY_C_TRANSPORT", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TRANSLOW", value: 0X00000100, description: "TR_6T_CATEGORY_C_TRANSLOW", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CLIENT", value: 0X00000200, description: "TR_6T_CATEGORY_C_CLIENT", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CLIENTRPT", value: 0X00000400, description: "TR_6T_CATEGORY_C_CLIENTRPT", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_CLIENTSTATE", value: 0X00000800, description: "TR_6T_CATEGORY_C_CLIENTSTATE", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TIME", value: 0X00001000, description: "TR_6T_CATEGORY_C_TIME", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_TARGET", value: 0X00002000, description: "TR_6T_CATEGORY_C_TARGET", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_C_DYNAMIC_DATASETS", value: 0X00004000, description: "TR_6T_CATEGORY_C_DYNAMIC_DATASETS", isChecked: false });
      //STANDARD STACK
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_GENERAL", value: 0X00010000, description: "TR_6T_CATEGORY_GENERAL", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_READ", value: 0X00020000, description: "TR_6T_CATEGORY_READ", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_WRITE", value: 0X00040000, description: "TR_6T_CATEGORY_WRITE", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_XML", value: 0X00080000, description: "TR_6T_CATEGORY_XML", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_APP", value: 0X00100000, description: "TR_6T_CATEGORY_APP", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_GOOSE", value: 0X00200000, description: "TR_6T_CATEGORY_GOOSE", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_EXTREF", value: 0X00400000, description: "TR_6T_CATEGORY_EXTREF", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_DISCOVERY", value: 0X00800000, description: "TR_6T_CATEGORY_DISCOVERY", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_REPORT", value: 0X01000000, description: "TR_6T_CATEGORY_REPORT", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_CONTROL", value: 0X02000000, description: "TR_6T_CATEGORY_CONTROL", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_CLIENTPARSEVALUES", value: 0X10000000, description: "TR_6T_CATEGORY_CLIENTPARSEVALUES", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_READ_HANDLER", value: 0X20000000, description: "TR_6T_CATEGORY_READ_HANDLER", isChecked: false });
      this.option6TCategoryList.push({ name: "TR_6T_CATEGORY_TIME_SYNCH", value: 0X80000000, description: "TR_6T_CATEGORY_TIME_SYNCH", isChecked: false });
    }
  }

  private optionSeverityMaskComponentDataOnInit(): void {
    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Exception)] });
    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Error)] });
    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Warning)] });
    this.severitymaskComponentData.push({ value: Object.keys(Severity)[Object.values(Severity).indexOf(Severity.Information)] });
  }
}

enum Source {
  SDG = "SDG",
  SCL = "SCL",
  _6T = "6T"
}

enum Severity {
  Exception = 0x0001,
  Error = 0x0002,
  Warning = 0x0004,
  Information = 0x0008,
  Debug = 0x0020
}

export interface SeverityMaskComponentData {
  value?: string;
}