﻿import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from "@angular/core";
import { Subscription } from "rxjs";
import { HealthWSApi } from "./data/wsApi/wsApi";
import { HealthObjectDTO, EngineStateEnumDTO, EngineExitFailStateEnumDTO, BroadcastEventTypeEnumDTO, BroadcastEventDTO } from "./data/model/models";
import { GlobalDataService } from "./global/global.data.service";
import { AlertService } from "./modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from '@angular/router';


@Component({
	selector: "appHealthComponent",
  template: `
  <div *ngIf="(this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_NO_LICENSE.toString() && warningOpen)" class="alert blinking" [innerHtml]="this.noLicenseDetail"></div>
  <div *ngIf="(this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_IN_GRACE_PERIOD.toString() && warningOpen )" class="alert blinking" [innerHtml]="this.gracePeriodLicenseDetail"></div>
  <div *ngIf="(this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_NO_LICENSE.toString() || this.healthData?.engineState == EngineStateEnumDTO?.RUNNING_IN_GRACE_PERIOD.toString()) && warningOpen">
    <div class="button-close round-button warning" title="Close" (click)="closeWarning()"><img class="image-button" src="../../images/close.svg"></div>
  </div>
  <div style="float: right;">
		<div>{{'TR_STATUS' | translate}}&nbsp;</div>
		<div title="{{'TR_MONITOR' | translate}}">
			<div>{{'TR_MONITOR_ABBREVIATION' | translate}}</div>
			<div *ngIf="this.healthData?.monitorOk" class="glyphicon glyphicon-ok-sign" style="color:green;font-size: 12px"></div>
			<div *ngIf="!this.healthData?.monitorOk" class="glyphicon glyphicon-exclamation-sign" style="color:red;font-size: 12px;"></div>
		</div>
		<div title="{{'TR_ENGINE' | translate}}">
			<div>{{'TR_ENGINE_ABBREVIATION' | translate}}</div>
			<div *ngIf="this.healthData?.engineState == EngineStateEnumDTO?.RUNNING.toString()" class="glyphicon glyphicon-ok-sign" style="color:green;font-size: 12px;"></div>
			<div *ngIf="this.healthData?.engineState==null || this.healthData?.engineState == EngineStateEnumDTO?.NOTRUNNING.toString()" class="glyphicon glyphicon-exclamation-sign" style="color:red;font-size: 12px;"></div>
			<div *ngIf="this.healthData?.engineState != EngineStateEnumDTO?.RUNNING.toString() && this.healthData?.engineState != EngineStateEnumDTO?.NOTRUNNING.toString() && this.healthData?.engineState != null" class="blinking glyphicon glyphicon-ok-sign" style="font-size: 12px;">
        <div style="font-size: 10px;font-weight: bold;vertical-align: text-top;">({{ ('TR_' + this.healthData?.engineState) | translate}})</div>
			</div>
		</div>
		<div style="width:10px;"></div>
		<div><img src="../../images/cpu.svg" class="image" title="{{'TR_CPU_ABBREVIATION' | translate}}"/></div>
		<div title="{{'TR_OVERALL' | translate}}">{{this.healthData?.sysCpu > 0 ? this.healthData?.sysCpu + '%' : '0%'}}/</div>
		<div title="{{'TR_THIS_SYSTEM' | translate}}">{{this.healthData?.engineCpu > 0 ? this.healthData?.engineCpu + '%' : '0%'}}</div>
		<div style="width:10px;"></div>
		<div><img src="../../images/memory.svg" class="image" title="{{'TR_MEMORY_ABBREVIATION' | translate}}"/></div>
		<div title="{{'TR_OVERALL' | translate}}">{{this.healthData?.sysMem > 0 ?  this.healthData?.sysMem + '%' : '0%'}}/</div>
		<div title="{{'TR_THIS_SYSTEM' | translate}}">{{this.healthData?.engineMem > 0 ? this.healthData?.engineMem + '%' : '0%'}}&nbsp;&nbsp;</div>
	</div>
  `,
	styles: [`
		div {
			display: inline-block;
		}
		.before-box {
			clear: left;
		}
		.after-box {
			clear: left;
		}
    .blinking{
        animation:blinkingText 0.8s infinite;
    }
    @keyframes blinkingText{
        0%{     color: orange;    }
        50%{    color: green;     }
        100%{   color: orange;    }
    }
    .warning{
      position: fixed;
      bottom: 70px;
      right: 8px;
      z-index: 999;
    }
    .alert {
      position: fixed;
      bottom: 4px;
      right: 4px;
      z-index: 999;
      font-weight: bold;
      background-color: red;
      border: 2px solid gray;
      font-size: 14px;
    }
`]
})
export class AppHealthComponent implements OnInit, OnDestroy {
  private healthData: HealthObjectDTO = {};
  private isEngineRunning: boolean = true;
  private isEngineExitFailState: boolean = true;
  private EngineStateEnumDTO = EngineStateEnumDTO;
  private noLicenseDetail = "";
  private gracePeriodLicenseDetail = "";
  private warningOpen: boolean = true;
  private healthServiceSubscription: Subscription;
  private reconnectAttempts: number = 0;

  constructor(private router: Router, private healthWSApi: HealthWSApi, private globalDataService: GlobalDataService, private alertService: AlertService, private translate: TranslateService) { }

	public ngOnInit(): void {
    this.getHealth();
    this.translate.get("TR_RUNNING_NO_LICENSE_DETAIL", { URL_ENGINE: "https://" + this.globalDataService.SDGConfig.gtwHost + ":" + this.globalDataService.SDGConfig.monHttpPort, HOST: this.globalDataService.SDGConfig.gtwHost }).subscribe(res => {
      this.noLicenseDetail = res;
    });
    this.translate.get("TR_RUNNING_GRACE_PERIOD_LICENSE_DETAIL", { URL_ENGINE: "https://" + this.globalDataService.SDGConfig.gtwHost + ":" + this.globalDataService.SDGConfig.monHttpPort, HOST: this.globalDataService.SDGConfig.gtwHost }).subscribe(res => {
      this.gracePeriodLicenseDetail = res;
    });
  }

  public ngOnDestroy(): void {
    if (this.healthWSApi.websocket !== null && this.healthWSApi.websocket.readyState === WebSocket.OPEN)
      this.healthWSApi.websocket.close();
    if (this.healthServiceSubscription != null)
      this.healthServiceSubscription.unsubscribe();
  }

	private getHealth(): void {
    this.healthServiceSubscription = this.healthWSApi.getHealthData().subscribe(
      event => {
        if (event.type === 'message') {
          this.reconnectAttempts = 0
          this.healthData = JSON.parse(event.data);
          this.globalDataService.healthData = this.healthData;

          this.isEngineExitFailState = false;
          if (this.healthData != null && this.healthData.engineState != EngineStateEnumDTO.RUNNING.toString() && this.healthData.engineExitFailState != EngineExitFailStateEnumDTO.SUCCESS.toString())
            this.isEngineExitFailState = true;

          if (this.healthData != null && this.healthData.engineState != EngineStateEnumDTO.RUNNING.toString() && this.healthData.engineState != EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString() && this.healthData.engineState != EngineStateEnumDTO.RUNNING_NO_LICENSE.toString() && this.isEngineRunning) {
            this.isEngineRunning = false;
            window.setTimeout(() => { this.checkIsEngineRunningState() }, 300000);
          }
          else if (this.healthData != null && (this.healthData.engineState == EngineStateEnumDTO.RUNNING.toString() || this.healthData.engineState == EngineStateEnumDTO.RUNNING_IN_GRACE_PERIOD.toString())) {
            this.isEngineRunning = true;
          }
        }
      },
      error => {
        this.healthData = {};
        this.globalDataService.healthData = {};
        if (error != "restart") {
          this.displayRefreshModal();
        }
      }
    );
	}

  private checkIsEngineRunningState(): void {
    if (!this.isEngineRunning ) {
      let broadcastEvent: BroadcastEventDTO = {
        messageKey: "TR_ENGINE_NOT_RESPONDING",
        messageLogMask: 3, //  eventLog = 1, alertPopup = 2
        messageText: "Gateway Engine is not running (check the SDG Engine logs or System logs for possible errors).",
        messageTime: new Date().toUTCString(),
        messageType: BroadcastEventTypeEnumDTO.MessageError,
        parameters: null
      }
      this.alertService.show(broadcastEvent, false, -1);
      this.router.navigate(["/config"]);
    }
  }

  private displayRefreshModal(): void {
    let broadcastEvent: BroadcastEventDTO = {
      messageKey: "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER",
      messageLogMask: 3, //  eventLog = 1, alertPopup = 2
      messageText: "The connection with the Gateway Monitor was lost.<br /> Please refresh your browser when the monitor is running again.",
      messageTime: new Date().toUTCString(),
      messageType: BroadcastEventTypeEnumDTO.MessageError,
      parameters: null
    }
    this.alertService.show(broadcastEvent, false, -1);
  }

  private closeWarning(): void {
    this.warningOpen = false;
  }
}
