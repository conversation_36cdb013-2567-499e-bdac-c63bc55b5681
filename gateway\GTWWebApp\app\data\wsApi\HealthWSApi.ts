﻿import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { Observable, Observer } from "rxjs";
import { Configuration } from '../configuration';
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { share, map } from "rxjs/operators";

@Injectable()
export class HealthWSApi {
  public basePath: string = "ws://localhost/rest";
  public configuration: Configuration = new Configuration();
  public websocket: WebSocket;
  private subject: Subject<any>;
  private isReconnectNeeded = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;

  constructor(private alertService: AlertService, private translate: TranslateService) { }

  public getHealthData(): Subject<any> {
    if (!this.subject)
      this.subject = this.create();
    return this.subject;
  }

  public close(): void {
    if (this.websocket) {
      this.websocket.close();
      this.subject = null;
    }
  }

  private create(): Subject<any> {
    const observable = Observable.create(
      (obs: Observer<any>) => {
        this.createObservable(obs);
      }).pipe(share());

    const observer = {
      next: (data: Object) => {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          this.websocket.send(JSON.stringify(data));
        }
      }
    };
    return Subject.create(observer, observable);
  }

  private createObservable(obs: Observer<any>) {
    if (this.configuration.apiKeys) {
      let wsUrl = "";
      if (location.protocol != 'https:')
        wsUrl = "ws://" + window.location.host + "/getHealth?token=" + this.configuration.apiKeys["Authorization"];
      else
        wsUrl = "wss://" + window.location.host + "/getHealth?token=" + this.configuration.apiKeys["Authorization"];

      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = (openEvent) => {
        // Reset reconnect attempts on successful connection
        this.reconnectAttempts = 0;

        this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getHealth" }).subscribe(res => {
          this.alertService.debug(res);
        });
      };
      // Set up event handlers for the new connection - That is the most important part to keep the subscriber connected
      this.websocket.onmessage = obs.next.bind(obs);

      // Don't initiate reconnect here - let onclose handle it
      // WebSocket spec indicates that onclose will be called after onerror
      this.websocket.onerror = (error) => { };

      this.websocket.onclose = (evt) => {
        // Don't null the subject here to maintain the subscription
        if (evt.reason === "restart") {
          this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getHealth", reason: evt.type }).subscribe(res => {
            this.alertService.debug(res);
            obs.error("restart");
          });
        }
        else {
          this.reconnectWS(obs);
        }
      };
    }
  }

  private reconnectWS(obs: Observer<any>) {
    // Try to reconnect
    this.reconnectAttempts++;
    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      // Set timeout to reconnect
      setTimeout(() => {
        this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getHealth", reconnectAttempt: this.reconnectAttempts }).subscribe(res => {
          this.alertService.debug(res);
        });
        this.createObservable(obs);
      }, this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1)); // Exponential back-off
    }
    else {
      // Max reconnect attempts reached
      // Only now error the observable
      this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getHealth", reason: "Reopen fail" }).subscribe(res => {
        obs.error(res);
      });
      this.close();
    }
  }
}