/**
 * SDG Runtime
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export interface TreeNodeDTO {
    nodeClassName?: string;

    nodeIcon?: number;

    parentName?: string;

    nodeName?: string;

    nodeFullName?: string;

    nodeLabel?: string;

    nodeDescription?: string;

    isExpanded?: boolean;

    isMappingTarget?: boolean;

    isFilterTarget?: boolean;

    isHealthy?: boolean;

    hasChildren?: boolean;

    isWarningActive?: boolean;

    nodeCollectionKind?: TreeNodeDTO.NodeCollectionKindEnum;

    memberClass?: string;

    children?: Array<models.TreeNodeDTO>;

}
export namespace TreeNodeDTO {
    export enum NodeCollectionKindEnum {
        MDO = <any> 'MDO',
        SDO = <any> 'SDO',
        ALL = <any> 'ALL'
    }
}

