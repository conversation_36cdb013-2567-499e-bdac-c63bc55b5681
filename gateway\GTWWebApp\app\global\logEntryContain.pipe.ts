﻿import { Pipe, PipeTransform } from "@angular/core";
import { LogEntryDTO } from "../data/model/models";

@Pipe({ name: "logEntryContain", pure: false})
export class LogEntryContainPipe implements PipeTransform {
  transform(logEntries: Array<LogEntryDTO>, logEntryFilter: LogEntryDTO): Array<LogEntryDTO> {
    if (logEntries == null || !Array.isArray(logEntries) || logEntries.length == 0)
      return 
    return logEntries.filter(log => {
      if (
        log.timeStamp.toLowerCase().indexOf(logEntryFilter.timeStamp.toLowerCase()) !== -1 &&
        log.source.toLowerCase().indexOf(logEntryFilter.source.toLowerCase()) !== -1 &&
        log.category.toLowerCase().indexOf(logEntryFilter.category.toLowerCase()) !== -1 &&
        log.severity.toLowerCase().indexOf(logEntryFilter.severity.toLowerCase()) !== -1 &&
        log.message.toLowerCase().indexOf(logEntryFilter.message.toLowerCase()) !== -1 
      )
      return log;
    });
  }
}