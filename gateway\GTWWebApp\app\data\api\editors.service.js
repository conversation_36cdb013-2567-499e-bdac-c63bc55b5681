System.register(["@angular/core", "@angular/common/http", "../encoder", "../variables", "../configuration"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var __param = (this && this.__param) || function (paramIndex, decorator) {
        return function (target, key) { decorator(target, key, paramIndex); }
    };
    var core_1, http_1, encoder_1, variables_1, configuration_1, EditorsService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (http_1_1) {
                http_1 = http_1_1;
            },
            function (encoder_1_1) {
                encoder_1 = encoder_1_1;
            },
            function (variables_1_1) {
                variables_1 = variables_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            }
        ],
        execute: function () {
            EditorsService = (function () {
                function EditorsService(httpClient, basePath, configuration) {
                    this.httpClient = httpClient;
                    this.basePath = 'http://localhost/rest';
                    this.defaultHeaders = new http_1.HttpHeaders();
                    this.configuration = new configuration_1.Configuration();
                    if (basePath) {
                        this.basePath = basePath;
                    }
                    if (configuration) {
                        this.configuration = configuration;
                        this.basePath = basePath || configuration.basePath || this.basePath;
                    }
                }
                EditorsService.prototype.canConsumeForm = function (consumes) {
                    var form = 'multipart/form-data';
                    for (var _i = 0, consumes_1 = consumes; _i < consumes_1.length; _i++) {
                        var consume = consumes_1[_i];
                        if (form === consume) {
                            return true;
                        }
                    }
                    return false;
                };
                EditorsService.prototype.createOrUpdateEditorObject = function (command, body, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (command === null || command === undefined) {
                        throw new Error('Required parameter command was null or undefined when calling createOrUpdateEditorObject.');
                    }
                    if (body === null || body === undefined) {
                        throw new Error('Required parameter body was null or undefined when calling createOrUpdateEditorObject.');
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [
                        'application/json'
                    ];
                    var httpContentTypeSelected = this.configuration.selectHeaderContentType(consumes);
                    if (httpContentTypeSelected != undefined) {
                        headers = headers.set('Content-Type', httpContentTypeSelected);
                    }
                    return this.httpClient.put(this.basePath + "/editors/" + encodeURIComponent(String(command)), body, {
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                EditorsService.prototype.editorAction = function (objectName, action, objectCollectionKind, parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (objectName === null || objectName === undefined) {
                        throw new Error('Required parameter objectName was null or undefined when calling editorAction.');
                    }
                    if (action === null || action === undefined) {
                        throw new Error('Required parameter action was null or undefined when calling editorAction.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (objectName !== undefined && objectName !== null) {
                        queryParameters = queryParameters.set('objectName', objectName);
                    }
                    if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
                        queryParameters = queryParameters.set('objectCollectionKind', objectCollectionKind);
                    }
                    if (action !== undefined && action !== null) {
                        queryParameters = queryParameters.set('action', action);
                    }
                    if (parameter1 !== undefined && parameter1 !== null) {
                        queryParameters = queryParameters.set('parameter_1', parameter1);
                    }
                    if (parameter2 !== undefined && parameter2 !== null) {
                        queryParameters = queryParameters.set('parameter_2', parameter2);
                    }
                    if (parameter3 !== undefined && parameter3 !== null) {
                        queryParameters = queryParameters.set('parameter_3', parameter3);
                    }
                    if (parameter4 !== undefined && parameter4 !== null) {
                        queryParameters = queryParameters.set('parameter_4', parameter4);
                    }
                    if (parameter5 !== undefined && parameter5 !== null) {
                        queryParameters = queryParameters.set('parameter_5', parameter5);
                    }
                    if (parameter6 !== undefined && parameter6 !== null) {
                        queryParameters = queryParameters.set('parameter_6', parameter6);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/editor/action", {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                EditorsService.prototype.getEditorData = function (command, objectName, parentObjectName, editAtRuntime, objectClassName, objectCollectionKind, observe, reportProgress) {
                    if (observe === void 0) { observe = 'body'; }
                    if (reportProgress === void 0) { reportProgress = false; }
                    if (command === null || command === undefined) {
                        throw new Error('Required parameter command was null or undefined when calling getEditorData.');
                    }
                    if (objectName === null || objectName === undefined) {
                        throw new Error('Required parameter objectName was null or undefined when calling getEditorData.');
                    }
                    if (parentObjectName === null || parentObjectName === undefined) {
                        throw new Error('Required parameter parentObjectName was null or undefined when calling getEditorData.');
                    }
                    if (editAtRuntime === null || editAtRuntime === undefined) {
                        throw new Error('Required parameter editAtRuntime was null or undefined when calling getEditorData.');
                    }
                    var queryParameters = new http_1.HttpParams({ encoder: new encoder_1.CustomHttpUrlEncodingCodec() });
                    if (objectName !== undefined && objectName !== null) {
                        queryParameters = queryParameters.set('objectName', objectName);
                    }
                    if (objectClassName !== undefined && objectClassName !== null) {
                        queryParameters = queryParameters.set('objectClassName', objectClassName);
                    }
                    if (parentObjectName !== undefined && parentObjectName !== null) {
                        queryParameters = queryParameters.set('parentObjectName', parentObjectName);
                    }
                    if (editAtRuntime !== undefined && editAtRuntime !== null) {
                        queryParameters = queryParameters.set('editAtRuntime', editAtRuntime);
                    }
                    if (objectCollectionKind !== undefined && objectCollectionKind !== null) {
                        queryParameters = queryParameters.set('objectCollectionKind', objectCollectionKind);
                    }
                    var headers = this.defaultHeaders;
                    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
                        headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
                    }
                    var httpHeaderAccepts = [
                        'application/json'
                    ];
                    var httpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
                    if (httpHeaderAcceptSelected != undefined) {
                        headers = headers.set('Accept', httpHeaderAcceptSelected);
                    }
                    var consumes = [];
                    return this.httpClient.get(this.basePath + "/editors/" + encodeURIComponent(String(command)), {
                        params: queryParameters,
                        withCredentials: this.configuration.withCredentials,
                        headers: headers,
                        observe: observe,
                        reportProgress: reportProgress
                    });
                };
                EditorsService = __decorate([
                    core_1.Injectable(),
                    __param(1, core_1.Optional()),
                    __param(1, core_1.Inject(variables_1.BASE_PATH)),
                    __param(2, core_1.Optional()),
                    __metadata("design:paramtypes", [http_1.HttpClient, String, configuration_1.Configuration])
                ], EditorsService);
                return EditorsService;
            }());
            exports_1("EditorsService", EditorsService);
        }
    };
});
//# sourceMappingURL=editors.service.js.map