System.register(["@angular/core", "./tooltip.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, tooltip_service_1, TooltipComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (tooltip_service_1_1) {
                tooltip_service_1 = tooltip_service_1_1;
            }
        ],
        execute: function () {
            TooltipComponent = (function () {
                function TooltipComponent(tooltipService) {
                    var _this = this;
                    this.tooltipService = tooltipService;
                    this.tooltipItems = [];
                    this.isShown = false;
                    this.mouseLocation = { left: 0, top: 0 };
                    tooltipService.show.subscribe(function (e) { return _this.showTooltip(e.event, e.obj); });
                    tooltipService.hide.subscribe(function (e) { return _this.isShown = false; });
                }
                Object.defineProperty(TooltipComponent.prototype, "locationCss", {
                    get: function () {
                        return {
                            "z-index": "9999",
                            "background-image": "linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35))",
                            "filter": "drop-shadow(1px 1px 1px rgba(0, 0, 0, .7))",
                            "position": "fixed",
                            "padding": "4px",
                            "display": this.isShown ? "block" : "none",
                            left: this.mouseLocation.left + "px",
                            top: this.mouseLocation.top + "px",
                        };
                    },
                    enumerable: false,
                    configurable: true
                });
                TooltipComponent.prototype.showTooltip = function (event, tooltipItems) {
                    var tooltipItemsContent = "";
                    tooltipItems.forEach(function (tooltipItem) {
                        tooltipItemsContent += tooltipItem.content;
                    });
                    if (tooltipItemsContent != "") {
                        this.isShown = true;
                        this.tooltipItems = tooltipItems;
                        this.mouseLocation = {
                            left: event.clientX,
                            top: event.clientY
                        };
                    }
                };
                TooltipComponent = __decorate([
                    core_1.Component({
                        selector: "tooltipComponent",
                        styles: ["\n    .item {\n      padding: 2px;\n      white-space: pre-line;\n    }\n    .tooltipComponent {\n      //do nor remove need for bitmaskEditorComponent\n    } \n  "],
                        template: "\n      <div [ngStyle]=\"locationCss\" class=\"tooltipComponent panel panel-default\">\n        <div class=\"item\" *ngFor=\"let item of tooltipItems\">\n          <ng-container>{{item.content | translate}}</ng-container>\n        </div>\n      </div>\n    "
                    }),
                    __metadata("design:paramtypes", [tooltip_service_1.TooltipService])
                ], TooltipComponent);
                return TooltipComponent;
            }());
            exports_1("TooltipComponent", TooltipComponent);
        }
    };
});
//# sourceMappingURL=tooltip.component.js.map