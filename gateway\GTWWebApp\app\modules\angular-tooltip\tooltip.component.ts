import { Component, Output, EventEmitter} from "@angular/core";
import { TooltipService } from "./tooltip.service";
import { TooltipItem } from "./tooltip-item";
import { Subject } from 'rxjs';

@Component({
  selector: "tooltipComponent",
  styles: [`
    .item {
      padding: 2px;
      white-space: pre-line;
    }
    .tooltipComponent {
      //do nor remove need for bitmaskEditorComponent
    } 
  `],
  template:`
      <div [ngStyle]="locationCss" class="tooltipComponent panel panel-default">
        <div class="item" *ngFor="let item of tooltipItems">
          <ng-container>{{item.content | translate}}</ng-container>
        </div>
      </div>
    `
})

export class TooltipComponent{
  private tooltipItems: Array<TooltipItem> = [];
  private isShown = false;
  private mouseLocation: { left: number, top: number } = { left: 0, top: 0 };


  constructor(private tooltipService: TooltipService) {
    tooltipService.show.subscribe(e => this.showTooltip(e.event, e.obj));
    tooltipService.hide.subscribe(e => this.isShown = false);
  } 
  
  private get locationCss(){ 
    return {
      "z-index": "9999",
      "background-image": "linear-gradient(to right, rgba(251, 253, 180, 0.95), rgba(251, 253, 180, 0.35))",
      "filter": "drop-shadow(1px 1px 1px rgba(0, 0, 0, .7))",
      "position": "fixed",
      "padding": "4px",
      "display":this.isShown ?  "block":"none",
      left: this.mouseLocation.left + "px",
      top:this.mouseLocation.top + "px",
    };
  }
  
  private showTooltip(event, tooltipItems): void {
    let tooltipItemsContent: string = "";
    tooltipItems.forEach((tooltipItem) => {
      tooltipItemsContent += tooltipItem.content;
    });
    if (tooltipItemsContent != "") {
      this.isShown = true;
      this.tooltipItems = tooltipItems;
      this.mouseLocation = {
        left: event.clientX,
        top: event.clientY
      }
    }
  }
}