/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import * as models from './models';

export enum EngineExitFailStateEnumDTO {
    SUCCESS = <any> 'SUCCESS',
    EXCEPTION = <any> 'EXCEPTION',
    VALIDATE = <any> 'VALIDATE',
    HTTPSTART = <any> 'HTTP_START',
    HTTPCREATE = <any> 'HTTP_CREATE',
    HTTPSSTART = <any> 'HTTPS_START',
    HTTPSCREATE = <any> 'HTTPS_CREATE',
    CREATEWORKSPACEDIR = <any> 'CREATE_WORKSPACE_DIR',
    INILOAD = <any> 'INI_LOAD',
    SAVEPOINTMAP = <any> 'SAVE_POINTMAP',
    INITBEFORECSV = <any> 'INIT_BEFORE_CSV',
    OPCCLASSICSTART = <any> 'OPC_CLASSIC_START',
    LOADPOINTMAP = <any> 'LOAD_POINTMAP',
    CREATETRIALLICENSE = <any> 'CREATE_TRIAL_LICENSE',
    NOLICENSE = <any> 'NO_LICENSE',
    NOINIFILE = <any> 'NO_INI_FILE',
    INVALIDMONITORLOCATION = <any> 'INVALID_MONITOR_LOCATION',
    KEYREJECTED = <any>'KEY_REJECTED'
}
