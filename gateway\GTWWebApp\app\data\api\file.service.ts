/**
 * SDG Config
 * SDG Configuration API
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent }                           from '@angular/common/http';
import { CustomHttpUrlEncodingCodec }                        from '../encoder';

import { Observable }                                        from 'rxjs';


import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';


@Injectable()
export class FileService {

    protected basePath = 'http://localhost/rest';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }

  /**
   * download a file of a specific type
   * 
   * @param fileName file name/path
   * @param fileType file type
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public fileGet(fileName: string, fileType: string, observe?: 'body', reportProgress?: boolean): Observable<Blob>;
  public fileGet(fileName: string, fileType: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Blob>>;
  public fileGet(fileName: string, fileType: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Blob>>;
  public fileGet(fileName: string, fileType: string, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (fileName === null || fileName === undefined) {
      throw new Error('Required parameter fileName was null or undefined when calling fileGet.');
    }

    if (fileType === null || fileType === undefined) {
      throw new Error('Required parameter fileType was null or undefined when calling fileGet.');
    }

    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    if (fileName !== undefined && fileName !== null) {
      queryParameters = queryParameters.set('fileName', <any>fileName);
    }
    if (fileType !== undefined && fileType !== null) {
      queryParameters = queryParameters.set('fileType', <any>fileType);
    }

    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/octet-stream'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];

    return this.httpClient.get(`${this.basePath}/file`,
      {
        params: queryParameters,
        responseType: "blob",
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }
  /**
   * download a file of a specific type
   * 
   * @param fileName file name/path
   * @param fileType file type
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public licenseLogZipFileGet(observe?: 'body', reportProgress?: boolean): Observable<Blob>;
  public licenseLogZipFileGet(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Blob>>;
  public licenseLogZipFileGet(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Blob>>;
  public licenseLogZipFileGet(observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
    let headers = this.defaultHeaders;

    // authentication (Bearer) required
    if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
      headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      'application/octet-stream'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];

    return this.httpClient.get(`${this.basePath}/licenseLogZipFile`,
      {
        params: queryParameters,
        responseType: "blob",
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

    /**
     * upload a file for a work space
     * 
     * @param file file
     * @param fileType file type
     * @param workspaceName workspace name if blank its the current work space
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public filePost(file: File, fileType: string, workspaceName?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public filePost(file: File, fileType: string, workspaceName?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public filePost(file: File, fileType: string, workspaceName?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public filePost(file: File, fileType: string, workspaceName?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (file === null || file === undefined) {
            throw new Error('Required parameter file was null or undefined when calling filePost.');
        }

        if (fileType === null || fileType === undefined) {
            throw new Error('Required parameter fileType was null or undefined when calling filePost.');
        }


        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (fileType !== undefined && fileType !== null) {
            queryParameters = queryParameters.set('fileType', <any>fileType);
        }
        if (workspaceName !== undefined && workspaceName !== null) {
            queryParameters = queryParameters.set('workspaceName', <any>workspaceName);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): void | HttpParams; };
        let useForm = false;
        let convertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        }

        if (file !== undefined) {
            formParams = formParams.append('file', <any>file) || formParams;
        }

        return this.httpClient.post<any>(`${this.basePath}/file`,
            convertFormParamsToString ? formParams.toString() : formParams,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * list files of a requested type
     * 
     * @param fileType file type
     * @param fileExtensions file extensions
     * @param workspaceName workspace name
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public filesGet(fileType: string, fileExtensions?: string, workspaceName?: string, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public filesGet(fileType: string, fileExtensions?: string, workspaceName?: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public filesGet(fileType: string, fileExtensions?: string, workspaceName?: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public filesGet(fileType: string, fileExtensions?: string, workspaceName?: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (fileType === null || fileType === undefined) {
            throw new Error('Required parameter fileType was null or undefined when calling filesGet.');
        }



        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (fileExtensions !== undefined && fileExtensions !== null) {
            queryParameters = queryParameters.set('fileExtensions', <any>fileExtensions);
        }
        if (fileType !== undefined && fileType !== null) {
            queryParameters = queryParameters.set('fileType', <any>fileType);
        }
        if (workspaceName !== undefined && workspaceName !== null) {
            queryParameters = queryParameters.set('workspaceName', <any>workspaceName);
        }

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];

        return this.httpClient.get<any>(`${this.basePath}/files`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * list work spaces
     * 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public workSpacesGet(observe?: 'body', reportProgress?: boolean): Observable<any>;
    public workSpacesGet(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public workSpacesGet(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public workSpacesGet(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // authentication (Bearer) required
        if (this.configuration.apiKeys && this.configuration.apiKeys["Authorization"]) {
            headers = headers.set('Authorization', this.configuration.apiKeys["Authorization"]);
        }

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            'application/json'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.get<any>(`${this.basePath}/work_spaces`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
