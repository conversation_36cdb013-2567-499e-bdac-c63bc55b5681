System.register(["@angular/core", "../../modules/angular-tooltip/tooltip-item", "../../modules/angular-tooltip/tooltip.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, tooltip_item_1, tooltip_service_1, TooltipDirective;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (tooltip_item_1_1) {
                tooltip_item_1 = tooltip_item_1_1;
            },
            function (tooltip_service_1_1) {
                tooltip_service_1 = tooltip_service_1_1;
            }
        ],
        execute: function () {
            TooltipDirective = (function () {
                function TooltipDirective(tooltipService) {
                    this.tooltipService = tooltipService;
                }
                TooltipDirective.prototype.onMouseEnter = function (event) {
                    var _this = this;
                    this.tooltipId = setTimeout(function () {
                        _this.loadTooltip(event);
                    }, 1000);
                    event.preventDefault();
                };
                TooltipDirective.prototype.onMouseLeave = function (event) {
                    clearTimeout(this.tooltipId);
                    this.tooltipService.hide.next({ event: event, obj: null });
                    event.preventDefault();
                };
                TooltipDirective.prototype.loadTooltip = function (event) {
                    var tooltipItems = [];
                    this.tooltipContents.forEach(function (tooltipContent) {
                        tooltipItems.push(new tooltip_item_1.TooltipItem(tooltipContent));
                    });
                    if (tooltipItems.length > 0)
                        this.tooltipService.show.next({ event: event, obj: tooltipItems });
                };
                __decorate([
                    core_1.Input("tooltip"),
                    __metadata("design:type", Array)
                ], TooltipDirective.prototype, "tooltipContents", void 0);
                TooltipDirective = __decorate([
                    core_1.Directive({
                        selector: "[tooltip]",
                        host: {
                            "(mouseenter)": "onMouseEnter($event)",
                            "(mouseleave)": "onMouseLeave($event)"
                        }
                    }),
                    __metadata("design:paramtypes", [tooltip_service_1.TooltipService])
                ], TooltipDirective);
                return TooltipDirective;
            }());
            exports_1("TooltipDirective", TooltipDirective);
        }
    };
});
//# sourceMappingURL=tooltip.directive.js.map